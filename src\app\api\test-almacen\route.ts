import { NextResponse } from 'next/server';
import { executeQuery } from '@/eccsa_back/lib/db';

export async function GET() {
  try {
    console.log('Testing almacen table connection...');
    
    // Test if almacen table exists
    const tableCheck = await executeQuery({
      query: "SHOW TABLES LIKE 'almacen'"
    });
    
    console.log('Table check result:', tableCheck);
    
    // Get table structure
    const tableStructure = await executeQuery({
      query: "DESCRIBE almacen"
    });
    
    console.log('Table structure:', tableStructure);
    
    // Count total records
    const countResult = await executeQuery({
      query: "SELECT COUNT(*) as total FROM almacen"
    });
    
    console.log('Total records:', countResult);
    
    // Get first 5 records
    const sampleData = await executeQuery({
      query: `
        SELECT 
          id, numero_almacen, modelo_existente, marcas, 
          cantidad_nuevo, precio_venta, precio_ml, activo
        FROM almacen 
        LIMIT 5
      `
    });
    
    console.log('Sample data:', sampleData);
    
    return NextResponse.json({
      success: true,
      tableExists: Array.isArray(tableCheck) && tableCheck.length > 0,
      structure: tableStructure,
      totalRecords: countResult,
      sampleData: sampleData
    });
    
  } catch (error) {
    console.error('Error testing almacen table:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
