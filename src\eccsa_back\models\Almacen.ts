import { executeQuery } from '@/eccsa_back/lib/db';

/**
 * Interfaz para el modelo de almacén
 */
export interface Almacen {
  id?: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  minimo?: number;
  maximo?: number;
  pedir_cantidad?: number;
  precio_us?: number;
  precio_mx?: number;
  impuesto?: number;
  codigo_sat?: string;
  nota?: string;
  proveedor?: string;
  tiempo_entrega_proveedor?: string;
  fecha_pedido?: Date | string;
  fecha_recibido?: Date | string;
  fecha_creacion?: Date;
  fecha_actualizacion?: Date;
  activo?: boolean;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

/**
 * Clase para manejar operaciones CRUD del almacén
 */
export class AlmacenModel {
  /**
   * Obtiene todos los productos del almacén
   * @returns {Promise<Almacen[]>} Lista de productos
   */
  static async getAll(): Promise<Almacen[]> {
    try {
      const products = await executeQuery({
        query: `
          SELECT
            id, numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
            fecha_creacion, fecha_actualizacion, activo
          FROM almacen
          WHERE activo = TRUE
          ORDER BY numero_almacen
        `
      }) as Almacen[];

      return products;
    } catch (error) {
      console.error('Error al obtener productos del almacén:', error);
      throw error;
    }
  }

  /**
   * Obtiene un producto por ID
   * @param {number} id - ID del producto
   * @returns {Promise<Almacen | null>} Producto encontrado o null
   */
  static async getById(id: number): Promise<Almacen | null> {
    try {
      const products = await executeQuery({
        query: `
          SELECT
            id, numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
            fecha_creacion, fecha_actualizacion, activo
          FROM almacen
          WHERE id = ? AND activo = TRUE
        `,
        values: [id]
      }) as Almacen[];

      return products.length > 0 ? products[0] : null;
    } catch (error) {
      console.error('Error al obtener producto por ID:', error);
      throw error;
    }
  }

  /**
   * Obtiene un producto por número de almacén
   * @param {string} numero_almacen - Número de almacén
   * @returns {Promise<Almacen | null>} Producto encontrado o null
   */
  static async getByNumber(numero_almacen: string): Promise<Almacen | null> {
    try {
      const products = await executeQuery({
        query: `
          SELECT
            id, numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
            fecha_creacion, fecha_actualizacion, activo
          FROM almacen
          WHERE numero_almacen = ? AND activo = TRUE
        `,
        values: [numero_almacen]
      }) as Almacen[];

      return products.length > 0 ? products[0] : null;
    } catch (error) {
      console.error('Error al obtener producto por número:', error);
      throw error;
    }
  }

  /**
   * Crea un nuevo producto en el almacén
   * @param {Almacen} product - Datos del producto
   * @returns {Promise<number>} ID del producto creado
   */
  static async create(product: Almacen): Promise<number> {
    try {
      const result = await executeQuery({
        query: `
          INSERT INTO almacen (
            numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        values: [
          product.numero_almacen,
          product.estante || null,
          product.modelo_existente,
          product.descripcion || null,
          product.precio_venta || null,
          product.precio_ml || null,
          product.vendidos || 0,
          product.cantidad_nuevo || 0,
          product.minimo || 0,
          product.maximo || 0,
          product.pedir_cantidad || 0,
          product.precio_us || null,
          product.precio_mx || null,
          product.impuesto || 16.00,
          product.codigo_sat || null,
          product.nota || null,
          product.proveedor || null,
          product.tiempo_entrega_proveedor || null,
          product.fecha_pedido || null,
          product.fecha_recibido || null
        ]
      }) as { insertId: number };

      return result.insertId;
    } catch (error) {
      console.error('Error al crear producto en almacén:', error);
      throw error;
    }
  }

  /**
   * Actualiza un producto del almacén
   * @param {number} id - ID del producto
   * @param {Partial<Almacen>} updates - Campos a actualizar
   * @returns {Promise<boolean>} True si se actualizó correctamente
   */
  static async update(id: number, updates: Partial<Almacen>): Promise<boolean> {
    try {
      const fields = [];
      const values = [];

      // Construir dinámicamente la consulta UPDATE
      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'fecha_creacion' && key !== 'fecha_actualizacion') {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      });

      if (fields.length === 0) {
        return false;
      }

      fields.push('fecha_actualizacion = NOW()');
      values.push(id);

      await executeQuery({
        query: `
          UPDATE almacen
          SET ${fields.join(', ')}
          WHERE id = ?
        `,
        values
      });

      return true;
    } catch (error) {
      console.error('Error al actualizar producto:', error);
      throw error;
    }
  }

  /**
   * Elimina (desactiva) un producto del almacén
   * @param {number} id - ID del producto
   * @returns {Promise<boolean>} True si se eliminó correctamente
   */
  static async delete(id: number): Promise<boolean> {
    try {
      await executeQuery({
        query: `
          UPDATE almacen
          SET activo = FALSE, fecha_actualizacion = NOW()
          WHERE id = ?
        `,
        values: [id]
      });

      return true;
    } catch (error) {
      console.error('Error al eliminar producto:', error);
      throw error;
    }
  }

  /**
   * Busca productos en el almacén
   * @param {string} searchTerm - Término de búsqueda
   * @returns {Promise<Almacen[]>} Lista de productos encontrados
   */
  static async search(searchTerm: string): Promise<Almacen[]> {
    try {
      const searchPattern = `%${searchTerm}%`;
      const products = await executeQuery({
        query: `
          SELECT
            id, numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
            fecha_creacion, fecha_actualizacion, activo
          FROM almacen
          WHERE activo = TRUE
            AND (
              numero_almacen LIKE ? OR
              modelo_existente LIKE ? OR
              descripcion LIKE ? OR
              proveedor LIKE ? OR
              codigo_sat LIKE ?
            )
          ORDER BY numero_almacen
        `,
        values: [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern]
      }) as Almacen[];

      return products;
    } catch (error) {
      console.error('Error al buscar productos:', error);
      throw error;
    }
  }

  /**
   * Obtiene productos con stock bajo
   * @returns {Promise<Almacen[]>} Lista de productos con stock bajo
   */
  static async getLowStock(): Promise<Almacen[]> {
    try {
      const products = await executeQuery({
        query: `
          SELECT
            id, numero_almacen, estante, modelo_existente, descripcion,
            precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
            pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
            proveedor, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
            fecha_creacion, fecha_actualizacion, activo
          FROM almacen
          WHERE activo = TRUE
            AND cantidad_nuevo < minimo
            AND minimo > 0
          ORDER BY (cantidad_nuevo - minimo) ASC
        `
      }) as Almacen[];

      return products;
    } catch (error) {
      console.error('Error al obtener productos con stock bajo:', error);
      throw error;
    }
  }

  /**
   * Obtiene estadísticas del almacén
   * @returns {Promise<any>} Estadísticas del almacén
   */
  static async getStats(): Promise<any> {
    try {
      const stats = await executeQuery({
        query: `
          SELECT
            COUNT(*) as total_productos,
            SUM(cantidad_nuevo) as total_cantidad,
            AVG(precio_venta) as precio_promedio,
            COUNT(CASE WHEN cantidad_nuevo < minimo AND minimo > 0 THEN 1 END) as productos_stock_bajo,
            COUNT(CASE WHEN cantidad_nuevo = 0 THEN 1 END) as productos_sin_stock,
            SUM(precio_venta * cantidad_nuevo) as valor_total_inventario
          FROM almacen
          WHERE activo = TRUE
        `
      }) as any[];

      return stats[0];
    } catch (error) {
      console.error('Error al obtener estadísticas:', error);
      throw error;
    }
  }
}
