// Script para agregar a <PERSON> y Esteban a la base de datos
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config({ path: '.env.local' });

// Configuración de la conexión a la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'eccsa',
  password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
  database: process.env.DB_NAME || 'EccsaWeb',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : undefined,
};

async function executeQuery(query, params = []) {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function checkUsers() {
  try {
    console.log('Verificando usuarios en la base de datos...');

    // Consultar los usuarios actuales
    const users = await executeQuery(`
      SELECT
        id,
        nombre_usuario as username,
        nombre as name,
        nivel_ingeniero as role_level,
        correo as email
      FROM usuarios
    `);

    console.log('Usuarios encontrados:');
    console.table(users);

    return users;
  } catch (error) {
    console.error('Error al verificar usuarios:', error);
    throw error;
  }
}

async function addUsers() {
  try {
    console.log('Agregando a Oscar y Esteban a la base de datos...');

    // Verificar si los usuarios ya existen
    const existingUsers = await executeQuery(`
      SELECT nombre_usuario FROM usuarios
      WHERE nombre_usuario IN ('oscar.castillo', 'esteban.carrera')
    `);

    const existingUsernames = existingUsers.map(user => user.nombre_usuario);

    // Agregar a Oscar si no existe
    if (!existingUsernames.includes('oscar.castillo')) {
      await executeQuery(`
        INSERT INTO usuarios (
          nombre,
          nombre_usuario,
          foto_usuario,
          correo,
          contrasena,
          telefono,
          nivel_ingeniero,
          descripcion
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'Oscar Castillo',
        'oscar.castillo',
        '/images/usuarios/oscar.jpg',
        '<EMAIL>',
        'password123',
        '8182803296',
        0,
        'Jefe de automatización industrial con acceso administrativo'
      ]);
      console.log('Usuario Oscar Castillo agregado correctamente');
    } else {
      console.log('El usuario Oscar Castillo ya existe');

      // Actualizar nivel a 0 si ya existe
      await executeQuery(`
        UPDATE usuarios
        SET nivel_ingeniero = 0
        WHERE nombre_usuario = 'oscar.castillo'
      `);
      console.log('Nivel de Oscar Castillo actualizado a 0 (Administrador)');
    }

    // Agregar a Esteban si no existe
    if (!existingUsernames.includes('esteban.carrera')) {
      await executeQuery(`
        INSERT INTO usuarios (
          nombre,
          nombre_usuario,
          foto_usuario,
          correo,
          contrasena,
          telefono,
          nivel_ingeniero,
          descripcion
        ) VALUES (
          'Esteban Carrera',
          'esteban.carrera',
          '/images/usuarios/esteban.jpg',
          '<EMAIL>',
          'password123',
          '8187043546',
          0,
          'Especialista en ventas y soporte técnico con acceso administrativo'
        )
      `);
      console.log('Usuario Esteban Carrera agregado correctamente');
    } else {
      console.log('El usuario Esteban Carrera ya existe');

      // Actualizar nivel a 0 si ya existe
      await executeQuery(`
        UPDATE usuarios
        SET nivel_ingeniero = 0
        WHERE nombre_usuario = 'esteban.carrera'
      `);
      console.log('Nivel de Esteban Carrera actualizado a 0 (Administrador)');
    }

    // Verificar los cambios
    await checkUsers();

    return true;
  } catch (error) {
    console.error('Error al agregar usuarios:', error);
    throw error;
  }
}

// Ejecutar las funciones
async function main() {
  try {
    console.log('=== Estado inicial de los usuarios ===');
    await checkUsers();

    console.log('\n=== Agregando usuarios ===');
    await addUsers();

    console.log('\n=== Operación completada con éxito ===');
  } catch (error) {
    console.error('Error en el script principal:', error);
    process.exit(1);
  }
}

main();
