'use client';

import { FaExclamationTriangle, FaTrash, FaUserTimes, FaCheckCircle } from 'react-icons/fa';

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  type: 'danger' | 'warning' | 'info';
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
}

export default function ConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  isLoading = false
}: ConfirmModalProps) {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'danger':
        return <FaTrash className="w-8 h-8 text-red-500" />;
      case 'warning':
        return <FaExclamationTriangle className="w-8 h-8 text-yellow-500" />;
      case 'info':
        return <FaCheckCircle className="w-8 h-8 text-blue-500" />;
      default:
        return <FaExclamationTriangle className="w-8 h-8 text-yellow-500" />;
    }
  };

  const getTypeClass = () => {
    switch (type) {
      case 'danger':
        return 'confirm-modal-danger';
      case 'warning':
        return 'confirm-modal-warning';
      case 'info':
        return 'confirm-modal-info';
      default:
        return 'confirm-modal-warning';
    }
  };

  return (
    <div className="confirm-modal-overlay" onClick={onClose}>
      <div className={`confirm-modal ${getTypeClass()}`} onClick={(e) => e.stopPropagation()}>
        <div className="confirm-modal-header">
          <div className="confirm-modal-icon">
            {getIcon()}
          </div>
          <h3 className="confirm-modal-title">{title}</h3>
        </div>

        <div className="confirm-modal-body">
          <p className="confirm-modal-message">{message}</p>
        </div>

        <div className="confirm-modal-footer">
          <button
            type="button"
            className="confirm-modal-button confirm-modal-button-cancel"
            onClick={onClose}
            disabled={isLoading}
          >
            {cancelText}
          </button>
          <button
            type="button"
            className={`confirm-modal-button confirm-modal-button-confirm ${type}`}
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? 'Procesando...' : confirmText}
          </button>
        </div>
      </div>
    </div>
  );
}
