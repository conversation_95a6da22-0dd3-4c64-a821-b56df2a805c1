import mysql from 'serverless-mysql';

// Determine if we're using a mock database for development
const useMockDb = process.env.USE_MOCK_DB === 'true' || !process.env.DB_HOST;

// Database connection configuration
const db = mysql({
  config: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'eccsa',
    password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
    database: process.env.DB_NAME || 'EccsaWeb',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    connectTimeout: 10000, // 10 seconds timeout
  },
});

// Helper function to execute SQL queries
export async function executeQuery({ query, values = [] }: { query: string; values?: any[] }) {
  try {
    if (useMockDb) {
      console.log('Using mock database. Query:', query);
      // Return mock data based on the query
      return mockDatabaseResponse(query, values);
    }

    // Connect to the database
    const results = await db.query(query, values);
    // Release the connection
    await db.end();
    // Return the results
    return results;
  } catch (error) {
    console.error('Database query error:', error);

    if (useMockDb) {
      // If we're using a mock DB, don't throw errors
      console.log('Returning mock data due to error');
      return mockDatabaseResponse(query, values);
    }

    throw error;
  }
}

// Function to generate mock responses for development
function mockDatabaseResponse(query: string, values: any[] = []): any {
  console.log('MOCK DB: Generating mock response for query:', query);

  // Mock users for authentication
  if (query.includes('SELECT') && query.includes('FROM users')) {
    if (values.length >= 2 && values[0] === 'admin' && values[1] === 'eccsa2023') {
      return [{ id: 1, username: 'admin', role: 'admin' }];
    }
    return [];
  }

  // Mock products list
  if (query.includes('SELECT') && query.includes('FROM products')) {
    if (query.includes('WHERE p.id = ?')) {
      const productId = values[0];
      return [mockProducts.find(p => p.id === productId) || null];
    }
    return mockProducts;
  }

  // Mock categories
  if (query.includes('SELECT') && query.includes('FROM categories')) {
    return mockCategories;
  }

  // Mock insert operations
  if (query.includes('INSERT INTO')) {
    return { insertId: Math.floor(Math.random() * 1000) + 100 };
  }

  // Mock update operations
  if (query.includes('UPDATE')) {
    return { affectedRows: 1 };
  }

  // Mock delete operations
  if (query.includes('DELETE')) {
    return { affectedRows: 1 };
  }

  // Default mock response for any other query
  return [];
}

// Mock data for development
const mockProducts = [
  {
    id: 1,
    name: "PLC Siemens S7-1200",
    description: "Controlador lógico programable para automatización industrial",
    price: 5000,
    category_id: 1,
    category_name: "Controladores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "Siemens",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 2,
    name: "Variador de Frecuencia ABB ACS580",
    description: "Variador de frecuencia para control de motores",
    price: 8000,
    category_id: 2,
    category_name: "Variadores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "ABB",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 3,
    name: "Sensor de Proximidad Allen-Bradley",
    description: "Sensor inductivo para detección de objetos metálicos",
    price: 1200,
    category_id: 3,
    category_name: "Sensores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "Allen-Bradley",
    created_at: new Date(),
    updated_at: new Date()
  }
];

const mockCategories = [
  {
    id: 1,
    name: "Controladores",
    description: "PLCs y sistemas de control",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 2,
    name: "Variadores",
    description: "Variadores de frecuencia y arrancadores",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 3,
    name: "Sensores",
    description: "Sensores industriales de todo tipo",
    created_at: new Date(),
    updated_at: new Date()
  }
];

// Function to get a connection string (for reference)
export function getConnectionString() {
  return `server=${process.env.DB_HOST};uid=${process.env.DB_USER};pwd=${process.env.DB_PASSWORD};database=${process.env.DB_NAME}`;
}

export default db;
