#!/usr/bin/env node

/**
 * Script para automatizar el proceso de build y creación del archivo ZIP para producción
 * Este script realiza las siguientes tareas:
 * 1. Limpia la caché y directorios temporales
 * 2. Ejecuta el comando de build de Next.js
 * 3. Crea el directorio 'pagina' si no existe
 * 4. Comprime el contenido del directorio 'out' en un archivo ZIP
 * 5. Guarda el archivo ZIP en el directorio 'pagina' con el nombre 'eccsa web.zip'
 *
 * Este archivo ZIP está listo para ser subido a cPanel y descomprimido en la carpeta raíz
 * para que la página web se vea y funcione exactamente igual que en el entorno local.
 * @type {module}
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtener el directorio actual del módulo ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },

  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Función para imprimir mensajes con formato
function printMessage(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();

  switch (type) {
    case 'info':
      console.log(`${colors.fg.blue}[${timestamp}] INFO:${colors.reset} ${message}`);
      break;
    case 'success':
      console.log(`${colors.fg.green}[${timestamp}] ÉXITO:${colors.reset} ${message}`);
      break;
    case 'warning':
      console.log(`${colors.fg.yellow}[${timestamp}] ADVERTENCIA:${colors.reset} ${message}`);
      break;
    case 'error':
      console.log(`${colors.fg.red}[${timestamp}] ERROR:${colors.reset} ${message}`);
      break;
    default:
      console.log(`[${timestamp}] ${message}`);
  }
}

// Función para ejecutar comandos y mostrar la salida
function executeCommand(command, errorMessage) {
  try {
    printMessage(`Ejecutando: ${command}`);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    printMessage(`${errorMessage}: ${error.message}`, 'error');
    return false;
  }
}

// Función para encontrar todos los archivos HTML en un directorio y sus subdirectorios
function findHtmlFiles(dir) {
  const htmlFiles = [];

  // Función recursiva para buscar archivos HTML
  function searchDir(currentDir) {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // Si es un directorio, buscar recursivamente
        searchDir(filePath);
      } else if (file.endsWith('.html')) {
        // Si es un archivo HTML, agregarlo a la lista
        htmlFiles.push(filePath);
      }
    }
  }

  // Iniciar la búsqueda
  searchDir(dir);

  return htmlFiles;
}

// Función principal
async function main() {
  try {
    // Paso 1: Limpiar la caché
    printMessage('Limpiando caché...', 'info');

    if (!executeCommand('npm run clean', 'Error al limpiar la caché')) {
      process.exit(1);
    }

    // Paso 2: Ejecutar el comando de build
    printMessage('Iniciando proceso de build para producción...', 'info');

    if (!executeCommand('npm run build', 'Error al ejecutar el build')) {
      process.exit(1);
    }

    printMessage('Build completado exitosamente', 'success');

    // Paso 3: Verificar que el directorio 'out' existe
    const outDir = path.join(__dirname, 'out');

    if (!fs.existsSync(outDir)) {
      printMessage('El directorio "out" no existe. Asegúrate de que el build se haya completado correctamente.', 'error');
      process.exit(1);
    }

    // Paso 3.1: Copiar archivos adicionales al directorio de salida
    printMessage('Copiando archivos adicionales para corregir problemas de MIME type...', 'info');

    // Lista de archivos a copiar
    const filesToCopy = [
      { source: 'public/htaccess.txt', dest: '.htaccess' },
      { source: 'public/resource-server.php', dest: 'resource-server.php' },
      { source: 'public/fallback.html', dest: 'fallback.html' },
      { source: 'public/dynamic-loader.js', dest: 'dynamic-loader.js' },
      { source: 'public/css-handler.php', dest: 'css-handler.php' },
      { source: 'public/css-fallback.js', dest: 'css-fallback.js' }
    ];

    // Copiar cada archivo
    for (const file of filesToCopy) {
      const sourceFile = path.join(__dirname, file.source);
      const destFile = path.join(outDir, file.dest);

      try {
        if (fs.existsSync(sourceFile)) {
          fs.copyFileSync(sourceFile, destFile);
          printMessage(`Archivo ${file.dest} copiado correctamente`, 'success');
        } else {
          printMessage(`Archivo ${file.source} no encontrado`, 'warning');
        }
      } catch (error) {
        printMessage(`Error al copiar el archivo ${file.source}: ${error.message}`, 'error');
        printMessage('Continuando con el proceso...', 'warning');
      }
    }

    // Ejecutar el script de inlineado de recursos
    printMessage('Ejecutando script de inlineado de recursos...', 'info');

    try {
      const inlineResourcesModule = path.join(__dirname, 'public', 'inline-resources.js');
      if (fs.existsSync(inlineResourcesModule)) {
        const inlineResources = require(inlineResourcesModule);
        await inlineResources(outDir);
        printMessage('Recursos inlineados correctamente', 'success');
      } else {
        printMessage('Archivo inline-resources.js no encontrado', 'warning');
      }
    } catch (error) {
      printMessage(`Error al ejecutar el script de inlineado de recursos: ${error.message}`, 'error');
      printMessage('Continuando con el proceso...', 'warning');
    }

    // Generar versión estática del sitio
    printMessage('Generando versión estática del sitio...', 'info');

    try {
      const staticVersionModule = path.join(__dirname, 'public', 'generate-static-version.js');
      if (fs.existsSync(staticVersionModule)) {
        const generateStaticVersion = require(staticVersionModule);
        const staticDir = path.join(outDir, 'static');
        await generateStaticVersion(outDir, staticDir);
        printMessage('Versión estática generada correctamente', 'success');
      } else {
        printMessage('Archivo generate-static-version.js no encontrado', 'warning');
      }
    } catch (error) {
      printMessage(`Error al generar la versión estática del sitio: ${error.message}`, 'error');
      printMessage('Continuando con el proceso...', 'warning');
    }

    // Modificar los archivos HTML para incluir el script de fallback CSS
    printMessage('Modificando archivos HTML para incluir el script de fallback CSS...', 'info');

    try {
      // Obtener todos los archivos HTML en el directorio de salida
      const htmlFiles = findHtmlFiles(outDir);

      // Para cada archivo HTML, agregar el script de fallback CSS
      let modifiedCount = 0;

      for (const htmlFile of htmlFiles) {
        const htmlContent = fs.readFileSync(htmlFile, 'utf8');

        // Verificar si el script ya está incluido
        if (htmlContent.includes('css-fallback.js')) {
          continue;
        }

        // Agregar el script antes del cierre del head
        const modifiedContent = htmlContent.replace(
          '</head>',
          '<script src="/css-fallback.js"></script></head>'
        );

        // Guardar el archivo modificado
        fs.writeFileSync(htmlFile, modifiedContent, 'utf8');
        modifiedCount++;
      }

      printMessage(`${modifiedCount} archivos HTML modificados correctamente`, 'success');
    } catch (error) {
      printMessage(`Error al modificar archivos HTML: ${error.message}`, 'error');
      printMessage('Continuando con el proceso...', 'warning');
    }

    // Paso 4: Usar la ruta absoluta a la carpeta 'pagiana'
    const pagianaDir = 'C:\\Users\\<USER>\\OneDrive\\Documentos\\GitHub\\Eccsa-web\\pagiana';

    if (!fs.existsSync(pagianaDir)) {
      printMessage(`Creando directorio "${pagianaDir}"...`, 'info');
      fs.mkdirSync(pagianaDir, { recursive: true });
    } else {
      printMessage(`Usando directorio existente "${pagianaDir}"...`, 'info');
    }

    // Paso 5: Comprimir el contenido del directorio 'out' en un archivo ZIP
    printMessage('Comprimiendo archivos para producción...', 'info');

    const zipFilePath = path.join(pagianaDir, 'eccsa web.zip');

    // Usar PowerShell para comprimir archivos en Windows
    const compressionCommand = `powershell -Command "Compress-Archive -Path '${outDir}\\*' -DestinationPath '${zipFilePath}' -Force"`;

    if (!executeCommand(compressionCommand, 'Error al comprimir archivos')) {
      process.exit(1);
    }

    printMessage(`Archivo ZIP creado exitosamente en: ${zipFilePath}`, 'success');
    printMessage('El proceso de build para producción ha finalizado correctamente', 'success');
    printMessage('', 'info');
    printMessage('Instrucciones para desplegar en cPanel:', 'info');
    printMessage('1. Accede a tu cPanel', 'info');
    printMessage('2. Ve a Administrador de Archivos', 'info');
    printMessage('3. Navega a la carpeta donde quieres desplegar el sitio (generalmente la carpeta raíz del dominio)', 'info');
    printMessage('4. Sube el archivo "eccsa web.zip" desde la carpeta:', 'info');
    printMessage(`   ${pagianaDir}`, 'info');
    printMessage('5. Selecciona el archivo ZIP subido y haz clic en "Extraer" para descomprimir su contenido', 'info');
    printMessage('6. Verifica que los siguientes archivos estén presentes en la raíz del sitio:', 'info');
    printMessage('   - .htaccess', 'info');
    printMessage('   - resource-server.php', 'info');
    printMessage('   - fallback.html', 'info');
    printMessage('   - dynamic-loader.js', 'info');
    printMessage('   - css-handler.php', 'info');
    printMessage('   - css-fallback.js', 'info');
    printMessage('7. Si sigues teniendo problemas con los tipos MIME, sigue estos pasos adicionales:', 'info');
    printMessage('   a. En cPanel, ve a "MIME Types"', 'info');
    printMessage('   b. Agrega los tipos MIME que falten según la documentación', 'info');
    printMessage('   c. Contacta al soporte de tu hosting si los problemas persisten', 'info');
    printMessage('8. El sitio debería estar funcionando correctamente después de estos pasos', 'info');

  } catch (error) {
    printMessage(`Error inesperado: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
