Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   ECCSA Web - Servidor de Desarrollo" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Iniciando servidor en http://localhost:3000" -ForegroundColor Green
Write-Host "Presiona Ctrl+C para detener el servidor" -ForegroundColor Yellow
Write-Host ""

# Limpiar caché si existe
if (Test-Path ".next") {
    Write-Host "Limpiando caché anterior..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force .next -ErrorAction SilentlyContinue
}

# Iniciar el servidor de desarrollo
Write-Host "Iniciando Next.js..." -ForegroundColor Green
try {
    npx next dev
} catch {
    Write-Host "Error al iniciar el servidor: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Servidor detenido." -ForegroundColor Red
Read-Host "Presiona Enter para continuar"
