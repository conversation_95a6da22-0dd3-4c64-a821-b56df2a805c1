/**
 * Application constants
 */

// Navigation
export const MAIN_ROUTES = {
  HOME: '/',
  SERVICES: '/servicios',
  PRODUCTS: '/productos',
  PROJECTS: '/proyectos',
  CLIENTS: '/clients',
  CONTACT: '/contacto',
  REPORTS: '/reports',
  CART: '/carrito',
  ADMIN: '/admin',
};

// Routes for prefetching
export const PREFETCH_ROUTES = [
  MAIN_ROUTES.HOME,
  MAIN_ROUTES.SERVICES,
  MAIN_ROUTES.PRODUCTS,
  MAIN_ROUTES.PROJECTS,
  MAIN_ROUTES.CONTACT,
  MAIN_ROUTES.CART,
];

// Contact information
export const CONTACT_INFO = {
  ADDRESS: 'Fray <PERSON> 1713, <PERSON><PERSON><PERSON>, 64820 Monterrey, N.L.',
  PHONE: '81 8358 9075',
  EMAIL: '<EMAIL>',
  HOURS: 'Lunes a Viernes de 9:00 AM a 6:30 PM',
  WHATSAPP_SALES: '+5218187043546',
  WHATSAPP_SERVICE: '+5218182803296',
  EMAIL_SERVICE: '<EMAIL>',
  EMAIL_SALES: '<EMAIL>',
  MAPS_EMBED_URL: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3596.3028212352097!2d-100.35778492394847!3d25.6626097138609!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8662be2b5a2e5cab%3A0x1c1c6a8e4f1d0ba9!2sFray%20Luis%20de%20Le%C3%B3n%201713%2C%20Jard%C3%ADn%20Espa%C3%B1ol%2C%2064820%20Monterrey%2C%20N.L.!5e0!3m2!1ses-419!2smx!4v1718493600000!5m2!1ses-419!2smx',
};

// Default messages
export const DEFAULT_MESSAGES = {
  WHATSAPP: 'Hola, estoy interesado en sus servicios de automatización industrial.',
};

// Image paths
export const IMAGES = {
  LOGO: {
    SMALL: '/images/logos/logo_pequeno.png', // Nombre normalizado sin ñ
    LARGE: '/images/logos/logo_largo.png',
  },
  DEFAULT: '/images/defaults/Automatizacion-Industrial.jpg', // Nombre normalizado sin ó
};

// Company information
export const COMPANY_INFO = {
  NAME: 'ECCSA Automation',
  YEARS_EXPERIENCE: 25,
  SIEMENS_PARTNER: 'Somos SIEMENS Approved Partner',
};

// Social media links
export const SOCIAL_MEDIA = {
  FACEBOOK: 'https://facebook.com',
  LINKEDIN: 'https://linkedin.com',
  INSTAGRAM: 'https://instagram.com',
  YOUTUBE: 'https://youtube.com',
};

// Animation durations
export const ANIMATION = {
  TRANSITION_DURATION: 50, // ms
  SLIDER_INTERVAL: 5000, // ms
  BRANDS_SCROLL_INTERVAL: 30, // ms
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  MOBILE: 576,
  TABLET: 768,
  LAPTOP: 992,
  DESKTOP: 1200,
};
