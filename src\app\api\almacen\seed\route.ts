import { NextResponse } from 'next/server';
import { executeQuery } from '@/eccsa_back/lib/db';

export async function POST() {
  try {
    console.log('Seeding almacen table with sample data...');
    
    // Datos de ejemplo para la tabla almacén
    const sampleProducts = [
      {
        numero_almacen: 'ALM001',
        estante: 'A1-01',
        modelo_existente: 'Siemens S7-1200 CPU 1214C',
        descripcion: 'Controlador lógico programable compacto para automatización industrial',
        precio_venta: 8500.00,
        precio_ml: 9200.00,
        vendidos: 15,
        cantidad_nuevo: 25,
        minimo: 5,
        maximo: 50,
        pedir_cantidad: 10,
        precio_us: 450.00,
        precio_mx: 8500.00,
        impuesto: 16.00,
        codigo_sat: '85371099',
        nota: 'Producto estrella de automatización',
        proveedor: 'Siemens México',
        marcas: 'Siemens',
        tiempo_entrega_proveedor: '2-3 semanas',
        activo: true,
        Url_imagen: 'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400',
        Datos_importantes_Descripcion_muestra: 'PLC compacto ideal para aplicaciones medianas',
        tiempo_de_Entrega: '2-3 semanas'
      },
      {
        numero_almacen: 'ALM002',
        estante: 'A1-02',
        modelo_existente: 'ABB ACS580 Variador de Frecuencia',
        descripcion: 'Variador de frecuencia para control de motores AC',
        precio_venta: 12500.00,
        precio_ml: 13800.00,
        vendidos: 8,
        cantidad_nuevo: 12,
        minimo: 3,
        maximo: 30,
        pedir_cantidad: 5,
        precio_us: 650.00,
        precio_mx: 12500.00,
        impuesto: 16.00,
        codigo_sat: '85371099',
        nota: 'Variador de alta eficiencia',
        proveedor: 'ABB México',
        marcas: 'ABB',
        tiempo_entrega_proveedor: '3-4 semanas',
        activo: true,
        Url_imagen: 'https://images.unsplash.com/photo-1581092160567-40aa08e78837?w=400',
        Datos_importantes_Descripcion_muestra: 'Control preciso de velocidad de motores',
        tiempo_de_Entrega: '3-4 semanas'
      },
      {
        numero_almacen: 'ALM003',
        estante: 'B2-01',
        modelo_existente: 'Schneider Electric Modicon M221',
        descripcion: 'Controlador lógico compacto para automatización básica',
        precio_venta: 4500.00,
        precio_ml: 5200.00,
        vendidos: 22,
        cantidad_nuevo: 18,
        minimo: 8,
        maximo: 40,
        pedir_cantidad: 15,
        precio_us: 240.00,
        precio_mx: 4500.00,
        impuesto: 16.00,
        codigo_sat: '85371099',
        nota: 'Ideal para aplicaciones pequeñas',
        proveedor: 'Schneider Electric',
        marcas: 'Schneider Electric',
        tiempo_entrega_proveedor: '1-2 semanas',
        activo: true,
        Url_imagen: 'https://images.unsplash.com/photo-1581092160572-40aa08e78837?w=400',
        Datos_importantes_Descripcion_muestra: 'PLC económico para automatización básica',
        tiempo_de_Entrega: '1-2 semanas'
      },
      {
        numero_almacen: 'ALM004',
        estante: 'C1-03',
        modelo_existente: 'Rockwell Allen-Bradley CompactLogix',
        descripcion: 'Sistema de control modular para aplicaciones medianas',
        precio_venta: 15800.00,
        precio_ml: 17500.00,
        vendidos: 5,
        cantidad_nuevo: 8,
        minimo: 2,
        maximo: 20,
        pedir_cantidad: 3,
        precio_us: 850.00,
        precio_mx: 15800.00,
        impuesto: 16.00,
        codigo_sat: '85371099',
        nota: 'Sistema modular expandible',
        proveedor: 'Rockwell Automation',
        marcas: 'Allen-Bradley',
        tiempo_entrega_proveedor: '4-6 semanas',
        activo: true,
        Url_imagen: 'https://images.unsplash.com/photo-1581092160577-40aa08e78837?w=400',
        Datos_importantes_Descripcion_muestra: 'Control modular de alta performance',
        tiempo_de_Entrega: '4-6 semanas'
      },
      {
        numero_almacen: 'ALM005',
        estante: 'A2-05',
        modelo_existente: 'Omron CP1E PLC Compacto',
        descripcion: 'Controlador económico para automatización simple',
        precio_venta: 3200.00,
        precio_ml: 3800.00,
        vendidos: 35,
        cantidad_nuevo: 2,
        minimo: 10,
        maximo: 50,
        pedir_cantidad: 20,
        precio_us: 170.00,
        precio_mx: 3200.00,
        impuesto: 16.00,
        codigo_sat: '85371099',
        nota: 'Stock bajo - reabastecer pronto',
        proveedor: 'Omron Electronics',
        marcas: 'Omron',
        tiempo_entrega_proveedor: '2-3 semanas',
        activo: true,
        Url_imagen: 'https://images.unsplash.com/photo-1581092160582-40aa08e78837?w=400',
        Datos_importantes_Descripcion_muestra: 'PLC económico y confiable',
        tiempo_de_Entrega: '2-3 semanas'
      }
    ];

    // Insertar cada producto
    for (const product of sampleProducts) {
      const query = `
        INSERT INTO almacen (
          numero_almacen, estante, modelo_existente, descripcion,
          precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
          pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
          proveedor, marcas, tiempo_entrega_proveedor, activo,
          Url_imagen, Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await executeQuery({
        query,
        values: [
          product.numero_almacen,
          product.estante,
          product.modelo_existente,
          product.descripcion,
          product.precio_venta,
          product.precio_ml,
          product.vendidos,
          product.cantidad_nuevo,
          product.minimo,
          product.maximo,
          product.pedir_cantidad,
          product.precio_us,
          product.precio_mx,
          product.impuesto,
          product.codigo_sat,
          product.nota,
          product.proveedor,
          product.marcas,
          product.tiempo_entrega_proveedor,
          product.activo,
          product.Url_imagen,
          product.Datos_importantes_Descripcion_muestra,
          product.tiempo_de_Entrega
        ]
      });
    }

    return NextResponse.json({
      success: true,
      message: `${sampleProducts.length} productos agregados exitosamente al almacén`,
      products: sampleProducts.length
    });

  } catch (error) {
    console.error('Error seeding almacen table:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
