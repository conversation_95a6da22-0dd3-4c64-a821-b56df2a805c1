<?php
/**
 * Resource Server
 * 
 * Este script sirve archivos estáticos con el tipo MIME correcto,
 * evitando problemas con servidores que no tienen configurados correctamente los tipos MIME.
 * 
 * Uso:
 * - Para archivos JavaScript: resource-server.php?type=js&file=path/to/file.js
 * - Para archivos CSS: resource-server.php?type=css&file=path/to/file.css
 * - Para otros archivos: resource-server.php?type=auto&file=path/to/file.ext
 */

// Configuración
$config = [
    'debug' => false,
    'cache_time' => 604800, // 1 semana en segundos
    'allowed_extensions' => [
        'js', 'mjs', 'jsx', 'css', 'json', 'svg', 'png', 'jpg', 'jpeg', 'gif', 'webp',
        'ttf', 'otf', 'woff', 'woff2', 'eot', 'ico'
    ],
    'mime_types' => [
        // JavaScript
        'js' => 'application/javascript',
        'mjs' => 'application/javascript',
        'jsx' => 'application/javascript',
        // CSS
        'css' => 'text/css',
        // Fuentes
        'ttf' => 'font/ttf',
        'otf' => 'font/otf',
        'woff' => 'font/woff',
        'woff2' => 'font/woff2',
        'eot' => 'application/vnd.ms-fontobject',
        // Imágenes
        'svg' => 'image/svg+xml',
        'png' => 'image/png',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'ico' => 'image/x-icon',
        // Otros
        'json' => 'application/json',
        'txt' => 'text/plain',
        'html' => 'text/html',
        'htm' => 'text/html',
    ]
];

// Función para imprimir mensajes de depuración
function debug($message) {
    global $config;
    if ($config['debug']) {
        error_log('[ResourceServer] ' . $message);
    }
}

// Función para obtener el tipo MIME de un archivo
function getMimeType($file) {
    global $config;
    
    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    
    if (isset($config['mime_types'][$extension])) {
        return $config['mime_types'][$extension];
    }
    
    // Si no se encuentra el tipo MIME, devolver text/plain
    return 'text/plain';
}

// Función para verificar si un archivo es seguro
function isSafeFile($file) {
    global $config;
    
    // Verificar que el archivo exista
    if (!file_exists($file) || !is_file($file)) {
        return false;
    }
    
    // Verificar que el archivo tenga una extensión permitida
    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    if (!in_array($extension, $config['allowed_extensions'])) {
        return false;
    }
    
    // Verificar que el archivo esté dentro del directorio del script
    $realpath = realpath($file);
    $scriptDir = realpath(dirname(__FILE__));
    
    if (strpos($realpath, $scriptDir) !== 0) {
        return false;
    }
    
    return true;
}

// Función para servir un archivo
function serveFile($file, $mimeType) {
    global $config;
    
    // Verificar que el archivo sea seguro
    if (!isSafeFile($file)) {
        header('HTTP/1.1 404 Not Found');
        echo 'File not found or not allowed';
        exit;
    }
    
    // Obtener la fecha de modificación del archivo
    $lastModified = filemtime($file);
    $etag = md5_file($file);
    
    // Configurar cabeceras de caché
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s', $lastModified) . ' GMT');
    header('ETag: "' . $etag . '"');
    header('Cache-Control: public, max-age=' . $config['cache_time']);
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $config['cache_time']) . ' GMT');
    
    // Verificar si el archivo ha cambiado desde la última solicitud
    if (isset($_SERVER['HTTP_IF_MODIFIED_SINCE']) && strtotime($_SERVER['HTTP_IF_MODIFIED_SINCE']) >= $lastModified) {
        header('HTTP/1.1 304 Not Modified');
        exit;
    }
    
    if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && trim($_SERVER['HTTP_IF_NONE_MATCH'], '"') === $etag) {
        header('HTTP/1.1 304 Not Modified');
        exit;
    }
    
    // Configurar cabecera de tipo MIME
    header('Content-Type: ' . $mimeType);
    header('Content-Length: ' . filesize($file));
    
    // Enviar el archivo
    readfile($file);
    exit;
}

// Función principal
function main() {
    // Verificar parámetros
    if (!isset($_GET['file']) || empty($_GET['file'])) {
        header('HTTP/1.1 400 Bad Request');
        echo 'Missing file parameter';
        exit;
    }
    
    // Obtener el tipo de archivo
    $type = isset($_GET['type']) ? $_GET['type'] : 'auto';
    
    // Construir la ruta al archivo
    $file = $_GET['file'];
    
    // Eliminar cualquier intento de acceder a directorios superiores
    $file = str_replace('..', '', $file);
    $file = ltrim($file, '/');
    
    // Construir la ruta completa
    $filePath = __DIR__ . '/' . $file;
    
    // Determinar el tipo MIME
    $mimeType = '';
    
    switch ($type) {
        case 'js':
            $mimeType = 'application/javascript';
            break;
        case 'css':
            $mimeType = 'text/css';
            break;
        case 'auto':
        default:
            $mimeType = getMimeType($filePath);
            break;
    }
    
    // Servir el archivo
    serveFile($filePath, $mimeType);
}

// Ejecutar la función principal
main();
?>
