/* ===== MODERN ADMIN STYLES ===== */

/* ===== ADMIN SIDEBAR STYLES ===== */

/* Mobile Toggle Button */
.admin-mobile-toggle {
  display: none;
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border: none;
  border-radius: 8px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
  transition: all 0.3s ease;
}

.admin-mobile-toggle span {
  width: 20px;
  height: 2px;
  background: white;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.admin-mobile-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 86, 166, 0.4);
}

/* Mobile Overlay */
.admin-mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Executive Admin Sidebar */
.admin-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 300px;
  background: linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0056a6 100%);
  color: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    8px 0 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.admin-sidebar.collapsed {
  width: 85px;
}

/* Executive Sidebar Header */
.admin-sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 90px;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.1), rgba(0, 51, 102, 0.1));
  position: relative;
}

.admin-sidebar-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #003366, #f7941d);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.admin-logo-container {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.admin-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  padding: 6px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.admin-logo:hover {
  transform: scale(1.05);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.admin-logo-text h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #0056a6, #003366);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.admin-logo-text span {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
  margin-top: 0.25rem;
}

.admin-collapse-btn {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.2), rgba(0, 51, 102, 0.2));
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.1rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.admin-collapse-btn:hover {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.3), rgba(0, 51, 102, 0.3));
  transform: scale(1.1) rotate(180deg);
  box-shadow: 0 4px 16px rgba(0, 86, 166, 0.3);
}

/* Executive User Profile in Sidebar */
.admin-user-profile {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.08), rgba(0, 51, 102, 0.08));
  position: relative;
  margin: 1rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-user-profile.clickable {
  cursor: pointer;
}

.admin-user-profile.clickable:hover {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.15), rgba(0, 51, 102, 0.15));
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 86, 166, 0.2);
  border-color: rgba(0, 86, 166, 0.3);
}

.admin-user-profile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #003366, #f7941d);
  border-radius: 16px 16px 0 0;
}

.admin-user-avatar {
  position: relative;
  flex-shrink: 0;
}

.admin-user-avatar img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(0, 86, 166, 0.5);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.admin-avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 800;
  font-size: 1.2rem;
  border: 2px solid rgba(0, 86, 166, 0.3);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  flex-shrink: 0;
}

.admin-avatar-placeholder::before {
  content: '';
  position: absolute;
  inset: 1px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
}

.admin-user-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-user-name {
  font-weight: 700;
  font-size: 1rem;
  color: white;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.025em;
}

.admin-user-username {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.admin-user-role {
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.3), rgba(0, 51, 102, 0.3));
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: inline-block;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #60a5fa;
  align-self: flex-start;
  margin-top: 0.25rem;
}

/* Executive Navigation Menu */
.admin-nav {
  flex: 1;
  padding: 1.5rem 0;
  overflow-y: auto;
}

.admin-nav-item {
  width: 100%;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  padding: 1.25rem 1.5rem;
  margin: 0.25rem 1rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.admin-nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  transition: all 0.3s ease;
}

.admin-nav-item:hover {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.15), rgba(0, 51, 102, 0.15));
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 86, 166, 0.2);
}

.admin-nav-item:hover::before {
  background: linear-gradient(135deg, #0056a6, #003366);
}

.admin-nav-item.active {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.25), rgba(0, 51, 102, 0.25));
  color: #0ea5e9;
  transform: translateX(6px);
  box-shadow: 0 6px 24px rgba(0, 86, 166, 0.3);
}

.admin-nav-item.active::before {
  background: linear-gradient(135deg, #0056a6, #003366);
}

.admin-nav-icon {
  font-size: 1.75rem;
  flex-shrink: 0;
  width: 28px;
  text-align: center;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.admin-nav-content {
  flex: 1;
  min-width: 0;
}

.admin-nav-title {
  display: block;
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 0.375rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.025em;
}

.admin-nav-description {
  display: block;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

/* Executive Sidebar Footer */
.admin-sidebar-footer {
  padding: 2rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
}

.admin-logout-btn {
  width: 100%;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 1.25rem;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-weight: 700;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 6px 20px rgba(239, 68, 68, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.admin-logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.admin-logout-btn:hover::before {
  left: 100%;
}

.admin-logout-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-3px);
  box-shadow:
    0 8px 32px rgba(239, 68, 68, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.admin-logout-btn:disabled,
.admin-logout-btn.logging-out {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.admin-logout-btn.logging-out {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.admin-logout-btn.logging-out:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  transform: none;
}

.admin-logout-icon {
  font-size: 1.4rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* User Profile Modal */
.user-profile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.user-profile-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
}

.modal-header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid #e2e8f0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 1.25rem;
}

.user-info {
  flex: 1;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.user-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.modal-close-btn {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 1rem;
  flex-shrink: 0;
}

.modal-close-btn:hover {
  background: #e2e8f0;
  color: #475569;
  transform: scale(1.05);
}

.modal-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
}

.tab-btn:hover {
  background: rgba(0, 86, 166, 0.05);
  color: #0056a6;
}

.tab-btn.active {
  background: white;
  color: #0056a6;
  border-bottom: 2px solid #0056a6;
}

.tab-icon {
  font-size: 1rem;
  width: 16px;
  height: 16px;
}

.modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
  background: white;
}

.modal-message {
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

.modal-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.modal-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.profile-form,
.password-form,
.photo-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #1e293b;
  font-weight: 500;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.info-value.description {
  min-height: 4rem;
  align-items: flex-start;
  padding-top: 1rem;
}

.role-badge {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.info-note {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
  font-size: 0.875rem;
}

.info-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
}

/* Photo Form Styles */
.current-photo,
.photo-preview-new {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.photo-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #e2e8f0;
  background: #f8fafc;
}

.current-photo-img,
.preview-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  text-align: center;
}

.no-photo-placeholder svg {
  width: 3rem;
  height: 3rem;
}

.no-photo-placeholder span {
  font-size: 0.75rem;
  font-weight: 500;
}

.form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .user-profile-modal-overlay {
    padding: 0.5rem;
  }

  .user-profile-modal {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .modal-title {
    font-size: 1.125rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .tab-btn {
    padding: 0.875rem 1rem;
    font-size: 0.8rem;
  }

  .tab-icon {
    width: 14px;
    height: 14px;
  }

  .photo-preview {
    width: 100px;
    height: 100px;
  }
}

/* Action Buttons */
.admin-action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-action-btn.view {
  background: #3b82f6;
  color: white;
}

.admin-action-btn.view:hover {
  background: #2563eb;
}

.admin-action-btn.edit {
  background: #f59e0b;
  color: white;
}

.admin-action-btn.edit:hover {
  background: #d97706;
}

.admin-action-btn.delete {
  background: #ef4444;
  color: white;
}

.admin-action-btn.delete:hover {
  background: #dc2626;
}

.admin-action-btn.success {
  background: #10b981;
  color: white;
}

.admin-action-btn.success:hover {
  background: #059669;
}

.admin-action-btn.danger {
  background: #dc2626;
  color: white;
}

.admin-action-btn.danger:hover {
  background: #b91c1c;
}

/* Table Actions Container */
.admin-table-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

/* Confirm Modal Styles */
.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15000;
  padding: 2rem;
}

.confirm-modal {
  background: white;
  border-radius: 16px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: confirmModalSlideIn 0.3s ease-out;
  width: 100%;
  max-width: 500px;
  overflow: hidden;
}

@keyframes confirmModalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.confirm-modal-header {
  padding: 2rem 2rem 1rem 2rem;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.confirm-modal-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.confirm-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.confirm-modal-body {
  padding: 1.5rem 2rem;
}

.confirm-modal-message {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  text-align: center;
  margin: 0;
  white-space: pre-line;
}

.confirm-modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
  gap: 1rem;
  background: #f8fafc;
}

.confirm-modal-button {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.confirm-modal-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.confirm-modal-button-cancel {
  background: #f1f5f9;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.confirm-modal-button-cancel:hover:not(:disabled) {
  background: #e2e8f0;
  color: #475569;
}

.confirm-modal-button-confirm.danger {
  background: #dc2626;
  color: white;
}

.confirm-modal-button-confirm.danger:hover:not(:disabled) {
  background: #b91c1c;
}

.confirm-modal-button-confirm.warning {
  background: #f59e0b;
  color: white;
}

.confirm-modal-button-confirm.warning:hover:not(:disabled) {
  background: #d97706;
}

.confirm-modal-button-confirm.info {
  background: #3b82f6;
  color: white;
}

.confirm-modal-button-confirm.info:hover:not(:disabled) {
  background: #2563eb;
}

/* Large Modal Styles */
.large-modal {
  max-width: 1000px;
  width: 98vw;
  max-height: 90vh;
  overflow-y: auto;
  margin-top: 1vh;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-text {
  font-weight: 500;
  color: #374151;
}

.message {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.message.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.message.error {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .large-modal {
    width: 95vw;
    max-width: none;
    margin-top: 1vh;
  }
}

/* Modal Overlay and Content Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 10000;
  padding: 1vh 1rem;
  overflow-y: auto;
  padding-top: 1vh;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
  width: 100%;
  max-width: 600px;
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  background: #f8fafc;
  border-radius: 0 0 16px 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-input {
  padding: 0.875rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #0056a6;
  box-shadow: 0 0 0 3px rgba(0, 86, 166, 0.1);
}

.password-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-group .form-input {
  padding-right: 3rem;
  flex: 1;
}

.password-toggle {
  position: absolute;
  right: 0.875rem;
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  color: #0056a6;
  background: rgba(0, 86, 166, 0.1);
}

.password-strength {
  margin-top: 0.75rem;
}

.strength-bar {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 3px;
}

.strength-feedback {
  font-size: 0.75rem;
  color: #64748b;
}

.strength-feedback ul {
  margin: 0;
  padding-left: 1rem;
  list-style-type: disc;
}

.strength-feedback li {
  margin-bottom: 0.25rem;
}

.password-mismatch {
  color: #dc2626;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

.submit-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.3);
}

.submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #003366, #002244);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 86, 166, 0.4);
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .user-profile-modal-overlay {
    padding: 1rem;
  }

  .user-profile-modal {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .modal-header-content {
    width: 100%;
  }

  .modal-close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .modal-subtitle {
    font-size: 0.8rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .tab-btn {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .form-input {
    padding: 0.75rem;
  }
}

/* Collapsed State Adjustments */
.admin-sidebar.collapsed .admin-user-profile {
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.admin-sidebar.collapsed .admin-user-info {
  display: none;
}

.admin-sidebar.collapsed .admin-nav-content {
  display: none;
}

.admin-sidebar.collapsed .admin-logout-btn span:not(.admin-logout-icon) {
  display: none;
}

/* Executive Content Area Adjustment */
.admin-content-area {
  margin-left: 300px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
}

.admin-content-area.sidebar-collapsed {
  margin-left: 85px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-mobile-toggle {
    display: flex;
  }

  .admin-mobile-overlay {
    display: block;
  }

  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-sidebar.mobile-open {
    transform: translateX(0);
  }

  .admin-content-area {
    margin-left: 0;
  }

  .admin-content-area.sidebar-collapsed {
    margin-left: 0;
  }

  .admin-main-content {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 1rem !important;
  }
}

/* Scrollbar Styling for Sidebar */
.admin-nav::-webkit-scrollbar {
  width: 4px;
}

.admin-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.admin-nav::-webkit-scrollbar-thumb {
  background: rgba(247, 148, 29, 0.5);
  border-radius: 2px;
}

.admin-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(247, 148, 29, 0.7);
}

/* ===== MODERN LOGIN PAGE STYLES ===== */

.modern-login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modern-login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  z-index: 0;
}

.modern-login-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -150px;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -100px;
  animation-delay: -5s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: 20%;
  animation-delay: -10s;
}

.shape-4 {
  width: 250px;
  height: 250px;
  top: 20%;
  right: 30%;
  animation-delay: -15s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

.modern-login-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 450px;
}

.modern-login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-login-header {
  text-align: center;
  padding: 3rem 2rem 2rem 2rem;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.1) 0%, rgba(0, 51, 102, 0.1) 100%);
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
}

.modern-login-logo-container {
  margin-bottom: 1.5rem;
}

.modern-login-logo {
  width: 80px;
  height: auto;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.modern-login-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-login-subtitle {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.modern-login-form-container {
  padding: 2rem;
}

.modern-login-error {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
  color: #dc2626;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.modern-error-icon {
  flex-shrink: 0;
}

.modern-login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modern-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modern-input-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.modern-input-wrapper {
  position: relative;
}

.modern-input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  z-index: 2;
}

.modern-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f9fafb;
  color: #1f2937;
}

.modern-input:focus {
  outline: none;
  border-color: #0056a6;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 86, 166, 0.1);
}

.modern-input:focus + .modern-input-icon {
  color: #0056a6;
}

.modern-login-button {
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.modern-login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.4);
}

.modern-login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.modern-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.modern-button-icon {
  transition: transform 0.2s ease;
}

.modern-login-button:hover .modern-button-icon {
  transform: translateX(2px);
}

.modern-login-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.modern-credentials-info {
  margin-bottom: 1.5rem;
}

.modern-info-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  border: 1px solid #93c5fd;
}

.modern-credentials-text {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.modern-credentials-text strong {
  color: #374151;
  font-weight: 600;
}

.modern-copyright {
  text-align: center;
}

.modern-copyright p {
  font-size: 0.8rem;
  color: #9ca3af;
  margin: 0;
}

/* Responsive Modern Login */
@media (max-width: 768px) {
  .modern-login-page {
    padding: 0.5rem;
  }

  .modern-login-container {
    max-width: 380px;
  }

  .modern-login-header {
    padding: 2rem 1.5rem 1.5rem 1.5rem;
  }

  .modern-login-logo {
    width: 60px;
  }

  .modern-login-title {
    font-size: 1.75rem;
  }

  .modern-login-form-container {
    padding: 1.5rem;
  }

  .shape-1, .shape-2, .shape-3, .shape-4 {
    opacity: 0.5;
  }
}

@media (max-width: 480px) {
  .modern-login-container {
    max-width: 320px;
  }

  .modern-login-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .modern-login-title {
    font-size: 1.5rem;
  }

  .modern-login-form-container {
    padding: 1rem;
  }

  .modern-input {
    padding: 0.875rem 0.875rem 0.875rem 2.75rem;
  }

  .modern-login-button {
    padding: 0.875rem 1.5rem;
  }
}

/* ===== LOGIN PAGE STYLES (LEGACY) ===== */

.admin-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1000px;
  width: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-height: 600px;
}

.login-left-panel {
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.login-left-panel::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  animation: float 20s linear infinite;
}

@keyframes float {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.login-illustration {
  position: relative;
  z-index: 2;
  margin-bottom: 2rem;
}

.login-logo {
  width: 120px;
  height: auto;
  margin-bottom: 2rem;
  filter: brightness(0) invert(1);
}

.login-left-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.login-left-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.login-right-panel {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
  text-align: center;
}

.login-description {
  color: #718096;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.login-error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 500;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login-input-group {
  position: relative;
}

.login-input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  z-index: 2;
}

.login-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f7fafc;
}

.login-input:focus {
  outline: none;
  border-color: #0056a6;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 86, 166, 0.1);
}

.login-remember {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
}

.login-remember input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #0056a6;
}

.login-button {
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.4);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-footer {
  text-align: center;
  margin-top: 1rem;
}

.login-forgot {
  color: #0056a6;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.login-forgot:hover {
  color: #003366;
  text-decoration: underline;
}

/* Admin Dashboard Styles (when logged in) */
.admin-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
  text-align: center;
}

.admin-welcome {
  color: #718096;
  text-align: center;
  margin-bottom: 3rem;
  font-size: 1.1rem;
}

/* Responsive Login Styles */
@media (max-width: 768px) {
  .login-container {
    grid-template-columns: 1fr;
    max-width: 400px;
    min-height: auto;
  }

  .login-left-panel {
    padding: 2rem;
    min-height: 200px;
  }

  .login-left-title {
    font-size: 1.5rem;
  }

  .login-right-panel {
    padding: 2rem;
  }

  .login-title {
    font-size: 2rem;
  }

  .login-logo {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .admin-page {
    padding: 0.5rem;
  }

  .login-container {
    max-width: 350px;
  }

  .login-left-panel,
  .login-right-panel {
    padding: 1.5rem;
  }

  .login-title {
    font-size: 1.8rem;
  }

  .login-left-title {
    font-size: 1.3rem;
  }
}

/* Admin Stats Grid */
.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.admin-stat-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.admin-stat-content {
  flex: 1;
}

.admin-stat-title {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.admin-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

/* Admin Actions */
.admin-actions {
  margin-bottom: 3rem;
}

.admin-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.admin-action-button {
  background: white;
  border: 2px solid #e2e8f0;
  padding: 1.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.admin-action-button:hover {
  border-color: #0056a6;
  color: #0056a6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.15);
}

.admin-action-button svg {
  color: #0056a6;
}

/* Admin Recent Activity */
.admin-recent {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.admin-activity-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.admin-activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #f1f5f9;
}

.admin-activity-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f7941d 0%, #e67e22 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.admin-activity-content {
  flex: 1;
}

.admin-activity-text {
  font-size: 0.95rem;
  color: #374151;
  margin-bottom: 0.25rem;
  line-height: 1.5;
}

.admin-activity-time {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

/* Responsive Admin Dashboard */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 1rem;
  }

  .admin-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-stat-card {
    padding: 1.5rem;
  }

  .admin-stat-icon {
    width: 50px;
    height: 50px;
  }

  .admin-stat-value {
    font-size: 1.5rem;
  }

  .admin-buttons {
    grid-template-columns: 1fr;
  }

  .admin-action-button {
    padding: 1rem;
    justify-content: center;
  }

  .admin-recent {
    padding: 1.5rem;
  }

  .admin-activity-item {
    padding: 0.75rem;
  }

  .admin-activity-icon {
    width: 35px;
    height: 35px;
  }
}

/* Base Admin Page Styles */
.modern-admin-page {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Modern Admin Header */
.modern-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 86, 166, 0.1);
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.modern-admin-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Admin Actions */
.modern-admin-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2rem;
  padding: 0 1.5rem;
}

/* Modern Admin Buttons */
.modern-admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  gap: 0.5rem;
}

.modern-admin-button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.25);
}

.modern-admin-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.35);
}

.modern-admin-button-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.modern-admin-button-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.modern-admin-button-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
  border: 2px solid #06b6d4;
}

.modern-admin-button-info:hover {
  background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
  border-color: #0891b2;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

.modern-admin-button-disabled {
  background: #f1f5f9;
  color: #9ca3af;
  border: 1px solid #e2e8f0;
  cursor: not-allowed;
  opacity: 0.6;
}

.modern-admin-button-disabled:hover {
  background: #f1f5f9;
  border-color: #e2e8f0;
  transform: none;
}

/* Modern Admin Cards */
.modern-admin-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid #f1f5f9;
}

.modern-admin-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.modern-admin-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.modern-admin-card-content {
  padding: 1.5rem;
}

/* Modern Admin Filters */
.modern-admin-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.modern-admin-search {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease-in-out;
  min-width: 250px;
}

.modern-admin-search:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-admin-select {
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-width: 180px;
}

.modern-admin-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Modern Admin Tables */
.modern-admin-table-container {
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
  background: white;
}

.modern-admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.modern-admin-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-admin-table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: #2d3748;
  vertical-align: middle;
}

.modern-admin-table tbody tr:hover {
  background: #f8fafc;
}

.modern-admin-table-empty {
  text-align: center;
  color: #718096;
  font-style: italic;
  padding: 3rem 1rem;
}

/* Modern Admin Loading States */
.modern-admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #718096;
}

.modern-admin-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Admin Error States */
.modern-admin-error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #c53030;
  text-align: center;
}

/* Modern Admin Empty States */
.modern-admin-empty {
  text-align: center;
  padding: 3rem;
  color: #718096;
}

.modern-admin-empty-icon {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.modern-admin-empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.modern-admin-empty-text {
  color: #718096;
  margin: 0;
}

/* Modern Admin Summary */
.modern-admin-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.modern-admin-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.modern-admin-summary-label {
  font-weight: 500;
  color: #4a5568;
}

.modern-admin-summary-value {
  font-weight: 600;
  color: #2d3748;
  font-size: 1.125rem;
}

.modern-admin-summary-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.modern-admin-summary-total .modern-admin-summary-label,
.modern-admin-summary-total .modern-admin-summary-value {
  color: white;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 1024px) {
  .modern-admin-page {
    padding: 1.5rem;
  }

  .modern-admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .modern-admin-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .modern-admin-filters {
    width: 100%;
    justify-content: flex-start;
  }

  .modern-admin-search {
    min-width: 200px;
    flex: 1;
  }

  .modern-admin-select {
    min-width: 150px;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .modern-admin-page {
    padding: 1rem;
  }

  .modern-admin-title {
    font-size: 1.5rem;
  }

  .modern-admin-header {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
  }

  .modern-admin-card {
    margin-bottom: 1.5rem;
    border-radius: 0.75rem;
  }

  .modern-admin-card-header {
    padding: 1rem;
  }

  .modern-admin-card-content {
    padding: 1rem;
  }

  .modern-admin-card-title {
    font-size: 1.125rem;
  }

  .modern-admin-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .modern-admin-search,
  .modern-admin-select {
    width: 100%;
    min-width: auto;
  }

  .modern-admin-button {
    width: 100%;
    justify-content: center;
  }

  /* Mobile Table Styles */
  .modern-admin-table-container {
    border-radius: 0.5rem;
    margin: 0 -1rem;
    border-left: none;
    border-right: none;
  }

  .modern-admin-table {
    font-size: 0.75rem;
    min-width: 600px;
  }

  .modern-admin-table th,
  .modern-admin-table td {
    padding: 0.75rem 0.5rem;
  }

  .modern-admin-table th {
    font-size: 0.75rem;
  }

  /* Mobile Summary Grid */
  .modern-admin-summary {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .modern-admin-summary-item {
    padding: 0.75rem;
  }

  .modern-admin-summary-value {
    font-size: 1rem;
  }

  /* Mobile Empty States */
  .modern-admin-empty {
    padding: 2rem 1rem;
  }

  .modern-admin-empty-icon svg {
    width: 3rem;
    height: 3rem;
  }

  .modern-admin-empty-title {
    font-size: 1rem;
  }

  .modern-admin-empty-text {
    font-size: 0.875rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .modern-admin-page {
    padding: 0.75rem;
  }

  .modern-admin-title {
    font-size: 1.25rem;
  }

  .modern-admin-card-header,
  .modern-admin-card-content {
    padding: 0.75rem;
  }

  .modern-admin-table {
    min-width: 500px;
  }

  .modern-admin-table th,
  .modern-admin-table td {
    padding: 0.5rem 0.25rem;
  }

  .modern-admin-summary {
    gap: 0.5rem;
  }

  .modern-admin-summary-item {
    padding: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* ===== LEGACY COMPATIBILITY STYLES ===== */

/* Mobile Header for Admin Pages */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, #1a1d29 0%, #232940 100%);
  color: white;
  z-index: 998;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.mobile-menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-header-title {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Mobile Overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.mobile-overlay.active {
  display: block;
}

/* Legacy Dashboard Styles */
.admin-dashboard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 30px;
}

.admin-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #667eea;
}

.admin-welcome {
  font-size: 16px;
  color: #555;
  margin-bottom: 30px;
}

/* Legacy Stats Grid */
.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.admin-stat-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.admin-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #667eea;
}

.admin-stat-content {
  flex: 1;
}

.admin-stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.admin-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #667eea;
}

/* Legacy Section Styles */
.admin-section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.admin-actions {
  margin-bottom: 30px;
}

.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.admin-action-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.admin-action-button:hover {
  background-color: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

/* Legacy Activity Styles */
.admin-recent {
  margin-bottom: 30px;
}

.admin-activity-list {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.admin-activity-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #e0e0e0;
}

.admin-activity-item:last-child {
  border-bottom: none;
}

.admin-activity-icon {
  width: 36px;
  height: 36px;
  background-color: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #667eea;
}

.admin-activity-content {
  flex: 1;
}

.admin-activity-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.admin-activity-time {
  font-size: 12px;
  color: #666;
}

/* Legacy Mobile Responsive */
@media (max-width: 768px) {
  .mobile-header {
    display: flex;
  }

  .admin-stats {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }

  .admin-stat-card {
    padding: 15px;
  }

  .admin-buttons {
    grid-template-columns: 1fr;
  }
}

/* Estilos específicos para página de precios */
.admin-page-container {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.admin-page-header {
  margin-bottom: 2rem;
}

.admin-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.admin-header-text {
  flex: 1;
}

.admin-page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #0056a6, #003366);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-page-subtitle {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

.admin-header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Filtros */
.admin-filters-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.admin-filters-content {
  padding: 1.5rem;
}

.admin-filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: 1.5rem;
  align-items: center;
}

.admin-filter-item {
  position: relative;
}

.admin-search-container,
.admin-select-container {
  position: relative;
  display: flex;
  align-items: center;
}

.admin-search-icon,
.admin-select-icon {
  position: absolute;
  left: 1rem;
  color: #64748b;
  z-index: 1;
}

.admin-search-input,
.admin-select-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.admin-search-input:focus,
.admin-select-input:focus {
  outline: none;
  border-color: #0056a6;
  box-shadow: 0 0 0 3px rgba(0, 86, 166, 0.1);
}

/* Tabla */
.admin-table-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.admin-table-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.admin-table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.admin-table-content {
  padding: 0;
}

.admin-table-container {
  overflow-x: auto;
  background: white;
}

.admin-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  font-size: 0.875rem;
}

.admin-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.25rem 1rem;
  text-align: left;
  font-weight: 700;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.75rem;
  border-bottom: 2px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.admin-table td {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #f1f5f9;
  color: #374151;
  vertical-align: middle;
  transition: all 0.3s ease;
}

.admin-table tbody tr {
  transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.admin-table tbody tr:hover td {
  border-color: #e2e8f0;
}

/* Estilos específicos para celdas */
.admin-table-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 700;
  color: #0056a6;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  padding: 0.75rem !important;
  text-align: center;
  min-width: 100px;
}

.admin-table-model {
  max-width: 250px;
}

.admin-product-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.admin-product-description {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
  line-height: 1.4;
}

.admin-table-quantity {
  text-align: center;
  min-width: 80px;
}

.admin-table-price {
  text-align: right;
  font-weight: 600;
  color: #059669;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  min-width: 120px;
}

/* Efectos adicionales para la tabla */
.admin-table th:first-child {
  border-top-left-radius: 12px;
}

.admin-table th:last-child {
  border-top-right-radius: 12px;
}

.admin-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: 12px;
}

.admin-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 12px;
}

.admin-table tbody tr:last-child td {
  border-bottom: none;
}

/* Animaciones para los números */
.admin-stock-number,
.admin-pedido-number {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive para la tabla */
@media (max-width: 768px) {
  .admin-table th,
  .admin-table td {
    padding: 1rem 0.5rem;
    font-size: 0.75rem;
  }

  .admin-table-model {
    max-width: 150px;
  }

  .admin-product-description {
    display: none;
  }

  .admin-status-badge {
    min-width: 80px;
    font-size: 0.625rem;
    padding: 0.375rem 0.75rem;
  }
}

/* Estilos para paginación */
.admin-pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.admin-pagination-info {
  display: flex;
  align-items: center;
}

.admin-pagination-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.admin-pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.admin-pagination-button:hover:not(:disabled) {
  background: #0056a6;
  color: white;
  border-color: #0056a6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 86, 166, 0.2);
}

.admin-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f3f4f6;
  color: #9ca3af;
}

.admin-pagination-numbers {
  display: flex;
  gap: 0.25rem;
  margin: 0 1rem;
}

.admin-pagination-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid #e2e8f0;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-pagination-number:hover {
  background: #f0f9ff;
  border-color: #0056a6;
  color: #0056a6;
  transform: translateY(-1px);
}

.admin-pagination-number.active {
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  border-color: #0056a6;
  box-shadow: 0 4px 8px rgba(0, 86, 166, 0.3);
}

/* Responsive para paginación */
@media (max-width: 768px) {
  .admin-pagination-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .admin-pagination-controls {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .admin-pagination-numbers {
    margin: 0;
    justify-content: center;
    flex-wrap: wrap;
  }

  .admin-pagination-button {
    width: 100%;
    max-width: 200px;
  }
}

/* Estilos para tabla de materiales pedidos */
.admin-table-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
  font-style: italic;
}

.admin-order-quantity {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  color: #2563eb;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 12px;
  border: 2px solid #3b82f6;
  min-width: 60px;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.admin-supplier-name {
  display: inline-block;
  padding: 0.5rem 0.75rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #166534;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid #bbf7d0;
}

.admin-delivery-time {
  display: inline-block;
  padding: 0.5rem 0.75rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid #fbbf24;
}

.admin-order-date,
.admin-received-date {
  display: inline-block;
  padding: 0.5rem 0.75rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #374151;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 1px solid #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.admin-order-status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 100px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.admin-order-status-badge.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  border: 2px solid #fbbf24;
}

.admin-order-status-badge.pending::after {
  content: '⏳';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.admin-order-status-badge.received {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: 2px solid #10b981;
}

.admin-order-status-badge.received::after {
  content: '✅';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

/* Estilos específicos para celdas de la tabla de pedidos */
.admin-table-supplier,
.admin-table-delivery,
.admin-table-date {
  text-align: center;
  min-width: 120px;
}

/* Responsive para tabla de pedidos */
@media (max-width: 768px) {
  .admin-supplier-name,
  .admin-delivery-time,
  .admin-order-date,
  .admin-received-date {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  .admin-order-quantity {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
    min-width: 50px;
  }

  .admin-order-status-badge {
    min-width: 80px;
    font-size: 0.625rem;
    padding: 0.375rem 0.75rem;
  }
}

/* Estados de carga y vacío */
.admin-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
}

.admin-loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #0056a6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.admin-loading-text {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.admin-empty-icon {
  color: #9ca3af;
  margin-bottom: 1rem;
}

.admin-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.admin-empty-text {
  color: #6b7280;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-page-container {
    padding: 1rem;
  }

  .admin-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .admin-filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-page-title {
    font-size: 1.5rem;
  }
}

/* Estilos específicos para almacén */
.admin-stats-display {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-weight: 500;
}

.admin-product-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.admin-product-description {
  font-size: 0.875rem;
  color: #64748b;
  font-style: italic;
}

.admin-stock-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 100px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.admin-status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.admin-status-badge:hover::before {
  left: 100%;
}

.admin-status-badge.normal {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: 2px solid #10b981;
}

.admin-status-badge.normal::after {
  content: '✓';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.admin-status-badge.pedir-material {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: 2px solid #f59e0b;
}

.admin-status-badge.pedir-material::after {
  content: '⚠';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.admin-status-badge.no-hay {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: 2px solid #ef4444;
}

.admin-status-badge.no-hay::after {
  content: '✕';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

.admin-status-badge.pedido-realizado {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: 2px solid #3b82f6;
}

.admin-status-badge.pedido-realizado::after {
  content: '📦';
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

/* Estilos para badges de pedidos */
.admin-pedido-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 60px;
  margin-bottom: 0.25rem;
}

.admin-pedido-badge.sin-pedidos {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.admin-pedido-badge.pedido-realizado {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #059669;
  border: 1px solid #10b981;
}

/* Estilos para números de stock y pedidos */
.admin-stock-number,
.admin-pedido-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  min-width: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.admin-stock-number:hover,
.admin-pedido-number:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #0056a6;
}

/* Estilos para texto de estado */
.admin-stock-status,
.admin-pedido-status {
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  margin-top: 0.25rem;
  opacity: 0.8;
}

.admin-status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-badge.active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.admin-status-badge.inactive {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

/* Responsive para almacén */
@media (max-width: 768px) {
  .admin-filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .admin-stats-display {
    justify-content: center;
  }
}

/* Estilos para el modal de agregar producto */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  padding: 20px;
  backdrop-filter: blur(4px);
}

.large-modal {
  width: 95vw;
  max-width: 1400px;
  height: 95vh;
  max-height: 95vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  height: calc(100% - 140px); /* Resta altura del header y footer */
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  padding: 2rem;
  min-height: 100%;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0056a6;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #0056a6;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-label.required::after {
  content: ' *';
  color: #dc2626;
  font-weight: bold;
  font-size: 1rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 10px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #0056a6;
  box-shadow: 0 0 0 4px rgba(0, 86, 166, 0.1);
  transform: translateY(-1px);
}

.form-input.error,
.form-textarea.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

.form-input.calculated-field {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  color: #0056a6;
  font-weight: 600;
  border-color: #0056a6;
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
  font-style: italic;
  background: #f9fafb;
  padding: 0.5rem;
  border-radius: 6px;
  border-left: 3px solid #0056a6;
}

.form-checkbox-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.form-checkbox {
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
  accent-color: #0056a6;
}

.error-text {
  display: flex;
  align-items: center;
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.5rem;
  font-weight: 600;
  background: #fef2f2;
  padding: 0.5rem;
  border-radius: 6px;
  border-left: 3px solid #dc2626;
}

.error-text::before {
  content: '⚠';
  margin-right: 0.5rem;
  font-size: 0.875rem;
}

.error-message {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 2px solid #f87171;
  color: #dc2626;
  padding: 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: 0 4px 6px rgba(220, 38, 38, 0.1);
}

/* Estilos específicos para el modal header y footer */
.modal-header {
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
}

.modal-close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.modal-close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-footer {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;
}

.modal-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 120px;
}

.modal-button-primary {
  background: #0056a6;
  color: white;
}

.modal-button-primary:hover:not(:disabled) {
  background: #003d75;
  transform: translateY(-1px);
}

.modal-button-secondary {
  background: #e2e8f0;
  color: #374151;
}

.modal-button-secondary:hover:not(:disabled) {
  background: #cbd5e0;
}

.modal-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive para el modal */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .large-modal {
    width: 98vw;
    height: 98vh;
    max-height: 98vh;
  }

  .modal-content {
    height: calc(100% - 120px);
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
  }

  .form-section-title {
    font-size: 1.125rem;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    flex-direction: row;
    justify-content: space-between;
  }

  .modal-button {
    flex: 1;
    margin: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 5px;
  }

  .large-modal {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .form-grid {
    padding: 1rem;
    gap: 1rem;
  }

  .form-section {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
    gap: 0.5rem;
  }

  .modal-button {
    width: 100%;
    margin: 0;
  }
}
