'use client';

import { ReactNode } from 'react';

/**
 * Modern Container Component
 * Provides responsive padding and max-width constraints
 */
interface ContainerProps {
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export function Container({ 
  children, 
  size = 'xl', 
  className = '' 
}: ContainerProps) {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <div className={`
      ${sizeClasses[size]} mx-auto px-fluid-md lg:px-fluid-xl
      ${className}
    `}>
      {children}
    </div>
  );
}

/**
 * Modern Section Component
 * Provides consistent vertical spacing
 */
interface SectionProps {
  children: ReactNode;
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  background?: 'white' | 'gray' | 'primary' | 'accent';
  className?: string;
}

export function Section({ 
  children, 
  spacing = 'lg', 
  background = 'white',
  className = '' 
}: SectionProps) {
  const spacingClasses = {
    sm: 'py-fluid-lg',
    md: 'py-fluid-xl',
    lg: 'py-fluid-2xl',
    xl: 'py-fluid-3xl'
  };

  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-eccsa-gray-50',
    primary: 'bg-eccsa-primary text-white',
    accent: 'bg-eccsa-accent text-white'
  };

  return (
    <section className={`
      ${spacingClasses[spacing]} ${backgroundClasses[background]}
      ${className}
    `}>
      {children}
    </section>
  );
}

/**
 * Modern Grid Component
 * Implements responsive grid with auto-fit columns
 */
interface GridProps {
  children: ReactNode;
  cols?: 'auto-fit-xs' | 'auto-fit-sm' | 'auto-fit-md' | 'auto-fit-lg' | 'auto-fill-xs' | 'auto-fill-sm' | 'auto-fill-md' | 'auto-fill-lg';
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function Grid({ 
  children, 
  cols = 'auto-fit-md', 
  gap = 'lg',
  className = '' 
}: GridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
    xl: 'gap-12'
  };

  return (
    <div className={`
      grid grid-cols-${cols} ${gapClasses[gap]}
      ${className}
    `}>
      {children}
    </div>
  );
}

/**
 * Modern Flex Component
 * Provides flexible layout options
 */
interface FlexProps {
  children: ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function Flex({ 
  children, 
  direction = 'row',
  align = 'start',
  justify = 'start',
  wrap = false,
  gap = 'md',
  className = '' 
}: FlexProps) {
  const directionClass = direction === 'row' ? 'flex-row' : 'flex-col';
  
  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  };

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  return (
    <div className={`
      flex ${directionClass} ${alignClasses[align]} ${justifyClasses[justify]}
      ${wrap ? 'flex-wrap' : ''} ${gapClasses[gap]}
      ${className}
    `}>
      {children}
    </div>
  );
}

/**
 * Modern Card Component
 * Provides consistent card styling
 */
interface CardProps {
  children: ReactNode;
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'soft' | 'medium' | 'strong';
  hover?: boolean;
  className?: string;
}

export function Card({ 
  children, 
  padding = 'lg',
  shadow = 'soft',
  hover = false,
  className = '' 
}: CardProps) {
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-12'
  };

  const shadowClasses = {
    soft: 'shadow-soft',
    medium: 'shadow-medium',
    strong: 'shadow-strong'
  };

  return (
    <div className={`
      bg-white rounded-xl ${paddingClasses[padding]} ${shadowClasses[shadow]}
      ${hover ? 'hover:shadow-strong hover:-translate-y-1 transition-all duration-300' : ''}
      ${className}
    `}>
      {children}
    </div>
  );
}

/**
 * Modern Button Component
 * Provides consistent button styling
 */
interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export function Button({ 
  children, 
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  className = '' 
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-eccsa-primary text-white hover:bg-eccsa-primary-dark focus:ring-eccsa-primary shadow-eccsa',
    secondary: 'bg-eccsa-gray-600 text-white hover:bg-eccsa-gray-700 focus:ring-eccsa-gray-500',
    accent: 'bg-eccsa-accent text-white hover:bg-eccsa-accent-dark focus:ring-eccsa-accent shadow-accent',
    outline: 'border-2 border-eccsa-primary text-eccsa-primary hover:bg-eccsa-primary hover:text-white focus:ring-eccsa-primary',
    ghost: 'text-eccsa-primary hover:bg-eccsa-primary/10 focus:ring-eccsa-primary'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl'
  };

  const disabledClasses = disabled || loading 
    ? 'opacity-50 cursor-not-allowed hover:transform-none' 
    : 'hover:-translate-y-0.5';

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses}
        ${fullWidth ? 'w-full' : ''}
        ${className}
      `}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );
}
