'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface Brand {
  src: string;
  alt: string;
}

interface ModernBrandsSliderProps {
  brands: Brand[];
  speed?: number;
  title?: string;
}

export default function ModernBrandsSlider({ 
  brands, 
  speed = 5, 
  title = "Nuestros Socios Tecnológicos" 
}: ModernBrandsSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % brands.length);
    }, speed * 1000);

    return () => clearInterval(interval);
  }, [brands.length, speed]);

  return (
    <section className="py-16 bg-gradient-to-r from-gray-50 to-white border-y border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Trabajamos con las marcas líderes en tecnología industrial para ofrecerle 
            las mejores soluciones del mercado
          </p>
        </div>
        
        <div className="relative overflow-hidden">
          <div className="flex animate-scroll">
            {/* Duplicamos las marcas para crear un efecto infinito */}
            {[...brands, ...brands].map((brand, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-48 h-24 mx-8 flex items-center justify-center bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                <div className="relative w-32 h-16 grayscale group-hover:grayscale-0 transition-all duration-300">
                  <Image
                    src={brand.src}
                    alt={brand.alt}
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 128px, 128px"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Indicadores */}
        <div className="flex justify-center mt-8 space-x-2">
          {brands.slice(0, Math.min(brands.length, 8)).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex % brands.length
                  ? 'bg-blue-600 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>
      
      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-scroll {
          animation: scroll ${speed * brands.length}s linear infinite;
        }
        
        .animate-scroll:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  );
}
