import { executeQuery } from './db';
import { Almacen } from '../types/database';

/**
 * Obtener todos los productos del almacén
 */
export async function getAllAlmacenProducts(): Promise<Almacen[]> {
  try {
    // Consulta simple para obtener TODOS los campos que existen en la tabla
    const query = `SELECT * FROM almacen ORDER BY id ASC`;

    const products = await executeQuery({ query }) as Almacen[];

    // Agregar campos de imagen si no existen
    const productsWithImages = products.map((product, index) => ({
      ...product,
      // Solo agregar imagen si no existe el campo
      Url_imagen: product.Url_imagen || `https://images.unsplash.com/photo-158109216056${index % 2 === 0 ? '2' : '7'}-40aa08e78837?w=400`,
      // Solo agregar descripción si no existe el campo
      Datos_importantes_Descripcion_muestra: product.Datos_importantes_Descripcion_muestra || product.descripcion || 'Producto de automatización industrial',
      // Solo agregar tiempo de entrega si no existe el campo
      tiempo_de_Entrega: product.tiempo_de_Entrega || product.tiempo_entrega_proveedor || '2-3 semanas'
    }));

    return productsWithImages;
  } catch (error) {
    console.error('Error fetching almacen products:', error);
    return [];
  }
}

/**
 * Obtener un producto del almacén por ID
 */
export async function getAlmacenProductById(id: number): Promise<Almacen | null> {
  try {
    const query = `
      SELECT
        id, numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
        fecha_creacion, fecha_actualizacion, activo
      FROM almacen
      WHERE id = ? AND activo = TRUE
    `;

    const products = await executeQuery({
      query,
      values: [id]
    }) as Almacen[];

    if (products.length > 0) {
      const product = products[0];
      return {
        ...product,
        Url_imagen: `https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400`,
        Datos_importantes_Descripcion_muestra: product.descripcion || 'Producto de automatización industrial',
        tiempo_de_Entrega: product.tiempo_entrega_proveedor || '2-3 semanas'
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching almacen product by ID:', error);
    return null;
  }
}

/**
 * Obtener un producto del almacén por número de almacén
 */
export async function getAlmacenProductByNumber(numero_almacen: string): Promise<Almacen | null> {
  try {
    const query = `
      SELECT
        id, numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
        fecha_creacion, fecha_actualizacion, activo, Url_imagen,
        Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
      FROM almacen
      WHERE numero_almacen = ? AND activo = TRUE
    `;

    const products = await executeQuery({
      query,
      values: [numero_almacen]
    }) as Almacen[];

    return products.length > 0 ? products[0] : null;
  } catch (error) {
    console.error('Error fetching almacen product by number:', error);
    return null;
  }
}

/**
 * Crear un nuevo producto en el almacén
 */
export async function createAlmacenProduct(product: Omit<Almacen, 'id' | 'fecha_creacion' | 'fecha_actualizacion'>): Promise<number | null> {
  try {
    const query = `
      INSERT INTO almacen (
        numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido, activo,
        Url_imagen, Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery({
      query,
      values: [
        product.numero_almacen,
        product.estante || null,
        product.modelo_existente,
        product.descripcion || null,
        product.precio_venta || null,
        product.precio_ml || null,
        product.vendidos || 0,
        product.cantidad_nuevo || 0,
        product.minimo || 0,
        product.maximo || 0,
        product.pedir_cantidad || 0,
        product.precio_us || null,
        product.precio_mx || null,
        product.impuesto || 16.00,
        product.codigo_sat || null,
        product.nota || null,
        product.proveedor || null,
        product.marcas || null,
        product.tiempo_entrega_proveedor || null,
        product.fecha_pedido || null,
        product.fecha_recibido || null,
        product.activo !== undefined ? product.activo : true,
        product.Url_imagen || null,
        product.Datos_importantes_Descripcion_muestra || null,
        product.tiempo_de_Entrega || null
      ],
    }) as any;

    return result.insertId;
  } catch (error) {
    console.error('Error creating almacen product:', error);
    return null;
  }
}

/**
 * Actualizar un producto del almacén
 */
export async function updateAlmacenProduct(id: number, product: Partial<Almacen>): Promise<boolean> {
  try {
    const fields = [];
    const values = [];

    // Construir dinámicamente la consulta UPDATE
    if (product.numero_almacen !== undefined) {
      fields.push('numero_almacen = ?');
      values.push(product.numero_almacen);
    }
    if (product.estante !== undefined) {
      fields.push('estante = ?');
      values.push(product.estante);
    }
    if (product.modelo_existente !== undefined) {
      fields.push('modelo_existente = ?');
      values.push(product.modelo_existente);
    }
    if (product.descripcion !== undefined) {
      fields.push('descripcion = ?');
      values.push(product.descripcion);
    }
    if (product.precio_venta !== undefined) {
      fields.push('precio_venta = ?');
      values.push(product.precio_venta);
    }
    if (product.precio_ml !== undefined) {
      fields.push('precio_ml = ?');
      values.push(product.precio_ml);
    }
    if (product.vendidos !== undefined) {
      fields.push('vendidos = ?');
      values.push(product.vendidos);
    }
    if (product.cantidad_nuevo !== undefined) {
      fields.push('cantidad_nuevo = ?');
      values.push(product.cantidad_nuevo);
    }
    if (product.minimo !== undefined) {
      fields.push('minimo = ?');
      values.push(product.minimo);
    }
    if (product.maximo !== undefined) {
      fields.push('maximo = ?');
      values.push(product.maximo);
    }
    if (product.pedir_cantidad !== undefined) {
      fields.push('pedir_cantidad = ?');
      values.push(product.pedir_cantidad);
    }
    if (product.precio_us !== undefined) {
      fields.push('precio_us = ?');
      values.push(product.precio_us);
    }
    if (product.precio_mx !== undefined) {
      fields.push('precio_mx = ?');
      values.push(product.precio_mx);
    }
    if (product.impuesto !== undefined) {
      fields.push('impuesto = ?');
      values.push(product.impuesto);
    }
    if (product.codigo_sat !== undefined) {
      fields.push('codigo_sat = ?');
      values.push(product.codigo_sat);
    }
    if (product.nota !== undefined) {
      fields.push('nota = ?');
      values.push(product.nota);
    }
    if (product.proveedor !== undefined) {
      fields.push('proveedor = ?');
      values.push(product.proveedor);
    }
    if (product.marcas !== undefined) {
      fields.push('marcas = ?');
      values.push(product.marcas);
    }
    if (product.tiempo_entrega_proveedor !== undefined) {
      fields.push('tiempo_entrega_proveedor = ?');
      values.push(product.tiempo_entrega_proveedor);
    }
    if (product.fecha_pedido !== undefined) {
      fields.push('fecha_pedido = ?');
      values.push(product.fecha_pedido);
    }
    if (product.fecha_recibido !== undefined) {
      fields.push('fecha_recibido = ?');
      values.push(product.fecha_recibido);
    }
    if (product.activo !== undefined) {
      fields.push('activo = ?');
      values.push(product.activo);
    }
    if (product.Url_imagen !== undefined) {
      fields.push('Url_imagen = ?');
      values.push(product.Url_imagen);
    }
    if (product.Datos_importantes_Descripcion_muestra !== undefined) {
      fields.push('Datos_importantes_Descripcion_muestra = ?');
      values.push(product.Datos_importantes_Descripcion_muestra);
    }
    if (product.tiempo_de_Entrega !== undefined) {
      fields.push('tiempo_de_Entrega = ?');
      values.push(product.tiempo_de_Entrega);
    }

    if (fields.length === 0) {
      return false; // No hay campos para actualizar
    }

    // Agregar fecha de actualización
    fields.push('fecha_actualizacion = NOW()');
    values.push(id);

    const query = `
      UPDATE almacen
      SET ${fields.join(', ')}
      WHERE id = ?
    `;

    await executeQuery({ query, values });
    return true;
  } catch (error) {
    console.error('Error updating almacen product:', error);
    return false;
  }
}

/**
 * Eliminar (desactivar) un producto del almacén
 */
export async function deleteAlmacenProduct(id: number): Promise<boolean> {
  try {
    const query = `
      UPDATE almacen
      SET activo = FALSE, fecha_actualizacion = NOW()
      WHERE id = ?
    `;

    await executeQuery({
      query,
      values: [id]
    });

    return true;
  } catch (error) {
    console.error('Error deleting almacen product:', error);
    return false;
  }
}

/**
 * Eliminar permanentemente un producto del almacén de la base de datos
 */
export async function deleteAlmacenProductForever(id: number): Promise<boolean> {
  try {
    const query = `
      DELETE FROM almacen
      WHERE id = ?
    `;

    await executeQuery({
      query,
      values: [id]
    });

    return true;
  } catch (error) {
    console.error('Error permanently deleting almacen product:', error);
    return false;
  }
}

/**
 * Buscar productos en el almacén
 */
export async function searchAlmacenProducts(searchTerm: string): Promise<Almacen[]> {
  try {
    const query = `
      SELECT
        id, numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
        fecha_creacion, fecha_actualizacion, activo, Url_imagen,
        Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
      FROM almacen
      WHERE activo = TRUE
        AND (
          numero_almacen LIKE ? OR
          modelo_existente LIKE ? OR
          descripcion LIKE ? OR
          proveedor LIKE ? OR
          marcas LIKE ? OR
          codigo_sat LIKE ?
        )
      ORDER BY numero_almacen ASC
    `;

    const searchPattern = `%${searchTerm}%`;
    const products = await executeQuery({
      query,
      values: [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern]
    }) as Almacen[];

    return products;
  } catch (error) {
    console.error('Error searching almacen products:', error);
    return [];
  }
}

/**
 * Obtener productos con stock bajo (cantidad menor al mínimo)
 */
export async function getLowStockProducts(): Promise<Almacen[]> {
  try {
    const query = `
      SELECT
        id, numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
        fecha_creacion, fecha_actualizacion, activo, Url_imagen,
        Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
      FROM almacen
      WHERE activo = TRUE
        AND cantidad_nuevo <= minimo
        AND minimo > 0
      ORDER BY (cantidad_nuevo - minimo) ASC
    `;

    const products = await executeQuery({ query }) as Almacen[];
    return products;
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    return [];
  }
}

/**
 * Obtener estadísticas del almacén
 */
export async function getAlmacenStats() {
  try {
    const query = `
      SELECT
        COUNT(*) as total_productos,
        SUM(cantidad_nuevo) as total_cantidad,
        SUM(vendidos) as total_vendidos,
        COUNT(CASE WHEN cantidad_nuevo <= minimo AND minimo > 0 THEN 1 END) as productos_stock_bajo,
        COUNT(CASE WHEN cantidad_nuevo = 0 THEN 1 END) as productos_sin_stock,
        SUM(precio_venta * cantidad_nuevo) as valor_total_inventario
      FROM almacen
      WHERE activo = TRUE
    `;

    const stats = await executeQuery({ query }) as any[];
    return stats[0];
  } catch (error) {
    console.error('Error fetching almacen stats:', error);
    return null;
  }
}

/**
 * Obtener el producto más vendido
 */
export async function getMostSoldProduct() {
  try {
    const query = `
      SELECT
        numero_almacen, modelo_existente, marcas, vendidos
      FROM almacen
      WHERE activo = TRUE AND vendidos > 0
      ORDER BY vendidos DESC
      LIMIT 1
    `;

    const products = await executeQuery({ query }) as any[];
    return products[0] || null;
  } catch (error) {
    console.error('Error fetching most sold product:', error);
    return null;
  }
}

/**
 * Obtener productos agregados recientemente
 */
export async function getRecentProducts(limit: number = 5) {
  try {
    const query = `
      SELECT
        numero_almacen, modelo_existente, marcas, fecha_creacion
      FROM almacen
      WHERE activo = TRUE
      ORDER BY fecha_creacion DESC
      LIMIT ?
    `;

    const products = await executeQuery({ query, values: [limit] }) as any[];
    return products;
  } catch (error) {
    console.error('Error fetching recent products:', error);
    return [];
  }
}

/**
 * Obtener productos con más stock (para productos destacados)
 */
export async function getHighStockProducts(limit: number = 4): Promise<Almacen[]> {
  try {
    const query = `
      SELECT
        id, numero_almacen, estante, modelo_existente, descripcion,
        precio_venta, precio_ml, vendidos, cantidad_nuevo, minimo, maximo,
        pedir_cantidad, precio_us, precio_mx, impuesto, codigo_sat, nota,
        proveedor, marcas, tiempo_entrega_proveedor, fecha_pedido, fecha_recibido,
        fecha_creacion, fecha_actualizacion, activo, Url_imagen,
        Datos_importantes_Descripcion_muestra, tiempo_de_Entrega
      FROM almacen
      WHERE activo = TRUE AND cantidad_nuevo > 0
      ORDER BY cantidad_nuevo DESC
      LIMIT ?
    `;

    const products = await executeQuery({
      query,
      values: [limit]
    }) as Almacen[];

    // Agregar campos mock para imágenes y descripciones
    const productsWithMockData = products.map((product, index) => ({
      ...product,
      Url_imagen: `https://images.unsplash.com/photo-158109216056${index % 2 === 0 ? '2' : '7'}-40aa08e78837?w=400`,
      Datos_importantes_Descripcion_muestra: product.descripcion || 'Producto de automatización industrial',
      tiempo_de_Entrega: product.tiempo_entrega_proveedor || '2-3 semanas'
    }));

    return productsWithMockData;
  } catch (error) {
    console.error('Error fetching high stock products:', error);
    return [];
  }
}

/**
 * Obtener productos más vendidos (para productos destacados)
 */
export async function getBestSellingProducts(limit: number = 4): Promise<Almacen[]> {
  try {
    const query = `
      SELECT * FROM almacen
      WHERE vendidos > 0
      ORDER BY vendidos DESC, cantidad_nuevo DESC
      LIMIT ?
    `;

    const products = await executeQuery({
      query,
      values: [limit]
    }) as Almacen[];

    // Agregar campos de imagen si no existen
    const productsWithImages = products.map((product, index) => ({
      ...product,
      // Solo agregar imagen si no existe el campo
      Url_imagen: product.Url_imagen || `https://images.unsplash.com/photo-158109216056${index % 2 === 0 ? '2' : '7'}-40aa08e78837?w=400`,
      // Solo agregar descripción si no existe el campo
      Datos_importantes_Descripcion_muestra: product.Datos_importantes_Descripcion_muestra || product.descripcion || 'Producto de automatización industrial',
      // Solo agregar tiempo de entrega si no existe el campo
      tiempo_de_Entrega: product.tiempo_de_Entrega || product.tiempo_entrega_proveedor || '2-3 semanas'
    }));

    return productsWithImages;
  } catch (error) {
    console.error('Error fetching best selling products:', error);
    return [];
  }
}
