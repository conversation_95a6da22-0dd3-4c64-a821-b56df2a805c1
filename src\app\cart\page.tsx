'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProductImage from '@/components/ProductImage';

interface CartItem {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  marcas?: string;
  Url_imagen?: string;
  cantidad: number;
  stock_disponible: number;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
  estante?: string;
}

export default function CartPage() {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isClient, setIsClient] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    whatsapp: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stockAlerts, setStockAlerts] = useState<{[key: number]: boolean}>({});
  const router = useRouter();

  // Manejar hidratación del cliente
  useEffect(() => {
    setIsClient(true);
    loadCartItems();
  }, []);

  const loadCartItems = () => {
    try {
      const savedItems = localStorage.getItem('eccsa-cart');
      if (savedItems) {
        const parsedItems = JSON.parse(savedItems);
        setItems(parsedItems);

        // Inicializar alertas de stock
        const alerts: {[key: number]: boolean} = {};
        parsedItems.forEach((item: CartItem) => {
          alerts[item.id] = item.cantidad > item.stock_disponible;
        });
        setStockAlerts(alerts);
      }
    } catch (error) {
      console.error('Error loading cart items:', error);
    }
  };

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const updatedItems = items.map(item => {
      if (item.id === id) {
        return { ...item, cantidad: newQuantity };
      }
      return item;
    });

    setItems(updatedItems);
    localStorage.setItem('eccsa-cart', JSON.stringify(updatedItems));

    // Disparar evento para actualizar el contador en el navbar
    window.dispatchEvent(new CustomEvent('cartUpdated'));

    // Actualizar alerta de stock
    const item = items.find(item => item.id === id);
    if (item) {
      setStockAlerts(prev => ({
        ...prev,
        [id]: newQuantity > item.stock_disponible
      }));
    }
  };

  const removeItem = (id: number) => {
    const updatedItems = items.filter(item => item.id !== id);
    setItems(updatedItems);
    localStorage.setItem('eccsa-cart', JSON.stringify(updatedItems));

    // Disparar evento para actualizar el contador en el navbar
    window.dispatchEvent(new CustomEvent('cartUpdated'));

    // Remover alerta de stock
    setStockAlerts(prev => {
      const newAlerts = { ...prev };
      delete newAlerts[id];
      return newAlerts;
    });
  };

  const clearCart = () => {
    setItems([]);
    localStorage.removeItem('eccsa-cart');
    setStockAlerts({});
    // Disparar evento para actualizar el contador en el navbar
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => {
      const price = item.precio_ml || 0;
      return total + (price * item.cantidad);
    }, 0);
  };

  const getTotalWithTax = () => {
    const subtotal = getTotalPrice();
    return subtotal * 1.16; // 16% IVA
  };

  const formatPrice = (price: number) => {
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
    return formattedPrice;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validar email
      if (!formData.email) {
        alert('El email es obligatorio');
        setIsSubmitting(false);
        return;
      }

      // Enviar cotización de carrito como una sola solicitud
      const solicitudResponse = await fetch('/api/solicitudes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cliente_email: formData.email,
          cliente_whatsapp: formData.whatsapp || null,
          productos: items.map(item => ({
            numero_almacen: item.numero_almacen,
            modelo: item.modelo_existente,
            marca: item.marcas,
            cantidad: item.cantidad,
            precio: item.precio_ml
          }))
        }),
      });

      if (!solicitudResponse.ok) {
        const errorData = await solicitudResponse.json();
        throw new Error(errorData.error || 'Error al enviar la cotización');
      }

      const solicitudData = await solicitudResponse.json();

      // Mostrar mensaje de éxito con información detallada
      let mensajeExito = `¡Cotización de carrito enviada exitosamente!\n\n`;
      mensajeExito += `✅ Se creó una solicitud con ${solicitudData.resumen.total_productos} productos.\n`;
      mensajeExito += `📋 ID de solicitud: ${solicitudData.solicitud_grupo_id}\n`;

      if (solicitudData.resumen.productos_con_alerta > 0) {
        mensajeExito += `⚠️ ${solicitudData.resumen.productos_con_alerta} producto${solicitudData.resumen.productos_con_alerta > 1 ? 's tienen' : ' tiene'} stock limitado.\n`;
      }

      mensajeExito += `💰 Total estimado: $${solicitudData.resumen.total_con_iva.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN (con IVA)\n`;
      mensajeExito += `\nNos pondremos en contacto contigo pronto.`;

      alert(mensajeExito);

      // Limpiar formulario y carrito
      setFormData({
        email: '',
        whatsapp: ''
      });

      clearCart();

      // Redirigir a productos
      router.push('/products');

    } catch (error) {
      console.error('Error al enviar cotización:', error);
      alert('Error al enviar la cotización. Por favor, inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isClient) {
    return (
      <div className="cart-page-loading">
        <div className="loading-spinner"></div>
        <p>Cargando carrito...</p>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="cart-page-empty">
        <div className="empty-cart-content">
          <h1>Tu carrito está vacío</h1>
          <p>Agrega productos desde nuestro catálogo para solicitar una cotización.</p>
          <button
            onClick={() => router.push('/products')}
            className="btn-primary"
          >
            Ver Productos
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="cart-page">
      <div className="cart-page-container">
        <div className="cart-page-header">
          <h1>Carrito de Cotización</h1>
          <p>Revisa los productos seleccionados y solicita tu cotización</p>
        </div>

        <div className="cart-page-content">
          {/* Lista de productos */}
          <div className="cart-products-section">
            <h2>Productos Seleccionados ({items.length})</h2>

            <div className="cart-products-list">
              {items.map((item) => (
                <div key={item.id} className="cart-product-card">
                  <div className="cart-product-image">
                    {/* Usar img normal para URLs de base de datos, ProductImage para imágenes locales */}
                    {item.Url_imagen && (item.Url_imagen.startsWith('http') || item.Url_imagen.startsWith('https')) ? (
                      <img
                        src={item.Url_imagen}
                        alt={item.modelo_existente}
                        className="cart-product-img"
                        loading="lazy"
                        onError={(e) => {
                          // Si la imagen externa falla, usar imagen por defecto
                          (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                        }}
                      />
                    ) : (
                      <ProductImage
                        src={item.Url_imagen || "/images/logos/logo_pequeno.png"}
                        alt={item.modelo_existente}
                        width={120}
                        height={120}
                        className="cart-product-img"
                      />
                    )}
                  </div>

                  <div className="cart-product-info">
                    <h3>{item.modelo_existente}</h3>
                    <p className="cart-product-brand">{item.marcas || 'Sin marca'}</p>
                    <p className="cart-product-alm">ALM: {item.numero_almacen}</p>

                    {item.Datos_importantes_Descripcion_muestra && (
                      <div className="cart-product-description">
                        <h4>Descripción:</h4>
                        <p>{item.Datos_importantes_Descripcion_muestra}</p>
                      </div>
                    )}

                    <div className="cart-product-details">
                      <span>Stock disponible: {item.stock_disponible}</span>
                      {item.tiempo_de_Entrega && (
                        <span>Tiempo de entrega: {item.tiempo_de_Entrega}</span>
                      )}
                      {item.estante && (
                        <span>Ubicación: {item.estante}</span>
                      )}
                    </div>
                  </div>

                  <div className="cart-product-controls">
                    <div className="cart-product-price">
                      {item.precio_ml && (
                        <>
                          <span className="price-unit">
                            {formatPrice(item.precio_ml)} c/u
                          </span>
                          <span className="price-total">
                            {formatPrice(item.precio_ml * item.cantidad)}
                          </span>
                        </>
                      )}
                    </div>

                    <div className="cart-quantity-controls">
                      <label>Cantidad:</label>
                      <div className="quantity-input-group">
                        <button
                          onClick={() => updateQuantity(item.id, item.cantidad - 1)}
                          disabled={item.cantidad <= 1}
                          className="quantity-btn"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          value={item.cantidad}
                          onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 1)}
                          min="1"
                          className="quantity-input"
                        />
                        <button
                          onClick={() => updateQuantity(item.id, item.cantidad + 1)}
                          className="quantity-btn"
                        >
                          +
                        </button>
                      </div>
                    </div>

                    {stockAlerts[item.id] && (
                      <div className="stock-alert-cart">
                        ⚠️ No hay suficiente material disponible. Se le cotizará con mayor tiempo de entrega.
                      </div>
                    )}

                    <button
                      onClick={() => removeItem(item.id)}
                      className="remove-item-btn"
                    >
                      Eliminar
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Resumen y formulario */}
          <div className="cart-summary-section">
            <div className="cart-summary">
              <h3>Resumen de Cotización</h3>

              <div className="summary-totals">
                <div className="summary-row">
                  <span>Subtotal:</span>
                  <span>{formatPrice(getTotalPrice())}</span>
                </div>
                <div className="summary-row">
                  <span>IVA (16%):</span>
                  <span>{formatPrice(getTotalWithTax() - getTotalPrice())}</span>
                </div>
                <div className="summary-row summary-total">
                  <span>Total:</span>
                  <span>{formatPrice(getTotalWithTax())}</span>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="cart-quote-form">
              <h3>Solicitar Cotización</h3>

              <div className="form-group">
                <label htmlFor="email">
                  Email <span className="required">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="form-group">
                <label htmlFor="whatsapp">WhatsApp (opcional)</label>
                <input
                  type="tel"
                  id="whatsapp"
                  name="whatsapp"
                  value={formData.whatsapp}
                  onChange={handleInputChange}
                  placeholder="+52 81 1234 5678"
                />
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  onClick={() => router.push('/products')}
                  className="btn-secondary"
                >
                  Seguir Comprando
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary"
                >
                  {isSubmitting ? 'Enviando...' : 'Enviar Cotización'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
