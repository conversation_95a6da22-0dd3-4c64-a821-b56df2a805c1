const mysql = require('serverless-mysql');

const db = mysql({
  config: {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: parseInt(process.env.DB_PORT || '3306'),
    connectTimeout: 3000,
  }
});

async function createSolicitudesTable() {
  try {
    console.log('Creando tabla de solicitudes...');
    
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS solicitudes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        numero_almacen VARCHAR(50) NOT NULL,
        modelo VARCHAR(255) NOT NULL,
        marca VARCHAR(100),
        descripcion TEXT,
        estante VARCHAR(50),
        precio_unitario DECIMAL(10,2),
        cantidad_solicitada INT NOT NULL,
        cantidad_disponible INT,
        cliente_email VARCHAR(255) NOT NULL,
        cliente_whatsapp VARCHAR(20),
        estado <PERSON>NUM('pendiente', 'procesando', 'cotizado', 'rechazado') DEFAULT 'pendiente',
        alerta_stock BOOLEAN DEFAULT FALSE,
        tiempo_entrega_estimado VARCHAR(100),
        notas TEXT,
        fecha_solicitud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_numero_almacen (numero_almacen),
        INDEX idx_estado (estado),
        INDEX idx_fecha_solicitud (fecha_solicitud),
        INDEX idx_cliente_email (cliente_email)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createTableQuery);
    console.log('✅ Tabla solicitudes creada exitosamente');

    // Verificar que la tabla se creó correctamente
    const checkTable = await db.query('DESCRIBE solicitudes');
    console.log('📋 Estructura de la tabla solicitudes:');
    console.table(checkTable);

  } catch (error) {
    console.error('❌ Error al crear tabla solicitudes:', error);
    throw error;
  } finally {
    await db.end();
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createSolicitudesTable()
    .then(() => {
      console.log('🎉 Migración completada exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error en la migración:', error);
      process.exit(1);
    });
}

module.exports = { createSolicitudesTable };
