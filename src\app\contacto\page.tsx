'use client';

import React from 'react';

export default function ContactoPage() {

  const contactSections = [
    {
      title: 'Servicios de Automatización',
      subtitle: 'Contratación de servicios especializados',
      icon: '🔧',
      contact: {
        name: '<PERSON>',
        position: '<PERSON><PERSON><PERSON>',
        whatsapp: '+5218182803296',
        email: '<EMAIL>'
      },
      gradient: 'linear-gradient(135deg, #0056a6, #003366)',
      bgColor: '#f0f7ff'
    },
    {
      title: 'Compra de Productos',
      subtitle: 'Materiales y equipos industriales',
      icon: '🛒',
      contact: {
        name: '<PERSON><PERSON><PERSON>',
        position: '<PERSON><PERSON><PERSON> Ventas',
        whatsapp: '+5218187043546',
        email: '<EMAIL>'
      },
      gradient: 'linear-gradient(135deg, #f7941d, #e67e22)',
      bgColor: '#fff7ed'
    }
  ];

  const locationInfo = {
    title: 'Nuestra Ubicación',
    address: 'Monterrey, Nuevo León, México',
    description: 'Nos ubicamos en el corazón industrial del norte de México',
    coordinates: { lat: 25.6866, lng: -100.3161 }
  };

  return (
    <div style={{
      marginTop: '0px',
      backgroundColor: '#f8fafc',
      minHeight: '100vh'
    }}>
      {/* Hero Section */}
      <div style={{
        background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
        color: 'white',
        padding: '4rem 2rem 3rem 2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          right: '-50px',
          width: '200px',
          height: '200px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '-30px',
          left: '-30px',
          width: '150px',
          height: '150px',
          background: 'rgba(255,255,255,0.05)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}>
          {/* Badge */}
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            backgroundColor: 'rgba(255,255,255,0.2)',
            padding: '0.5rem 1rem',
            borderRadius: '25px',
            marginBottom: '1.5rem',
            fontSize: '0.9rem',
            fontWeight: '600'
          }}>
            <span>📞</span>
            <span>Contáctanos</span>
          </div>

          <h1 style={{
            fontSize: '3rem',
            marginBottom: '1rem',
            fontWeight: 'bold',
            lineHeight: '1.2'
          }}>
            Ponte en <span style={{ color: '#f7941d' }}>Contacto</span>
          </h1>

          <p style={{
            fontSize: '1.3rem',
            opacity: '0.9',
            maxWidth: '700px',
            margin: '0 auto 2rem auto',
            lineHeight: '1.6'
          }}>
            Conecta con nuestros especialistas para servicios de automatización y compra de productos industriales.
            Cada área cuenta con expertos dedicados para brindarte la mejor atención.
          </p>
        </div>
      </div>

      {/* Contact Sections */}
      <div style={{
        maxWidth: '1200px',
        margin: '-2rem auto 4rem auto',
        padding: '0 2rem',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '3rem',
          marginBottom: '4rem'
        }}>
          {contactSections.map((section, index) => (
            <div key={index} style={{
              backgroundColor: 'white',
              borderRadius: '24px',
              padding: '3rem',
              boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
              border: '1px solid #f1f5f9',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.12)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.08)';
            }}>
              {/* Background decoration */}
              <div style={{
                position: 'absolute',
                top: '-30px',
                right: '-30px',
                width: '120px',
                height: '120px',
                background: section.gradient,
                borderRadius: '50%',
                opacity: '0.08',
                zIndex: 1
              }}></div>

              {/* Header */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                marginBottom: '2rem',
                position: 'relative',
                zIndex: 2
              }}>
                <div style={{
                  fontSize: '3rem',
                  width: '80px',
                  height: '80px',
                  background: section.bgColor,
                  borderRadius: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: `2px solid ${section.gradient.includes('0056a6') ? '#e6f2ff' : '#fff4e6'}`
                }}>
                  {section.icon}
                </div>
                <div>
                  <h3 style={{
                    fontSize: '1.5rem',
                    fontWeight: '700',
                    color: '#1e293b',
                    marginBottom: '0.5rem',
                    lineHeight: '1.2'
                  }}>
                    {section.title}
                  </h3>
                  <p style={{
                    fontSize: '1rem',
                    color: '#64748b',
                    margin: 0
                  }}>
                    {section.subtitle}
                  </p>
                </div>
              </div>

              {/* Contact Person */}
              <div style={{
                backgroundColor: section.bgColor,
                borderRadius: '16px',
                padding: '2rem',
                marginBottom: '2rem',
                position: 'relative',
                zIndex: 2
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  marginBottom: '1.5rem'
                }}>
                  <div style={{
                    width: '60px',
                    height: '60px',
                    background: section.gradient,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem',
                    fontWeight: 'bold'
                  }}>
                    {section.contact.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <h4 style={{
                      fontSize: '1.2rem',
                      fontWeight: '700',
                      color: '#1e293b',
                      margin: 0,
                      marginBottom: '0.25rem'
                    }}>
                      {section.contact.name}
                    </h4>
                    <p style={{
                      fontSize: '0.9rem',
                      color: '#64748b',
                      margin: 0
                    }}>
                      {section.contact.position}
                    </p>
                  </div>
                </div>

                {/* Contact Methods */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '1rem'
                }}>
                  {/* WhatsApp */}
                  <a
                    href={`https://wa.me/${section.contact.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem',
                      padding: '1rem',
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      textDecoration: 'none',
                      color: '#1e293b',
                      border: '1px solid #e2e8f0',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#25d366';
                      e.currentTarget.style.color = 'white';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.color = '#1e293b';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
                    </svg>
                    <div>
                      <div style={{ fontSize: '0.8rem', color: 'inherit', opacity: 0.7 }}>WhatsApp</div>
                      <div style={{ fontSize: '1rem', fontWeight: '600' }}>{section.contact.whatsapp}</div>
                    </div>
                  </a>

                  {/* Email */}
                  <a
                    href={`mailto:${section.contact.email}`}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.75rem',
                      padding: '1rem',
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      textDecoration: 'none',
                      color: '#1e293b',
                      border: '1px solid #e2e8f0',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f1f5f9';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    <div>
                      <div style={{ fontSize: '0.8rem', color: 'inherit', opacity: 0.7 }}>Email</div>
                      <div style={{ fontSize: '1rem', fontWeight: '600' }}>{section.contact.email}</div>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Location Section */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '24px',
          padding: '3rem',
          boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
          border: '1px solid #f1f5f9',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background decoration */}
          <div style={{
            position: 'absolute',
            top: '-30px',
            left: '-30px',
            width: '120px',
            height: '120px',
            background: 'linear-gradient(135deg, #10b981, #059669)',
            borderRadius: '50%',
            opacity: '0.08',
            zIndex: 1
          }}></div>

          <div style={{ position: 'relative', zIndex: 2 }}>
            {/* Icon */}
            <div style={{
              width: '100px',
              height: '100px',
              background: 'linear-gradient(135deg, #10b981, #059669)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 2rem auto',
              color: 'white',
              fontSize: '3rem'
            }}>
              📍
            </div>

            <h3 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: '#1e293b',
              marginBottom: '1rem'
            }}>
              {locationInfo.title}
            </h3>

            <p style={{
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#10b981',
              marginBottom: '0.5rem'
            }}>
              {locationInfo.address}
            </p>

            <p style={{
              fontSize: '1rem',
              color: '#64748b',
              lineHeight: '1.6',
              maxWidth: '500px',
              margin: '0 auto'
            }}>
              {locationInfo.description}
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div style={{
        background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
        color: 'white',
        padding: '4rem 2rem',
        margin: '4rem 0 0 0',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '-30px',
          right: '-30px',
          width: '150px',
          height: '150px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>

        <div style={{
          maxWidth: '800px',
          margin: '0 auto',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}>
          {/* Icon */}
          <div style={{
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 2rem auto'
          }}>
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
          </div>

          <h3 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1.5rem',
            lineHeight: '1.2'
          }}>
            ¿Listo para <span style={{ color: '#f7941d' }}>comenzar</span> tu proyecto?
          </h3>

          <p style={{
            fontSize: '1.2rem',
            opacity: '0.9',
            marginBottom: '2.5rem',
            lineHeight: '1.6',
            maxWidth: '600px',
            margin: '0 auto 2.5rem auto'
          }}>
            Nuestro equipo de expertos está listo para ayudarte a automatizar y optimizar tus procesos industriales.
            Contáctanos hoy mismo para una consulta gratuita.
          </p>

          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            <a
              href="https://wa.me/5218182803296"
              target="_blank"
              rel="noopener noreferrer"
              style={{
                padding: '1rem 2rem',
                background: 'linear-gradient(135deg, #25d366, #128c7e)',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 4px 12px rgba(37,211,102,0.3)',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(37,211,102,0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(37,211,102,0.3)';
              }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
              </svg>
              WhatsApp Servicios
            </a>

            <a
              href="https://wa.me/5218187043546"
              target="_blank"
              rel="noopener noreferrer"
              style={{
                padding: '1rem 2rem',
                background: 'linear-gradient(135deg, #f7941d, #e67e22)',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 4px 12px rgba(247,148,29,0.3)',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(247,148,29,0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(247,148,29,0.3)';
              }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
              </svg>
              WhatsApp Ventas
            </a>

            <a href="mailto:<EMAIL>" style={{
              padding: '1rem 2rem',
              backgroundColor: 'transparent',
              color: 'white',
              border: '2px solid white',
              borderRadius: '12px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'white';
              e.currentTarget.style.color = '#0056a6';
              e.currentTarget.style.transform = 'translateY(-2px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.transform = 'translateY(0)';
            }}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              Enviar Email
            </a>
          </div>
        </div>
      </div>

      {/* CSS for animations and responsive design */}
      <style jsx>{`
        @media (max-width: 768px) {
          .hero-section {
            padding: 2rem 1rem !important;
          }

          .hero-title {
            font-size: 2rem !important;
          }

          .contact-sections-grid {
            grid-template-columns: 1fr !important;
            gap: 2rem !important;
          }

          .contact-section-card {
            padding: 2rem !important;
          }

          .contact-header {
            flex-direction: column !important;
            text-align: center !important;
            gap: 1rem !important;
          }

          .contact-methods {
            gap: 0.75rem !important;
          }

          .contact-method-link {
            padding: 0.75rem !important;
            font-size: 0.9rem !important;
          }

          .cta-actions {
            flex-direction: column !important;
            align-items: center !important;
            gap: 0.75rem !important;
          }

          .cta-button {
            width: 100% !important;
            max-width: 280px !important;
            justify-content: center !important;
          }
        }

        @media (max-width: 480px) {
          .contact-section-card {
            padding: 1.5rem !important;
          }

          .contact-icon-large {
            width: 60px !important;
            height: 60px !important;
            font-size: 2rem !important;
          }

          .contact-person-avatar {
            width: 50px !important;
            height: 50px !important;
            font-size: 1.2rem !important;
          }

          .location-icon {
            width: 80px !important;
            height: 80px !important;
            font-size: 2.5rem !important;
          }
        }

        /* Hover animations */
        .contact-section-card:hover {
          transform: translateY(-8px);
          box-shadow: 0 25px 50px rgba(0,0,0,0.12);
        }

        .contact-method-link:hover {
          transform: translateY(-2px);
        }

        .whatsapp-link:hover {
          background-color: #25d366 !important;
          color: white !important;
        }

        .email-link:hover {
          background-color: #f1f5f9 !important;
        }

        /* Smooth transitions */
        .contact-section-card,
        .contact-method-link,
        .cta-button {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      `}</style>
    </div>
  );
}
