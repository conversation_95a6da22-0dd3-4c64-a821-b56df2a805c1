'use client';

import { useState, useEffect } from 'react';

interface CartItem {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  marcas?: string;
  Url_imagen?: string;
  cantidad: number;
  stock_disponible: number;
}

interface CartQuoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  cartItems: CartItem[];
  totalPrice: number;
  totalWithTax: number;
  onQuoteSubmitted: () => void;
}

export default function CartQuoteModal({
  isOpen,
  onClose,
  cartItems,
  totalPrice,
  totalWithTax,
  onQuoteSubmitted
}: CartQuoteModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    whatsapp: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stockAlerts, setStockAlerts] = useState<{[key: number]: boolean}>({});
  const [editableQuantities, setEditableQuantities] = useState<{[key: number]: number}>({});

  // Inicializar cantidades editables cuando se abra el modal
  useEffect(() => {
    if (isOpen && cartItems.length > 0) {
      const initialQuantities: {[key: number]: number} = {};
      const initialAlerts: {[key: number]: boolean} = {};

      cartItems.forEach(item => {
        initialQuantities[item.id] = item.cantidad;
        initialAlerts[item.id] = item.cantidad > item.stock_disponible;
      });

      setEditableQuantities(initialQuantities);
      setStockAlerts(initialAlerts);
    }
  }, [isOpen, cartItems]);

  const formatPrice = (price: number) => {
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
    return formattedPrice;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleQuantityChange = (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const item = cartItems.find(item => item.id === itemId);
    if (!item) return;

    setEditableQuantities(prev => ({
      ...prev,
      [itemId]: newQuantity
    }));

    // Actualizar alerta de stock
    setStockAlerts(prev => ({
      ...prev,
      [itemId]: newQuantity > item.stock_disponible
    }));
  };

  const getUpdatedCartItems = () => {
    return cartItems.map(item => ({
      ...item,
      cantidad: editableQuantities[item.id] || item.cantidad
    }));
  };

  const getTotalPrice = () => {
    return getUpdatedCartItems().reduce((total, item) => {
      const price = item.precio_ml || 0;
      return total + (price * item.cantidad);
    }, 0);
  };

  const getTotalWithTax = () => {
    const subtotal = getTotalPrice();
    return subtotal * 1.16; // 16% IVA
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const updatedItems = getUpdatedCartItems();

      // Validar email
      if (!formData.email) {
        alert('El email es obligatorio');
        setIsSubmitting(false);
        return;
      }

      // Enviar solicitudes individuales para cada producto (como las cotizaciones normales)
      const solicitudesCreadas = [];
      let productosConAlerta = 0;
      let errores = 0;

      for (const item of updatedItems) {
        try {
          const solicitudResponse = await fetch('/api/solicitudes', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              numero_almacen: item.numero_almacen,
              cantidad_solicitada: item.cantidad,
              cliente_email: formData.email,
              cliente_whatsapp: formData.whatsapp || null
            }),
          });

          if (solicitudResponse.ok) {
            const solicitudData = await solicitudResponse.json();
            solicitudesCreadas.push({
              producto: item.modelo_existente,
              solicitud_id: solicitudData.solicitud_id,
              alerta_stock: solicitudData.alerta_stock,
              numero_almacen: item.numero_almacen
            });

            if (solicitudData.alerta_stock) {
              productosConAlerta++;
            }
          } else {
            errores++;
            console.error(`Error al crear solicitud para ${item.modelo_existente}`);
          }
        } catch (error) {
          errores++;
          console.error(`Error al crear solicitud individual para ${item.modelo_existente}:`, error);
        }
      }

      // Mostrar mensaje de éxito con información detallada
      let mensajeExito = `¡Cotización enviada exitosamente!\n\n`;
      mensajeExito += `✅ Se crearon ${solicitudesCreadas.length} solicitudes de cotización.\n`;

      if (productosConAlerta > 0) {
        mensajeExito += `⚠️ ${productosConAlerta} producto${productosConAlerta > 1 ? 's tienen' : ' tiene'} stock limitado.\n`;
      }

      if (errores > 0) {
        mensajeExito += `⚠️ ${errores} producto${errores > 1 ? 's no pudieron' : ' no pudo'} ser procesado${errores > 1 ? 's' : ''}.\n`;
      }

      mensajeExito += `\nNos pondremos en contacto contigo pronto.`;

      alert(mensajeExito);

      // Limpiar formulario
      setFormData({
        email: '',
        whatsapp: ''
      });

      onQuoteSubmitted();
      onClose();
    } catch (error) {
      console.error('Error al enviar cotización:', error);
      alert('Error al enviar la cotización. Por favor, inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="modal-overlay" onClick={onClose}></div>
      <div className="modal-container cart-quote-modal">
        <div className="modal-header">
          <h2>Solicitar Cotización</h2>
          <button className="modal-close" onClick={onClose}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="modal-content">
          {/* Resumen del carrito */}
          <div className="quote-cart-summary">
            <h3>Productos a cotizar ({cartItems.length} {cartItems.length === 1 ? 'producto' : 'productos'})</h3>
            <div className="quote-items-list">
              {cartItems.map((item) => {
                const currentQuantity = editableQuantities[item.id] || item.cantidad;
                const hasStockAlert = stockAlerts[item.id] || false;

                return (
                  <div key={item.id} className="quote-item">
                    <div className="quote-item-info">
                      <span className="quote-item-name">{item.modelo_existente}</span>
                      <span className="quote-item-details">
                        {item.marcas && `${item.marcas} • `}ALM: {item.numero_almacen} • Stock: {item.stock_disponible}
                      </span>

                      {/* Control de cantidad editable */}
                      <div className="quote-quantity-control">
                        <label>Cantidad:</label>
                        <div className="quantity-input-group">
                          <button
                            type="button"
                            onClick={() => handleQuantityChange(item.id, currentQuantity - 1)}
                            disabled={currentQuantity <= 1}
                            className="quantity-btn"
                          >
                            -
                          </button>
                          <input
                            type="number"
                            value={currentQuantity}
                            onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                            min="1"
                            className="quantity-input"
                          />
                          <button
                            type="button"
                            onClick={() => handleQuantityChange(item.id, currentQuantity + 1)}
                            className="quantity-btn"
                          >
                            +
                          </button>
                        </div>
                      </div>

                      {/* Alerta de stock */}
                      {hasStockAlert && (
                        <div className="stock-alert-quote">
                          ⚠️ No hay suficiente material disponible. Se le cotizará con mayor tiempo de entrega de lo que dice la tarjeta.
                        </div>
                      )}
                    </div>
                    <div className="quote-item-price">
                      {item.precio_ml && formatPrice(item.precio_ml * currentQuantity)}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="quote-totals">
              <div className="quote-total-row">
                <span>Subtotal:</span>
                <span>{formatPrice(getTotalPrice())}</span>
              </div>
              <div className="quote-total-row">
                <span>IVA (16%):</span>
                <span>{formatPrice(getTotalWithTax() - getTotalPrice())}</span>
              </div>
              <div className="quote-total-row quote-final">
                <span>Total:</span>
                <span>{formatPrice(getTotalWithTax())}</span>
              </div>
            </div>
          </div>

          {/* Formulario de contacto */}
          <form onSubmit={handleSubmit} className="quote-form">
            <h3>Información de contacto</h3>

            <div className="form-group">
              <label htmlFor="email">
                Email <span className="required">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label htmlFor="whatsapp">WhatsApp (opcional)</label>
              <input
                type="tel"
                id="whatsapp"
                name="whatsapp"
                value={formData.whatsapp}
                onChange={handleInputChange}
                placeholder="+52 81 1234 5678"
              />
            </div>

            <div className="form-actions">
              <button type="button" onClick={onClose} className="btn-secondary">
                Cancelar
              </button>
              <button type="submit" disabled={isSubmitting} className="btn-primary">
                {isSubmitting ? 'Enviando...' : 'Enviar Cotización'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
