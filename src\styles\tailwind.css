/* Tailwind CSS para componentes modernos */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos específicos para componentes modernos que usan Tailwind */
.modern-component {
  /* Aplicar reset de Tailwind solo a componentes modernos */
}

/* Utilidades adicionales para componentes modernos */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
}
