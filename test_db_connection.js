/**
 * Script simple para probar la conexión a la base de datos
 */

import mysql from 'serverless-mysql';

// Configuración de la base de datos
const db = mysql({
  config: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'eccsa',
    password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
    database: process.env.DB_NAME || 'EccsaWeb',
    port: parseInt(process.env.DB_PORT || '3306', 10),
  },
});

async function testConnection() {
  try {
    console.log('Probando conexión a la base de datos...');
    console.log('Host:', process.env.DB_HOST || 'localhost');
    console.log('Usuario:', process.env.DB_USER || 'eccsa');
    console.log('Base de datos:', process.env.DB_NAME || 'EccsaWeb');
    console.log('Puerto:', process.env.DB_PORT || '3306');
    
    // Probar conexión simple
    const result = await db.query('SELECT 1 as test');
    console.log('✓ Conexión exitosa!');
    console.log('Resultado de prueba:', result);
    
    // Mostrar tablas existentes
    const tables = await db.query('SHOW TABLES');
    console.log('\nTablas existentes en la base de datos:');
    tables.forEach(table => {
      console.log('-', Object.values(table)[0]);
    });
    
    // Verificar si la tabla almacen ya existe
    const almacenExists = await db.query("SHOW TABLES LIKE 'almacen'");
    if (almacenExists.length > 0) {
      console.log('\n✓ La tabla "almacen" ya existe en la base de datos.');
      
      // Mostrar estructura de la tabla
      const structure = await db.query('DESCRIBE almacen');
      console.log('\nEstructura de la tabla almacen:');
      structure.forEach(field => {
        console.log(`- ${field.Field}: ${field.Type}`);
      });
      
      // Contar registros
      const count = await db.query('SELECT COUNT(*) as total FROM almacen');
      console.log(`\nTotal de productos en almacén: ${count[0].total}`);
    } else {
      console.log('\n⚠ La tabla "almacen" no existe. Ejecuta el script de creación.');
    }
    
  } catch (error) {
    console.error('✗ Error de conexión:', error.message);
    console.error('Detalles del error:', error);
  } finally {
    await db.end();
    console.log('\nConexión cerrada.');
  }
}

// Ejecutar la prueba
testConnection().catch(console.error);
