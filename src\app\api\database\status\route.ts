import { NextResponse } from 'next/server';
import {
  testConnection,
  getConnectionString,
  getDatabaseStats
} from '@/eccsa_back/lib/db';
import { ConnectionInfo } from '@/eccsa_back/types/database';

export async function GET() {
  try {
    // Verificar si estamos usando la base de datos mock
    const useMockDb = process.env.USE_MOCK_DB === 'true' || !process.env.DB_HOST;

    // Información de la conexión
    const connectionInfo: ConnectionInfo = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'eccsa',
      database: process.env.DB_NAME || 'EccsaWeb',
      port: parseInt(process.env.DB_PORT || '3306', 10),
      usingMock: useMockDb
    };

    // Probar la conexión
    const connectionTest = await testConnection();
    let connectionStatus = connectionTest.success ? 'success' : 'error';

    // Determinar si estamos usando datos simulados
    const usingMockData = connectionTest.mock === true || useMockDb;

    // Actualizar la información de conexión con el estado de simulación
    connectionInfo.usingMock = usingMockData;

    // Mensaje de conexión
    let connectionMessage = '';
    if (connectionTest.success) {
      connectionMessage = usingMockData
        ? 'Usando datos simulados (no se pudo conectar a la base de datos real)'
        : 'Conexión exitosa a la base de datos';

      if (connectionTest.originalError) {
        connectionMessage += ` (Error original: ${connectionTest.originalError})`;
      }
    } else {
      connectionMessage = connectionTest.originalError || 'Error de conexión';
    }

    // Obtener estadísticas de la base de datos (tablas, registros, etc.)
    let dbStats = null;

    try {
      // Obtener estadísticas de la base de datos (real o simulada)
      dbStats = await getDatabaseStats();
    } catch (error) {
      console.error('Error al obtener estadísticas de la base de datos:', error);
      dbStats = { tables: [] };
    }

    // Obtener el string de conexión (para referencia)
    const connectionString = getConnectionString();

    return NextResponse.json({
      success: true,
      connection: {
        info: connectionInfo,
        status: connectionStatus,
        message: connectionMessage,
        testResult: connectionTest.result
      },
      stats: dbStats,
      connectionString
    });
  } catch (error) {
    console.error('Error al obtener el estado de la base de datos:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error al obtener el estado de la base de datos',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
