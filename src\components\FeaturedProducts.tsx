'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import ProductImage from '@/components/ProductImage';
import { ScrollAnimation, StaggerAnimation, TextReveal, CounterAnimation } from './ScrollAnimation';

// Interfaz para productos del almacén
interface AlmacenProduct {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  cantidad_nuevo?: number;
  marcas?: string;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

export default function FeaturedProducts() {
  const [products, setProducts] = useState<AlmacenProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Productos de respaldo en caso de error en la API
  const fallbackProducts: AlmacenProduct[] = [
    {
      id: 1,
      numero_almacen: "ALM-001",
      modelo_existente: "PLC Siemens S7-1200",
      marcas: "Siemens",
      precio_ml: 15000,
      cantidad_nuevo: 25,
      tiempo_de_Entrega: "3-5 días hábiles",
      Url_imagen: "/images/defaults/plc-siemens.jpg",
      Datos_importantes_Descripcion_muestra: "Controlador lógico programable compacto para automatización industrial",
      descripcion: "PLC compacto ideal para aplicaciones de automatización pequeñas y medianas"
    },
    {
      id: 2,
      numero_almacen: "ALM-002",
      modelo_existente: "HMI Schneider Electric",
      marcas: "Schneider Electric",
      precio_ml: 8500,
      cantidad_nuevo: 18,
      tiempo_de_Entrega: "2-4 días hábiles",
      Url_imagen: "/images/defaults/hmi-schneider.jpg",
      Datos_importantes_Descripcion_muestra: "Interfaz hombre-máquina con pantalla táctil de 7 pulgadas",
      descripcion: "HMI táctil para control y monitoreo de procesos industriales"
    },
    {
      id: 3,
      numero_almacen: "ALM-003",
      modelo_existente: "Variador de Frecuencia ABB",
      marcas: "ABB",
      precio_ml: 12000,
      cantidad_nuevo: 12,
      tiempo_de_Entrega: "5-7 días hábiles",
      Url_imagen: "/images/defaults/variador-abb.jpg",
      Datos_importantes_Descripcion_muestra: "Variador de frecuencia para control de motores trifásicos",
      descripcion: "Control preciso de velocidad y torque para motores industriales"
    },
    {
      id: 4,
      numero_almacen: "ALM-004",
      modelo_existente: "Sensor Inductivo Omron",
      marcas: "Omron",
      precio_ml: 850,
      cantidad_nuevo: 45,
      tiempo_de_Entrega: "1-2 días hábiles",
      Url_imagen: "/images/defaults/sensor-omron.jpg",
      Datos_importantes_Descripcion_muestra: "Sensor de proximidad inductivo M18 con salida PNP",
      descripcion: "Sensor robusto para detección de objetos metálicos en ambientes industriales"
    }
  ];

  // Cargar productos más vendidos desde la API
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/almacen?bestSelling=true&limit=4');
        const data = await response.json();

        if (response.ok && data.products && data.products.length > 0) {
          setProducts(data.products);
        } else {
          // Si no hay productos en la API, usar productos de respaldo
          setProducts(fallbackProducts);
        }
      } catch (err) {
        console.error('Error fetching featured products:', err);
        setError('Error al cargar productos destacados');
        // Usar productos de respaldo en caso de error
        setProducts(fallbackProducts);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  // Formatear precio con comas para miles
  const formatPrice = (price?: number) => {
    if (!price || price <= 0) return 'Consultar precio';
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
    return formattedPrice;
  };

  if (loading) {
    return (
      <section className="featured-products-modern-section">
        <div className="featured-products-modern-container">
          <div className="featured-products-modern-header">
            <h2 className="featured-products-modern-title">
              Productos <span className="featured-products-highlight">Destacados</span>
            </h2>
            <p className="featured-products-modern-subtitle">
              Cargando productos más vendidos...
            </p>
          </div>
          <div className="featured-products-premium-showcase">
            <div className="featured-products-premium-grid">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="featured-product-premium-card loading">
                  <div className="premium-card-bg">
                    <div className="premium-card-gradient"></div>
                  </div>
                  <div className="premium-product-image-container">
                    <div className="premium-image-wrapper">
                      <div className="premium-image-skeleton"></div>
                      <div className="premium-floating-elements">
                        <div className="premium-rank-badge skeleton"></div>
                        <div className="premium-stock-indicator skeleton"></div>
                      </div>
                    </div>
                  </div>
                  <div className="premium-product-content">
                    <div className="premium-product-meta">
                      <div className="skeleton-line short"></div>
                      <div className="skeleton-line short"></div>
                    </div>
                    <div className="skeleton-line medium"></div>
                    <div className="skeleton-line long"></div>
                    <div className="premium-product-features">
                      <div className="skeleton-line short"></div>
                      <div className="skeleton-line short"></div>
                      <div className="skeleton-line short"></div>
                    </div>
                    <div className="premium-product-footer">
                      <div className="skeleton-line medium"></div>
                      <div className="skeleton-line short"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="featured-products-modern-section">
      <div className="featured-products-modern-container">
        {/* Header con animaciones */}
        <ScrollAnimation animation="slide-down" delay={100}>
          <div className="featured-products-modern-header">
            <TextReveal>
              <h2 className="featured-products-modern-title">
                Productos <span className="featured-products-highlight">Destacados</span>
              </h2>
            </TextReveal>
            <ScrollAnimation animation="fade-in" delay={300}>
              <p className="featured-products-modern-subtitle">
                {error ? 'Productos de ejemplo más populares' : 'Descubre nuestros productos más vendidos y populares'}
              </p>
            </ScrollAnimation>
          </div>
        </ScrollAnimation>

        {/* Premium Products Showcase */}
        <div className="featured-products-premium-showcase">
          <StaggerAnimation
            staggerDelay={150}
            className="featured-products-premium-grid"
          >
            {products.slice(0, 4).map((product, index) => (
              <div key={product.id} className="featured-product-premium-card">
                {/* Card Background Effects */}
                <div className="premium-card-bg">
                  <div className="premium-card-gradient"></div>
                  <div className="premium-card-pattern"></div>
                </div>

                {/* Product Image Section */}
                <ScrollAnimation animation="scale-up" delay={100 + index * 100}>
                  <div className="premium-product-image-container">
                    <div className="premium-image-wrapper">
                      {product.Url_imagen && (product.Url_imagen.startsWith('http') || product.Url_imagen.startsWith('https')) ? (
                        <img
                          src={product.Url_imagen}
                          alt={product.modelo_existente}
                          className="premium-product-image"
                          loading="lazy"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                          }}
                        />
                      ) : (
                        <ProductImage
                          src={product.Url_imagen || "/images/logos/logo_pequeno.png"}
                          alt={product.modelo_existente}
                          width={280}
                          height={280}
                          className="premium-product-image"
                          priority={index < 2}
                        />
                      )}

                      {/* Floating Elements */}
                      <div className="premium-floating-elements">
                        <div className="premium-rank-badge">
                          <span className="rank-number">{String(index + 1).padStart(2, '0')}</span>
                          <span className="rank-label">MÁS VENDIDO</span>
                        </div>

                        <div className="premium-stock-indicator">
                          <div className="stock-pulse"></div>
                          <span className="stock-count">
                            <CounterAnimation
                              endValue={product.cantidad_nuevo || 0}
                              duration={1800}
                              suffix=" unidades"
                            />
                          </span>
                        </div>
                      </div>

                      {/* Hover Overlay */}
                      <div className="premium-image-overlay">
                        <div className="premium-overlay-content">
                          <div className="premium-quick-actions">
                            <button className="premium-quick-btn premium-view-btn">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                              </svg>
                            </button>
                            <button className="premium-quick-btn premium-cart-btn">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="9" cy="21" r="1"/>
                                <circle cx="20" cy="21" r="1"/>
                                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ScrollAnimation>

                {/* Product Content Section */}
                <div className="premium-product-content">
                  {/* Brand & Category */}
                  <ScrollAnimation animation="slide-up" delay={200 + index * 100}>
                    <div className="premium-product-meta">
                      <span className="premium-brand-tag">
                        {product.marcas || 'ECCSA'}
                      </span>
                      <span className="premium-category-tag">
                        Automatización
                      </span>
                    </div>
                  </ScrollAnimation>

                  {/* Product Title */}
                  <ScrollAnimation animation="slide-up" delay={300 + index * 100}>
                    <h3 className="premium-product-title">
                      {product.modelo_existente}
                    </h3>
                  </ScrollAnimation>

                  {/* Product Description */}
                  <ScrollAnimation animation="fade-in" delay={400 + index * 100}>
                    <p className="premium-product-description">
                      {product.Datos_importantes_Descripcion_muestra ||
                       product.descripcion ||
                       'Solución avanzada de automatización industrial con tecnología de vanguardia para optimizar procesos productivos.'}
                    </p>
                  </ScrollAnimation>



                  {/* Price & Actions */}
                  <ScrollAnimation animation="slide-up" delay={600 + index * 100}>
                    <div className="premium-product-footer">
                      <div className="premium-price-section">
                        <span className="premium-price-label">Precio desde</span>
                        <span className="premium-price-value">
                          {formatPrice(product.precio_ml)}
                        </span>
                      </div>

                      <div className="premium-action-buttons">
                        <Link href="/products" className="premium-primary-action">
                          <span>Ver Detalles</span>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="m9 18 6-6-6-6"/>
                          </svg>
                        </Link>
                        <button className="premium-secondary-action">
                          Cotizar
                        </button>
                      </div>
                    </div>
                  </ScrollAnimation>
                </div>
              </div>
            ))}
          </StaggerAnimation>
        </div>


      </div>
    </section>
  );
}
