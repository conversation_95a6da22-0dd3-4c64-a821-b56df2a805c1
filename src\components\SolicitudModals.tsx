'use client';

import { useState } from 'react';

interface Solicitud {
  id: number;
  numero_almacen: string;
  modelo: string;
  marca: string;
  descripcion: string;
  estante: string;
  precio_unitario: number;
  cantidad_solicitada: number;
  cantidad_disponible: number;
  cliente_email: string;
  cliente_whatsapp?: string;
  estado: 'pendiente' | 'procesando' | 'cotizado' | 'rechazado';
  alerta_stock: boolean;
  tiempo_entrega_estimado?: string;
  notas?: string;
  fecha_solicitud: string;
  fecha_actualizacion: string;
}

interface SolicitudModalsProps {
  solicitud: Solicitud | null;
  modalType: 'view' | 'edit' | 'delete' | null;
  onClose: () => void;
  onUpdate: () => void;
}

export default function SolicitudModals({
  solicitud,
  modalType,
  onClose,
  onUpdate
}: SolicitudModalsProps) {
  const [editData, setEditData] = useState<Partial<Solicitud>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  if (!modalType || !solicitud) return null;

  // Limpiar estados cuando se cierra el modal
  const handleClose = () => {
    setError('');
    setSuccess('');
    setEditData({});
    onClose();
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Formatear precio
  const formatPrice = (price: number) => {
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
    return formattedPrice;
  };

  // Inicializar datos de edición
  const initEditData = () => {
    setEditData({
      estado: solicitud.estado,
      notas: solicitud.notas || '',
      tiempo_entrega_estimado: solicitud.tiempo_entrega_estimado || ''
    });
  };

  // Manejar cambios en el formulario de edición
  const handleEditChange = (field: string, value: string) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Guardar cambios
  const handleSave = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`/api/solicitudes/${solicitud.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData)
      });

      const data = await response.json();

      if (response.ok && data && data.success === true) {
        // Mostrar mensaje de éxito
        setError(''); // Limpiar errores
        setSuccess('✅ Cambios guardados exitosamente');

        // Llamar a onUpdate para refrescar la lista
        onUpdate();

        // Cerrar el modal después de mostrar el éxito
        setTimeout(() => {
          handleClose();
        }, 1500);
      } else {
        // Manejar errores de la API
        const errorMessage = data?.error || `Error ${response.status}: ${response.statusText}`;
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Error al guardar:', err);
      setError('Error de conexión. Verifica tu internet e intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  // Eliminar solicitud
  const handleDelete = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/solicitudes/${solicitud.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (response.ok && data && data.success === true) {
        // Limpiar errores
        setError('');

        // Actualizar lista y cerrar modal
        onUpdate();
        handleClose();
      } else {
        // Manejar errores de la API
        const errorMessage = data?.error || `Error ${response.status}: ${response.statusText}`;
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Error al eliminar:', err);
      setError('Error de conexión. Verifica tu internet e intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  // Modal de Ver Detalles
  if (modalType === 'view') {
    return (
      <div className="modal-overlay" onClick={handleClose}>
        <div className="modal-content solicitud-detail-modal" onClick={(e) => e.stopPropagation()}>
          <div className="modal-header">
            <h2 className="modal-title">Detalles de Solicitud #{solicitud.id}</h2>
            <button className="modal-close" onClick={handleClose}>×</button>
          </div>

          <div className="modal-body">
            <div className="detail-grid">
              <div className="detail-section">
                <h3>Información del Producto</h3>
                <div className="detail-item">
                  <label>Número de Almacén:</label>
                  <span>{solicitud.numero_almacen}</span>
                </div>
                <div className="detail-item">
                  <label>Modelo:</label>
                  <span>{solicitud.modelo}</span>
                </div>
                <div className="detail-item">
                  <label>Marca:</label>
                  <span>{solicitud.marca}</span>
                </div>
                <div className="detail-item">
                  <label>Descripción:</label>
                  <span>{solicitud.descripcion}</span>
                </div>
                <div className="detail-item">
                  <label>Estante:</label>
                  <span>{solicitud.estante}</span>
                </div>
                <div className="detail-item">
                  <label>Precio Unitario:</label>
                  <span>{formatPrice(solicitud.precio_unitario)}</span>
                </div>
              </div>

              <div className="detail-section">
                <h3>Información de la Solicitud</h3>
                <div className="detail-item">
                  <label>Cantidad Solicitada:</label>
                  <span>{solicitud.cantidad_solicitada}</span>
                </div>
                <div className="detail-item">
                  <label>Stock Disponible:</label>
                  <span className={solicitud.cantidad_disponible > 0 ? 'stock-available' : 'stock-unavailable'}>
                    {solicitud.cantidad_disponible}
                  </span>
                </div>
                <div className="detail-item">
                  <label>Estado:</label>
                  <span className={`estado-badge estado-${solicitud.estado}`}>
                    {solicitud.estado.charAt(0).toUpperCase() + solicitud.estado.slice(1)}
                  </span>
                </div>
                {solicitud.alerta_stock && (
                  <div className="detail-item">
                    <label>Alerta:</label>
                    <span className="stock-alert-text">⚠️ Stock insuficiente</span>
                  </div>
                )}
                <div className="detail-item">
                  <label>Tiempo de Entrega:</label>
                  <span>{solicitud.tiempo_entrega_estimado || 'No especificado'}</span>
                </div>
              </div>

              <div className="detail-section">
                <h3>Información del Cliente</h3>
                <div className="detail-item">
                  <label>Email:</label>
                  <span>{solicitud.cliente_email}</span>
                </div>
                {solicitud.cliente_whatsapp && (
                  <div className="detail-item">
                    <label>WhatsApp:</label>
                    <span>{solicitud.cliente_whatsapp}</span>
                  </div>
                )}
              </div>

              <div className="detail-section">
                <h3>Fechas</h3>
                <div className="detail-item">
                  <label>Fecha de Solicitud:</label>
                  <span>{formatDate(solicitud.fecha_solicitud)}</span>
                </div>
                <div className="detail-item">
                  <label>Última Actualización:</label>
                  <span>{formatDate(solicitud.fecha_actualizacion)}</span>
                </div>
              </div>

              {solicitud.notas && (
                <div className="detail-section full-width">
                  <h3>Notas</h3>
                  <div className="notes-content">
                    {solicitud.notas}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="modal-footer">
            <button onClick={handleClose} className="btn-secondary">Cerrar</button>
          </div>
        </div>
      </div>
    );
  }

  // Modal de Editar
  if (modalType === 'edit') {
    if (Object.keys(editData).length === 0) {
      initEditData();
    }

    return (
      <div className="modal-overlay" onClick={handleClose}>
        <div className="modal-content solicitud-edit-modal" onClick={(e) => e.stopPropagation()}>
          <div className="modal-header">
            <h2 className="modal-title">Editar Solicitud #{solicitud.id}</h2>
            <button className="modal-close" onClick={handleClose}>×</button>
          </div>

          <div className="modal-body">
            {/* Información del producto (solo lectura) */}
            <div className="product-info-summary">
              <h3>📦 {solicitud.modelo}</h3>
              <p><strong>Marca:</strong> {solicitud.marca} | <strong>Almacén:</strong> {solicitud.numero_almacen}</p>
              <p><strong>Cliente:</strong> {solicitud.cliente_email}</p>
            </div>

            <div className="edit-form">
              <div className="form-group">
                <label htmlFor="estado-select">Estado de la Solicitud:</label>
                <select
                  id="estado-select"
                  value={editData.estado || solicitud.estado}
                  onChange={(e) => handleEditChange('estado', e.target.value)}
                  className="form-select"
                >
                  <option value="pendiente">🟡 Pendiente</option>
                  <option value="procesando">🔵 Procesando</option>
                  <option value="cotizado">🟢 Cotizado</option>
                  <option value="rechazado">🔴 Rechazado</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="tiempo-entrega">Tiempo de Entrega Estimado:</label>
                <input
                  id="tiempo-entrega"
                  type="text"
                  value={editData.tiempo_entrega_estimado || ''}
                  onChange={(e) => handleEditChange('tiempo_entrega_estimado', e.target.value)}
                  placeholder="Ej: 3-5 días hábiles"
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label htmlFor="notas-textarea">Notas:</label>
                <textarea
                  id="notas-textarea"
                  value={editData.notas || ''}
                  onChange={(e) => handleEditChange('notas', e.target.value)}
                  placeholder="Agregar notas sobre la solicitud..."
                  rows={4}
                  className="form-textarea"
                />
              </div>

              {error && <div className="error-message">❌ {error}</div>}
              {success && <div className="success-message">{success}</div>}
            </div>
          </div>

          <div className="modal-footer">
            <button onClick={handleClose} className="btn-secondary" disabled={loading}>
              Cancelar
            </button>
            <button onClick={handleSave} className="btn-primary" disabled={loading}>
              {loading ? '⏳ Guardando...' : '💾 Guardar Cambios'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Modal de Eliminar
  if (modalType === 'delete') {
    return (
      <div className="modal-overlay" onClick={handleClose}>
        <div className="modal-content solicitud-delete-modal" onClick={(e) => e.stopPropagation()}>
          <div className="modal-header">
            <h2 className="modal-title">Eliminar Solicitud</h2>
            <button className="modal-close" onClick={handleClose}>×</button>
          </div>

          <div className="modal-body">
            <div className="delete-confirmation">
              <div className="warning-icon">⚠️</div>
              <h3>¿Estás seguro de que deseas eliminar esta solicitud?</h3>
              <p>
                <strong>Solicitud #{solicitud.id}</strong><br />
                Producto: {solicitud.modelo}<br />
                Cliente: {solicitud.cliente_email}
              </p>
              <p className="warning-text">
                Esta acción no se puede deshacer.
              </p>
              {error && <div className="error-message">{error}</div>}
            </div>
          </div>

          <div className="modal-footer">
            <button onClick={handleClose} className="btn-secondary" disabled={loading}>
              Cancelar
            </button>
            <button onClick={handleDelete} className="btn-danger" disabled={loading}>
              {loading ? 'Eliminando...' : 'Eliminar Solicitud'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
