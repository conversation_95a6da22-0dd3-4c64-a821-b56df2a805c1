/**
 * Migración para crear la tabla de usuarios en la base de datos EccsaWeb
 */

import db from '@/eccsa_back/lib/db';
import crypto from 'crypto';

/**
 * Ejecuta una consulta SQL
 * @param {string} query - Consulta SQL a ejecutar
 * @param {Array} values - Valores para la consulta parametrizada
 * @returns {Promise<any>} - Resultado de la consulta
 */
async function executeQuery(query, values = []) {
  try {
    const results = await db.query(query, values);
    return results;
  } catch (error) {
    console.error('Error al ejecutar la consulta:', error);
    throw error;
  }
}

/**
 * Genera un hash seguro de contraseña
 * @param {string} password - Contraseña en texto plano
 * @returns {string} - Hash de la contraseña con formato salt:hash
 */
function hashPassword(password) {
  // Generar un salt aleatorio
  const salt = crypto.randomBytes(16).toString('hex');

  // Crear un hash con el salt
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');

  // Devolver el salt y el hash juntos
  return `${salt}:${hash}`;
}

/**
 * Crea la tabla de usuarios
 * @returns {Promise<void>}
 */
export async function createUsersTable() {
  try {
    console.log('Iniciando la creación de la tabla de usuarios...');

    // Eliminar la tabla si existe
    console.log('Eliminando la tabla de usuarios si existe...');
    await executeQuery("DROP TABLE IF EXISTS usuarios");
    console.log('Tabla eliminada o no existía.');

    // Crear la tabla de usuarios
    await executeQuery(`
      CREATE TABLE usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nombre VARCHAR(100) NOT NULL COMMENT 'Nombre completo del usuario',
        nombre_usuario VARCHAR(50) NOT NULL UNIQUE COMMENT 'Nombre de usuario para iniciar sesión',
        foto_usuario VARCHAR(255) DEFAULT NULL COMMENT 'URL o ruta a la foto del usuario',
        correo VARCHAR(100) NOT NULL UNIQUE COMMENT 'Correo electrónico',
        contrasena VARCHAR(255) NOT NULL COMMENT 'Contraseña (hash)',
        telefono VARCHAR(20) DEFAULT NULL COMMENT 'Número de teléfono',
        nivel_ingeniero INT DEFAULT 4 COMMENT 'Nivel de ingeniero (0=Administrador, 1=Jefe, 2=Senior, 3=Semi-Senior, 4=Junior)',
        descripcion TEXT DEFAULT NULL COMMENT 'Descripción o biografía del usuario',
        fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación del registro',
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
        activo BOOLEAN DEFAULT TRUE COMMENT 'Indica si el usuario está activo'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla de usuarios del sistema ECCSA'
    `);

    // Crear índices
    await executeQuery("CREATE INDEX idx_usuarios_nombre_usuario ON usuarios(nombre_usuario)");
    await executeQuery("CREATE INDEX idx_usuarios_correo ON usuarios(correo)");
    await executeQuery("CREATE INDEX idx_usuarios_nivel_ingeniero ON usuarios(nivel_ingeniero)");

    // Contraseñas originales (para mostrar al usuario)
    const adminPassword = 'Eccsa@Admin2023';
    const oscarPassword = 'Oscar@Eccsa2023';
    const estebanPassword = 'Esteban@Eccsa2023';

    // Contraseñas hasheadas para almacenar en la base de datos
    const hashedAdminPassword = hashPassword(adminPassword);

    // Insertar usuario administrador por defecto
    await executeQuery(`
      INSERT INTO usuarios (nombre, nombre_usuario, correo, contrasena, nivel_ingeniero, descripcion)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      'Administrador ECCSA',
      'admin',
      '<EMAIL>',
      hashedAdminPassword, // Contraseña hasheada
      0, // Nivel 0 = Administrador
      'Usuario administrador del sistema con acceso completo'
    ]);

    console.log('\n=== CREDENCIALES DE ACCESO ===');
    console.log('Usuario: admin | Contraseña:', adminPassword);

    // Contraseñas hasheadas para almacenar en la base de datos
    const hashedOscarPassword = hashPassword(oscarPassword);
    const hashedEstebanPassword = hashPassword(estebanPassword);

    // Insertar usuarios de ejemplo
    await executeQuery(`
      INSERT INTO usuarios (nombre, nombre_usuario, foto_usuario, correo, contrasena, telefono, nivel_ingeniero, descripcion)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?), (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Oscar Castillo',
      'oscar.castillo',
      '/images/usuarios/oscar.jpg',
      '<EMAIL>',
      hashedOscarPassword, // Contraseña hasheada
      '8182803296',
      0, // Nivel 0 = Administrador (actualizado)
      'Jefe de automatización industrial con acceso administrativo',

      'Esteban Carrera',
      'esteban.carrera',
      '/images/usuarios/esteban.jpg',
      '<EMAIL>',
      hashedEstebanPassword, // Contraseña hasheada
      '8187043546',
      0, // Nivel 0 = Administrador (actualizado)
      'Especialista en ventas y soporte técnico con acceso administrativo'
    ]);

    console.log('Usuario: oscar.castillo | Contraseña:', oscarPassword);
    console.log('Usuario: esteban.carrera | Contraseña:', estebanPassword);
    console.log('===============================');

    console.log('La tabla de usuarios ha sido creada exitosamente.');
  } catch (error) {
    console.error('Error al crear la tabla de usuarios:', error);
    throw error;
  } finally {
    await db.end();
  }
}

// Exportar la función para poder usarla en otros archivos
export default createUsersTable;
