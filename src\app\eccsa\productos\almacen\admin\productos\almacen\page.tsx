'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

interface AlmacenProduct {
  id: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  minimo?: number;
  maximo?: number;
  pedir_cantidad?: number;
  precio_us?: number;
  precio_mx?: number;
  impuesto?: number;
  codigo_sat?: string;
  nota?: string;
  proveedor?: string;
  marcas?: string;
  tiempo_entrega_proveedor?: string;
  fecha_pedido?: string;
  fecha_recibido?: string;
  fecha_creacion?: string;
  fecha_actualizacion?: string;
  activo?: boolean;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

interface AlmacenStats {
  total_productos: number;
  total_cantidad: number;
  total_vendidos: number;
  productos_stock_bajo: number;
  productos_sin_stock: number;
  valor_total_inventario: number;
}

export default function AlmacenPage() {
  const [loading, setLoading] = useState(true);
  const [productos, setProductos] = useState<AlmacenProduct[]>([]);
  const [stats, setStats] = useState<AlmacenStats | null>(null);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('todos');
  const [filteredProductos, setFilteredProductos] = useState<AlmacenProduct[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<AlmacenProduct | null>(null);
  const [viewingNotes, setViewingNotes] = useState<AlmacenProduct | null>(null);
  const [deletingProduct, setDeletingProduct] = useState<AlmacenProduct | null>(null);
  const [formData, setFormData] = useState({
    numero_almacen: '',
    estante: '',
    modelo_existente: '',
    descripcion: '',
    precio_us: '',
    precio_mx: '',
    precio_venta: '',
    precio_ml: '',
    vendidos: 0,
    cantidad_nuevo: 0,
    minimo: 0,
    maximo: 0,
    pedir_cantidad: 0,
    impuesto: 16,
    codigo_sat: '',
    nota: '',
    proveedor: '',
    marcas: '',
    tiempo_entrega_proveedor: '',
    fecha_pedido: '',
    fecha_recibido: '',
    Url_imagen: '',
    Datos_importantes_Descripcion_muestra: '',
    tiempo_de_Entrega: ''
  });
  const [usdToMxnRate] = useState(18.5); // Tipo de cambio USD a MXN (puedes hacer esto dinámico)

  // Cargar productos del almacén
  const loadProductos = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/almacen');
      const data = await response.json();

      if (response.ok) {
        setProductos(data.products || []);
        setFilteredProductos(data.products || []);
      } else {
        setError(data.error || 'Error al cargar productos');
      }
    } catch (err) {
      setError('Error de conexión al cargar productos');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Cargar estadísticas del almacén
  const loadStats = async () => {
    try {
      const response = await fetch('/api/almacen?stats=true');
      const data = await response.json();

      if (response.ok) {
        setStats(data.stats);
      }
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  useEffect(() => {
    loadProductos();
    loadStats();
  }, []);

  // Filtrar productos por búsqueda y estado
  useEffect(() => {
    let filtered = productos;

    // Filtrar por término de búsqueda
    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(producto =>
        producto.numero_almacen.toLowerCase().includes(searchTerm.toLowerCase()) ||
        producto.modelo_existente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (producto.descripcion && producto.descripcion.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (producto.proveedor && producto.proveedor.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (producto.marcas && producto.marcas.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (producto.codigo_sat && producto.codigo_sat.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtrar por estado
    if (statusFilter !== 'todos') {
      filtered = filtered.filter(producto => {
        const status = getStockStatus(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad);
        switch (statusFilter) {
          case 'en_stock':
            return status === 'En stock';
          case 'por_agotarse':
            return status === 'Por agotarse';
          case 'sin_stock':
            return status === 'Sin stock';
          case 'ya_pedido':
            return status === 'Ya se pidió';
          default:
            return true;
        }
      });
    }

    setFilteredProductos(filtered);
  }, [searchTerm, statusFilter, productos]);

  // Formatear precio
  const formatPrice = (price?: number) => {
    if (!price) return 'N/A';
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
    return formattedPrice;
  };

  // Formatear fecha
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('es-MX');
  };

  // Determinar color del stock y estado
  const getStockColor = (cantidad?: number, minimo?: number, maximo?: number, pedir_cantidad?: number) => {
    // Azul - ya se pidió (pedir_cantidad > 0)
    if (pedir_cantidad && pedir_cantidad > 0) return '#007bff';
    // Rojo - sin stock
    if (!cantidad || cantidad === 0) return '#dc3545';
    // Amarillo - por agotarse (cantidad igual al mínimo O menor al mínimo)
    if (minimo && cantidad <= minimo) return '#ffc107';
    // Verde - stock normal
    return '#28a745';
  };

  // Obtener texto del estado
  const getStockStatus = (cantidad?: number, minimo?: number, maximo?: number, pedir_cantidad?: number) => {
    if (pedir_cantidad && pedir_cantidad > 0) return 'Ya se pidió';
    if (!cantidad || cantidad === 0) return 'Sin stock';
    if (minimo && cantidad <= minimo) return 'Por agotarse';
    return 'En stock';
  };

  // Calcular precio de venta basado en la fórmula
  const calculatePrecioVenta = (precioMx: number, impuesto: number = 16) => {
    // Fórmula: (precio en pesos mexicanos * 1.2 * 1.25 * 1.16 + 100 + impuestos)
    const basePrice = precioMx * 1.2 * 1.25 * 1.16;
    const finalPrice = basePrice + 100 + impuesto;
    return Math.round(finalPrice * 100) / 100; // Redondear a 2 decimales
  };

  // Manejar cambio en precio USD
  const handleUsdPriceChange = (usdPrice: string) => {
    const usdValue = parseFloat(usdPrice) || 0;
    const mxnValue = usdValue * usdToMxnRate;

    setFormData(prev => ({
      ...prev,
      precio_us: usdPrice,
      precio_mx: mxnValue.toFixed(2),
      precio_venta: calculatePrecioVenta(mxnValue, prev.impuesto).toFixed(2)
    }));
  };

  // Manejar cambio en precio MXN
  const handleMxnPriceChange = (mxnPrice: string) => {
    const mxnValue = parseFloat(mxnPrice) || 0;

    setFormData(prev => ({
      ...prev,
      precio_mx: mxnPrice,
      precio_venta: calculatePrecioVenta(mxnValue, prev.impuesto).toFixed(2)
    }));
  };

  // Manejar cambio en impuesto
  const handleImpuestoChange = (impuesto: string) => {
    const impuestoValue = parseFloat(impuesto) || 0;
    const mxnValue = parseFloat(formData.precio_mx) || 0;

    setFormData(prev => ({
      ...prev,
      impuesto: impuestoValue,
      precio_venta: calculatePrecioVenta(mxnValue, impuestoValue).toFixed(2)
    }));
  };

  // Manejar cambios en el formulario
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'precio_us') {
      handleUsdPriceChange(value);
    } else if (name === 'precio_mx') {
      handleMxnPriceChange(value);
    } else if (name === 'impuesto') {
      handleImpuestoChange(value);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: name.includes('cantidad') || name.includes('vendidos') || name.includes('minimo') || name.includes('maximo') || name.includes('pedir')
          ? parseInt(value) || 0
          : value
      }));
    }
  };

  // Abrir modal para agregar producto
  const openAddModal = () => {
    setFormData({
      numero_almacen: '',
      estante: '',
      modelo_existente: '',
      descripcion: '',
      precio_us: '',
      precio_mx: '',
      precio_venta: '',
      precio_ml: '',
      vendidos: 0,
      cantidad_nuevo: 0,
      minimo: 0,
      maximo: 0,
      pedir_cantidad: 0,
      impuesto: 16,
      codigo_sat: '',
      nota: '',
      proveedor: '',
      marcas: '',
      tiempo_entrega_proveedor: '',
      fecha_pedido: '',
      fecha_recibido: '',
      Url_imagen: '',
      Datos_importantes_Descripcion_muestra: '',
      tiempo_de_Entrega: ''
    });
    setShowAddModal(true);
  };

  // Abrir modal para editar producto
  const openEditModal = (producto: AlmacenProduct) => {
    setEditingProduct(producto);
    setFormData({
      numero_almacen: producto.numero_almacen,
      estante: producto.estante || '',
      modelo_existente: producto.modelo_existente,
      descripcion: producto.descripcion || '',
      precio_us: producto.precio_us?.toString() || '',
      precio_mx: producto.precio_mx?.toString() || '',
      precio_venta: producto.precio_venta?.toString() || '',
      precio_ml: producto.precio_ml?.toString() || '',
      vendidos: producto.vendidos || 0,
      cantidad_nuevo: producto.cantidad_nuevo || 0,
      minimo: producto.minimo || 0,
      maximo: producto.maximo || 0,
      pedir_cantidad: producto.pedir_cantidad || 0,
      impuesto: producto.impuesto || 16,
      codigo_sat: producto.codigo_sat || '',
      nota: producto.nota || '',
      proveedor: producto.proveedor || '',
      marcas: producto.marcas || '',
      tiempo_entrega_proveedor: producto.tiempo_entrega_proveedor || '',
      fecha_pedido: producto.fecha_pedido ? producto.fecha_pedido.toString().split('T')[0] : '',
      fecha_recibido: producto.fecha_recibido ? producto.fecha_recibido.toString().split('T')[0] : '',
      Url_imagen: producto.Url_imagen || '',
      Datos_importantes_Descripcion_muestra: producto.Datos_importantes_Descripcion_muestra || '',
      tiempo_de_Entrega: producto.tiempo_de_Entrega || ''
    });
    setShowEditModal(true);
  };

  // Abrir modal para ver notas
  const openNotesModal = (producto: AlmacenProduct) => {
    setViewingNotes(producto);
    setShowNotesModal(true);
  };

  // Abrir modal para eliminar producto
  const openDeleteModal = (producto: AlmacenProduct) => {
    setDeletingProduct(producto);
    setShowDeleteModal(true);
  };

  // Cerrar modales
  const closeModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowNotesModal(false);
    setShowDeleteModal(false);
    setEditingProduct(null);
    setViewingNotes(null);
    setDeletingProduct(null);
  };

  // Validar si el número de almacén ya existe
  const validateNumeroAlmacen = (numero: string, excludeId?: number) => {
    return productos.some(producto =>
      producto.numero_almacen.toLowerCase() === numero.toLowerCase() &&
      producto.id !== excludeId
    );
  };

  // Crear nuevo producto
  const handleCreateProduct = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.numero_almacen || !formData.modelo_existente) {
      setError('Número de almacén y modelo son requeridos');
      return;
    }

    // Validar número de almacén duplicado
    if (validateNumeroAlmacen(formData.numero_almacen)) {
      setError(`El número de almacén "${formData.numero_almacen}" ya existe. Por favor, use un número diferente.`);
      return;
    }

    try {
      setLoading(true);
      setError('');

      const productData = {
        ...formData,
        precio_us: parseFloat(formData.precio_us) || null,
        precio_mx: parseFloat(formData.precio_mx) || null,
        precio_venta: parseFloat(formData.precio_venta) || null,
        precio_ml: parseFloat(formData.precio_ml) || null,
        fecha_pedido: formData.fecha_pedido || null,
        fecha_recibido: formData.fecha_recibido || null
      };



      const response = await fetch('/api/almacen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        await loadProductos();
        await loadStats();
        closeModals();
      } else {
        const data = await response.json();
        setError(data.error || 'Error al crear producto');
      }
    } catch (err) {
      setError('Error de conexión al crear producto');
      console.error('Error creating product:', err);
    } finally {
      setLoading(false);
    }
  };

  // Actualizar producto
  const handleUpdateProduct = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingProduct || !formData.numero_almacen || !formData.modelo_existente) {
      setError('Número de almacén y modelo son requeridos');
      return;
    }

    // Validar número de almacén duplicado (excluyendo el producto actual)
    if (validateNumeroAlmacen(formData.numero_almacen, editingProduct.id)) {
      setError(`El número de almacén "${formData.numero_almacen}" ya existe. Por favor, use un número diferente.`);
      return;
    }

    try {
      setLoading(true);
      setError('');

      const productData = {
        ...formData,
        precio_us: parseFloat(formData.precio_us) || null,
        precio_mx: parseFloat(formData.precio_mx) || null,
        precio_venta: parseFloat(formData.precio_venta) || null,
        precio_ml: parseFloat(formData.precio_ml) || null,
        fecha_pedido: formData.fecha_pedido || null,
        fecha_recibido: formData.fecha_recibido || null
      };



      const response = await fetch(`/api/almacen/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        await loadProductos();
        await loadStats();
        closeModals();
      } else {
        const data = await response.json();
        setError(data.error || 'Error al actualizar producto');
      }
    } catch (err) {
      setError('Error de conexión al actualizar producto');
      console.error('Error updating product:', err);
    } finally {
      setLoading(false);
    }
  };

  // Eliminar producto
  const handleDeleteProduct = async () => {
    if (!deletingProduct?.id) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/almacen/${deletingProduct.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadProductos();
        await loadStats();
        closeModals();
        // Mostrar mensaje de éxito (opcional)
        setError(''); // Limpiar cualquier error previo
      } else {
        const data = await response.json();
        setError(data.error || 'Error al eliminar producto');
      }
    } catch (err) {
      setError('Error de conexión al eliminar producto');
      console.error('Error deleting product:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthGuard>
      <div className="modern-warehouse-page">
        {/* Header moderno */}
        <div className="warehouse-header">
          <div className="header-content">
            <div className="header-title-section">
              <div className="title-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <div className="title-text">
                <h1 className="warehouse-title">Gestión de Almacén</h1>
                <p className="warehouse-subtitle">Control completo de inventario y productos</p>
              </div>
            </div>
            <div className="header-actions">
              <button
                className="refresh-button"
                onClick={() => {
                  loadProductos();
                  loadStats();
                }}
                disabled={loading}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 20 1 14 7 14"></polyline>
                  <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                {loading ? 'Actualizando...' : 'Actualizar'}
              </button>
              <button
                className="add-product-button"
                onClick={openAddModal}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Agregar Producto
              </button>
            </div>
          </div>
        </div>

        {/* Estadísticas modernas */}
        {stats && (
          <div className="warehouse-stats">
            <div className="stats-grid">
              <div className="stat-card primary">
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{stats.total_productos}</div>
                  <div className="stat-label">Total Productos</div>
                </div>
              </div>

              <div className="stat-card success">
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{stats.total_cantidad}</div>
                  <div className="stat-label">Unidades en Stock</div>
                </div>
              </div>

              <div className="stat-card info">
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{stats.total_vendidos || 0}</div>
                  <div className="stat-label">Total Vendidos</div>
                </div>
              </div>

              <div className="stat-card accent">
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="12" y1="1" x2="12" y2="23"></line>
                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{formatPrice(stats.valor_total_inventario)}</div>
                  <div className="stat-label">Valor Total</div>
                </div>
              </div>

              <div className={`stat-card ${stats.productos_stock_bajo > 0 ? 'warning' : 'success'}`}>
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{stats.productos_stock_bajo}</div>
                  <div className="stat-label">Stock Bajo</div>
                </div>
              </div>

              <div className={`stat-card ${stats.productos_sin_stock > 0 ? 'danger' : 'success'}`}>
                <div className="stat-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{stats.productos_sin_stock}</div>
                  <div className="stat-label">Sin Stock</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Sección de productos moderna */}
        <div className="warehouse-products">
          <div className="products-header">
            <div className="products-title">
              <h2>Inventario de Productos</h2>
              <p>Gestiona y controla todos los productos del almacén</p>
            </div>
            <div className="products-filters">
              <div className="search-container">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
                <input
                  type="text"
                  placeholder="Buscar por número, modelo, marca..."
                  className="search-input"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="filter-container">
                <select
                  className="filter-select"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="todos">Todos los estados</option>
                  <option value="en_stock">En stock</option>
                  <option value="por_agotarse">Por agotarse</option>
                  <option value="sin_stock">Sin stock</option>
                  <option value="ya_pedido">Ya se pidió</option>
                </select>
              </div>
            </div>
          </div>

          <div className="products-content">
            {loading ? (
              <div className="loading-state">
                <div className="loading-spinner">
                  <div className="spinner"></div>
                </div>
                <h3>Cargando productos...</h3>
                <p>Obteniendo información del inventario</p>
              </div>
            ) : error ? (
              <div className="error-state">
                <div className="error-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <h3>Error al cargar productos</h3>
                <p>{error}</p>
                <button
                  className="retry-button"
                  onClick={() => {
                    setError('');
                    loadProductos();
                  }}
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="23 4 23 10 17 10"></polyline>
                    <polyline points="1 20 1 14 7 14"></polyline>
                    <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                  </svg>
                  Reintentar
                </button>
              </div>
            ) : filteredProductos.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                  </svg>
                </div>
                <h3>{searchTerm ? 'No se encontraron productos' : 'Almacén vacío'}</h3>
                <p>
                  {searchTerm
                    ? 'No hay productos que coincidan con tu búsqueda. Intenta con otros términos.'
                    : 'No hay productos en el almacén. Comienza agregando tu primer producto.'
                  }
                </p>
                {!searchTerm && (
                  <button
                    className="add-first-product-button"
                    onClick={openAddModal}
                  >
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Agregar Primer Producto
                  </button>
                )}
              </div>
            ) : (
                <>
                {/* Tabla para desktop */}
                <div className="admin-table-container desktop-only">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>Número</th>
                      <th>Modelo</th>
                      <th>Marca</th>
                      <th>Estante</th>
                      <th>Stock</th>
                      <th>Precio Venta</th>
                      <th>Precio ML</th>
                      <th>Notas</th>
                      <th>Estado</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProductos.map((producto) => (
                      <tr key={producto.id}>
                        <td>
                          <div className="admin-table-cell">
                            <strong>{producto.numero_almacen}</strong>
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            <div className="admin-product-name">{producto.modelo_existente}</div>
                            {producto.descripcion && (
                              <div className="admin-product-description">
                                {producto.descripcion.length > 50
                                  ? `${producto.descripcion.substring(0, 50)}...`
                                  : producto.descripcion}
                              </div>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            <div className="admin-brand-name" style={{ fontWeight: '500', color: '#0056a6' }}>
                              {producto.marcas || 'N/A'}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            {producto.estante || 'N/A'}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            <span
                              className="admin-stock-badge"
                              style={{
                                backgroundColor: getStockColor(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad),
                                color: 'white',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}
                            >
                              {producto.cantidad_nuevo || 0}
                            </span>
                            {producto.minimo && (
                              <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                                Min: {producto.minimo} | Max: {producto.maximo || 'N/A'}
                              </div>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            {formatPrice(producto.precio_venta)}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            {formatPrice(producto.precio_ml)}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            {producto.nota ? (
                              <div style={{ fontSize: '12px', color: '#333' }}>
                                {producto.nota.length > 30
                                  ? `${producto.nota.substring(0, 30)}...`
                                  : producto.nota}
                              </div>
                            ) : (
                              <span style={{ color: '#999', fontSize: '11px' }}>Sin notas</span>
                            )}
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-cell">
                            <span
                              className="admin-status-badge"
                              style={{
                                backgroundColor: getStockColor(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad),
                                color: 'white',
                                padding: '2px 6px',
                                borderRadius: '3px',
                                fontSize: '11px'
                              }}
                            >
                              {getStockStatus(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad)}
                            </span>
                          </div>
                        </td>
                        <td>
                          <div className="admin-table-actions">
                            <button
                              className="admin-action-button admin-edit-button"
                              onClick={() => openEditModal(producto)}
                              title="Editar producto"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                              </svg>
                            </button>
                            <button
                              className="admin-action-button admin-notes-button"
                              onClick={() => openNotesModal(producto)}
                              title="Ver notas"
                              style={{ color: '#6c757d', borderColor: '#6c757d' }}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10,9 9,9 8,9"></polyline>
                              </svg>
                            </button>
                            <button
                              className="admin-action-button admin-delete-button"
                              onClick={() => openDeleteModal(producto)}
                              title="Eliminar producto"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                </div>

                {/* Vista móvil en cards */}
                <div className="admin-table-mobile mobile-only">
                  {filteredProductos.map((producto) => (
                    <div key={producto.id} className="admin-card-item">
                      <div className="admin-card-item-header">
                        <div className="admin-card-item-title">
                          {producto.numero_almacen} - {producto.modelo_existente}
                        </div>
                        <span
                          className="admin-stock-badge"
                          style={{
                            backgroundColor: getStockColor(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad),
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '11px',
                            fontWeight: 'bold'
                          }}
                        >
                          {producto.cantidad_nuevo || 0}
                        </span>
                      </div>
                      <div className="admin-card-item-content">
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Marca</div>
                          <div className="admin-card-item-value">{producto.marcas || 'N/A'}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Estante</div>
                          <div className="admin-card-item-value">{producto.estante || 'N/A'}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Precio Venta</div>
                          <div className="admin-card-item-value">{formatPrice(producto.precio_venta)}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Estado</div>
                          <div className="admin-card-item-value">
                            <span
                              style={{
                                backgroundColor: getStockColor(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad),
                                color: 'white',
                                padding: '2px 6px',
                                borderRadius: '3px',
                                fontSize: '10px'
                              }}
                            >
                              {getStockStatus(producto.cantidad_nuevo, producto.minimo, producto.maximo, producto.pedir_cantidad)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="admin-table-actions" style={{ marginTop: '10px', justifyContent: 'center' }}>
                        <button
                          className="admin-action-button admin-edit-button"
                          onClick={() => openEditModal(producto)}
                          title="Editar producto"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                        </button>
                        <button
                          className="admin-action-button admin-notes-button"
                          onClick={() => openNotesModal(producto)}
                          title="Ver notas"
                          style={{ color: '#6c757d', borderColor: '#6c757d' }}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                          </svg>
                        </button>
                        <button
                          className="admin-action-button admin-delete-button"
                          onClick={() => openDeleteModal(producto)}
                          title="Eliminar producto"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Modal para agregar producto */}
        {showAddModal && (
        <div className="modal-overlay">
          <div className="modal-container modal-large">
            <div className="modal-header">
              <h2>Agregar Producto al Almacén</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <form onSubmit={handleCreateProduct} className="modal-form">
              <div className="modal-form-group">
                <label htmlFor="numero_almacen">Número de Almacén*</label>
                <input
                  type="text"
                  id="numero_almacen"
                  name="numero_almacen"
                  value={formData.numero_almacen}
                  onChange={handleInputChange}
                  required
                  placeholder="ALM-001"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="modelo_existente">Modelo*</label>
                <input
                  type="text"
                  id="modelo_existente"
                  name="modelo_existente"
                  value={formData.modelo_existente}
                  onChange={handleInputChange}
                  required
                  placeholder="Nombre del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="marcas">Marca</label>
                <input
                  type="text"
                  id="marcas"
                  name="marcas"
                  value={formData.marcas}
                  onChange={handleInputChange}
                  placeholder="Marca del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="estante">Estante</label>
                <input
                  type="text"
                  id="estante"
                  name="estante"
                  value={formData.estante}
                  onChange={handleInputChange}
                  placeholder="A1-01"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="descripcion">Descripción</label>
                <textarea
                  id="descripcion"
                  name="descripcion"
                  value={formData.descripcion}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Descripción detallada del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="precio_us">Precio USD</label>
                <input
                  type="number"
                  id="precio_us"
                  name="precio_us"
                  value={formData.precio_us}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
                <small style={{ color: '#666', fontSize: '11px' }}>
                  Tipo de cambio: ${usdToMxnRate} MXN
                </small>
              </div>

              <div className="modal-form-group">
                <label htmlFor="precio_mx">Precio MXN</label>
                <input
                  type="number"
                  id="precio_mx"
                  name="precio_mx"
                  value={formData.precio_mx}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="impuesto">Impuesto (%)</label>
                <input
                  type="number"
                  id="impuesto"
                  name="impuesto"
                  value={formData.impuesto}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="16.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="precio_venta">Precio de Venta (Calculado)</label>
                <input
                  type="number"
                  id="precio_venta"
                  name="precio_venta"
                  value={formData.precio_venta}
                  readOnly
                  step="0.01"
                  style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                />
                <small style={{ color: '#666', fontSize: '11px' }}>
                  Fórmula: (Precio MX × 1.2 × 1.25 × 1.16) + 100 + Impuesto
                </small>
              </div>

              <div className="modal-form-group">
                <label htmlFor="precio_ml">Precio Mercado Libre</label>
                <input
                  type="number"
                  id="precio_ml"
                  name="precio_ml"
                  value={formData.precio_ml}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="cantidad_nuevo">Cantidad Disponible</label>
                <input
                  type="number"
                  id="cantidad_nuevo"
                  name="cantidad_nuevo"
                  value={formData.cantidad_nuevo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="minimo">Cantidad Mínima</label>
                <input
                  type="number"
                  id="minimo"
                  name="minimo"
                  value={formData.minimo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="maximo">Cantidad Máxima</label>
                <input
                  type="number"
                  id="maximo"
                  name="maximo"
                  value={formData.maximo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="pedir_cantidad">Cantidad Pedida</label>
                <input
                  type="number"
                  id="pedir_cantidad"
                  name="pedir_cantidad"
                  value={formData.pedir_cantidad}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="vendidos">Cantidad Vendidos</label>
                <input
                  type="number"
                  id="vendidos"
                  name="vendidos"
                  value={formData.vendidos}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="codigo_sat">Código SAT</label>
                <input
                  type="text"
                  id="codigo_sat"
                  name="codigo_sat"
                  value={formData.codigo_sat}
                  onChange={handleInputChange}
                  placeholder="85371099"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="proveedor">Proveedor</label>
                <input
                  type="text"
                  id="proveedor"
                  name="proveedor"
                  value={formData.proveedor}
                  onChange={handleInputChange}
                  placeholder="Nombre del proveedor"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="tiempo_entrega_proveedor">Tiempo de Entrega</label>
                <input
                  type="text"
                  id="tiempo_entrega_proveedor"
                  name="tiempo_entrega_proveedor"
                  value={formData.tiempo_entrega_proveedor}
                  onChange={handleInputChange}
                  placeholder="2-3 semanas"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="fecha_pedido">Fecha de Pedido</label>
                <input
                  type="date"
                  id="fecha_pedido"
                  name="fecha_pedido"
                  value={formData.fecha_pedido}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="fecha_recibido">Fecha de Recepción</label>
                <input
                  type="date"
                  id="fecha_recibido"
                  name="fecha_recibido"
                  value={formData.fecha_recibido}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="nota">Notas</label>
                <textarea
                  id="nota"
                  name="nota"
                  value={formData.nota}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Notas adicionales sobre el producto"
                />
              </div>

              {/* Nuevas columnas agregadas */}
              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="Url_imagen">URL de Imagen del Producto</label>
                <input
                  type="url"
                  id="Url_imagen"
                  name="Url_imagen"
                  value={formData.Url_imagen}
                  onChange={handleInputChange}
                  placeholder="https://ejemplo.com/imagen.jpg"
                />
              </div>

              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="Datos_importantes_Descripcion_muestra">Descripción para Mostrar en Productos</label>
                <textarea
                  id="Datos_importantes_Descripcion_muestra"
                  name="Datos_importantes_Descripcion_muestra"
                  value={formData.Datos_importantes_Descripcion_muestra}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Descripción detallada que se mostrará en la página de productos y productos destacados"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="tiempo_de_Entrega">Tiempo de Entrega</label>
                <input
                  type="text"
                  id="tiempo_de_Entrega"
                  name="tiempo_de_Entrega"
                  value={formData.tiempo_de_Entrega}
                  onChange={handleInputChange}
                  placeholder="2-3 días hábiles"
                />
              </div>

              {error && (
                <div className="modal-form-error" style={{ gridColumn: '1 / -1' }}>
                  {error}
                </div>
              )}

              <div className="modal-actions" style={{ gridColumn: '1 / -1' }}>
                <button type="button" className="modal-cancel" onClick={closeModals}>
                  Cancelar
                </button>
                <button type="submit" className="modal-submit" disabled={loading}>
                  {loading ? 'Guardando...' : 'Guardar Producto'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal para editar producto */}
      {showEditModal && editingProduct && (
        <div className="modal-overlay">
          <div className="modal-container modal-large">
            <div className="modal-header">
              <h2>Editar Producto: {editingProduct.numero_almacen}</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <form onSubmit={handleUpdateProduct} className="modal-form">
              <div className="modal-form-group">
                <label htmlFor="edit_numero_almacen">Número de Almacén*</label>
                <input
                  type="text"
                  id="edit_numero_almacen"
                  name="numero_almacen"
                  value={formData.numero_almacen}
                  onChange={handleInputChange}
                  required
                  placeholder="ALM-001"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_modelo_existente">Modelo*</label>
                <input
                  type="text"
                  id="edit_modelo_existente"
                  name="modelo_existente"
                  value={formData.modelo_existente}
                  onChange={handleInputChange}
                  required
                  placeholder="Nombre del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_marcas">Marca</label>
                <input
                  type="text"
                  id="edit_marcas"
                  name="marcas"
                  value={formData.marcas}
                  onChange={handleInputChange}
                  placeholder="Marca del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_estante">Estante</label>
                <input
                  type="text"
                  id="edit_estante"
                  name="estante"
                  value={formData.estante}
                  onChange={handleInputChange}
                  placeholder="A1-01"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_descripcion">Descripción</label>
                <textarea
                  id="edit_descripcion"
                  name="descripcion"
                  value={formData.descripcion}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Descripción detallada del producto"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_precio_us">Precio USD</label>
                <input
                  type="number"
                  id="edit_precio_us"
                  name="precio_us"
                  value={formData.precio_us}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
                <small style={{ color: '#666', fontSize: '11px' }}>
                  Tipo de cambio: ${usdToMxnRate} MXN
                </small>
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_precio_mx">Precio MXN</label>
                <input
                  type="number"
                  id="edit_precio_mx"
                  name="precio_mx"
                  value={formData.precio_mx}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_impuesto">Impuesto (%)</label>
                <input
                  type="number"
                  id="edit_impuesto"
                  name="impuesto"
                  value={formData.impuesto}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="16.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_precio_venta">Precio de Venta (Calculado)</label>
                <input
                  type="number"
                  id="edit_precio_venta"
                  name="precio_venta"
                  value={formData.precio_venta}
                  readOnly
                  step="0.01"
                  style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                />
                <small style={{ color: '#666', fontSize: '11px' }}>
                  Fórmula: (Precio MX × 1.2 × 1.25 × 1.16) + 100 + Impuesto
                </small>
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_precio_ml">Precio Mercado Libre</label>
                <input
                  type="number"
                  id="edit_precio_ml"
                  name="precio_ml"
                  value={formData.precio_ml}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="0.00"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_cantidad_nuevo">Cantidad Disponible</label>
                <input
                  type="number"
                  id="edit_cantidad_nuevo"
                  name="cantidad_nuevo"
                  value={formData.cantidad_nuevo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_minimo">Cantidad Mínima</label>
                <input
                  type="number"
                  id="edit_minimo"
                  name="minimo"
                  value={formData.minimo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_maximo">Cantidad Máxima</label>
                <input
                  type="number"
                  id="edit_maximo"
                  name="maximo"
                  value={formData.maximo}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_pedir_cantidad">Cantidad Pedida</label>
                <input
                  type="number"
                  id="edit_pedir_cantidad"
                  name="pedir_cantidad"
                  value={formData.pedir_cantidad}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_vendidos">Cantidad Vendidos</label>
                <input
                  type="number"
                  id="edit_vendidos"
                  name="vendidos"
                  value={formData.vendidos}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="0"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_codigo_sat">Código SAT</label>
                <input
                  type="text"
                  id="edit_codigo_sat"
                  name="codigo_sat"
                  value={formData.codigo_sat}
                  onChange={handleInputChange}
                  placeholder="85371099"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_proveedor">Proveedor</label>
                <input
                  type="text"
                  id="edit_proveedor"
                  name="proveedor"
                  value={formData.proveedor}
                  onChange={handleInputChange}
                  placeholder="Nombre del proveedor"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_tiempo_entrega_proveedor">Tiempo de Entrega</label>
                <input
                  type="text"
                  id="edit_tiempo_entrega_proveedor"
                  name="tiempo_entrega_proveedor"
                  value={formData.tiempo_entrega_proveedor}
                  onChange={handleInputChange}
                  placeholder="2-3 semanas"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_fecha_pedido">Fecha de Pedido</label>
                <input
                  type="date"
                  id="edit_fecha_pedido"
                  name="fecha_pedido"
                  value={formData.fecha_pedido}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_fecha_recibido">Fecha de Recepción</label>
                <input
                  type="date"
                  id="edit_fecha_recibido"
                  name="fecha_recibido"
                  value={formData.fecha_recibido}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="edit_nota">Notas</label>
                <textarea
                  id="edit_nota"
                  name="nota"
                  value={formData.nota}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Notas adicionales sobre el producto"
                />
              </div>

              {/* Nuevas columnas agregadas */}
              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="edit_Url_imagen">URL de Imagen del Producto</label>
                <input
                  type="url"
                  id="edit_Url_imagen"
                  name="Url_imagen"
                  value={formData.Url_imagen}
                  onChange={handleInputChange}
                  placeholder="https://ejemplo.com/imagen.jpg"
                />
              </div>

              <div className="modal-form-group" style={{ gridColumn: '1 / -1' }}>
                <label htmlFor="edit_Datos_importantes_Descripcion_muestra">Descripción para Mostrar en Productos</label>
                <textarea
                  id="edit_Datos_importantes_Descripcion_muestra"
                  name="Datos_importantes_Descripcion_muestra"
                  value={formData.Datos_importantes_Descripcion_muestra}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Descripción detallada que se mostrará en la página de productos y productos destacados"
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="edit_tiempo_de_Entrega">Tiempo de Entrega</label>
                <input
                  type="text"
                  id="edit_tiempo_de_Entrega"
                  name="tiempo_de_Entrega"
                  value={formData.tiempo_de_Entrega}
                  onChange={handleInputChange}
                  placeholder="2-3 días hábiles"
                />
              </div>

              {error && (
                <div className="modal-form-error" style={{ gridColumn: '1 / -1' }}>
                  {error}
                </div>
              )}

              <div className="modal-actions" style={{ gridColumn: '1 / -1' }}>
                <button type="button" className="modal-cancel" onClick={closeModals}>
                  Cancelar
                </button>
                <button type="submit" className="modal-submit" disabled={loading}>
                  {loading ? 'Actualizando...' : 'Actualizar Producto'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal para ver notas */}
      {showNotesModal && viewingNotes && (
        <div className="modal-overlay">
          <div className="modal-container modal-small">
            <div className="modal-header">
              <h2>Notas del Producto</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <div className="modal-content">
              <div style={{ marginBottom: '15px' }}>
                <strong>Producto:</strong> {viewingNotes.numero_almacen} - {viewingNotes.modelo_existente}
              </div>
              <div style={{ marginBottom: '15px' }}>
                <strong>Marca:</strong> {viewingNotes.marcas || 'No especificada'}
              </div>
              <div style={{ marginBottom: '15px' }}>
                <strong>Proveedor:</strong> {viewingNotes.proveedor || 'No especificado'}
              </div>
              {viewingNotes.tiempo_entrega_proveedor && (
                <div style={{ marginBottom: '15px' }}>
                  <strong>Tiempo de entrega:</strong> {viewingNotes.tiempo_entrega_proveedor}
                </div>
              )}
              <div style={{ marginBottom: '15px' }}>
                <strong>Notas:</strong>
              </div>
              <div style={{
                backgroundColor: '#f8f9fa',
                padding: '15px',
                borderRadius: '4px',
                border: '1px solid #e9ecef',
                minHeight: '100px',
                whiteSpace: 'pre-wrap'
              }}>
                {viewingNotes.nota || 'No hay notas disponibles para este producto.'}
              </div>
            </div>
            <div className="modal-actions">
              <button type="button" className="modal-cancel" onClick={closeModals}>
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal para confirmar eliminación */}
      {showDeleteModal && deletingProduct && (
        <div className="modal-overlay">
          <div className="modal-container modal-small">
            <div className="modal-header">
              <h2>Confirmar Eliminación</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <div className="modal-content">
              <div className="modal-warning-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#e74c3c" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
              </div>
              <div className="modal-warning-content">
                <p>¿Está seguro de que desea eliminar el siguiente producto?</p>
                <div className="modal-product-info">
                  <div><strong>Número:</strong> {deletingProduct.numero_almacen}</div>
                  <div><strong>Modelo:</strong> {deletingProduct.modelo_existente}</div>
                  <div><strong>Marca:</strong> {deletingProduct.marcas || 'No especificada'}</div>
                  {(deletingProduct.cantidad_nuevo || 0) > 0 && (
                    <div><strong>Stock actual:</strong> {deletingProduct.cantidad_nuevo} unidades</div>
                  )}
                </div>
                <p className="modal-warning-text">
                  <strong>⚠️ Esta acción no se puede deshacer.</strong>
                </p>
              </div>
            </div>
            <div className="modal-actions">
              <button type="button" className="modal-cancel" onClick={closeModals}>
                Cancelar
              </button>
              <button
                type="button"
                className="modal-delete"
                onClick={handleDeleteProduct}
                disabled={loading}
              >
                {loading ? 'Eliminando...' : 'Eliminar Producto'}
              </button>
            </div>
          </div>
        </div>
      )}
    </AuthGuard>
  );
}
