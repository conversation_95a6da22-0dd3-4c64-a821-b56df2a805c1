/* Professional Executive Dashboard Styles */
.modern-admin-page,
.modern-dashboard-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  background-attachment: fixed;
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  color: #1e293b;
  position: relative;
}

.modern-dashboard-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.modern-dashboard-page > * {
  position: relative;
  z-index: 1;
}

/* Executive Header */
.modern-admin-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  margin-bottom: 2.5rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
}

.modern-admin-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0056a6, #003366, #f7941d, #0ea5e9);
  background-size: 300% 100%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.modern-admin-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #0056a6, #003366);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.modern-admin-subtitle {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0.5rem 0 0 0;
  font-weight: 500;
  opacity: 0.9;
}

.modern-admin-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.25rem;
  background: rgba(0, 86, 166, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(0, 86, 166, 0.2);
}

.admin-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
}

.admin-user-details h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.admin-user-details p {
  margin: 0;
  font-size: 0.75rem;
  color: #64748b;
}

/* Executive Buttons */
.modern-admin-button {
  display: inline-flex;
  align-items: center;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-admin-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-admin-button:hover::before {
  left: 100%;
}

.modern-admin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.modern-admin-button-primary {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  box-shadow: 0 8px 24px rgba(0, 86, 166, 0.4);
}

.modern-admin-button-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 86, 166, 0.5);
}

.modern-admin-button-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #475569;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.modern-admin-button-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.modern-admin-button-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.4);
}

.modern-admin-button-warning:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(245, 158, 11, 0.5);
}

/* Executive Cards */
.modern-admin-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 2.5rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-admin-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
}

.modern-admin-card-header {
  padding: 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  position: relative;
}

.modern-admin-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #003366);
}

.modern-admin-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

.modern-admin-card-content {
  padding: 2rem;
}

/* Executive Stats Grid */
.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.admin-stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
}

.admin-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0056a6, #003366);
}

.admin-stat-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 1px 0px rgba(255, 255, 255, 0.2) inset;
}

.admin-stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 8px 24px rgba(0, 86, 166, 0.3);
  position: relative;
}

.admin-stat-icon::before {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 14px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  pointer-events: none;
}

.admin-stat-content {
  flex: 1;
}

.admin-stat-title {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.admin-stat-change {
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.admin-stat-change.positive {
  color: #059669;
}

.admin-stat-change.negative {
  color: #dc2626;
}

/* Executive Action Buttons Grid */
.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
}

.admin-action-button {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  text-decoration: none;
  color: #1e293b;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.admin-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 166, 0.1), transparent);
  transition: left 0.5s;
}

.admin-action-button:hover::before {
  left: 100%;
}

.admin-action-button:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(0, 86, 166, 0.3);
  transform: translateY(-4px);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12);
}

.admin-action-button svg {
  width: 24px;
  height: 24px;
  color: #0056a6;
}

/* Filters */
.admin-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.admin-filter-group {
  display: flex;
  flex-direction: column;
}

.modern-admin-input,
.modern-admin-select {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  color: #1e293b;
  transition: all 0.2s ease;
}

.modern-admin-input:focus,
.modern-admin-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tables */
.admin-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.admin-table th {
  background: #f8fafc;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
}

.admin-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.admin-table tbody tr:hover {
  background: #f8fafc;
}

/* Table Actions */
.admin-table-actions {
  display: flex;
  gap: 0.5rem;
}

.admin-action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-action-btn.view {
  background: #dbeafe;
  color: #1d4ed8;
}

.admin-action-btn.view:hover {
  background: #bfdbfe;
}

.admin-action-btn.edit {
  background: #fef3c7;
  color: #d97706;
}

.admin-action-btn.edit:hover {
  background: #fde68a;
}

.admin-action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.admin-action-btn.delete:hover {
  background: #fecaca;
}

/* Badges */
.admin-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.admin-badge-primary {
  background: #dbeafe;
  color: #1d4ed8;
}

.admin-badge-success {
  background: #dcfce7;
  color: #166534;
}

.admin-badge-warning {
  background: #fef3c7;
  color: #d97706;
}

.admin-badge-danger {
  background: #fee2e2;
  color: #dc2626;
}

.admin-badge-info {
  background: #e0f2fe;
  color: #0369a1;
}

.admin-badge-secondary {
  background: #f1f5f9;
  color: #475569;
}

/* Quantity Badge */
.admin-quantity-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.admin-quantity-badge.normal {
  background: #dcfce7;
  color: #166534;
}

.admin-quantity-badge.low {
  background: #fee2e2;
  color: #dc2626;
}

/* Stock Badge */
.admin-stock-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.admin-stock-badge.disponible {
  background: #dcfce7;
  color: #166534;
}

.admin-stock-badge.agotado {
  background: #fee2e2;
  color: #dc2626;
}

.admin-stock-badge.por-pedir {
  background: #fef3c7;
  color: #d97706;
}

/* Loading States */
.modern-admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.modern-admin-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty States */
.modern-admin-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.modern-admin-empty-icon {
  margin-bottom: 1rem;
  color: #9ca3af;
}

.modern-admin-empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.modern-admin-empty-text {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
}

/* Error States */
.modern-admin-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: #fee2e2;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

.modern-admin-error p {
  color: #dc2626;
  margin: 0 0 1rem 0;
  font-weight: 500;
}

/* Executive Activity Lists */
.admin-activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.admin-activity-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #0056a6, #003366);
}

.admin-activity-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.admin-activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.3);
}

.admin-activity-content {
  flex: 1;
  min-width: 0;
}

.admin-activity-text {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.admin-activity-time {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Client Info */
.admin-client-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.admin-client-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.admin-client-email {
  color: #64748b;
  font-size: 0.75rem;
}

.admin-client-phone {
  color: #64748b;
  font-size: 0.75rem;
}

/* Employee Info */
.admin-employee-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.admin-employee-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.admin-employee-email {
  color: #64748b;
  font-size: 0.75rem;
}

.admin-employee-number {
  color: #94a3b8;
  font-size: 0.625rem;
  font-weight: 500;
}

/* Database Info */
.admin-db-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.admin-db-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.admin-db-info-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.admin-db-info-value {
  color: #1e293b;
  font-size: 0.875rem;
}

/* Table Specific Styles */
.admin-table-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.75rem;
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: #475569;
}

.admin-table-model {
  font-weight: 500;
  color: #1e293b;
}

.admin-table-price {
  font-weight: 600;
  color: #059669;
  font-family: 'Monaco', 'Menlo', monospace;
}

.admin-table-profit {
  font-weight: 600;
  color: #dc2626;
  font-family: 'Monaco', 'Menlo', monospace;
}

.admin-table-factor {
  font-weight: 500;
  color: #7c3aed;
}

.admin-table-quantity {
  text-align: center;
}

.admin-table-date {
  color: #64748b;
  font-size: 0.75rem;
}

.admin-table-name {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #1e293b;
}

.admin-table-rows {
  font-family: 'Monaco', 'Menlo', monospace;
  color: #059669;
}

.admin-table-size {
  font-family: 'Monaco', 'Menlo', monospace;
  color: #7c3aed;
}

.admin-table-brand {
  font-weight: 500;
  color: #3b82f6;
}

.admin-table-description {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Professional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Apply animations */
.admin-stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.admin-stat-card:nth-child(1) { animation-delay: 0.1s; }
.admin-stat-card:nth-child(2) { animation-delay: 0.2s; }
.admin-stat-card:nth-child(3) { animation-delay: 0.3s; }
.admin-stat-card:nth-child(4) { animation-delay: 0.4s; }

.admin-action-button {
  animation: slideInRight 0.6s ease-out;
}

.admin-action-button:nth-child(1) { animation-delay: 0.2s; }
.admin-action-button:nth-child(2) { animation-delay: 0.3s; }
.admin-action-button:nth-child(3) { animation-delay: 0.4s; }
.admin-action-button:nth-child(4) { animation-delay: 0.5s; }
.admin-action-button:nth-child(5) { animation-delay: 0.6s; }
.admin-action-button:nth-child(6) { animation-delay: 0.7s; }

.modern-admin-card {
  animation: fadeInUp 0.6s ease-out;
}

.admin-activity-item {
  animation: fadeInUp 0.4s ease-out;
}

.admin-activity-item:nth-child(1) { animation-delay: 0.1s; }
.admin-activity-item:nth-child(2) { animation-delay: 0.2s; }
.admin-activity-item:nth-child(3) { animation-delay: 0.3s; }
.admin-activity-item:nth-child(4) { animation-delay: 0.4s; }
.admin-activity-item:nth-child(5) { animation-delay: 0.5s; }

/* Hover effects */
.admin-stat-card:hover .admin-stat-icon {
  animation: pulse 0.6s ease-in-out;
}

.admin-action-button:hover svg {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

/* Professional Responsive Design */
@media (max-width: 768px) {
  .modern-admin-page,
  .modern-dashboard-page {
    padding: 1rem;
  }

  .modern-admin-header {
    padding: 1.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .modern-admin-title {
    font-size: 2rem;
  }

  .modern-admin-actions {
    width: 100%;
    justify-content: space-between;
    flex-direction: column;
    gap: 1rem;
  }

  .admin-user-info {
    order: -1;
    width: 100%;
    justify-content: center;
  }

  .admin-stats {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .admin-filters {
    grid-template-columns: 1fr;
  }

  .admin-buttons {
    grid-template-columns: 1fr;
  }

  .admin-db-info {
    grid-template-columns: 1fr;
  }

  .admin-table-container {
    font-size: 0.75rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
  }

  .admin-stat-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .admin-stat-value {
    font-size: 2rem;
  }

  .admin-action-button {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .admin-activity-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}

/* ===== GLASSMORPHISM SIDEBAR STYLES ===== */

.glassmorphism-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 320px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glassmorphism-sidebar.collapsed {
  width: 80px;
}

.glassmorphism-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: 0 20px 20px 0;
  pointer-events: none;
}

/* Header Section */
.glassmorphism-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.glassmorphism-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.glassmorphism-logo-container {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.glassmorphism-logo-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glassmorphism-logo {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  object-fit: contain;
}

.glassmorphism-brand-text {
  opacity: 1;
  transition: all 0.3s ease;
}

.glassmorphism-sidebar.collapsed .glassmorphism-brand-text {
  opacity: 0;
  transform: translateX(-20px);
}

.glassmorphism-brand-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glassmorphism-brand-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

/* Toggle Button */
.glassmorphism-toggle {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  color: #475569;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* Navigation Section */
.glassmorphism-nav-container {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-container {
  padding: 0.5rem 0;
}

.glassmorphism-nav-container::-webkit-scrollbar {
  width: 4px;
}

.glassmorphism-nav-container::-webkit-scrollbar-track {
  background: transparent;
}

.glassmorphism-nav-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.glassmorphism-nav {
  padding: 0 1rem;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav {
  padding: 0 0.5rem;
}

.glassmorphism-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-list {
  gap: 0.75rem;
}

.glassmorphism-nav-item {
  position: relative;
}

.glassmorphism-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 16px;
  text-decoration: none;
  color: #475569;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-link {
  padding: 0.875rem;
  justify-content: center;
  gap: 0;
}

.glassmorphism-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.glassmorphism-nav-link:hover::before {
  left: 100%;
}

.glassmorphism-nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.glassmorphism-nav-link.active {
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-nav-icon-container {
  position: relative;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-icon-container {
  width: 40px;
  height: 40px;
}

.glassmorphism-nav-link:hover .glassmorphism-nav-icon-container {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.glassmorphism-nav-link.active .glassmorphism-nav-icon-container {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.glassmorphism-nav-icon {
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.glassmorphism-nav-text {
  font-size: 0.95rem;
  font-weight: 600;
  opacity: 1;
  transition: all 0.3s ease;
  flex: 1;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-text {
  opacity: 0;
  transform: translateX(-20px);
}

.glassmorphism-nav-description {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 400;
  margin-top: 0.25rem;
  opacity: 1;
  transition: all 0.3s ease;
}

.glassmorphism-sidebar.collapsed .glassmorphism-nav-description {
  opacity: 0;
  transform: translateX(-20px);
}

/* User Profile Section */
.glassmorphism-user-section {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.glassmorphism-sidebar.collapsed .glassmorphism-user-section {
  padding: 1rem 0.5rem;
}

.glassmorphism-user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
}

.glassmorphism-sidebar.collapsed .glassmorphism-user-profile {
  padding: 0.75rem;
  justify-content: center;
  gap: 0;
}

.glassmorphism-user-profile:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.glassmorphism-sidebar.collapsed .glassmorphism-user-avatar {
  width: 40px;
  height: 40px;
  font-size: 1rem;
}

.glassmorphism-user-info {
  flex: 1;
  opacity: 1;
  transition: all 0.3s ease;
}

.glassmorphism-sidebar.collapsed .glassmorphism-user-info {
  opacity: 0;
  transform: translateX(-20px);
}

.glassmorphism-user-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
}

.glassmorphism-user-role {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

/* Logout Button */
.glassmorphism-logout {
  margin-top: 1rem;
  width: 100%;
  padding: 0.875rem 1rem;
  border: none;
  border-radius: 12px;
  background: rgba(239, 68, 68, 0.1);
  backdrop-filter: blur(10px);
  color: #dc2626;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.glassmorphism-logout:hover {
  background: rgba(239, 68, 68, 0.15);
  transform: translateY(-2px);
  box-shadow:
    0 4px 12px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-sidebar.collapsed .glassmorphism-logout {
  padding: 0.75rem;
  justify-content: center;
}

.glassmorphism-logout-text {
  opacity: 1;
  transition: all 0.3s ease;
}

.glassmorphism-sidebar.collapsed .glassmorphism-logout-text {
  opacity: 0;
  transform: translateX(-20px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .glassmorphism-sidebar {
    width: 100%;
    max-width: 350px;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glassmorphism-sidebar.mobile-open {
    transform: translateX(0);
  }

  .glassmorphism-sidebar.collapsed {
    transform: translateX(-100%);
    width: 100%;
    max-width: 350px;
  }

  .glassmorphism-header {
    padding: 1.5rem;
  }

  .glassmorphism-toggle {
    display: none;
  }
}

/* Content Area Adjustment */
.glassmorphism-content {
  margin-left: 320px;
  transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    #f8fafc 0%,
    #e2e8f0 50%,
    #cbd5e1 100%
  );
  position: relative;
}

.glassmorphism-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.glassmorphism-content.sidebar-collapsed {
  margin-left: 80px;
}

@media (max-width: 768px) {
  .glassmorphism-content {
    margin-left: 0;
  }

  .glassmorphism-content.sidebar-collapsed {
    margin-left: 0;
  }
}

/* ===== GLASSMORPHISM DASHBOARD STYLES ===== */

.glassmorphism-dashboard {
  padding: 2rem;
  min-height: 100vh;
  background: transparent;
}

.glassmorphism-dashboard-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.glassmorphism-dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8, #8b5cf6, #06b6d4);
  background-size: 300% 100%;
  animation: gradientShift 8s ease infinite;
}

.glassmorphism-dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.glassmorphism-dashboard-subtitle {
  color: #64748b;
  font-size: 1.125rem;
  margin: 0;
  font-weight: 500;
  opacity: 0.9;
}

.glassmorphism-dashboard-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.glassmorphism-refresh-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  color: #475569;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.875rem;
}

.glassmorphism-refresh-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Stats Grid */
.glassmorphism-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.glassmorphism-stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
}

.glassmorphism-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glassmorphism-stat-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-stat-card:hover::before {
  opacity: 1;
}

.glassmorphism-stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  flex-shrink: 0;
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.glassmorphism-stat-card:hover .glassmorphism-stat-icon {
  background: rgba(59, 130, 246, 0.15);
  transform: scale(1.05);
}

.glassmorphism-stat-content {
  flex: 1;
}

.glassmorphism-stat-title {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.glassmorphism-stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.glassmorphism-stat-change {
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.glassmorphism-stat-change.positive {
  color: #059669;
}

.glassmorphism-stat-change.negative {
  color: #dc2626;
}

/* Cards */
.glassmorphism-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glassmorphism-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-card-header {
  padding: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  position: relative;
}

.glassmorphism-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.glassmorphism-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

.glassmorphism-card-content {
  padding: 2rem;
}

/* Action Buttons Grid */
.glassmorphism-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.glassmorphism-action-button {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  text-decoration: none;
  color: #1e293b;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glassmorphism-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.glassmorphism-action-button:hover::before {
  left: 100%;
}

.glassmorphism-action-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.glassmorphism-action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.glassmorphism-action-button:hover .glassmorphism-action-icon {
  background: rgba(59, 130, 246, 0.25);
  transform: scale(1.1);
}

.glassmorphism-action-content {
  flex: 1;
}

.glassmorphism-action-title {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.glassmorphism-action-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
  font-weight: 500;
}

/* Activity List */
.glassmorphism-activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.glassmorphism-activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glassmorphism-activity-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glassmorphism-activity-item:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateX(4px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glassmorphism-activity-item:hover::before {
  opacity: 1;
}

.glassmorphism-activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.glassmorphism-activity-item:hover .glassmorphism-activity-icon {
  background: rgba(59, 130, 246, 0.25);
  transform: scale(1.05);
}

.glassmorphism-activity-content {
  flex: 1;
  min-width: 0;
}

.glassmorphism-activity-text {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.glassmorphism-activity-time {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Loading and Error States */
.glassmorphism-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.glassmorphism-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(59, 130, 246, 0.2);
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.glassmorphism-loading-text {
  color: #64748b;
  font-size: 1rem;
  font-weight: 600;
}

.glassmorphism-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(239, 68, 68, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(239, 68, 68, 0.15);
}

.glassmorphism-error-text {
  color: #dc2626;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.glassmorphism-error-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  background: rgba(239, 68, 68, 0.1);
  backdrop-filter: blur(10px);
  color: #dc2626;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.glassmorphism-error-button:hover {
  background: rgba(239, 68, 68, 0.15);
  transform: translateY(-2px);
}

.glassmorphism-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.glassmorphism-empty-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: rgba(156, 163, 175, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  margin-bottom: 1.5rem;
}

.glassmorphism-empty-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 0.5rem;
}

.glassmorphism-empty-text {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .glassmorphism-dashboard {
    padding: 1.5rem;
  }

  .glassmorphism-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .glassmorphism-actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .glassmorphism-dashboard {
    padding: 1rem;
  }

  .glassmorphism-dashboard-header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .glassmorphism-dashboard-title {
    font-size: 2rem;
  }

  .glassmorphism-dashboard-subtitle {
    font-size: 1rem;
  }

  .glassmorphism-stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .glassmorphism-stat-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .glassmorphism-stat-value {
    font-size: 2rem;
  }

  .glassmorphism-card-header,
  .glassmorphism-card-content {
    padding: 1.5rem;
  }

  .glassmorphism-action-button {
    padding: 1.25rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .glassmorphism-activity-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}

/* ===== GLASSMORPHISM TABLE STYLES ===== */

.glassmorphism-table-container {
  overflow-x: auto;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glassmorphism-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.glassmorphism-table th {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: #374151;
  font-weight: 700;
  text-align: left;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.glassmorphism-table td {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  color: #1e293b;
  vertical-align: middle;
}

.glassmorphism-table tbody tr {
  transition: all 0.3s ease;
  position: relative;
}

.glassmorphism-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.08);
}

/* Employee Info Styles */
.glassmorphism-employee-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.glassmorphism-employee-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.glassmorphism-employee-username {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

.glassmorphism-employee-description {
  color: #9ca3af;
  font-size: 0.75rem;
  font-style: italic;
}

/* Badge Styles */
.glassmorphism-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  backdrop-filter: blur(10px);
  border: 1px solid;
  transition: all 0.3s ease;
}

.glassmorphism-badge.admin {
  background: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.3);
}

.glassmorphism-badge.manager {
  background: rgba(245, 158, 11, 0.15);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.3);
}

.glassmorphism-badge.senior {
  background: rgba(59, 130, 246, 0.15);
  color: #2563eb;
  border-color: rgba(59, 130, 246, 0.3);
}

.glassmorphism-badge.junior {
  background: rgba(139, 92, 246, 0.15);
  color: #7c3aed;
  border-color: rgba(139, 92, 246, 0.3);
}

.glassmorphism-badge.support {
  background: rgba(107, 114, 128, 0.15);
  color: #374151;
  border-color: rgba(107, 114, 128, 0.3);
}

.glassmorphism-badge.intern {
  background: rgba(156, 163, 175, 0.15);
  color: #6b7280;
  border-color: rgba(156, 163, 175, 0.3);
}

.glassmorphism-badge.active {
  background: rgba(16, 185, 129, 0.15);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.3);
}

.glassmorphism-badge.inactive {
  background: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.3);
}

/* Action Buttons */
.glassmorphism-table-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.glassmorphism-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid;
  font-size: 0.875rem;
}

.glassmorphism-action-btn.edit {
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.3);
}

.glassmorphism-action-btn.edit:hover {
  background: rgba(59, 130, 246, 0.25);
  transform: scale(1.05);
}

.glassmorphism-action-btn.delete {
  background: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.3);
}

.glassmorphism-action-btn.delete:hover {
  background: rgba(239, 68, 68, 0.25);
  transform: scale(1.05);
}

.glassmorphism-action-btn.success {
  background: rgba(16, 185, 129, 0.15);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.3);
}

.glassmorphism-action-btn.success:hover {
  background: rgba(16, 185, 129, 0.25);
  transform: scale(1.05);
}

.glassmorphism-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Date Formatting */
.glassmorphism-table-date {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Table */
@media (max-width: 768px) {
  .glassmorphism-table-container {
    border-radius: 12px;
  }

  .glassmorphism-table th,
  .glassmorphism-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.75rem;
  }

  .glassmorphism-table-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .glassmorphism-action-btn {
    width: 32px;
    height: 32px;
  }
}
