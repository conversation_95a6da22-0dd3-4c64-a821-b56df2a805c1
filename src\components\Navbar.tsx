'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { preventBodyScroll } from '@/utils';
import { MAIN_ROUTES, IMAGES } from '@/constants';
import {
  HiOutlineMenu,
  HiOutlineX,
  HiOutlineShoppingCart,
  HiOutlinePhone,
  HiOutlineMail,
  HiOutlineLocationMarker
} from 'react-icons/hi';

/**
 * Professional Navbar component for ECCSA
 * Clean, stable design focused on usability
 */
export default function NavbarNew() {
  const pathname = usePathname();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [cartItems, setCartItems] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // Manejar hidratación del cliente
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Función para abrir el carrito
  const handleCartClick = () => {
    router.push(MAIN_ROUTES.CART);
  };

  // Función para actualizar el contador del carrito
  const updateCartCount = () => {
    const savedCart = localStorage.getItem('eccsa-cart');
    if (savedCart) {
      try {
        const cartItems = JSON.parse(savedCart);
        const totalItems = cartItems.reduce((total: number, item: any) => total + (item.cantidad || 0), 0);
        setCartItems(totalItems);
      } catch (error) {
        console.error('Error parsing cart:', error);
        setCartItems(0);
      }
    } else {
      setCartItems(0);
    }
  };

  // Escuchar cambios en el carrito
  useEffect(() => {
    if (typeof window !== 'undefined') {
      updateCartCount();
      window.addEventListener('storage', updateCartCount);
      window.addEventListener('cartUpdated', updateCartCount);
      const interval = setInterval(updateCartCount, 100);

      return () => {
        window.removeEventListener('storage', updateCartCount);
        window.removeEventListener('cartUpdated', updateCartCount);
        clearInterval(interval);
      };
    }
  }, [isClient]);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Prevent scroll when menu is open
  useEffect(() => {
    preventBodyScroll(isMenuOpen);
    return () => preventBodyScroll(false);
  }, [isMenuOpen]);

  // Handle navigation with prefetch
  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setIsMenuOpen(false);

    if (pathname !== href) {
      router.push(href);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Navigation links configuration
  const navLinks = [
    { href: MAIN_ROUTES.HOME, label: 'Inicio' },
    { href: MAIN_ROUTES.SERVICES, label: 'Servicios' },
    { href: MAIN_ROUTES.PRODUCTS, label: 'Productos' },
    { href: MAIN_ROUTES.PROJECTS, label: 'Proyectos' },
    { href: MAIN_ROUTES.CONTACT, label: 'Contacto' },
  ];

  return (
    <>
      {/* Top Contact Bar */}
      <div className="professional-navbar-top">
        <div className="professional-navbar-top-container">
          <div className="professional-contact-info">
            <div className="professional-contact-item">
              <HiOutlinePhone className="professional-contact-icon" />
              <span>81 8358 9075</span>
            </div>
            <div className="professional-contact-item">
              <HiOutlineMail className="professional-contact-icon" />
              <span><EMAIL></span>
            </div>
            <div className="professional-contact-item">
              <HiOutlineLocationMarker className="professional-contact-icon" />
              <span>Monterrey, N.L.</span>
            </div>
          </div>
          <div className="professional-tagline">
            <span>Líderes en automatización industrial desde 1999</span>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <header className="professional-navbar-main">
        <nav className="professional-navbar-container">
          {/* Logo Section */}
          <div className="professional-navbar-brand">
            <Link
              href={MAIN_ROUTES.HOME}
              onClick={(e) => handleNavigation(e, MAIN_ROUTES.HOME)}
              prefetch={true}
              className="professional-logo-link"
            >
              <Image
                src={IMAGES.LOGO.LARGE}
                alt="ECCSA - Automatización Industrial"
                width={150}
                height={35}
                priority
                className="professional-logo-img"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="professional-navbar-nav">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`professional-nav-link ${pathname === link.href ? 'professional-nav-active' : ''}`}
                onClick={(e) => handleNavigation(e, link.href)}
                prefetch={true}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="professional-navbar-actions">
            {/* Cart Button */}
            <button
              className="professional-cart-btn"
              onClick={handleCartClick}
              aria-label="Carrito de compras"
            >
              <HiOutlineShoppingCart className="professional-cart-icon" />
              {isClient && cartItems > 0 && (
                <span className="professional-cart-badge">{cartItems}</span>
              )}
            </button>

            {/* CTA Button */}
            <Link
              href={MAIN_ROUTES.CONTACT}
              className="professional-cta-btn"
              onClick={(e) => handleNavigation(e, MAIN_ROUTES.CONTACT)}
            >
              Cotizar Proyecto
            </Link>

            {/* Mobile Menu Toggle */}
            <button
              className="professional-mobile-toggle"
              onClick={toggleMenu}
              aria-label="Menú de navegación"
            >
              {isMenuOpen ? (
                <HiOutlineX className="professional-mobile-icon" />
              ) : (
                <HiOutlineMenu className="professional-mobile-icon" />
              )}
            </button>
          </div>
        </nav>

        {/* Mobile Menu */}
        <div className={`professional-mobile-menu ${isMenuOpen ? 'professional-mobile-open' : ''}`}>
          <div className="professional-mobile-overlay" onClick={() => setIsMenuOpen(false)}></div>
          <div className="professional-mobile-panel">
            <div className="professional-mobile-header">
              <Image
                src={IMAGES.LOGO.SMALL}
                alt="ECCSA"
                width={40}
                height={40}
              />
              <span>ECCSA</span>
            </div>

            <div className="professional-mobile-nav">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`professional-mobile-link ${pathname === link.href ? 'professional-mobile-active' : ''}`}
                  onClick={(e) => {
                    handleNavigation(e, link.href);
                    setIsMenuOpen(false);
                  }}
                  prefetch={true}
                >
                  {link.label}
                </Link>
              ))}
            </div>

            <div className="professional-mobile-footer">
              <div className="professional-mobile-contact">
                <div className="professional-mobile-contact-item">
                  <HiOutlinePhone />
                  <span>81 8358 9075</span>
                </div>
                <div className="professional-mobile-contact-item">
                  <HiOutlineMail />
                  <span><EMAIL></span>
                </div>
              </div>

              <Link
                href={MAIN_ROUTES.CONTACT}
                className="professional-mobile-cta"
                onClick={(e) => {
                  handleNavigation(e, MAIN_ROUTES.CONTACT);
                  setIsMenuOpen(false);
                }}
              >
                Cotizar Proyecto
              </Link>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
