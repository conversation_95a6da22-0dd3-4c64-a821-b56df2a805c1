'use client';

import { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaEyeSlash, FaTimes, FaCheck, FaExclamationTriangle } from 'react-icons/fa';

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    id: number;
    username: string;
    name: string;
    email?: string;
    phone?: string;
    role?: string;
    role_level?: number;
    description?: string;
    photo?: string;
  };
  onUserUpdate: (updatedUser: any) => void;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
}

export default function UserProfileModal({ isOpen, onClose, user, onUserUpdate }: UserProfileModalProps) {
  const [activeTab, setActiveTab] = useState<'profile' | 'password' | 'photo'>('profile');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Photo form state
  const [photoForm, setPhotoForm] = useState({
    photo: user.photo || ''
  });

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  // Reset forms when modal opens
  useEffect(() => {
    if (isOpen) {
      setPhotoForm({
        photo: user.photo || ''
      });
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setMessage(null);
      setActiveTab('profile');
    }
  }, [isOpen, user]);

  // Password strength validation
  const checkPasswordStrength = (password: string): PasswordStrength => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Mínimo 8 caracteres');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos una mayúscula');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos una minúscula');
    }

    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos un número');
    }

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos un carácter especial');
    }

    let color = '#ef4444'; // Red
    if (score >= 3) color = '#f59e0b'; // Yellow
    if (score >= 4) color = '#10b981'; // Green

    return { score, feedback, color };
  };

  const passwordStrength = checkPasswordStrength(passwordForm.newPassword);

  const handlePhotoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    try {
      const response = await fetch(`/api/users/${user.id}/photo`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo: photoForm.photo
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Foto actualizada exitosamente' });
        onUserUpdate({ ...user, photo: photoForm.photo });
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Error al actualizar foto' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    // Validations
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', text: 'Las contraseñas no coinciden' });
      setLoading(false);
      return;
    }

    if (passwordStrength.score < 3) {
      setMessage({ type: 'error', text: 'La contraseña no es lo suficientemente segura' });
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/users/${user.id}/change-password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Contraseña actualizada exitosamente' });
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Error al cambiar contraseña' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="user-profile-modal-overlay">
      <div className="user-profile-modal">
        {/* Header */}
        <div className="modal-header">
          <div className="modal-header-content">
            <div className="modal-icon">
              <FaUser />
            </div>
            <div className="modal-header-text">
              <h2 className="modal-title">Configuración de Usuario</h2>
              <p className="modal-subtitle">Gestiona tu información personal y configuración</p>
            </div>
          </div>
          <button className="modal-close-btn" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        {/* Tabs */}
        <div className="modal-tabs">
          <button
            className={`tab-btn ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <FaUser className="tab-icon" />
            Mi Información
          </button>
          <button
            className={`tab-btn ${activeTab === 'photo' ? 'active' : ''}`}
            onClick={() => setActiveTab('photo')}
          >
            <svg className="tab-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Foto
          </button>
          <button
            className={`tab-btn ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => setActiveTab('password')}
          >
            <FaLock className="tab-icon" />
            Contraseña
          </button>
        </div>

        {/* Content */}
        <div className="modal-content">
          {message && (
            <div className={`modal-message ${message.type}`}>
              {message.type === 'success' ? <FaCheck /> : <FaExclamationTriangle />}
              {message.text}
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="profile-info">
              <div className="info-grid">
                <div className="info-item">
                  <label className="info-label">Nombre Completo</label>
                  <div className="info-value">{user.name || 'No especificado'}</div>
                </div>

                <div className="info-item">
                  <label className="info-label">Usuario</label>
                  <div className="info-value">@{user.username}</div>
                </div>

                <div className="info-item">
                  <label className="info-label">Correo Electrónico</label>
                  <div className="info-value">{user.email || 'No especificado'}</div>
                </div>

                <div className="info-item">
                  <label className="info-label">Teléfono</label>
                  <div className="info-value">{user.phone || 'No especificado'}</div>
                </div>

                <div className="info-item">
                  <label className="info-label">Nivel de Ingeniero</label>
                  <div className="info-value">
                    <span className="role-badge">
                      {user.role || 'No especificado'}
                    </span>
                  </div>
                </div>

                <div className="info-item full-width">
                  <label className="info-label">Descripción</label>
                  <div className="info-value description">
                    {user.description || 'Sin descripción'}
                  </div>
                </div>
              </div>

              <div className="info-note">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Para modificar esta información, contacta al administrador del sistema.
              </div>
            </div>
          )}

          {activeTab === 'photo' && (
            <form onSubmit={handlePhotoSubmit} className="photo-form">
              <div className="current-photo">
                <label className="info-label">Foto Actual</label>
                <div className="photo-preview">
                  {user.photo ? (
                    <img src={user.photo} alt="Foto actual" className="current-photo-img" />
                  ) : (
                    <div className="no-photo-placeholder">
                      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <span>Sin foto</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="photo">URL de Nueva Foto</label>
                <input
                  type="url"
                  id="photo"
                  value={photoForm.photo}
                  onChange={(e) => setPhotoForm({ ...photoForm, photo: e.target.value })}
                  className="form-input"
                  placeholder="https://ejemplo.com/mi-foto.jpg"
                />
                <div className="form-help">
                  Ingresa la URL de tu nueva foto de perfil. Debe ser una imagen accesible públicamente.
                </div>
              </div>

              {photoForm.photo && photoForm.photo !== user.photo && (
                <div className="photo-preview-new">
                  <label className="info-label">Vista Previa</label>
                  <img
                    src={photoForm.photo}
                    alt="Vista previa"
                    className="preview-photo-img"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}

              <button type="submit" disabled={loading || !photoForm.photo} className="submit-btn">
                {loading ? 'Actualizando...' : 'Actualizar Foto'}
              </button>
            </form>
          )}

          {activeTab === 'password' && (
            <form onSubmit={handlePasswordSubmit} className="password-form">
              <div className="form-group">
                <label htmlFor="currentPassword">Contraseña Actual</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.current ? 'text' : 'password'}
                    id="currentPassword"
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                  >
                    {showPasswords.current ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="newPassword">Nueva Contraseña</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.new ? 'text' : 'password'}
                    id="newPassword"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                  >
                    {showPasswords.new ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                
                {passwordForm.newPassword && (
                  <div className="password-strength">
                    <div className="strength-bar">
                      <div 
                        className="strength-fill" 
                        style={{ 
                          width: `${(passwordStrength.score / 5) * 100}%`,
                          backgroundColor: passwordStrength.color 
                        }}
                      />
                    </div>
                    <div className="strength-feedback">
                      {passwordStrength.feedback.length > 0 ? (
                        <ul>
                          {passwordStrength.feedback.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      ) : (
                        <span style={{ color: passwordStrength.color }}>Contraseña segura</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirmar Nueva Contraseña</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.confirm ? 'text' : 'password'}
                    id="confirmPassword"
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                  >
                    {showPasswords.confirm ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                {passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword && (
                  <div className="password-mismatch">Las contraseñas no coinciden</div>
                )}
              </div>

              <button type="submit" disabled={loading || passwordStrength.score < 3} className="submit-btn">
                {loading ? 'Cambiando...' : 'Cambiar Contraseña'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
