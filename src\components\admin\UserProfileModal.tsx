'use client';

import { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaEyeSlash, FaTimes, FaCheck, FaExclamationTriangle } from 'react-icons/fa';

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    id: number;
    username: string;
    name: string;
    email?: string;
    phone?: string;
    role?: string;
    role_level?: number;
    description?: string;
    photo?: string;
  };
  onUserUpdate: (updatedUser: any) => void;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
}

export default function UserProfileModal({ isOpen, onClose, user, onUserUpdate }: UserProfileModalProps) {
  const [activeTab, setActiveTab] = useState<'profile' | 'password' | 'photo'>('profile');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Photo form state
  const [photoForm, setPhotoForm] = useState({
    photo: user.photo || ''
  });

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  // Reset forms when modal opens
  useEffect(() => {
    if (isOpen) {
      setPhotoForm({
        photo: user.photo || ''
      });
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setMessage(null);
      setActiveTab('profile');
    }
  }, [isOpen, user]);

  // Password strength validation
  const checkPasswordStrength = (password: string): PasswordStrength => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Mínimo 8 caracteres');
    }

    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos una mayúscula');
    }

    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos una minúscula');
    }

    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos un número');
    }

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Al menos un carácter especial');
    }

    let color = '#ef4444'; // Red
    if (score >= 3) color = '#f59e0b'; // Yellow
    if (score >= 4) color = '#10b981'; // Green

    return { score, feedback, color };
  };

  const passwordStrength = checkPasswordStrength(passwordForm.newPassword);

  const handlePhotoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    try {
      const response = await fetch(`/api/users/${user.id}/photo`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo: photoForm.photo
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Foto actualizada exitosamente' });
        onUserUpdate({ ...user, photo: photoForm.photo });
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Error al actualizar foto' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage(null);

    // Validations
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', text: 'Las contraseñas no coinciden' });
      setLoading(false);
      return;
    }

    if (passwordStrength.score < 3) {
      setMessage({ type: 'error', text: 'La contraseña no es lo suficientemente segura' });
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/users/${user.id}/change-password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Contraseña actualizada exitosamente' });
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Error al cambiar contraseña' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="user-profile-modal-overlay">
      <div className="user-profile-modal">
        {/* Header with User Avatar */}
        <div className="modal-header">
          <div className="modal-header-background">
            <div className="header-gradient"></div>
            <div className="header-pattern"></div>
          </div>

          <div className="modal-header-content">
            <div className="user-avatar-section">
              <div className="user-avatar-container">
                {user.photo ? (
                  <img src={user.photo} alt={user.name} className="user-avatar-img" />
                ) : (
                  <div className="user-avatar-placeholder">
                    <FaUser />
                  </div>
                )}
                <div className="avatar-status-indicator"></div>
              </div>

              <div className="user-info-header">
                <h2 className="user-name">{user.name}</h2>
                <p className="user-role">{user.role}</p>
                <div className="user-status">
                  <span className="status-dot"></span>
                  En línea
                </div>
              </div>
            </div>

            <button className="modal-close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="modal-navigation">
          <div className="nav-tabs">
            <button
              className={`nav-tab ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              <div className="tab-icon-container">
                <FaUser className="tab-icon" />
              </div>
              <div className="tab-content">
                <span className="tab-title">Información Personal</span>
                <span className="tab-description">Datos básicos del perfil</span>
              </div>
            </button>

            <button
              className={`nav-tab ${activeTab === 'photo' ? 'active' : ''}`}
              onClick={() => setActiveTab('photo')}
            >
              <div className="tab-icon-container">
                <svg className="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="tab-content">
                <span className="tab-title">Foto de Perfil</span>
                <span className="tab-description">Actualizar imagen</span>
              </div>
            </button>

            <button
              className={`nav-tab ${activeTab === 'password' ? 'active' : ''}`}
              onClick={() => setActiveTab('password')}
            >
              <div className="tab-icon-container">
                <FaLock className="tab-icon" />
              </div>
              <div className="tab-content">
                <span className="tab-title">Seguridad</span>
                <span className="tab-description">Cambiar contraseña</span>
              </div>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="modal-content">
          {message && (
            <div className={`modal-message ${message.type}`}>
              {message.type === 'success' ? <FaCheck /> : <FaExclamationTriangle />}
              {message.text}
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="profile-info">
              <div className="profile-section">
                <h3 className="section-title">
                  <FaUser className="section-icon" />
                  Información Personal
                </h3>

                <div className="info-cards">
                  <div className="info-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Nombre Completo</label>
                      <div className="card-value">{user.name || 'No especificado'}</div>
                    </div>
                  </div>

                  <div className="info-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Usuario</label>
                      <div className="card-value">@{user.username}</div>
                    </div>
                  </div>

                  <div className="info-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Correo Electrónico</label>
                      <div className="card-value">{user.email || 'No especificado'}</div>
                    </div>
                  </div>

                  <div className="info-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Teléfono</label>
                      <div className="card-value">{user.phone || 'No especificado'}</div>
                    </div>
                  </div>

                  <div className="info-card role-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Nivel de Acceso</label>
                      <div className="card-value">
                        <span className="role-badge">
                          {user.role || 'No especificado'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="info-card description-card">
                    <div className="card-icon">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="card-content">
                      <label className="card-label">Descripción</label>
                      <div className="card-value description">
                        {user.description || 'Sin descripción disponible'}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="info-notice">
                  <div className="notice-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="notice-content">
                    <p className="notice-title">Información de Solo Lectura</p>
                    <p className="notice-text">Para modificar estos datos, contacta al administrador del sistema.</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'photo' && (
            <form onSubmit={handlePhotoSubmit} className="photo-form">
              <div className="current-photo">
                <label className="info-label">Foto Actual</label>
                <div className="photo-preview">
                  {user.photo ? (
                    <img src={user.photo} alt="Foto actual" className="current-photo-img" />
                  ) : (
                    <div className="no-photo-placeholder">
                      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <span>Sin foto</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="photo">URL de Nueva Foto</label>
                <input
                  type="url"
                  id="photo"
                  value={photoForm.photo}
                  onChange={(e) => setPhotoForm({ ...photoForm, photo: e.target.value })}
                  className="form-input"
                  placeholder="https://ejemplo.com/mi-foto.jpg"
                />
                <div className="form-help">
                  Ingresa la URL de tu nueva foto de perfil. Debe ser una imagen accesible públicamente.
                </div>
              </div>

              {photoForm.photo && photoForm.photo !== user.photo && (
                <div className="photo-preview-new">
                  <label className="info-label">Vista Previa</label>
                  <img
                    src={photoForm.photo}
                    alt="Vista previa"
                    className="preview-photo-img"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}

              <button type="submit" disabled={loading || !photoForm.photo} className="submit-btn">
                {loading ? 'Actualizando...' : 'Actualizar Foto'}
              </button>
            </form>
          )}

          {activeTab === 'password' && (
            <form onSubmit={handlePasswordSubmit} className="password-form">
              <div className="form-group">
                <label htmlFor="currentPassword">Contraseña Actual</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.current ? 'text' : 'password'}
                    id="currentPassword"
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                  >
                    {showPasswords.current ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="newPassword">Nueva Contraseña</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.new ? 'text' : 'password'}
                    id="newPassword"
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                  >
                    {showPasswords.new ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                
                {passwordForm.newPassword && (
                  <div className="password-strength">
                    <div className="strength-bar">
                      <div 
                        className="strength-fill" 
                        style={{ 
                          width: `${(passwordStrength.score / 5) * 100}%`,
                          backgroundColor: passwordStrength.color 
                        }}
                      />
                    </div>
                    <div className="strength-feedback">
                      {passwordStrength.feedback.length > 0 ? (
                        <ul>
                          {passwordStrength.feedback.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      ) : (
                        <span style={{ color: passwordStrength.color }}>Contraseña segura</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirmar Nueva Contraseña</label>
                <div className="password-input-group">
                  <input
                    type={showPasswords.confirm ? 'text' : 'password'}
                    id="confirmPassword"
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                    required
                    className="form-input"
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                  >
                    {showPasswords.confirm ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
                {passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword && (
                  <div className="password-mismatch">Las contraseñas no coinciden</div>
                )}
              </div>

              <button type="submit" disabled={loading || passwordStrength.score < 3} className="submit-btn">
                {loading ? 'Cambiando...' : 'Cambiar Contraseña'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
