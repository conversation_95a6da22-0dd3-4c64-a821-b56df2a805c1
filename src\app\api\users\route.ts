import { NextRequest, NextResponse } from 'next/server';
import { UserModel, User } from '@/eccsa_back/models/User';
import { hashPassword } from '@/eccsa_back/lib/password';

/**
 * GET /api/users
 * Obtiene todos los usuarios
 */
export async function GET() {
  try {
    const users = await UserModel.getAll();
    return NextResponse.json({ success: true, data: users });
  } catch (error) {
    console.error('Error al obtener usuarios:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error al obtener usuarios',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/users
 * Crea un nuevo usuario
 */
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();

    // Validaciones básicas
    if (!userData.nombre || !userData.nombre_usuario || !userData.password) {
      return NextResponse.json(
        { error: 'Nombre, usuario y contraseña son requeridos' },
        { status: 400 }
      );
    }

    // Verificar si el username ya existe
    const existingUser = await UserModel.getByUsername(userData.nombre_usuario);
    if (existingUser) {
      return NextResponse.json(
        { error: 'El nombre de usuario ya está en uso' },
        { status: 409 }
      );
    }

    // Verificar si el email ya existe (si se proporciona)
    if (userData.correo) {
      const existingEmail = await UserModel.getByEmail(userData.correo);
      if (existingEmail) {
        return NextResponse.json(
          { error: 'El correo electrónico ya está en uso' },
          { status: 409 }
        );
      }
    }

    // Hash de la contraseña
    const hashedPassword = hashPassword(userData.password);

    // Preparar datos para crear usuario
    const newUserData = {
      nombre: userData.nombre,
      nombre_usuario: userData.nombre_usuario,
      correo: userData.correo || null,
      contrasena: hashedPassword,
      telefono: userData.telefono || null,
      nivel_ingeniero: userData.nivel_ingeniero !== undefined ? userData.nivel_ingeniero : 4,
      descripcion: userData.descripcion || null,
      foto_usuario: userData.foto_usuario || null,
      activo: userData.activo !== undefined ? userData.activo : true
    };

    // Crear el usuario
    const newUserId = await UserModel.create(newUserData);

    if (!newUserId) {
      return NextResponse.json(
        { error: 'No se pudo crear el usuario' },
        { status: 500 }
      );
    }

    // Obtener el usuario creado
    const createdUser = await UserModel.getById(newUserId);

    return NextResponse.json({
      success: true,
      user: createdUser,
      message: 'Usuario creado exitosamente'
    }, { status: 201 });

  } catch (error) {
    console.error('Error al crear usuario:', error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('Duplicate entry')) {
      if (errorMessage.includes('nombre_usuario')) {
        return NextResponse.json(
          { error: 'El nombre de usuario ya está en uso' },
          { status: 409 }
        );
      } else if (errorMessage.includes('correo')) {
        return NextResponse.json(
          { error: 'El correo electrónico ya está en uso' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
