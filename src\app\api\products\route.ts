import { NextRequest, NextResponse } from 'next/server';
import { getAllProducts, createProduct } from '@/eccsa_back/lib/products';

// GET handler to fetch all products
export async function GET() {
  try {
    const products = await getAllProducts();

    return NextResponse.json({ products });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST handler to create a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.description || !body.category_id) {
      return NextResponse.json(
        { error: 'Name, description, and category are required' },
        { status: 400 }
      );
    }

    const productId = await createProduct({
      name: body.name,
      description: body.description,
      price: body.price,
      category_id: body.category_id,
      image_url: body.image_url,
      brand: body.brand,
    });

    if (!productId) {
      return NextResponse.json(
        { error: 'Failed to create product' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, productId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
