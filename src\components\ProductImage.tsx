'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { IMAGES } from '@/constants';

interface ProductImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
  className?: string;
}

/**
 * Componente optimizado para manejar imágenes de productos con fallback
 * Usa Next.js Image para mejor rendimiento y carga lazy
 */
export default function ProductImage({
  src,
  alt,
  width = 400,
  height = 200,
  priority = false,
  className = '',
}: ProductImageProps) {
  const [imgSrc, setImgSrc] = useState<string>(src);
  const [isError, setIsError] = useState<boolean>(false);

  // Resetear el estado si cambia la fuente de la imagen
  useEffect(() => {
    setImgSrc(src);
    setIsError(false);
  }, [src]);

  // Manejar error de carga de imagen
  const handleError = () => {
    if (!isError) {
      setIsError(true);
      setImgSrc(IMAGES.LOGO.SMALL);
    }
  };

  return (
    <div className={`product-image-container ${className}`} style={{ position: 'relative', width: '100%', height: height }}>
      <Image
        src={imgSrc}
        alt={alt}
        fill
        priority={priority}
        onError={handleError}
        style={{
          objectFit: 'cover',
        }}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      />
    </div>
  );
}
