'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface SidebarItem {
  name: string;
  icon: JSX.Element;
  path: string;
  subItems?: { name: string; path: string }[];
}

interface User {
  id?: number;
  username: string;
  name?: string;
  role?: string;
  role_level?: number;
  email?: string;
  photo?: string;
}

interface AdminSidebarProps {
  mobileOpen?: boolean;
  onMobileToggle?: () => void;
  onToggle?: (collapsed: boolean) => void;
}

export default function AdminSidebar({ mobileOpen = false, onMobileToggle, onToggle }: AdminSidebarProps) {
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [activeItem, setActiveItem] = useState<string>('');
  const [activeSubItem, setActiveSubItem] = useState<string>('');
  const [expandedSubMenu, setExpandedSubMenu] = useState<string | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [solicitudesPendientes, setSolicitudesPendientes] = useState<number>(0);

  // Detectar si es móvil
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Cargar los datos del usuario desde localStorage y determinar la página activa
  useEffect(() => {
    // Cargar datos del usuario
    const savedUserData = localStorage.getItem('eccsaUserData');
    if (savedUserData) {
      try {
        const parsedUserData = JSON.parse(savedUserData);
        setUserData(parsedUserData);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }

    // Determinar la página activa basada en la URL actual
    const path = window.location.pathname;

    // Verificar si estamos en el dashboard
    if (path === '/eccsa/productos/almacen/admin') {
      setActiveItem('Dashboard');
      return;
    }

    // Verificar si estamos en la página de empleados
    if (path.includes('/eccsa/productos/almacen/admin/empleados')) {
      setActiveItem('Empleados');
      return;
    }

    // Verificar si estamos en la página de base de datos
    if (path.includes('/eccsa/productos/almacen/admin/database')) {
      setActiveItem('Base de Datos');
      return;
    }

    // Verificar si estamos en la página de solicitudes
    if (path.includes('/eccsa/productos/almacen/admin/solicitudes')) {
      setActiveItem('Solicitudes');
      return;
    }

    // Verificar si estamos en alguna subpágina de Almacén
    if (path.includes('/admin/')) {
      if (path.includes('/admin/almacen')) {
        setActiveItem('Almacén');
        setActiveSubItem('Almacén');
        setExpandedSubMenu('Almacén');
      } else if (path.includes('/admin/precios')) {
        setActiveItem('Almacén');
        setActiveSubItem('Listado de Precios');
        setExpandedSubMenu('Almacén');
      } else if (path.includes('/admin/solicitudes')) {
        setActiveItem('Solicitudes');
      } else if (path.includes('/admin/empleados')) {
        setActiveItem('Empleados');
      } else if (path === '/admin') {
        setActiveItem('Dashboard');
      }
    }

    // Cargar número de solicitudes pendientes
    loadSolicitudesPendientes();
  }, []);

  // Función para cargar solicitudes pendientes
  const loadSolicitudesPendientes = async () => {
    try {
      const response = await fetch('/api/solicitudes?estado=pendiente&limit=1');
      const data = await response.json();
      if (data.success) {
        setSolicitudesPendientes(data.total || 0);
      }
    } catch (error) {
      console.error('Error loading solicitudes pendientes:', error);
    }
  };

  const handleLogout = () => {
    console.log('Cerrando sesión...');

    // Eliminar los datos del usuario del localStorage
    localStorage.removeItem('eccsaUserData');
    localStorage.removeItem('eccsaIsAuthenticated');

    // Redirigir al login
    router.push('/admin');

    // Recargar la página para asegurar que se limpie el estado
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  // Función para verificar si el usuario tiene acceso a ciertas opciones
  const hasAccess = (requiredLevel: number) => {
    if (!userData || userData.role_level === undefined) return false;
    return userData.role_level <= requiredLevel; // Nivel 0 es el más alto (administrador)
  };

  const sidebarItems: SidebarItem[] = [
    {
      name: 'Dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="7" height="9"></rect>
          <rect x="14" y="3" width="7" height="5"></rect>
          <rect x="14" y="12" width="7" height="9"></rect>
          <rect x="3" y="16" width="7" height="5"></rect>
        </svg>
      ),
      path: '/admin'
    },
    {
      name: 'Almacén',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <path d="M16 10a4 4 0 0 1-8 0"></path>
        </svg>
      ),
      path: '/admin/almacen'
    },
    {
      name: 'Lista de Precios',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2v20m8-10H4"></path>
        </svg>
      ),
      path: '/admin/precios'
    },
    // Opción de Empleados - solo visible para administradores (nivel 0)
    ...(hasAccess(0) ? [{
      name: 'Empleados',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      ),
      path: '/admin/empleados'
    }] : []),
    // Opción de Base de Datos - solo visible para administradores (nivel 0)
    ...(hasAccess(0) ? [{
      name: 'Base de Datos',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
          <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
          <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
        </svg>
      ),
      path: '/admin/database'
    }] : []),
    {
      name: 'Solicitudes',
      icon: (
        <div style={{ position: 'relative', display: 'inline-block' }}>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
          </svg>
          {solicitudesPendientes > 0 && (
            <span style={{
              position: 'absolute',
              top: '-8px',
              right: '-8px',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '50%',
              width: '18px',
              height: '18px',
              fontSize: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 'bold'
            }}>
              {solicitudesPendientes > 99 ? '99+' : solicitudesPendientes}
            </span>
          )}
        </div>
      ),
      path: '/admin/solicitudes'
    },
    // Quitamos temporalmente la opción de Configuración
    // y el botón de Perfil (ahora se accederá dando clic al nombre)
  ];

  const toggleSidebar = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onToggle) {
      onToggle(newCollapsed);
    }
  };

  const handleItemClick = (itemName: string) => {
    setActiveItem(itemName);
    if (expandedSubMenu === itemName) {
      setExpandedSubMenu(null);
    } else {
      const item = sidebarItems.find(item => item.name === itemName);
      if (item?.subItems) {
        setExpandedSubMenu(itemName);
      } else {
        setExpandedSubMenu(null);
        // Cerrar sidebar en móvil cuando se selecciona un item sin subitems
        if (isMobile && onMobileToggle) {
          onMobileToggle();
        }
      }
    }
  };

  const handleSubItemClick = (subItemName: string) => {
    setActiveSubItem(subItemName);
    // Cerrar sidebar en móvil cuando se selecciona un subitem
    if (isMobile && onMobileToggle) {
      onMobileToggle();
    }
  };

  return (
    <div className={`modern-admin-sidebar ${!isMobile && collapsed ? 'collapsed' : ''} ${mobileOpen ? 'mobile-open' : ''}`}>
      {/* Header Section */}
      <div className="modern-sidebar-header">
        <div className="sidebar-brand">
          <div className="brand-logo-container">
            <img
              src="/images/logos/logo_pequeno.png"
              alt="ECCSA Logo"
              className="brand-logo"
            />
            <div className="brand-glow"></div>
          </div>
          {(isMobile || !collapsed) && (
            <div className="brand-text">
              <h3 className="brand-title">ECCSA</h3>
              <span className="brand-subtitle">Admin Panel</span>
            </div>
          )}
        </div>
        {!isMobile && (
          <button
            className="modern-sidebar-toggle"
            onClick={toggleSidebar}
            title={collapsed ? "Expandir sidebar" : "Contraer sidebar"}
          >
            <div className="toggle-icon">
              {collapsed ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="13 17 18 12 13 7"></polyline>
                  <polyline points="6 17 11 12 6 7"></polyline>
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
              )}
            </div>
          </button>
        )}
      </div>

      {/* User Profile Section */}
      {(isMobile || !collapsed) && userData && (
        <Link href="/admin/perfil" className="modern-user-profile-link">
          <div className="modern-user-profile">
            <div className="user-avatar-container">
              {userData.photo ? (
                <img
                  src={userData.photo}
                  alt={userData.name || userData.username}
                  className="user-avatar-image"
                />
              ) : (
                <div className="user-avatar-placeholder">
                  <span className="user-initial">
                    {(userData.name || userData.username).charAt(0).toUpperCase()}
                  </span>
                  <div className="avatar-ring"></div>
                </div>
              )}
              <div className="user-status-indicator"></div>
            </div>
            <div className="user-info">
              <div className="user-name">{userData.name || userData.username}</div>
              <div className="user-role">
                <span className="role-badge">{userData.role || 'Usuario'}</span>
              </div>
            </div>
            <div className="profile-arrow">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </div>
          </div>
        </Link>
      )}

      {/* Navigation Menu */}
      <div className="modern-sidebar-content">
        <nav className="modern-sidebar-nav">
          <ul className="nav-menu">
            {sidebarItems.map((item) => (
              <li key={item.name} className={`nav-item ${activeItem === item.name ? 'active' : ''}`}>
                <Link
                  href={item.path}
                  className="nav-link"
                  onClick={() => handleItemClick(item.name)}
                >
                  <div className="nav-link-content">
                    <div className="nav-icon-container">
                      <span className="nav-icon">{item.icon}</span>
                      <div className="icon-bg"></div>
                    </div>
                    {(isMobile || !collapsed) && (
                      <>
                        <span className="nav-text">{item.name}</span>
                        {item.subItems && (
                          <div className="nav-arrow">
                            <svg
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              className={expandedSubMenu === item.name ? 'rotated' : ''}
                            >
                              <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  {activeItem === item.name && <div className="active-indicator"></div>}
                </Link>

                {/* Submenu */}
                {(isMobile || !collapsed) && item.subItems && expandedSubMenu === item.name && (
                  <ul className="nav-submenu">
                    {item.subItems.map((subItem) => (
                      <li key={subItem.name} className={`nav-subitem ${activeSubItem === subItem.name ? 'active' : ''}`}>
                        <Link
                          href={subItem.path}
                          className="nav-sublink"
                          onClick={() => handleSubItemClick(subItem.name)}
                        >
                          <div className="sublink-indicator"></div>
                          <span className="sublink-text">{subItem.name}</span>
                          {activeSubItem === subItem.name && <div className="sublink-active"></div>}
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Footer Section */}
      <div className="modern-sidebar-footer">
        <button
          onClick={handleLogout}
          className="modern-logout-button"
          type="button"
        >
          <div className="logout-icon-container">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
          </div>
          {(isMobile || !collapsed) && (
            <span className="logout-text">Cerrar Sesión</span>
          )}
        </button>
      </div>
    </div>
  );
}
