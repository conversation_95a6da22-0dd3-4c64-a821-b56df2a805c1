import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/eccsa_back/lib/auth';
import jwt from 'jsonwebtoken';

// Clave secreta para JWT (en producción debe estar en variables de entorno)
const JWT_SECRET = process.env.JWT_SECRET || 'eccsa-secret-key-2024';

// Credenciales de emergencia
const EMERGENCY_CREDENTIALS = {
  username: 'admin',
  password: 'eccsa2023',
  name: 'Administrador ECCSA',
  role: 'Administrador',
  role_level: 0
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Validate input
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Usuario y contraseña son requeridos' },
        { status: 400 }
      );
    }

    let user = null;

    try {
      // Intentar autenticación con base de datos
      user = await authenticateUser(username, password);
    } catch (dbError) {
      console.error('Database authentication error:', dbError);
      // Continuar con credenciales de emergencia si falla la DB
    }

    // Si no se autenticó con DB, intentar credenciales de emergencia
    if (!user && username === EMERGENCY_CREDENTIALS.username && password === EMERGENCY_CREDENTIALS.password) {
      user = {
        id: 0,
        username: EMERGENCY_CREDENTIALS.username,
        name: EMERGENCY_CREDENTIALS.name,
        role: EMERGENCY_CREDENTIALS.role,
        role_level: EMERGENCY_CREDENTIALS.role_level,
        email: '<EMAIL>',
        phone: '',
        photo: '',
        description: 'Administrador del sistema'
      };
    }

    // Check if user exists
    if (!user) {
      return NextResponse.json(
        { error: 'Credenciales inválidas' },
        { status: 401 }
      );
    }

    // Generar token JWT
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role,
        role_level: user.role_level
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Crear datos de sesión
    const sessionData = {
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        role_level: user.role_level,
        email: user.email,
        phone: user.phone,
        photo: user.photo,
        description: user.description
      },
      expires: Date.now() + 24 * 60 * 60 * 1000, // 24 horas
      loginTime: Date.now(),
      lastActivity: Date.now()
    };

    // Crear respuesta
    const response = NextResponse.json({
      success: true,
      user: sessionData.user,
      message: 'Autenticación exitosa'
    });

    // Establecer cookies seguras
    response.cookies.set('eccsa-auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 horas
      path: '/'
    });

    response.cookies.set('eccsa-session', JSON.stringify(sessionData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 horas
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Método GET para verificar estado de autenticación
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('eccsa-auth-token')?.value;
    const sessionData = request.cookies.get('eccsa-session')?.value;

    if (!token || !sessionData) {
      return NextResponse.json(
        { authenticated: false, error: 'No hay sesión activa' },
        { status: 401 }
      );
    }

    // Verificar token JWT
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const session = JSON.parse(sessionData);

    // Verificar expiración
    if (session.expires && Date.now() > session.expires) {
      return NextResponse.json(
        { authenticated: false, error: 'Sesión expirada' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      authenticated: true,
      user: session.user,
      expiresAt: session.expires
    });

  } catch (error) {
    console.error('Auth verification error:', error);
    return NextResponse.json(
      { authenticated: false, error: 'Token inválido' },
      { status: 401 }
    );
  }
}
