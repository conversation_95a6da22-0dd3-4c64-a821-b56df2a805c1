'use client';

import { useState, useEffect } from 'react';

// Breakpoints que coinciden con Tailwind CSS
const breakpoints = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

type Breakpoint = keyof typeof breakpoints;

/**
 * Hook para detectar el breakpoint actual
 * Proporciona una alternativa moderna a las media queries CSS
 */
export function useBreakpoint() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('lg');
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      setWindowWidth(width);

      if (width >= breakpoints['2xl']) {
        setCurrentBreakpoint('2xl');
      } else if (width >= breakpoints.xl) {
        setCurrentBreakpoint('xl');
      } else if (width >= breakpoints.lg) {
        setCurrentBreakpoint('lg');
      } else if (width >= breakpoints.md) {
        setCurrentBreakpoint('md');
      } else if (width >= breakpoints.sm) {
        setCurrentBreakpoint('sm');
      } else {
        setCurrentBreakpoint('xs');
      }
    };

    // Ejecutar inmediatamente
    updateBreakpoint();

    // Escuchar cambios de tamaño
    window.addEventListener('resize', updateBreakpoint);
    
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return { currentBreakpoint, windowWidth };
}

/**
 * Hook para verificar si estamos en un breakpoint específico o mayor
 */
export function useMediaQuery(breakpoint: Breakpoint) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const updateMatches = () => {
      setMatches(window.innerWidth >= breakpoints[breakpoint]);
    };

    updateMatches();
    window.addEventListener('resize', updateMatches);
    
    return () => window.removeEventListener('resize', updateMatches);
  }, [breakpoint]);

  return matches;
}

/**
 * Hook para detectar si es dispositivo móvil
 */
export function useIsMobile() {
  return !useMediaQuery('md');
}

/**
 * Hook para detectar si es tablet
 */
export function useIsTablet() {
  const isMd = useMediaQuery('md');
  const isLg = useMediaQuery('lg');
  return isMd && !isLg;
}

/**
 * Hook para detectar si es desktop
 */
export function useIsDesktop() {
  return useMediaQuery('lg');
}

/**
 * Hook para obtener valores responsivos basados en el breakpoint actual
 */
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>) {
  const { currentBreakpoint } = useBreakpoint();
  
  // Orden de prioridad de breakpoints (de mayor a menor)
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  
  // Encontrar el valor más apropiado para el breakpoint actual
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  // Si no se encuentra ningún valor, devolver el primer valor disponible
  for (const bp of breakpointOrder) {
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return undefined;
}

/**
 * Hook para manejar grid columns responsivo
 */
export function useResponsiveColumns(config: {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}) {
  return useResponsiveValue(config) || 1;
}

/**
 * Hook para manejar espaciado responsivo
 */
export function useResponsiveSpacing(config: {
  xs?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  '2xl'?: string;
}) {
  return useResponsiveValue(config) || '1rem';
}

/**
 * Hook para detectar orientación del dispositivo
 */
export function useOrientation() {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    updateOrientation();
    window.addEventListener('resize', updateOrientation);
    
    return () => window.removeEventListener('resize', updateOrientation);
  }, []);

  return orientation;
}

/**
 * Hook para detectar si el usuario prefiere motion reducido
 */
export function usePrefersReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

/**
 * Hook para detectar el tema preferido del usuario
 */
export function usePrefersColorScheme() {
  const [colorScheme, setColorScheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setColorScheme(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (event: MediaQueryListEvent) => {
      setColorScheme(event.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return colorScheme;
}

/**
 * Hook para manejar container queries de manera programática
 */
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {
  const [containerWidth, setContainerWidth] = useState(0);
  const [containerBreakpoint, setContainerBreakpoint] = useState<Breakpoint>('xs');

  useEffect(() => {
    if (!containerRef.current) return;

    const updateContainerSize = () => {
      if (!containerRef.current) return;
      
      const width = containerRef.current.offsetWidth;
      setContainerWidth(width);

      // Determinar breakpoint del container
      if (width >= breakpoints['2xl']) {
        setContainerBreakpoint('2xl');
      } else if (width >= breakpoints.xl) {
        setContainerBreakpoint('xl');
      } else if (width >= breakpoints.lg) {
        setContainerBreakpoint('lg');
      } else if (width >= breakpoints.md) {
        setContainerBreakpoint('md');
      } else if (width >= breakpoints.sm) {
        setContainerBreakpoint('sm');
      } else {
        setContainerBreakpoint('xs');
      }
    };

    // Observer para cambios de tamaño del container
    const resizeObserver = new ResizeObserver(updateContainerSize);
    resizeObserver.observe(containerRef.current);

    // Ejecutar inmediatamente
    updateContainerSize();

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);

  return { containerWidth, containerBreakpoint };
}
