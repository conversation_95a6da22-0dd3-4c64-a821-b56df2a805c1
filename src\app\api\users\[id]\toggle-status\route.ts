import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/eccsa_back/models/User';

/**
 * PUT /api/users/[id]/toggle-status
 * Activa o desactiva un usuario
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    // Verificar si el usuario existe
    const existingUser = await UserModel.getById(id);

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Cambiar el estado del usuario
    const newStatus = !existingUser.activo;
    const updated = await UserModel.update(id, { activo: newStatus });

    if (!updated) {
      return NextResponse.json(
        { error: 'No se pudo actualizar el estado del usuario' },
        { status: 500 }
      );
    }

    // Obtener el usuario actualizado
    const updatedUser = await UserModel.getById(id);

    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: `Usuario ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error(`Error al cambiar estado del usuario con ID ${params.id}:`, error);

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
