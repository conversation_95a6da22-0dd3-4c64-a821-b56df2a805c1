import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/eccsa_back/lib/db';

// Datos mock para solicitudes
const mockSolicitudes = [
  {
    id: 1,
    numero_almacen: 'ALM001',
    modelo: 'Sensor de Proximidad XS18A4PAL2',
    marca: 'Schneider Electric',
    descripcion: 'Sensor de proximidad inductivo M18, alcance 8mm',
    estante: 'A1-B2',
    precio_unitario: 1250.00,
    cantidad_solicitada: 5,
    cantidad_disponible: 3,
    cliente_email: '<EMAIL>',
    cliente_whatsapp: '+5218112345678',
    estado: 'pendiente',
    alerta_stock: true,
    tiempo_entrega_estimado: '3-5 días hábiles',
    notas: null,
    fecha_solicitud: '2024-01-15T10:30:00Z',
    fecha_actualizacion: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    numero_almacen: 'ALM002',
    modelo: 'Contactor LC1D18BD',
    marca: 'Schneider Electric',
    descripcion: 'Contactor tripolar 18A, bobina 24VDC',
    estante: 'B2-C1',
    precio_unitario: 890.50,
    cantidad_solicitada: 2,
    cantidad_disponible: 8,
    cliente_email: '<EMAIL>',
    cliente_whatsapp: null,
    estado: 'procesando',
    alerta_stock: false,
    tiempo_entrega_estimado: '1-2 días hábiles',
    notas: 'Cliente requiere factura urgente',
    fecha_solicitud: '2024-01-14T14:20:00Z',
    fecha_actualizacion: '2024-01-15T09:15:00Z'
  },
  {
    id: 3,
    numero_almacen: 'ALM003',
    modelo: 'PLC Modicon M221',
    marca: 'Schneider Electric',
    descripcion: 'PLC compacto 16 E/S, alimentación 24VDC',
    estante: 'C1-D3',
    precio_unitario: 4500.00,
    cantidad_solicitada: 1,
    cantidad_disponible: 2,
    cliente_email: '<EMAIL>',
    cliente_whatsapp: '+5218187654321',
    estado: 'cotizado',
    alerta_stock: false,
    tiempo_entrega_estimado: '5-7 días hábiles',
    notas: 'Cotización enviada por email',
    fecha_solicitud: '2024-01-13T16:45:00Z',
    fecha_actualizacion: '2024-01-14T11:30:00Z'
  },
  {
    id: 4,
    numero_almacen: 'ALM004',
    modelo: 'Variador ATV12H075M2',
    marca: 'Schneider Electric',
    descripcion: 'Variador de frecuencia 0.75kW, 220V monofásico',
    estante: 'D1-E2',
    precio_unitario: 3200.00,
    cantidad_solicitada: 3,
    cantidad_disponible: 0,
    cliente_email: '<EMAIL>',
    cliente_whatsapp: '+5218198765432',
    estado: 'pendiente',
    alerta_stock: true,
    tiempo_entrega_estimado: '10-15 días hábiles',
    notas: null,
    fecha_solicitud: '2024-01-15T08:15:00Z',
    fecha_actualizacion: '2024-01-15T08:15:00Z'
  },
  {
    id: 5,
    numero_almacen: 'ALM005',
    modelo: 'Relé Térmico LRD10',
    marca: 'Schneider Electric',
    descripcion: 'Relé térmico 4-6A para contactor LC1D09',
    estante: 'A3-B1',
    precio_unitario: 450.00,
    cantidad_solicitada: 4,
    cantidad_disponible: 12,
    cliente_email: '<EMAIL>',
    cliente_whatsapp: null,
    estado: 'cotizado',
    alerta_stock: false,
    tiempo_entrega_estimado: '1-2 días hábiles',
    notas: 'Cliente frecuente - descuento aplicado',
    fecha_solicitud: '2024-01-12T13:20:00Z',
    fecha_actualizacion: '2024-01-13T10:45:00Z'
  }
];

// GET - Obtener todas las solicitudes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const estado = searchParams.get('estado');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let query = `
      SELECT
        s.*,
        a.modelo_existente,
        a.marcas,
        a.descripcion as descripcion_producto,
        a.cantidad_nuevo as stock_actual
      FROM solicitudes s
      LEFT JOIN almacen a ON s.numero_almacen = a.numero_almacen
    `;

    const queryParams: any[] = [];

    if (estado && estado !== 'todos') {
      query += ' WHERE s.estado = ?';
      queryParams.push(estado);
    }

    query += ' ORDER BY s.fecha_solicitud DESC LIMIT ? OFFSET ?';
    queryParams.push(limit, offset);

    const solicitudes = await executeQuery({
      query,
      values: queryParams
    });

    // Contar total de solicitudes
    let countQuery = 'SELECT COUNT(*) as total FROM solicitudes';
    const countParams: any[] = [];

    if (estado && estado !== 'todos') {
      countQuery += ' WHERE estado = ?';
      countParams.push(estado);
    }

    const countResult = await executeQuery({
      query: countQuery,
      values: countParams
    });

    const total = Array.isArray(countResult) ? countResult[0]?.total || 0 : 0;

    return NextResponse.json({
      success: true,
      solicitudes: Array.isArray(solicitudes) ? solicitudes : [],
      total,
      limit,
      offset
    });

  } catch (error) {
    console.error('Error al obtener solicitudes:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error al obtener solicitudes',
        solicitudes: [],
        total: 0
      },
      { status: 500 }
    );
  }
}

// POST - Crear nueva solicitud
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      numero_almacen,
      cantidad_solicitada,
      cliente_email,
      cliente_whatsapp,
      nombre,
      telefono,
      empresa,
      mensaje,
      productos // Array de productos desde el carrito
    } = body;

    // Validaciones para solicitud desde carrito
    if (productos && Array.isArray(productos) && productos.length > 0) {
      // Es una cotización desde el carrito con múltiples productos
      if (!cliente_email) {
        return NextResponse.json(
          {
            success: false,
            error: 'El email es obligatorio para cotización desde carrito'
          },
          { status: 400 }
        );
      }

      // Procesar cotización desde carrito (una sola solicitud)
      return await procesarCotizacionCarrito({
        cliente_email,
        cliente_whatsapp,
        nombre,
        productos
      });
    } else {
      // Es una solicitud individual
      if (!numero_almacen || !cantidad_solicitada || !cliente_email) {
        return NextResponse.json(
          {
            success: false,
            error: 'Faltan campos obligatorios: numero_almacen, cantidad_solicitada, cliente_email'
          },
          { status: 400 }
        );
      }
    }

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cliente_email)) {
      return NextResponse.json(
        { success: false, error: 'Email inválido' },
        { status: 400 }
      );
    }

    // Obtener información completa del producto del almacén
    const productoQuery = `
      SELECT
        numero_almacen,
        modelo_existente,
        marcas,
        descripcion,
        estante,
        precio_ml,
        cantidad_nuevo,
        tiempo_de_Entrega,
        Datos_importantes_Descripcion_muestra
      FROM almacen
      WHERE numero_almacen = ?
    `;

    const producto = await executeQuery({
      query: productoQuery,
      values: [numero_almacen]
    });

    if (!Array.isArray(producto) || producto.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Producto no encontrado en el almacén' },
        { status: 404 }
      );
    }

    const prod = producto[0];
    const stockDisponible = prod.cantidad_nuevo || 0;
    const alertaStock = cantidad_solicitada > stockDisponible;

    // Insertar solicitud con toda la información del producto
    const insertQuery = `
      INSERT INTO solicitudes (
        numero_almacen,
        modelo,
        marca,
        descripcion,
        estante,
        precio_unitario,
        cantidad_solicitada,
        cantidad_disponible,
        cliente_email,
        cliente_whatsapp,
        alerta_stock,
        tiempo_entrega_estimado,
        notas
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const descripcionCompleta = prod.Datos_importantes_Descripcion_muestra || prod.descripcion || '';
    const notasIniciales = alertaStock ?
      `ALERTA: Stock insuficiente. Solicitado: ${cantidad_solicitada}, Disponible: ${stockDisponible}` :
      null;

    const result = await executeQuery({
      query: insertQuery,
      values: [
        numero_almacen,
        prod.modelo_existente,
        prod.marcas || 'Sin marca',
        descripcionCompleta,
        prod.estante || 'Sin ubicación',
        prod.precio_ml || 0,
        cantidad_solicitada,
        stockDisponible,
        cliente_email,
        cliente_whatsapp || null,
        alertaStock,
        prod.tiempo_de_Entrega || 'Consultar',
        notasIniciales
      ]
    });

    const solicitudId = Array.isArray(result) && result[0] ? result[0].insertId : null;

    return NextResponse.json({
      success: true,
      message: 'Solicitud creada exitosamente',
      solicitud_id: solicitudId,
      alerta_stock: alertaStock,
      stock_disponible: stockDisponible,
      producto_info: {
        modelo: prod.modelo_existente,
        marca: prod.marcas || 'Sin marca',
        precio: prod.precio_ml || 0,
        estante: prod.estante || 'Sin ubicación'
      }
    });

  } catch (error) {
    console.error('Error al crear solicitud:', error);
    return NextResponse.json(
      { success: false, error: 'Error al crear solicitud' },
      { status: 500 }
    );
  }
}

// Función para procesar cotización desde el carrito
async function procesarCotizacionCarrito(data: {
  cliente_email: string;
  cliente_whatsapp?: string;
  nombre?: string;
  productos: Array<{
    numero_almacen: string;
    modelo: string;
    marca?: string;
    cantidad: number;
    precio?: number;
  }>;
}) {
  try {
    const { cliente_email, cliente_whatsapp, nombre, productos } = data;

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cliente_email)) {
      return NextResponse.json(
        { success: false, error: 'Email inválido' },
        { status: 400 }
      );
    }

    // Generar ID único para el grupo de carrito
    const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
    const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
    const solicitudGrupoId = `CART_${timestamp}_${randomSuffix}`;

    // Obtener información completa de todos los productos
    const productosCompletos = [];
    let totalProductos = 0;
    let totalCantidadSolicitada = 0;
    let productosConAlerta = 0;
    let totalSubtotal = 0;

    for (const producto of productos) {
      try {
        // Obtener información completa del producto del almacén
        const productoQuery = `
          SELECT
            numero_almacen,
            modelo_existente,
            marcas,
            descripcion,
            estante,
            precio_ml,
            cantidad_nuevo,
            tiempo_de_Entrega,
            Datos_importantes_Descripcion_muestra
          FROM almacen
          WHERE numero_almacen = ?
        `;

        const productoInfo = await executeQuery({
          query: productoQuery,
          values: [producto.numero_almacen]
        });

        if (!Array.isArray(productoInfo) || productoInfo.length === 0) {
          console.warn(`Producto no encontrado: ${producto.numero_almacen}`);
          continue;
        }

        const prod = productoInfo[0];
        const stockDisponible = prod.cantidad_nuevo || 0;
        const alertaStock = producto.cantidad > stockDisponible;
        const precioUnitario = prod.precio_ml || 0;
        const subtotalProducto = precioUnitario * producto.cantidad;

        if (alertaStock) {
          productosConAlerta++;
        }

        totalSubtotal += subtotalProducto;
        totalProductos++;
        totalCantidadSolicitada += producto.cantidad;

        // Agregar producto completo al array
        productosCompletos.push({
          numero_almacen: producto.numero_almacen,
          modelo: prod.modelo_existente,
          marca: prod.marcas || 'Sin marca',
          descripcion: prod.Datos_importantes_Descripcion_muestra || prod.descripcion || '',
          cantidad_solicitada: producto.cantidad,
          cantidad_disponible: stockDisponible,
          precio_unitario: precioUnitario,
          subtotal: subtotalProducto,
          alerta_stock: alertaStock,
          tiempo_entrega: prod.tiempo_de_Entrega || 'Consultar',
          estante: prod.estante || 'Sin ubicación'
        });

      } catch (error) {
        console.error(`Error procesando producto ${producto.numero_almacen}:`, error);
      }
    }

    if (productosCompletos.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No se pudieron procesar los productos del carrito' },
        { status: 400 }
      );
    }

    // Crear resumen de la cotización
    const totalConIVA = totalSubtotal * 1.16;
    const productosResumen = productosCompletos.map(p =>
      `• ${p.modelo} (${p.marca}) - ALM: ${p.numero_almacen} - Cantidad: ${p.cantidad_solicitada} - Subtotal: $${p.subtotal.toLocaleString('es-MX', { minimumFractionDigits: 2 })}`
    ).join('\n');

    const mensajeCompleto = `
COTIZACIÓN DESDE CARRITO DE COMPRAS
ID del Grupo: ${solicitudGrupoId}

DATOS DEL CLIENTE:
${nombre ? `Nombre: ${nombre}` : ''}
Email: ${cliente_email}
${cliente_whatsapp ? `WhatsApp: ${cliente_whatsapp}` : ''}

PRODUCTOS SOLICITADOS (${totalProductos} productos):
${productosResumen}

RESUMEN FINANCIERO:
Subtotal: $${totalSubtotal.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN
IVA (16%): $${(totalConIVA - totalSubtotal).toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN
Total: $${totalConIVA.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN

${productosConAlerta > 0 ? `⚠️ ALERTAS: ${productosConAlerta} producto${productosConAlerta > 1 ? 's tienen' : ' tiene'} stock limitado` : '✅ Todos los productos tienen stock disponible'}

---
Cotización generada desde el carrito de compras del sitio web.
Fecha: ${new Date().toLocaleString('es-MX')}
    `.trim();

    // Usar el primer producto como referencia principal
    const productoReferencia = productosCompletos[0];

    // Insertar UNA SOLA solicitud con toda la información
    const insertQuery = `
      INSERT INTO solicitudes (
        tipo_solicitud,
        solicitud_grupo_id,
        numero_almacen,
        modelo,
        marca,
        descripcion,
        estante,
        precio_unitario,
        cantidad_solicitada,
        cantidad_disponible,
        cliente_email,
        cliente_whatsapp,
        alerta_stock,
        tiempo_entrega_estimado,
        notas,
        productos_json,
        Nombre
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery({
      query: insertQuery,
      values: [
        'carrito',
        solicitudGrupoId,
        productoReferencia.numero_almacen,
        `CARRITO: ${totalProductos} productos`,
        'Múltiples marcas',
        `Cotización de carrito con ${totalProductos} productos diferentes`,
        'Múltiples ubicaciones',
        totalSubtotal,
        totalCantidadSolicitada,
        productosCompletos.reduce((sum, p) => sum + p.cantidad_disponible, 0),
        cliente_email,
        cliente_whatsapp || null,
        productosConAlerta > 0,
        'Según productos individuales',
        mensajeCompleto,
        JSON.stringify(productosCompletos),
        nombre || 'Cliente desde carrito'
      ]
    });

    const solicitudId = Array.isArray(result) && result[0] ? result[0].insertId : null;

    return NextResponse.json({
      success: true,
      message: `¡Cotización de carrito enviada exitosamente!`,
      solicitud_id: solicitudId,
      solicitud_grupo_id: solicitudGrupoId,
      resumen: {
        total_productos: totalProductos,
        productos_con_alerta: productosConAlerta,
        subtotal: totalSubtotal,
        total_con_iva: totalConIVA,
        cliente: {
          email: cliente_email,
          whatsapp: cliente_whatsapp || null
        }
      }
    });

  } catch (error) {
    console.error('Error al procesar cotización del carrito:', error);
    return NextResponse.json(
      { success: false, error: 'Error al procesar cotización del carrito' },
      { status: 500 }
    );
  }
}
