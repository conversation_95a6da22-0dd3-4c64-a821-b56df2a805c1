'use client';

import { useState, useEffect } from 'react';

// Definición de tipos para los productos del almacén
interface AlmacenProduct {
  id: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  marcas?: string;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

export default function ProductosPage() {
  const [products, setProducts] = useState<AlmacenProduct[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  // Estados para filtros y paginación
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('Todos');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [categories, setCategories] = useState<string[]>([]);
  const itemsPerPage = 10;

  // Cargar productos del almacén
  const loadProducts = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Cargando productos del almacén...');
      const response = await fetch('/api/almacen');
      const data = await response.json();

      console.log('📦 Respuesta de la API:', data);

      if (response.ok) {
        const almacenProductsData = data.products || [];
        console.log('🏪 Productos del almacén:', almacenProductsData);
        console.log('📊 Cantidad de productos:', almacenProductsData.length);

        setProducts(almacenProductsData);

        // Extraer categorías únicas de las marcas
        const uniqueCategories = Array.from(
          new Set(
            almacenProductsData
              .map((product: AlmacenProduct) => product.marcas)
              .filter((marca: string | undefined) => marca && marca.trim() !== '')
          )
        ) as string[];

        setCategories(uniqueCategories);
        console.log('📂 Categorías encontradas:', uniqueCategories);
      } else {
        console.error('❌ Error en la respuesta:', data);
        setError(data.error || 'Error al cargar productos');
      }
    } catch (err) {
      console.error('💥 Error de conexión:', err);
      setError('Error de conexión al cargar productos');
    } finally {
      setLoading(false);
    }
  };

  // Cargar productos al montar el componente
  useEffect(() => {
    console.log('🚀 Componente ProductosPage montado, cargando productos...');
    loadProducts();
  }, []);

  // Función para formatear precio
  const formatPrice = (product: AlmacenProduct): string => {
    // Priorizar precio_ml sobre precio_venta
    const precioBase = product.precio_ml || product.precio_venta;

    if (precioBase && Number(precioBase) > 0) {
      // Restar el 16% de IVA del precio
      const precioSinIVA = Number(precioBase) / 1.16;
      return new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(precioSinIVA);
    }

    return "Consultar precio";
  };

  // Función para agregar al carrito
  const addToCart = (product: AlmacenProduct) => {
    const savedCart = localStorage.getItem('eccsa-cart');
    let cartItems = [];

    if (savedCart) {
      try {
        cartItems = JSON.parse(savedCart);
      } catch (error) {
        console.error('Error parsing cart:', error);
        cartItems = [];
      }
    }

    // Calcular precio sin IVA para el carrito (priorizar precio_ml)
    const precioBase = product.precio_ml || product.precio_venta;
    const precioSinIVA = precioBase ? Number(precioBase) / 1.16 : 0;

    const cartItem = {
      id: product.id,
      numero_almacen: product.numero_almacen,
      modelo_existente: product.modelo_existente,
      descripcion: product.Datos_importantes_Descripcion_muestra || product.descripcion,
      Datos_importantes_Descripcion_muestra: product.Datos_importantes_Descripcion_muestra,
      tiempo_de_Entrega: product.tiempo_de_Entrega,
      precio_ml: precioSinIVA,
      marcas: product.marcas,
      Url_imagen: product.Url_imagen,
      stock_disponible: product.cantidad_nuevo || 0,
      cantidad: 1,
      sin_stock: (product.cantidad_nuevo || 0) === 0,
      mensaje_stock: (product.cantidad_nuevo || 0) === 0 ? 'No hay stock - Se cotiza tiempo de entrega' : undefined
    };

    // Buscar si el item ya existe
    const existingItemIndex = cartItems.findIndex((item: any) => item.id === product.id);

    if (existingItemIndex >= 0) {
      // Si existe, incrementar cantidad
      cartItems[existingItemIndex].cantidad += 1;
    } else {
      // Si no existe, agregarlo
      cartItems.push(cartItem);
    }

    // Guardar en localStorage
    localStorage.setItem('eccsa-cart', JSON.stringify(cartItems));

    // Disparar eventos
    window.dispatchEvent(new CustomEvent('cartUpdated'));
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('openCart'));
    }, 50);

    console.log('Producto agregado al carrito:', product.modelo_existente);

    // Mostrar mensaje informativo si no hay stock
    if ((product.cantidad_nuevo || 0) === 0) {
      alert('Producto agregado al carrito. No hay stock disponible - Se cotizará tiempo de entrega.');
    }
  };

  // Filtrar productos por búsqueda y categoría
  const filteredProducts = products.filter(product => {
    const matchesSearch = searchTerm === '' ||
      product.modelo_existente.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.descripcion?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.Datos_importantes_Descripcion_muestra?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.numero_almacen.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.marcas?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === 'Todos' || product.marcas === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Calcular paginación
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  // Resetear página cuando cambian los filtros
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setCurrentPage(1);
  };

  // Funciones de paginación
  const goToPage = (page: number) => {
    setCurrentPage(page);
    // Scroll hacia arriba cuando cambia la página
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  // Mostrar estado de carga
  if (loading) {
    return (
      <div style={{ 
        marginTop: '120px', 
        padding: '2rem', 
        textAlign: 'center',
        minHeight: '50vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #0056a6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          marginBottom: '1rem'
        }}></div>
        <h1 style={{ color: '#333', marginBottom: '1rem' }}>Cargando Productos</h1>
        <p style={{ color: '#666' }}>Obteniendo productos del almacén...</p>
      </div>
    );
  }

  // Mostrar error si hay alguno
  if (error) {
    return (
      <div style={{ 
        marginTop: '120px', 
        padding: '2rem', 
        textAlign: 'center',
        minHeight: '50vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h1 style={{ color: '#333', marginBottom: '1rem' }}>Error al Cargar Productos</h1>
        <p style={{ color: 'red', marginBottom: '2rem' }}>{error}</p>
        <button 
          onClick={loadProducts} 
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#0056a6',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div style={{
      marginTop: '0px',
      backgroundColor: '#f8fafc',
      minHeight: '100vh',
      paddingTop: '92px' // Compensar altura del navbar fijo
    }}>
      {/* Hero Section */}
      <div style={{
        background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
        color: 'white',
        padding: '4rem 2rem 3rem 2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          right: '-50px',
          width: '200px',
          height: '200px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '-30px',
          left: '-30px',
          width: '150px',
          height: '150px',
          background: 'rgba(255,255,255,0.05)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}>
          {/* Badge */}
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            backgroundColor: 'rgba(255,255,255,0.2)',
            padding: '0.5rem 1rem',
            borderRadius: '25px',
            marginBottom: '1.5rem',
            fontSize: '0.9rem',
            fontWeight: '600'
          }}>
            <span>⚡</span>
            <span>Catálogo Profesional</span>
          </div>

          <h1 style={{
            fontSize: '3rem',
            marginBottom: '1rem',
            fontWeight: 'bold',
            lineHeight: '1.2'
          }}>
            Catálogo de <span style={{ color: '#f7941d' }}>Productos</span>
          </h1>

          <p style={{
            fontSize: '1.3rem',
            opacity: '0.9',
            maxWidth: '700px',
            margin: '0 auto 2rem auto',
            lineHeight: '1.6'
          }}>
            Descubre nuestra amplia gama de productos de automatización industrial de las mejores marcas del mercado
          </p>

          {/* Stats */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '3rem',
            flexWrap: 'wrap',
            marginTop: '2rem'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: '#f7941d',
                marginBottom: '0.5rem'
              }}>
                {filteredProducts.length}
              </div>
              <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                Productos{searchTerm || selectedCategory !== 'Todos' ? ' Filtrados' : ''}
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: '#f7941d',
                marginBottom: '0.5rem'
              }}>
                {categories.length}+
              </div>
              <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                Marcas
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: '#f7941d',
                marginBottom: '0.5rem'
              }}>
                25+
              </div>
              <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                Años de Experiencia
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filtros de Búsqueda */}
      <div style={{
        backgroundColor: 'white',
        padding: '2.5rem 2rem',
        margin: '-2rem auto 3rem auto',
        borderRadius: '20px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
        maxWidth: '1200px',
        position: 'relative',
        zIndex: 10,
        border: '1px solid rgba(0,86,166,0.1)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            width: '50px',
            height: '50px',
            backgroundColor: '#0056a6',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.5rem'
          }}>
            🔍
          </div>
          <div>
            <h3 style={{
              fontSize: '1.5rem',
              color: '#0056a6',
              margin: '0',
              fontWeight: 'bold'
            }}>
              Filtros Inteligentes
            </h3>
            <p style={{
              fontSize: '1rem',
              color: '#666',
              margin: '0.25rem 0 0 0'
            }}>
              Encuentra productos específicos con precisión
            </p>
          </div>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '2rem',
          alignItems: 'end'
        }}>
          {/* Búsqueda por texto */}
          <div style={{ position: 'relative' }}>
            <label style={{
              display: 'block',
              marginBottom: '0.75rem',
              fontWeight: '600',
              color: '#333',
              fontSize: '1rem'
            }}>
              🔎 Buscar producto:
            </label>
            <div style={{ position: 'relative' }}>
              <input
                type="text"
                placeholder="Buscar por nombre, descripción, marca..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                style={{
                  width: '100%',
                  padding: '1rem 1rem 1rem 3rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '12px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  boxSizing: 'border-box',
                  backgroundColor: '#f8fafc',
                  outline: 'none'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#0056a6';
                  e.target.style.backgroundColor = 'white';
                  e.target.style.boxShadow = '0 0 0 3px rgba(0,86,166,0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e2e8f0';
                  e.target.style.backgroundColor = '#f8fafc';
                  e.target.style.boxShadow = 'none';
                }}
              />
              <div style={{
                position: 'absolute',
                left: '1rem',
                top: '50%',
                transform: 'translateY(-50%)',
                color: '#64748b',
                fontSize: '1.2rem',
                pointerEvents: 'none'
              }}>
                🔍
              </div>
              {searchTerm && (
                <button
                  onClick={() => handleSearchChange('')}
                  style={{
                    position: 'absolute',
                    right: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    color: '#64748b',
                    cursor: 'pointer',
                    fontSize: '1.2rem',
                    padding: '0.25rem',
                    borderRadius: '50%',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                    e.currentTarget.style.color = '#dc3545';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#64748b';
                  }}
                >
                  ✕
                </button>
              )}
            </div>
          </div>

          {/* Filtro por categoría */}
          <div style={{ position: 'relative' }}>
            <label style={{
              display: 'block',
              marginBottom: '0.75rem',
              fontWeight: '600',
              color: '#333',
              fontSize: '1rem'
            }}>
              🏷️ Filtrar por marca:
            </label>
            <div style={{ position: 'relative' }}>
              <select
                value={selectedCategory}
                onChange={(e) => handleCategoryChange(e.target.value)}
                style={{
                  width: '100%',
                  padding: '1rem 3rem 1rem 1rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '12px',
                  fontSize: '1rem',
                  backgroundColor: '#f8fafc',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  boxSizing: 'border-box',
                  outline: 'none',
                  appearance: 'none'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#0056a6';
                  e.target.style.backgroundColor = 'white';
                  e.target.style.boxShadow = '0 0 0 3px rgba(0,86,166,0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e2e8f0';
                  e.target.style.backgroundColor = '#f8fafc';
                  e.target.style.boxShadow = 'none';
                }}
              >
                <option value="Todos">Todas las marcas</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <div style={{
                position: 'absolute',
                right: '1rem',
                top: '50%',
                transform: 'translateY(-50%)',
                color: '#64748b',
                fontSize: '1rem',
                pointerEvents: 'none'
              }}>
                ▼
              </div>
            </div>
          </div>

          {/* Botón limpiar filtros */}
          {(searchTerm || selectedCategory !== 'Todos') && (
            <div style={{ display: 'flex', alignItems: 'end' }}>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('Todos');
                  setCurrentPage(1);
                }}
                style={{
                  padding: '1rem 2rem',
                  background: 'linear-gradient(135deg, #ef4444, #dc2626)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.5rem',
                  boxShadow: '0 4px 12px rgba(239,68,68,0.3)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(239,68,68,0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(239,68,68,0.3)';
                }}
              >
                <span style={{ fontSize: '1.2rem' }}>🗑️</span>
                <span>Limpiar Filtros</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Products Grid */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 2rem'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
          gap: '2rem',
          marginBottom: '3rem'
        }}>
        {currentProducts.map((product) => (
          <div key={product.id} style={{
            backgroundColor: 'white',
            borderRadius: '20px',
            overflow: 'hidden',
            boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
            border: '1px solid #f1f5f9',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            height: '100%'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-8px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.12)';
            e.currentTarget.style.borderColor = '#e2e8f0';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
            e.currentTarget.style.borderColor = '#f1f5f9';
          }}>
            
            {/* Product Image */}
            <div style={{
              height: '240px',
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              overflow: 'hidden',
              padding: '1.5rem'
            }}>
              {product.Url_imagen ? (
                <img
                  src={product.Url_imagen}
                  alt={product.modelo_existente}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                    transition: 'transform 0.4s ease'
                  }}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
              ) : (
                <img
                  src="/images/logos/logo_pequeno.png"
                  alt={product.modelo_existente}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                    transition: 'transform 0.4s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.05)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
              )}

              {/* Stock Badge */}
              <div style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                padding: '0.375rem 0.75rem',
                borderRadius: '20px',
                fontSize: '0.75rem',
                fontWeight: '600',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                background: (product.cantidad_nuevo || 0) > 0
                  ? 'linear-gradient(135deg, #10b981, #059669)'
                  : 'linear-gradient(135deg, #ef4444, #dc2626)',
                color: 'white',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                zIndex: 10
              }}>
                {(product.cantidad_nuevo || 0) > 0 ? `${product.cantidad_nuevo} DISPONIBLES` : 'SIN STOCK'}
              </div>

              {/* Quick Actions */}
              <div
                style={{
                  position: 'absolute',
                  top: '1rem',
                  left: '1rem',
                  display: 'flex',
                  gap: '0.5rem',
                  opacity: 0,
                  transform: 'translateY(-10px)',
                  transition: 'all 0.3s ease',
                  zIndex: 10
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '1';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <button style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  border: 'none',
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  color: '#0056a6',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.2rem',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#0056a6';
                  e.currentTarget.style.color = 'white';
                  e.currentTarget.style.transform = 'scale(1.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.9)';
                  e.currentTarget.style.color = '#0056a6';
                  e.currentTarget.style.transform = 'scale(1)';
                }}>
                  👁️
                </button>
                <button style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  border: 'none',
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  color: '#0056a6',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.2rem',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#0056a6';
                  e.currentTarget.style.color = 'white';
                  e.currentTarget.style.transform = 'scale(1.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.9)';
                  e.currentTarget.style.color = '#0056a6';
                  e.currentTarget.style.transform = 'scale(1)';
                }}>
                  ❤️
                </button>
              </div>
            </div>

            {/* Product Info */}
            <div style={{ padding: '1.5rem', flex: 1, display: 'flex', flexDirection: 'column' }}>
              {/* Category */}
              {product.marcas && (
                <div style={{
                  display: 'inline-block',
                  padding: '0.375rem 1rem',
                  background: 'linear-gradient(135deg, #0056a6, #003366)',
                  color: 'white',
                  borderRadius: '25px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  marginBottom: '1rem',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  alignSelf: 'flex-start'
                }}>
                  {product.marcas}
                </div>
              )}

              {/* Product Name */}
              <h3 style={{
                fontSize: '1.2rem',
                fontWeight: '700',
                color: '#1e293b',
                marginBottom: '0.75rem',
                lineHeight: '1.4',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {product.modelo_existente}
              </h3>



              {/* Description */}
              {(product.Datos_importantes_Descripcion_muestra || product.descripcion) && (
                <p style={{
                  fontSize: '0.9rem',
                  color: '#64748b',
                  marginBottom: '1rem',
                  lineHeight: '1.6',
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  flex: 1
                }}>
                  {product.Datos_importantes_Descripcion_muestra || product.descripcion}
                </p>
              )}

              {/* Tiempo de Entrega */}
              {product.tiempo_de_Entrega && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '1.5rem',
                  padding: '0.75rem',
                  backgroundColor: '#f0f9ff',
                  borderRadius: '8px',
                  border: '1px solid #e0f2fe'
                }}>
                  <span style={{
                    fontSize: '1rem',
                    color: '#0056a6'
                  }}>🚚</span>
                  <span style={{
                    fontSize: '0.85rem',
                    color: '#0056a6',
                    fontWeight: '600'
                  }}>
                    Tiempo de entrega: {product.tiempo_de_Entrega}
                  </span>
                </div>
              )}

              {/* Shipping Info */}
              <div style={{
                marginBottom: '1.5rem',
                padding: '1rem',
                backgroundColor: '#f8fafc',
                borderRadius: '12px',
                border: '1px solid #e2e8f0'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.85rem',
                  color: '#64748b',
                  marginBottom: '0.5rem',
                  gap: '0.5rem'
                }}>
                  <span style={{
                    fontSize: '1rem',
                    width: '20px',
                    textAlign: 'center'
                  }}>🚚</span>
                  <span>Envío no incluido</span>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.85rem',
                  color: '#64748b',
                  gap: '0.5rem'
                }}>
                  <span style={{
                    fontSize: '1rem',
                    width: '20px',
                    textAlign: 'center'
                  }}>📍</span>
                  <span>Recoger en tienda</span>
                </div>
              </div>

              {/* Price */}
              <div style={{
                marginBottom: '1.5rem',
                padding: '1rem',
                backgroundColor: '#f0f9ff',
                borderRadius: '12px',
                border: '2px solid #e0f2fe',
                textAlign: 'center'
              }}>
                <div style={{
                  fontSize: '1.75rem',
                  fontWeight: '700',
                  color: '#0056a6',
                  marginBottom: '0.25rem'
                }}>
                  {formatPrice(product)}
                </div>
                <div style={{
                  fontSize: '0.8rem',
                  color: '#64748b',
                  fontWeight: '500'
                }}>
                  Precio sin IVA
                </div>
              </div>

              {/* Actions */}
              <div style={{
                display: 'flex',
                gap: '0.75rem',
                marginTop: 'auto'
              }}>
                <button
                  onClick={() => addToCart(product)}
                  style={{
                    flex: 1,
                    padding: '1rem',
                    background: 'linear-gradient(135deg, #0056a6, #003366)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem',
                    boxShadow: '0 4px 12px rgba(0,86,166,0.3)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,86,166,0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,86,166,0.3)';
                  }}
                >
                  <span style={{ fontSize: '1.1rem' }}>🛒</span>
                  <span>Agregar al Carrito</span>
                </button>

                <button
                  style={{
                    padding: '1rem',
                    backgroundColor: 'transparent',
                    color: '#0056a6',
                    border: '2px solid #0056a6',
                    borderRadius: '12px',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    minWidth: '60px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#0056a6';
                    e.currentTarget.style.color = 'white';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#0056a6';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                  title="Solicitar cotización"
                >
                  💬
                </button>
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '1rem',
          margin: '3rem auto',
          padding: '2rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          maxWidth: '1200px'
        }}>
          {/* Botón anterior */}
          <button
            onClick={goToPreviousPage}
            disabled={currentPage === 1}
            style={{
              padding: '0.75rem 1rem',
              backgroundColor: currentPage === 1 ? '#e0e0e0' : '#0056a6',
              color: currentPage === 1 ? '#999' : 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
              transition: 'background-color 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
            onMouseEnter={(e) => {
              if (currentPage !== 1) {
                e.currentTarget.style.backgroundColor = '#003d7a';
              }
            }}
            onMouseLeave={(e) => {
              if (currentPage !== 1) {
                e.currentTarget.style.backgroundColor = '#0056a6';
              }
            }}
          >
            ← Anterior
          </button>

          {/* Información de página */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '1rem',
            color: '#333'
          }}>
            <span>Página</span>
            <strong style={{ color: '#0056a6' }}>{currentPage}</strong>
            <span>de</span>
            <strong style={{ color: '#0056a6' }}>{totalPages}</strong>
          </div>

          {/* Números de página */}
          <div style={{
            display: 'flex',
            gap: '0.5rem'
          }}>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNumber: number;
              if (totalPages <= 5) {
                pageNumber = i + 1;
              } else if (currentPage <= 3) {
                pageNumber = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNumber = totalPages - 4 + i;
              } else {
                pageNumber = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNumber}
                  onClick={() => goToPage(pageNumber)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    backgroundColor: currentPage === pageNumber ? '#0056a6' : 'transparent',
                    color: currentPage === pageNumber ? 'white' : '#0056a6',
                    border: '2px solid #0056a6',
                    borderRadius: '6px',
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    minWidth: '40px'
                  }}
                  onMouseEnter={(e) => {
                    if (currentPage !== pageNumber) {
                      e.currentTarget.style.backgroundColor = '#0056a6';
                      e.currentTarget.style.color = 'white';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentPage !== pageNumber) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#0056a6';
                    }
                  }}
                >
                  {pageNumber}
                </button>
              );
            })}
          </div>

          {/* Botón siguiente */}
          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
            style={{
              padding: '0.75rem 1rem',
              backgroundColor: currentPage === totalPages ? '#e0e0e0' : '#0056a6',
              color: currentPage === totalPages ? '#999' : 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
              transition: 'background-color 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
            onMouseEnter={(e) => {
              if (currentPage !== totalPages) {
                e.currentTarget.style.backgroundColor = '#003d7a';
              }
            }}
            onMouseLeave={(e) => {
              if (currentPage !== totalPages) {
                e.currentTarget.style.backgroundColor = '#0056a6';
              }
            }}
          >
            Siguiente →
          </button>
        </div>
      )}

      {/* Información de resultados */}
      {filteredProducts.length > 0 && (
        <div style={{
          textAlign: 'center',
          margin: '2rem auto',
          padding: '1rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          maxWidth: '1200px',
          color: '#666'
        }}>
          Mostrando {startIndex + 1} - {Math.min(endIndex, filteredProducts.length)} de {filteredProducts.length} productos
          {searchTerm || selectedCategory !== 'Todos' ? ' (filtrados)' : ''}
        </div>
      )}

      {/* No products message */}
      {filteredProducts.length === 0 && !loading && !error && (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          margin: '2rem auto',
          maxWidth: '600px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔍</div>
          <h3 style={{ color: '#666', marginBottom: '1rem' }}>
            {searchTerm || selectedCategory !== 'Todos'
              ? 'No se encontraron productos con los filtros aplicados'
              : 'No se encontraron productos'
            }
          </h3>
          <p style={{ color: '#999', marginBottom: '2rem' }}>
            {searchTerm || selectedCategory !== 'Todos'
              ? 'Intenta cambiar los filtros de búsqueda o limpiar los filtros para ver todos los productos.'
              : 'No hay productos disponibles en el almacén en este momento.'
            }
          </p>
          {(searchTerm || selectedCategory !== 'Todos') && (
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('Todos');
                setCurrentPage(1);
              }}
              style={{
                padding: '0.75rem 1.5rem',
                backgroundColor: '#0056a6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#003d7a';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#0056a6';
              }}
            >
              🗑️ Limpiar Filtros
            </button>
          )}
        </div>
      )}

      {/* CSS for animations and responsive design */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
          .hero-stats {
            flex-direction: column !important;
            gap: 2rem !important;
          }

          .filters-grid {
            grid-template-columns: 1fr !important;
            gap: 1.5rem !important;
          }

          .products-grid {
            grid-template-columns: 1fr !important;
            gap: 1.5rem !important;
          }

          .product-actions {
            flex-direction: column !important;
          }

          .pagination-container {
            flex-direction: column !important;
            gap: 1rem !important;
          }

          .pagination-numbers {
            order: -1 !important;
          }
        }

        @media (max-width: 480px) {
          .hero-section {
            padding: 2rem 1rem !important;
          }

          .hero-title {
            font-size: 2rem !important;
          }

          .filters-section {
            margin: -1rem auto 2rem auto !important;
            padding: 1.5rem 1rem !important;
          }

          .product-card {
            margin: 0 auto !important;
            max-width: 350px !important;
          }
        }
      `}</style>
    </div>
  );
}
