'use client';

import { useState, useEffect } from 'react';
import AdminSidebar from '@/components/AdminSidebar';
import AuthGuard from '@/components/AuthGuard';

interface User {
  id?: number;
  username: string;
  name?: string;
  role?: string;
  role_level?: number;
  email?: string;
  photo?: string;
  phone?: string;
  description?: string;
}

export default function PerfilPage() {
  const [userData, setUserData] = useState<User | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Cargar los datos del usuario desde localStorage
  useEffect(() => {
    const savedUserData = localStorage.getItem('eccsaUserData');
    if (savedUserData) {
      try {
        const parsedUserData = JSON.parse(savedUserData);
        setUserData(parsedUserData);
        setFormData(parsedUserData);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => prev ? { ...prev, [name]: value } : null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData || !userData?.id) return;

    // Validar contraseñas si se están cambiando
    if (password || confirmPassword) {
      if (password !== confirmPassword) {
        setPasswordError('Las contraseñas no coinciden');
        return;
      }

      if (password.length < 6) {
        setPasswordError('La contraseña debe tener al menos 6 caracteres');
        return;
      }
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');
    setPasswordError('');

    try {
      // Preparar los datos para enviar a la API
      const dataToUpdate: any = {
        nombre: formData.name,
        nombre_usuario: formData.username,
        correo: formData.email,
        telefono: formData.phone,
        foto_usuario: formData.photo
      };

      // Agregar contraseña solo si se proporcionó una nueva
      if (password) {
        dataToUpdate.contrasena = password;
      }

      console.log('Actualizando perfil:', dataToUpdate);

      // Llamar a la API para actualizar los datos del usuario
      const response = await fetch(`/api/users/${userData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToUpdate),
      });

      const result = await response.json();
      console.log('Respuesta de la API:', result);

      if (response.ok && result.success) {
        // Mapear los datos del backend al formato del frontend
        const updatedUser = {
          id: result.data.id,
          username: result.data.nombre_usuario,
          name: result.data.nombre,
          email: result.data.correo,
          phone: result.data.telefono,
          role: userData.role, // Mantener el rol actual
          role_level: userData.role_level, // Mantener el nivel actual
          photo: result.data.foto_usuario,
          description: userData.description // Mantener la descripción actual
        };

        // Actualizar localStorage
        localStorage.setItem('eccsaUserData', JSON.stringify(updatedUser));

        setUserData(updatedUser);
        setIsEditing(false);
        setSuccessMessage('Perfil actualizado correctamente');

        // Limpiar campos de contraseña
        setPassword('');
        setConfirmPassword('');

        // Ocultar el mensaje de éxito después de 3 segundos
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } else {
        setError(result.error || 'Error al actualizar el perfil');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Error de conexión al servidor. Intente nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData(userData);
    setIsEditing(false);
    setError('');
    setPasswordError('');
    setPassword('');
    setConfirmPassword('');
  };

  const getRoleName = (roleLevel: number | undefined) => {
    if (roleLevel === undefined) return 'Usuario';

    switch (roleLevel) {
      case 0: return 'Administrador';
      case 1: return 'Jefe';
      case 2: return 'Senior';
      case 3: return 'Semi-Senior';
      case 4: return 'Junior';
      default: return 'Usuario';
    }
  };

  return (
    <AuthGuard>
      <div className="admin-layout">
        <AdminSidebar />
        <div className="admin-content">
          <div className="admin-dashboard">
            <h1 className="admin-title">Perfil de Usuario</h1>

          {userData ? (
            <div className="profile-container">
              {successMessage && (
                <div className="profile-success-message">
                  {successMessage}
                </div>
              )}

              {error && (
                <div className="profile-error-message">
                  {error}
                </div>
              )}

              <div className="profile-header">
                <div className="profile-avatar">
                  {userData.photo ? (
                    <img
                      src={userData.photo}
                      alt={userData.name || userData.username}
                    />
                  ) : (
                    <div className="profile-avatar-placeholder">
                      {(userData.name || userData.username).charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>

                <div className="profile-info">
                  <h2 className="profile-name">{userData.name || userData.username}</h2>
                  <p className="profile-role">{getRoleName(userData.role_level)}</p>

                  {!isEditing && (
                    <button
                      className="profile-edit-button"
                      onClick={() => setIsEditing(true)}
                    >
                      Editar Perfil
                    </button>
                  )}
                </div>
              </div>

              {isEditing ? (
                <form className="profile-form" onSubmit={handleSubmit}>
                  <div className="profile-form-group">
                    <label htmlFor="name">Nombre Completo</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData?.name || ''}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="profile-form-group">
                    <label htmlFor="email">Correo Electrónico</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData?.email || ''}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="profile-form-group">
                    <label htmlFor="phone">Teléfono</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData?.phone || ''}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="profile-form-group">
                    <label htmlFor="photo">URL de Foto</label>
                    <input
                      type="url"
                      id="photo"
                      name="photo"
                      value={formData?.photo || ''}
                      onChange={handleInputChange}
                      placeholder="https://ejemplo.com/foto.jpg"
                    />
                  </div>

                  {/* Campos para cambiar la contraseña */}
                  <div className="profile-form-group">
                    <label htmlFor="password">Nueva Contraseña (dejar en blanco para mantener la actual)</label>
                    <input
                      type="password"
                      id="password"
                      name="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>

                  <div className="profile-form-group">
                    <label htmlFor="confirmPassword">Confirmar Nueva Contraseña</label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                    {passwordError && <div className="profile-form-error">{passwordError}</div>}
                  </div>

                  {/* La descripción no se puede editar */}
                  {userData?.description && (
                    <div className="profile-form-group profile-readonly">
                      <label htmlFor="description">Descripción (no editable)</label>
                      <div className="profile-readonly-value">{userData.description}</div>
                    </div>
                  )}

                  <div className="profile-form-actions">
                    <button
                      type="button"
                      className="profile-cancel-button"
                      onClick={handleCancel}
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      className="profile-save-button"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Guardando...' : 'Guardar Cambios'}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="profile-details">
                  <div className="profile-detail-item">
                    <div className="profile-detail-label">Nombre de Usuario</div>
                    <div className="profile-detail-value">{userData.username}</div>
                  </div>

                  <div className="profile-detail-item">
                    <div className="profile-detail-label">Correo Electrónico</div>
                    <div className="profile-detail-value">{userData.email || 'No especificado'}</div>
                  </div>

                  <div className="profile-detail-item">
                    <div className="profile-detail-label">Teléfono</div>
                    <div className="profile-detail-value">{userData.phone || 'No especificado'}</div>
                  </div>

                  <div className="profile-detail-item">
                    <div className="profile-detail-label">Nivel</div>
                    <div className="profile-detail-value">{getRoleName(userData.role_level)}</div>
                  </div>

                  {userData.description && (
                    <div className="profile-detail-item profile-description">
                      <div className="profile-detail-label">Descripción</div>
                      <div className="profile-detail-value">{userData.description}</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="profile-not-found">
              <p>No se encontró información del usuario. Por favor, inicie sesión nuevamente.</p>
            </div>
          )}
        </div>
      </div>
      </div>
    </AuthGuard>
  );
}
