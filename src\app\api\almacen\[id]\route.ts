import { NextRequest, NextResponse } from 'next/server';
import {
  getAlmacenProductById,
  updateAlmacenProduct,
  deleteAlmacenProduct,
  deleteAlmacenProductForever,
  getAlmacenProductByNumber
} from '@/eccsa_back/lib/almacen';

// GET handler to fetch a specific almacen product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid product ID' },
        { status: 400 }
      );
    }

    const product = await getAlmacenProductById(id);

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error('Error fetching almacen product:', error);
    return NextResponse.json(
      { error: 'Failed to fetch almacen product' },
      { status: 500 }
    );
  }
}

// PUT handler to update a specific almacen product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid product ID' },
        { status: 400 }
      );
    }

    const body = await request.json();



    // Check if numero_almacen already exists (excluding current product)
    if (body.numero_almacen) {
      const existingProduct = await getAlmacenProductByNumber(body.numero_almacen);
      if (existingProduct && existingProduct.id !== id) {
        return NextResponse.json(
          { error: `El número de almacén "${body.numero_almacen}" ya existe. Por favor, use un número diferente.` },
          { status: 400 }
        );
      }
    }

    const success = await updateAlmacenProduct(id, {
      numero_almacen: body.numero_almacen,
      estante: body.estante,
      modelo_existente: body.modelo_existente,
      descripcion: body.descripcion,
      precio_venta: body.precio_venta,
      precio_ml: body.precio_ml,
      vendidos: body.vendidos,
      cantidad_nuevo: body.cantidad_nuevo,
      minimo: body.minimo,
      maximo: body.maximo,
      pedir_cantidad: body.pedir_cantidad,
      precio_us: body.precio_us,
      precio_mx: body.precio_mx,
      impuesto: body.impuesto,
      codigo_sat: body.codigo_sat,
      nota: body.nota,
      proveedor: body.proveedor,
      marcas: body.marcas,
      tiempo_entrega_proveedor: body.tiempo_entrega_proveedor,
      fecha_pedido: body.fecha_pedido,
      fecha_recibido: body.fecha_recibido,
      activo: body.activo,
      Url_imagen: body.Url_imagen,
      Datos_importantes_Descripcion_muestra: body.Datos_importantes_Descripcion_muestra,
      tiempo_de_Entrega: body.tiempo_de_Entrega,
    });

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update almacen product' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating almacen product:', error);
    return NextResponse.json(
      { error: 'Failed to update almacen product' },
      { status: 500 }
    );
  }
}

// DELETE handler to permanently delete a specific almacen product from database
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid product ID' },
        { status: 400 }
      );
    }

    // Verificar si el producto existe antes de eliminarlo
    const existingProduct = await getAlmacenProductById(id);
    if (!existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Eliminar permanentemente el producto de la base de datos
    const success = await deleteAlmacenProductForever(id);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to permanently delete almacen product' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Product permanently deleted from database'
    });
  } catch (error) {
    console.error('Error permanently deleting almacen product:', error);
    return NextResponse.json(
      { error: 'Failed to permanently delete almacen product' },
      { status: 500 }
    );
  }
}
