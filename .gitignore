# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/dist

# deployment files
/pagiana
*.zip
*.tar.gz
eccsa-web.zip
eccsa-web.tar.gz
/espera
INSTRUCCIONES_DESPLIEGUE.md
index.html
deploy-complete.php
/back-eccsa/deploy/build-and-deploy.js
/back-eccsa/deploy/build-deploy.ps1
/back-eccsa/deploy/deploy-to-cpanel.js
/back-eccsa/deploy/deploy-cpanel.ps1
/back-eccsa/deploy/deploy-full.ps1
/back-eccsa/deploy/create-zip.js
/back-eccsa/deploy/create-zip.ps1
/back-eccsa/deploy/verify-zip.js
/back-eccsa/deploy/verify-images.js

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# cPanel
.cpanel.yml
.htaccess

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE specific files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# OS specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs
logs
*.log

# Cache directories
.npm
.eslintcache
.stylelintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Temporary directories
/temp
/tmp
/temp_extract

# Backup files
*.bak
*.backup
*~
*.swp
*.swo

# Database test files and backend configuration
/back-eccsa/db/diagnose-db-connection.js
/back-eccsa/db/mysql-test.js
/back-eccsa/db/test-db-connection.js
/back-eccsa/utils/fix-admin.js

# PM2 ecosystem file
/back-eccsa/config/ecosystem.config.js
/back-eccsa/config/start.js

# Database files to track (these should be included in git)
# crear_tabla_almacen.sql
# database-setup.sql
# README_ALMACEN.md

# Database connection files (sensitive)
.env.database
database.config.js
db-credentials.json
