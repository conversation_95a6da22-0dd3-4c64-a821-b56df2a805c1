<?php
/**
 * CSS Handler para solucionar problemas de MIME type en cPanel
 * 
 * Este script sirve archivos CSS como texto plano con el tipo MIME correcto
 * cuando todo lo demás falla. Se utiliza como último recurso cuando las
 * configuraciones de .htaccess no funcionan.
 */

// Obtener el nombre del archivo CSS solicitado
$file = isset($_GET['file']) ? $_GET['file'] : '';

// Validar que el archivo sea seguro (solo permitir caracteres alfanuméricos, guiones y puntos)
if (!preg_match('/^[a-zA-Z0-9\-\.]+$/', $file)) {
    header('HTTP/1.1 400 Bad Request');
    exit('Invalid file name');
}

// Construir la ruta al archivo CSS
$cssPath = __DIR__ . '/_next/static/css/' . $file . '.css';

// Verificar que el archivo exista y sea legible
if (!file_exists($cssPath) || !is_readable($cssPath)) {
    header('HTTP/1.1 404 Not Found');
    exit('CSS file not found');
}

// Establecer los encabezados correctos para CSS
header('Content-Type: text/css');
header('Cache-Control: public, max-age=31536000'); // Cache por 1 año
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');

// Leer y enviar el contenido del archivo CSS
readfile($cssPath);
exit;
?>
