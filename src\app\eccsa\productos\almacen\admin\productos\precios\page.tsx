'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

interface PrecioItem {
  id: number;
  partida: number;
  cantidad: number;
  descripcion: string;
  modelo: string;
  precio_compra_dls?: number;
  precio_compra: number;
  precio_total_compra: number;
  factor: number;
  precio_venta_unitario: number;
  precio_venta_total: number;
  ganancia: number;
  marca: string;
  proveedor?: string;
  tiempo_entrega?: string;
  fecha_cotizacion: string;
  stock: string;
  folio_cotizacion?: string;
  serie_familia?: string;
  responsable?: string;
}

export default function ListadoPreciosPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [precios, setPrecios] = useState<PrecioItem[]>([]);
  const [filteredPrecios, setFilteredPrecios] = useState<PrecioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [sortBy, setSortBy] = useState<'marca' | 'modelo' | 'precio_venta_total' | 'precio_total_compra' | 'ganancia'>('marca');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Estados para modales
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingPrice, setEditingPrice] = useState<PrecioItem | null>(null);
  const [deletingPrice, setDeletingPrice] = useState<PrecioItem | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Estado del formulario
  const [formData, setFormData] = useState({
    partida: '1',
    cantidad: '',
    descripcion: '',
    modelo: '',
    precio_compra_dls: '',
    precio_compra: '',
    precio_total_compra: '',
    factor: '',
    precio_venta_unitario: '',
    precio_venta_total: '',
    ganancia: '',
    marca: '',
    proveedor: '',
    tiempo_entrega: '',
    fecha_cotizacion: new Date().toISOString().split('T')[0],
    stock: '',
    folio_cotizacion: '',
    serie_familia: '',
    responsable: ''
  });

  // Cargar precios
  const loadPrecios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/precios');
      const data = await response.json();

      if (response.ok && data.success) {
        const preciosData = data.precios || [];
        setPrecios(preciosData);
        setFilteredPrecios(preciosData);

        // Extraer marcas únicas
        const marcasArray = preciosData.map((p: PrecioItem) => p.marca).filter(Boolean) as string[];
        const uniqueBrands = Array.from(new Set(marcasArray));
        setBrands(uniqueBrands.sort());
      }
    } catch (error) {
      console.error('Error loading precios:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPrecios();
  }, []);

  // Filtrar y ordenar precios
  useEffect(() => {
    let filtered = precios.filter(precio => {
      const matchesSearch =
        precio.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
        precio.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
        precio.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (precio.proveedor && precio.proveedor.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesBrand = !selectedBrand || precio.marca === selectedBrand;

      return matchesSearch && matchesBrand;
    });

    // Ordenar
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'marca':
          aValue = a.marca;
          bValue = b.marca;
          break;
        case 'modelo':
          aValue = a.modelo;
          bValue = b.modelo;
          break;
        case 'precio_venta_total':
          aValue = a.precio_venta_total || 0;
          bValue = b.precio_venta_total || 0;
          break;
        case 'precio_total_compra':
          aValue = a.precio_total_compra || 0;
          bValue = b.precio_total_compra || 0;
          break;
        case 'ganancia':
          aValue = a.ganancia || 0;
          bValue = b.ganancia || 0;
          break;
        default:
          aValue = a.marca;
          bValue = b.marca;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredPrecios(filtered);
  }, [precios, searchTerm, selectedBrand, sortBy, sortOrder]);

  // Actualizar estado de selectAll cuando cambian los elementos seleccionados
  useEffect(() => {
    setSelectAll(selectedItems.length === filteredPrecios.length && filteredPrecios.length > 0);
  }, [selectedItems, filteredPrecios]);

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  // Funciones de selección
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredPrecios.map(p => p.id));
    }
  };

  const handleSelectItem = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Exportar a Excel
  const exportToExcel = () => {
    if (selectedItems.length === 0) {
      alert('Selecciona al menos un elemento para exportar');
      return;
    }

    const selectedPrecios = precios.filter(p => selectedItems.includes(p.id));

    // Crear CSV
    const headers = ['Marca', 'Modelo', 'Descripción', 'Cantidad', 'Precio Compra', 'Precio Venta', 'Ganancias', 'Factor', 'Stock', 'Proveedor'];
    const csvContent = [
      headers.join(','),
      ...selectedPrecios.map(p => [
        p.marca,
        p.modelo,
        `"${p.descripcion}"`,
        p.cantidad,
        p.precio_total_compra,
        p.precio_venta_total,
        p.ganancia,
        `${p.factor}%`,
        p.stock,
        p.proveedor || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `precios_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printPriceList = () => {
    window.print();
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="modern-price-list-page">
      {/* Header */}
      <div className="price-list-header">
        <div className="header-content">
          <div className="header-title-section">
            <div className="title-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </div>
            <div className="title-text">
              <h1 className="page-title">Lista de Precios de Cotización</h1>
              <p className="page-subtitle">Gestión de precios y cotizaciones de productos ECCSA</p>
            </div>
          </div>
          <div className="header-actions">
            <button
              className="refresh-button"
              onClick={loadPrecios}
              disabled={loading}
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="23 4 23 10 17 10"></polyline>
                <polyline points="1 20 1 14 7 14"></polyline>
                <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
              </svg>
              {loading ? 'Actualizando...' : 'Actualizar'}
            </button>
            <button className="action-btn secondary" onClick={printPriceList}>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              Imprimir
            </button>
            <button
              className="add-price-button"
              onClick={() => setShowAddModal(true)}
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Agregar Precio
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="search-filter">
            <input
              type="text"
              placeholder="Buscar por marca, modelo, descripción..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <div className="brand-filter">
            <select
              value={selectedBrand}
              onChange={(e) => setSelectedBrand(e.target.value)}
              className="brand-select"
            >
              <option value="">Todas las marcas</option>
              {brands.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>
          </div>
          <div className="sort-controls">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="sort-select"
            >
              <option value="marca">Ordenar por Marca</option>
              <option value="modelo">Ordenar por Modelo</option>
              <option value="precio_venta_total">Ordenar por Precio Venta</option>
              <option value="precio_total_compra">Ordenar por Precio Compra</option>
              <option value="ganancia">Ordenar por Ganancias</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="sort-order-btn"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>

        <div className="actions-row">
          <div className="selection-info">
            <span>{selectedItems.length} de {filteredPrecios.length} elementos seleccionados</span>
          </div>
          <div className="bulk-actions">
            <button
              onClick={exportToExcel}
              disabled={selectedItems.length === 0}
              className="export-btn"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              Exportar Seleccionados
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="table-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Cargando precios...</p>
          </div>
        ) : filteredPrecios.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M16 16s-1.5-2-4-2-4 2-4 2"></path>
                <line x1="9" y1="9" x2="9.01" y2="9"></line>
                <line x1="15" y1="9" x2="15.01" y2="9"></line>
              </svg>
            </div>
            <h3>No se encontraron precios</h3>
            <p>No hay precios que coincidan con los filtros aplicados.</p>
          </div>
        ) : (
          <div className="table-wrapper">
            <table className="prices-table">
              <thead>
                <tr>
                  <th className="checkbox-column">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th>Marca</th>
                  <th>Modelo</th>
                  <th>Descripción</th>
                  <th>Cantidad</th>
                  <th>Precio Compra</th>
                  <th>Factor</th>
                  <th>Precio Venta</th>
                  <th>Ganancias</th>
                  <th>Stock</th>
                  <th>Proveedor</th>
                  <th>Acciones</th>
                </tr>
              </thead>
              <tbody>
                {filteredPrecios.map((precio) => (
                  <tr key={precio.id}>
                    <td className="checkbox-column">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(precio.id)}
                        onChange={() => handleSelectItem(precio.id)}
                      />
                    </td>
                    <td className="brand-cell">{precio.marca}</td>
                    <td className="model-cell">{precio.modelo}</td>
                    <td className="description-cell" title={precio.descripcion}>
                      {precio.descripcion.length > 50
                        ? `${precio.descripcion.substring(0, 50)}...`
                        : precio.descripcion}
                    </td>
                    <td className="quantity-cell">{precio.cantidad}</td>
                    <td className="price-cell">{formatPrice(precio.precio_total_compra)}</td>
                    <td className="factor-cell">{precio.factor}%</td>
                    <td className="price-cell">{formatPrice(precio.precio_venta_total)}</td>
                    <td className="profit-cell">{formatPrice(precio.ganancia)}</td>
                    <td className="stock-cell">
                      <span className={`stock-badge ${precio.stock.toLowerCase().replace(' ', '-')}`}>
                        {precio.stock}
                      </span>
                    </td>
                    <td className="supplier-cell">{precio.proveedor || '-'}</td>
                    <td className="actions-cell">
                      <div className="action-buttons">
                        <button
                          onClick={() => console.log('Edit', precio.id)}
                          className="action-btn edit"
                          title="Editar"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                        </button>
                        <button
                          onClick={() => console.log('Delete', precio.id)}
                          className="action-btn delete"
                          title="Eliminar"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
