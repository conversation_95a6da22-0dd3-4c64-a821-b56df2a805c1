'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function ServicesPage() {
  const [activeService, setActiveService] = useState('Automatización');

  const services = [
    {
      id: 'Automatización',
      name: 'Automatización Industrial',
      icon: '⚙️',
      shortDesc: 'Soluciones completas de automatización para procesos industriales'
    },
    {
      id: 'Programación',
      name: 'Programación de Sistemas',
      icon: '💻',
      shortDesc: 'Desarrollo de software especializado para control industrial'
    },
    {
      id: 'SCADA',
      name: 'Sistemas SCADA',
      icon: '📊',
      shortDesc: 'Monitoreo y control de procesos en tiempo real'
    },
    {
      id: 'Mantenimiento',
      name: 'Mantenimiento Especializado',
      icon: '🔧',
      shortDesc: 'Servicios de mantenimiento preventivo y correctivo'
    }
  ];

  // Definición de tipos para el contenido de servicios
  interface ServiceContent {
    title: string;
    subtitle: string;
    description: string;
    features: string[];
    benefits: string[];
    technologies: string[];
    brands?: string[];
    image: string;
  }

  // Contenido para cada servicio
  const serviceContent: Record<string, ServiceContent> = {
    Automatización: {
      title: 'Automatización Industrial',
      subtitle: 'Transformamos procesos industriales con tecnología de vanguardia',
      description: 'ECCSA Automation desarrolla soluciones integrales de automatización para procesos industriales. Utilizamos la mejor tecnología y los más exigentes estándares internacionales, proporcionando a nuestros clientes altos niveles de productividad y calidad con la mejor relación costo-beneficio.',
      features: [
        'Diseño e implementación de sistemas PLC',
        'Integración de variadores de frecuencia',
        'Sistemas de arranque y control de motores',
        'Centros de control de motores (CCM)',
        'Redes industriales de comunicación',
        'Sistemas de seguridad industrial',
        'Optimización de energía y potencia',
        'Interfaces de operador (HMI)',
        'Sistemas de bases de datos históricos'
      ],
      benefits: [
        'Incremento de productividad hasta 40%',
        'Reducción de costos operativos',
        'Mejora en la calidad del producto',
        'Mayor seguridad industrial',
        'Monitoreo en tiempo real'
      ],
      technologies: [
        'Allen-Bradley PLC',
        'Siemens S7',
        'Schneider Electric',
        'Mitsubishi Electric',
        'Omron'
      ],
      image: '/images/defaults/Automatización-Industrial.jpg'
    },
    Programación: {
      title: 'Programación de Sistemas',
      subtitle: 'Desarrollo especializado de software industrial y control',
      description: 'Nuestro equipo de ingenieros especializados desarrolla software robusto y eficiente para sistemas de control industrial. Utilizamos los lenguajes de programación más avanzados y metodologías probadas para garantizar la máxima confiabilidad y rendimiento.',
      features: [
        'Programación avanzada de PLC\'s',
        'Diagramas de escalera (Ladder Logic)',
        'Bloques funcionales (Function Block)',
        'Lenguaje estructurado (Structured Text)',
        'Grafcet (Sequential Function Chart)',
        'Sistemas de control distribuido (DCS)',
        'Sistemas de visión artificial',
        'Redes industriales Ethernet/IP',
        'Protocolos DeviceNet y ControlNet',
        'Comunicación Profibus/Profinet'
      ],
      benefits: [
        'Código optimizado y documentado',
        'Reducción de tiempos de desarrollo',
        'Sistemas escalables y modulares',
        'Interfaces intuitivas para operadores',
        'Soporte técnico especializado'
      ],
      technologies: [
        'RSLogix 5000/Studio 5000',
        'TIA Portal (Siemens)',
        'Unity Pro (Schneider)',
        'GX Works (Mitsubishi)',
        'CX-Programmer (Omron)'
      ],
      brands: [
        '/images/Marcas/rockwell.png',
        '/images/Marcas/siemens.png',
        '/images/Marcas/schneider.png',
        '/images/Marcas/mitsubishi.png',
        '/images/Marcas/omron.png',
        '/images/Marcas/ge.png',
        '/images/Marcas/fanuc.png'
      ],
      image: '/images/defaults/PROGRAMACION-PLC.jpeg'
    },
    SCADA: {
      title: 'Sistemas SCADA',
      subtitle: 'Supervisión, control y adquisición de datos en tiempo real',
      description: 'Implementamos sistemas SCADA avanzados que permiten el monitoreo y control centralizado de procesos industriales. Nuestras soluciones integran equipos de control para la recopilación, análisis y visualización de información de producción en tiempo real.',
      features: [
        'Sistemas de monitoreo en tiempo real',
        'Registro y análisis de datos históricos',
        'Visualización avanzada de tendencias',
        'Gráficos animados del proceso',
        'Sistemas de alarmas inteligentes',
        'Reportes automáticos de producción',
        'Interfaces web para acceso remoto',
        'Integración con sistemas ERP',
        'Dashboards ejecutivos personalizados'
      ],
      benefits: [
        'Visibilidad completa del proceso',
        'Toma de decisiones basada en datos',
        'Reducción de tiempos de respuesta',
        'Optimización de recursos',
        'Cumplimiento de normativas'
      ],
      technologies: [
        'Wonderware System Platform',
        'GE iFIX/Cimplicity',
        'Ignition by Inductive Automation',
        'Siemens WinCC',
        'Schneider Vijeo Citect'
      ],
      image: '/images/defaults/sistema-scada-gas-natual-eisenberg-monterrey.jpg'
    },
    Mantenimiento: {
      title: 'Mantenimiento Especializado',
      subtitle: 'Servicios integrales de mantenimiento preventivo y correctivo',
      description: 'Ofrecemos servicios especializados de mantenimiento para sistemas de automatización industrial. Nuestro equipo técnico altamente capacitado garantiza la continuidad operacional y el óptimo rendimiento de sus equipos e instalaciones.',
      features: [
        'Respaldo de programas y configuraciones',
        'Servicio de soporte técnico 24/7',
        'Mantenimiento preventivo programado',
        'Diagnóstico y reparación de fallas',
        'Actualización de software y firmware',
        'Calibración de instrumentos',
        'Elaboración de documentación técnica',
        'Capacitación de personal operativo',
        'Optimización de parámetros de control'
      ],
      benefits: [
        'Máxima disponibilidad de equipos',
        'Reducción de paros no programados',
        'Extensión de vida útil',
        'Cumplimiento de garantías',
        'Personal técnico certificado'
      ],
      technologies: [
        'Diagnóstico con analizadores',
        'Software de mantenimiento predictivo',
        'Herramientas de calibración',
        'Sistemas de monitoreo remoto',
        'Documentación digital'
      ],
      image: '/images/defaults/Mantenimiento-Industrial.jpg'
    }
  };

  return (
    <div className="modern-services-page">
      {/* Hero Section */}
      <div className="services-hero">
        <div className="services-hero-content">
          <div className="hero-badge">
            <span className="badge-icon">🔧</span>
            <span>Servicios Profesionales</span>
          </div>
          <h1 className="services-hero-title">
            Nuestros <span className="title-highlight">Servicios</span>
          </h1>
          <p className="services-hero-description">
            Soluciones integrales de automatización industrial con más de 25 años de experiencia.
            Transformamos procesos industriales con tecnología de vanguardia y soporte especializado.
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">25+</span>
              <span className="stat-label">Años de Experiencia</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">500+</span>
              <span className="stat-label">Proyectos Completados</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">24/7</span>
              <span className="stat-label">Soporte Técnico</span>
            </div>
          </div>
        </div>
        <div className="hero-decoration">
          <div className="decoration-circle decoration-circle-1"></div>
          <div className="decoration-circle decoration-circle-2"></div>
          <div className="decoration-circle decoration-circle-3"></div>
        </div>
      </div>

      {/* Services Navigation */}
      <div className="services-navigation-section">
        <div className="services-nav-container">
          <h2 className="nav-title">Explora Nuestros Servicios</h2>
          <div className="services-nav-grid">
            {services.map((service) => (
              <button
                key={service.id}
                className={`modern-service-nav-card ${activeService === service.id ? 'active' : ''}`}
                onClick={() => setActiveService(service.id)}
              >
                <div className="nav-card-icon">{service.icon}</div>
                <h3 className="nav-card-title">{service.name}</h3>
                <p className="nav-card-description">{service.shortDesc}</p>
                <div className="nav-card-arrow">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Service Detail Section */}
      <div className="service-detail-section">
        <div className="service-detail-container">
          <div className="service-detail-grid">
            {/* Service Image */}
            <div className="service-image-container">
              <div className="service-image-wrapper">
                <Image
                  src={serviceContent[activeService].image}
                  alt={serviceContent[activeService].title}
                  width={600}
                  height={400}
                  className="service-detail-image"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                  }}
                />
                <div className="image-overlay">
                  <div className="overlay-content">
                    <span className="overlay-icon">{services.find(s => s.id === activeService)?.icon}</span>
                    <span className="overlay-text">Tecnología Avanzada</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Content */}
            <div className="service-content-wrapper">
              <div className="service-header">
                <div className="service-category-badge">
                  {services.find(s => s.id === activeService)?.icon} Servicio Especializado
                </div>
                <h2 className="service-title">{serviceContent[activeService].title}</h2>
                <p className="service-subtitle">{serviceContent[activeService].subtitle}</p>
              </div>

              <div className="service-description">
                <p>{serviceContent[activeService].description}</p>
              </div>

              {/* Features */}
              <div className="service-features-section">
                <h3 className="features-title">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                  </svg>
                  Características Principales
                </h3>
                <div className="features-grid">
                  {serviceContent[activeService].features.map((feature, index) => (
                    <div key={index} className="feature-item">
                      <div className="feature-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                      </div>
                      <span className="feature-text">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Benefits */}
              <div className="service-benefits-section">
                <h3 className="benefits-title">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                  </svg>
                  Beneficios Clave
                </h3>
                <div className="benefits-list">
                  {serviceContent[activeService].benefits.map((benefit, index) => (
                    <div key={index} className="benefit-item">
                      <div className="benefit-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                        </svg>
                      </div>
                      <span className="benefit-text">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Technologies */}
              <div className="service-technologies-section">
                <h3 className="technologies-title">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                  </svg>
                  Tecnologías Utilizadas
                </h3>
                <div className="technologies-tags">
                  {serviceContent[activeService].technologies.map((tech, index) => (
                    <span key={index} className="technology-tag">
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Brands for Programming Service */}
              {activeService === 'Programación' && serviceContent[activeService].brands && (
                <div className="service-brands-section">
                  <h3 className="brands-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                    </svg>
                    Marcas Certificadas
                  </h3>
                  <div className="brands-grid">
                    {serviceContent[activeService].brands?.map((brand, index) => (
                      <div key={index} className="brand-logo-container">
                        <Image
                          src={brand}
                          alt={`Brand ${index + 1}`}
                          width={120}
                          height={60}
                          className="brand-logo-image"
                          style={{ objectFit: 'contain' }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* CTA Button */}
              <div className="service-cta">
                <button className="service-cta-button">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                  Solicitar Cotización
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
