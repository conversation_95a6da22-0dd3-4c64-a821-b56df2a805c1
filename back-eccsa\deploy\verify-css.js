// Script para verificar y asegurar que todos los archivos CSS estén correctamente incluidos en el build
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  fg: {
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m',
    cyan: '\x1b[36m'
  }
};

console.log(`${colors.fg.cyan}${colors.bright}=== Verificando archivos CSS para el despliegue ===${colors.reset}\n`);

// Directorios de origen y destino
const rootDir = path.join(__dirname, '..', '..');
const sourceDir = path.join(rootDir, 'src');
const outputDir = path.join(rootDir, 'out');

// Verificar si el directorio de salida existe
if (!fs.existsSync(outputDir)) {
  console.log(`${colors.fg.yellow}El directorio 'out' no existe. Ejecutando build primero...${colors.reset}`);
  try {
    execSync('npm run build', { stdio: 'inherit' });
  } catch (error) {
    console.error(`${colors.fg.red}Error al ejecutar build: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Verificar archivos CSS en el directorio de salida
console.log(`${colors.fg.blue}Verificando archivos CSS en el build...${colors.reset}`);
const cssDir = path.join(outputDir, '_next', 'static', 'css');

if (!fs.existsSync(cssDir)) {
  console.error(`${colors.fg.red}✗ Directorio CSS no encontrado: ${cssDir}${colors.reset}`);
  console.log(`${colors.fg.yellow}Creando directorio CSS...${colors.reset}`);
  fs.mkdirSync(cssDir, { recursive: true });
}

// Buscar archivos CSS en el directorio de origen
console.log(`${colors.fg.blue}Buscando archivos CSS en el código fuente...${colors.reset}`);
const sourceGlobalCss = path.join(sourceDir, 'app', 'globals.css');
const sourceStylesCss = path.join(sourceDir, 'app', 'styles.css');
const sourceAdminCss = path.join(sourceDir, 'app', 'admin-styles.css');

// Función para copiar CSS al directorio de salida
function copyCssToOutput(sourcePath, fileName) {
  if (fs.existsSync(sourcePath)) {
    console.log(`${colors.fg.green}✓ Encontrado archivo CSS: ${sourcePath}${colors.reset}`);
    
    // Crear nombre de archivo de destino
    const destFileName = `${path.parse(fileName).name}-${Date.now().toString().slice(-6)}.css`;
    const destPath = path.join(cssDir, destFileName);
    
    // Copiar archivo
    try {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`${colors.fg.green}✓ Copiado: ${sourcePath} -> ${destPath}${colors.reset}`);
      
      // Verificar que el archivo se haya copiado correctamente
      if (fs.existsSync(destPath)) {
        const sourceSize = fs.statSync(sourcePath).size;
        const destSize = fs.statSync(destPath).size;
        console.log(`${colors.fg.green}✓ Tamaño original: ${sourceSize} bytes, Tamaño copiado: ${destSize} bytes${colors.reset}`);
        
        // Verificar contenido
        const content = fs.readFileSync(destPath, 'utf8');
        if (content.length < 10) {
          console.warn(`${colors.fg.yellow}⚠️ El archivo copiado parece estar vacío o corrupto${colors.reset}`);
        }
      } else {
        console.error(`${colors.fg.red}✗ Error: El archivo no se copió correctamente${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.fg.red}✗ Error al copiar archivo: ${error.message}${colors.reset}`);
    }
  } else {
    console.warn(`${colors.fg.yellow}⚠️ Archivo CSS no encontrado: ${sourcePath}${colors.reset}`);
  }
}

// Copiar archivos CSS
copyCssToOutput(sourceGlobalCss, 'globals.css');
copyCssToOutput(sourceStylesCss, 'styles.css');
copyCssToOutput(sourceAdminCss, 'admin-styles.css');

// Crear un archivo CSS de respaldo con todos los estilos combinados
console.log(`${colors.fg.blue}Creando archivo CSS de respaldo...${colors.reset}`);
const backupCssPath = path.join(cssDir, 'all-styles-backup.css');
let combinedCss = '';

// Combinar todos los archivos CSS
if (fs.existsSync(sourceGlobalCss)) {
  combinedCss += fs.readFileSync(sourceGlobalCss, 'utf8') + '\n\n';
}
if (fs.existsSync(sourceStylesCss)) {
  combinedCss += fs.readFileSync(sourceStylesCss, 'utf8') + '\n\n';
}
if (fs.existsSync(sourceAdminCss)) {
  combinedCss += fs.readFileSync(sourceAdminCss, 'utf8') + '\n\n';
}

// Guardar archivo combinado
if (combinedCss.length > 0) {
  fs.writeFileSync(backupCssPath, combinedCss);
  console.log(`${colors.fg.green}✓ Archivo CSS de respaldo creado: ${backupCssPath}${colors.reset}`);
  console.log(`${colors.fg.green}✓ Tamaño: ${combinedCss.length} bytes${colors.reset}`);
} else {
  console.error(`${colors.fg.red}✗ No se pudo crear el archivo CSS de respaldo${colors.reset}`);
}

// Crear un archivo HTML de respaldo que incluya todos los CSS
console.log(`${colors.fg.blue}Creando archivo HTML de respaldo...${colors.reset}`);
const backupHtmlPath = path.join(outputDir, 'css-backup.html');
const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css'));

let htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ECCSA - Respaldo de CSS</title>
  <!-- CSS Files -->
`;

// Agregar referencias a todos los archivos CSS
for (const cssFile of cssFiles) {
  htmlContent += `  <link rel="stylesheet" href="_next/static/css/${cssFile}">\n`;
}

htmlContent += `
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .file {
      margin-bottom: 10px;
      padding: 5px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>ECCSA - Archivos CSS</h1>
  <p>Esta página incluye todos los archivos CSS del proyecto para verificar que se carguen correctamente.</p>
  <h2>Archivos CSS incluidos:</h2>
  <div id="files">
`;

// Listar todos los archivos CSS
for (const cssFile of cssFiles) {
  htmlContent += `    <div class="file">${cssFile}</div>\n`;
}

htmlContent += `
  </div>
</body>
</html>
`;

// Guardar archivo HTML
fs.writeFileSync(backupHtmlPath, htmlContent);
console.log(`${colors.fg.green}✓ Archivo HTML de respaldo creado: ${backupHtmlPath}${colors.reset}`);

console.log(`\n${colors.fg.cyan}${colors.bright}=== Verificación de CSS completada ===${colors.reset}`);
console.log(`${colors.fg.green}Todos los archivos CSS han sido verificados y respaldados.${colors.reset}`);
console.log(`${colors.fg.yellow}Recuerde incluir el archivo HTML de respaldo en el ZIP para verificar los CSS en el servidor.${colors.reset}`);
