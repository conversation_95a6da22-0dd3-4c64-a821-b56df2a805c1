'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

export default function SeguimientoPedidosPage() {
  const [loading, setLoading] = useState(false);
  const [pedidos, setPedidos] = useState([]);
  const [error, setError] = useState('');
  const [filtroEstado, setFiltroEstado] = useState('todos');

  useEffect(() => {
    // Aquí se cargarían los pedidos
    // Por ahora, dejamos un array vacío
  }, []);

  return (
    <AuthGuard>
        <div className="modern-admin-page">
          <div className="admin-header">
            <h1 className="admin-title">Seguimiento de Pedidos</h1>
            <div className="admin-header-actions">
              <select 
                className="admin-select"
                value={filtroEstado}
                onChange={(e) => setFiltroEstado(e.target.value)}
              >
                <option value="todos">Todos los estados</option>
                <option value="pendiente">Pendiente</option>
                <option value="procesando">Procesando</option>
                <option value="enviado">Enviado</option>
                <option value="entregado">Entregado</option>
                <option value="cancelado">Cancelado</option>
              </select>
              <button className="admin-button">Actualizar</button>
            </div>
          </div>

          <div className="admin-card">
            <div className="admin-card-header">
              <h2 className="admin-card-title">Pedidos Activos</h2>
              <div className="admin-card-actions">
                <input 
                  type="text" 
                  placeholder="Buscar pedido..." 
                  className="admin-search-input"
                />
                <input 
                  type="date" 
                  className="admin-date-input"
                />
              </div>
            </div>

            <div className="admin-card-content">
              {loading ? (
                <div className="admin-loading">
                  <div className="admin-loading-spinner"></div>
                  <p>Cargando pedidos...</p>
                </div>
              ) : error ? (
                <div className="admin-error">
                  <p>{error}</p>
                  <button 
                    className="admin-button"
                    onClick={() => {
                      setError('');
                      // Recargar pedidos
                    }}
                  >
                    Reintentar
                  </button>
                </div>
              ) : pedidos.length === 0 ? (
                <div className="admin-empty">
                  <p>No hay pedidos para mostrar.</p>
                  <p>Los pedidos realizados aparecerán aquí para su seguimiento.</p>
                </div>
              ) : (
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID Pedido</th>
                      <th>Fecha</th>
                      <th>Proveedor</th>
                      <th>Total</th>
                      <th>Estado</th>
                      <th>Fecha Estimada</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Aquí se mostrarían los pedidos */}
                    <tr>
                      <td colSpan={7} className="admin-table-empty">
                        No hay pedidos para mostrar
                      </td>
                    </tr>
                  </tbody>
                </table>
              )}
            </div>
          </div>

          <div className="admin-card mt-4">
            <div className="admin-card-header">
              <h2 className="admin-card-title">Historial de Pedidos</h2>
            </div>
            <div className="admin-card-content">
              <div className="admin-tabs">
                <button className="admin-tab active">Último Mes</button>
                <button className="admin-tab">Último Trimestre</button>
                <button className="admin-tab">Último Año</button>
                <button className="admin-tab">Todos</button>
              </div>
              
              <div className="admin-tab-content">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID Pedido</th>
                      <th>Fecha</th>
                      <th>Proveedor</th>
                      <th>Total</th>
                      <th>Estado</th>
                      <th>Fecha Entrega</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Aquí se mostrarían los pedidos históricos */}
                    <tr>
                      <td colSpan={7} className="admin-table-empty">
                        No hay pedidos históricos para mostrar
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
    </AuthGuard>
  );
}
