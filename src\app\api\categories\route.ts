import { NextRequest, NextResponse } from 'next/server';
import { getAllCategories } from '@/eccsa_back/lib/products';
import { executeQuery } from '@/eccsa_back/lib/db';

// GET handler to fetch all categories
export async function GET() {
  try {
    const categories = await getAllCategories();

    return NextResponse.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST handler to create a new category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      );
    }

    const query = `
      INSERT INTO categories (name, description, created_at, updated_at)
      VALUES (?, ?, NOW(), NOW())
    `;

    const result = await executeQuery({
      query,
      values: [body.name, body.description || null],
    }) as any;

    if (!result.insertId) {
      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, categoryId: result.insertId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
