'use client';

import { FaWhatsapp } from 'react-icons/fa';
import { usePathname } from 'next/navigation';
import { createWhatsAppUrl } from '@/utils';
import { DEFAULT_MESSAGES } from '@/constants';

interface WhatsAppButtonProps {
  phoneNumber: string;
  message?: string;
}

/**
 * WhatsApp button component that appears fixed on the screen
 * Allows users to contact the company via WhatsApp
 */
export default function WhatsAppButton({
  phoneNumber,
  message = DEFAULT_MESSAGES.WHATSAPP
}: WhatsAppButtonProps) {
  const pathname = usePathname();

  // Hide WhatsApp button on admin pages
  if (pathname?.startsWith('/eccsa/admin')) {
    return null;
  }

  const whatsappUrl = createWhatsAppUrl(phoneNumber, message);

  return (
    <a
      href={whatsappUrl}
      className="whatsapp-button"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Contactar por WhatsApp"
    >
      <FaWhatsapp size={24} />
      <span className="whatsapp-text">Contáctanos</span>
    </a>
  );
}
