'use client';

import { useState } from 'react';
import ImageWithFallback from '../ImageWithFallback';
import { IconType } from 'react-icons';

interface ModernServiceCardProps {
  name: string;
  description: string;
  icon: React.ElementType | string;
  imageSrc?: string;
  className?: string;
}

/**
 * Modern Service Card component with Tailwind CSS
 * Implements responsive design with container queries and fluid typography
 */
export function ModernServiceCard({
  name,
  description,
  icon,
  imageSrc,
  className = ''
}: ModernServiceCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const defaultImage = '/images/logos/logo_largo.png';

  // Render the icon based on its type
  const renderIcon = () => {
    if (typeof icon === 'string') {
      return <span className="text-3xl">{icon}</span>;
    } else {
      const IconComponent = icon as IconType;
      return <IconComponent className="w-8 h-8" />;
    }
  };

  return (
    <div
      className={`
        group relative overflow-hidden rounded-xl bg-white shadow-soft hover:shadow-strong
        transition-all duration-300 ease-out hover:-translate-y-2
        @container min-h-[400px] flex flex-col
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Container with Overlay */}
      <div className="relative h-48 @md:h-56 overflow-hidden">
        <ImageWithFallback
          src={imageSrc || defaultImage}
          fallbackSrc={defaultImage}
          alt={name}
          width={0}
          height={0}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

        {/* Icon Overlay */}
        <div className={`
          absolute top-4 right-4 p-3 rounded-full bg-white/90 backdrop-blur-sm
          text-eccsa-primary transition-all duration-300
          ${isHovered ? 'scale-110 bg-eccsa-primary text-white' : ''}
        `}>
          {renderIcon()}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 @md:p-8 flex flex-col">
        <h3 className="text-xl @md:text-2xl font-bold text-eccsa-gray-900 mb-4 group-hover:text-eccsa-primary transition-colors">
          {name}
        </h3>

        <p className="text-eccsa-gray-600 leading-relaxed flex-1 text-sm @md:text-base">
          {description}
        </p>

        {/* Call to Action */}
        <div className="mt-6 pt-4 border-t border-eccsa-gray-200">
          <button className={`
            inline-flex items-center text-sm font-semibold transition-all duration-300
            ${isHovered
              ? 'text-eccsa-primary translate-x-2'
              : 'text-eccsa-gray-700 hover:text-eccsa-primary'
            }
          `}>
            <span>Más información</span>
            <svg
              className={`ml-2 w-4 h-4 transition-transform duration-300 ${
                isHovered ? 'translate-x-1' : ''
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Hover Effect Border */}
      <div className={`
        absolute inset-0 rounded-xl border-2 transition-all duration-300 pointer-events-none
        ${isHovered ? 'border-eccsa-primary' : 'border-transparent'}
      `} />
    </div>
  );
}

/**
 * Service Cards Grid Container
 * Implements responsive grid with auto-fit columns
 */
export function ServiceCardsGrid({
  children,
  className = ''
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`
      grid grid-cols-auto-fit-sm gap-6 lg:gap-8
      @container-lg:grid-cols-auto-fit-md
      ${className}
    `}>
      {children}
    </div>
  );
}

/**
 * Service Section Container
 * Provides consistent spacing and layout
 */
export function ServiceSection({
  title,
  subtitle,
  children,
  className = ''
}: {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <section className={`py-fluid-2xl px-fluid-md ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-fluid-xl">
          <h2 className="text-3xl lg:text-4xl font-bold text-eccsa-gray-900 mb-4">
            {title}
          </h2>
          {subtitle && (
            <p className="text-lg text-eccsa-gray-600 max-w-3xl mx-auto leading-relaxed">
              {subtitle}
            </p>
          )}
        </div>

        {/* Content */}
        {children}
      </div>
    </section>
  );
}

// Export por defecto para compatibilidad
export default ModernServiceCard;