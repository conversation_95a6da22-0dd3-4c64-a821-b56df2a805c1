'use client';

import { useState, useEffect } from 'react';
import SolicitudModals from '@/components/SolicitudModals';

interface Solicitud {
  id: number;
  numero_almacen: string;
  modelo: string;
  marca: string;
  descripcion: string;
  estante: string;
  precio_unitario: number;
  cantidad_solicitada: number;
  cantidad_disponible: number;
  cliente_email: string;
  cliente_whatsapp?: string;
  estado: 'pendiente' | 'procesando' | 'cotizado' | 'rechazado';
  alerta_stock: boolean;
  tiempo_entrega_estimado?: string;
  notas?: string;
  fecha_solicitud: string;
  fecha_actualizacion: string;
  tipo_solicitud?: string;
  solicitud_grupo_id?: string;
  productos_json?: string;
}

export default function SolicitudesPage() {
  const [solicitudes, setSolicitudes] = useState<Solicitud[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [totalSolicitudes, setTotalSolicitudes] = useState(0);
  const [selectedSolicitud, setSelectedSolicitud] = useState<Solicitud | null>(null);
  const [modalType, setModalType] = useState<'view' | 'edit' | 'delete' | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Cargar solicitudes
  const loadSolicitudes = async () => {
    try {
      setLoading(true);
      setError('');

      const params = new URLSearchParams();
      if (filtroEstado !== 'todos') {
        params.append('estado', filtroEstado);
      }
      params.append('limit', '50');
      params.append('offset', '0');

      const response = await fetch(`/api/solicitudes?${params}`);
      const data = await response.json();

      if (data.success) {
        setSolicitudes(data.solicitudes || []);
        setTotalSolicitudes(data.total || 0);
      } else {
        setError(data.error || 'Error al cargar solicitudes');
      }
    } catch (err) {
      setError('Error de conexión al cargar solicitudes');
      console.error('Error loading solicitudes:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSolicitudes();
  }, [filtroEstado]);

  // Auto-refresh cada 30 segundos
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadSolicitudes();
    }, 30000); // 30 segundos

    return () => clearInterval(interval);
  }, [autoRefresh, filtroEstado]);

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Formatear precio
  const formatPrice = (price: number) => {
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
    return formattedPrice;
  };

  // Calcular total de la cotización
  const calcularTotalCotizacion = (solicitud: Solicitud) => {
    if (solicitud.tipo_solicitud === 'carrito' && solicitud.productos_json) {
      try {
        const productos = JSON.parse(solicitud.productos_json);
        const subtotal = productos.reduce((sum: number, prod: any) => sum + (prod.subtotal || 0), 0);
        return subtotal * 1.16; // Agregar IVA
      } catch (error) {
        console.error('Error parsing productos_json:', error);
      }
    }
    // Para solicitudes individuales
    return (solicitud.precio_unitario || 0) * (solicitud.cantidad_solicitada || 0) * 1.16;
  };

  // Obtener información del resumen
  const obtenerResumenSolicitud = (solicitud: Solicitud) => {
    if (solicitud.tipo_solicitud === 'carrito' && solicitud.productos_json) {
      try {
        const productos = JSON.parse(solicitud.productos_json);
        const totalProductos = productos.length;
        const subtotal = productos.reduce((sum: number, prod: any) => sum + (prod.subtotal || 0), 0);
        const totalConIVA = subtotal * 1.16;
        const productosConAlerta = productos.filter((prod: any) => prod.alerta_stock).length;

        return {
          esCarrito: true,
          totalProductos,
          subtotal,
          totalConIVA,
          productosConAlerta,
          productos
        };
      } catch (error) {
        console.error('Error parsing productos_json:', error);
      }
    }

    // Para solicitudes individuales
    const subtotal = (solicitud.precio_unitario || 0) * (solicitud.cantidad_solicitada || 0);
    return {
      esCarrito: false,
      totalProductos: 1,
      subtotal,
      totalConIVA: subtotal * 1.16,
      productosConAlerta: solicitud.alerta_stock ? 1 : 0,
      productos: []
    };
  };

  // Obtener color del estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'pendiente': return '#f59e0b';
      case 'procesando': return '#3b82f6';
      case 'cotizado': return '#10b981';
      case 'rechazado': return '#ef4444';
      default: return '#6b7280';
    }
  };

  // Contar solicitudes por estado
  const contarPorEstado = (estado: string) => {
    return solicitudes.filter(s => s.estado === estado).length;
  };

  // Funciones para manejar modales
  const handleOpenModal = (solicitud: Solicitud, type: 'view' | 'edit' | 'delete') => {
    setSelectedSolicitud(solicitud);
    setModalType(type);
  };

  const handleCloseModal = () => {
    setSelectedSolicitud(null);
    setModalType(null);
  };

  const handleUpdateSolicitud = () => {
    loadSolicitudes(); // Recargar la lista después de actualizar

    // Actualizar la solicitud seleccionada si está abierta
    if (selectedSolicitud && modalType === 'view') {
      // Buscar la solicitud actualizada en la lista
      setTimeout(() => {
        const updatedSolicitud = solicitudes.find(s => s.id === selectedSolicitud.id);
        if (updatedSolicitud) {
          setSelectedSolicitud(updatedSolicitud);
        }
      }, 500);
    }
  };

  return (
      <div className="modern-requests-page">
        {/* Header moderno */}
        <div className="requests-header">
          <div className="header-content">
            <div className="header-title-section">
              <div className="title-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10,9 9,9 8,9"></polyline>
                </svg>
              </div>
              <div className="title-text">
                <h1 className="requests-title">Gestión de Solicitudes</h1>
                <p className="requests-subtitle">Administra las solicitudes de cotización de los clientes</p>
              </div>
            </div>
            <div className="header-actions">
              <button
                className="refresh-button"
                onClick={loadSolicitudes}
                disabled={loading}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 20 1 14 7 14"></polyline>
                  <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                {loading ? 'Actualizando...' : 'Actualizar'}
              </button>
              <div className="auto-refresh-toggle">
                <label className="toggle-label">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    className="toggle-input"
                  />
                  <span className="toggle-slider"></span>
                  <span className="toggle-text">Auto-actualizar</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Estadísticas modernas */}
        <div className="requests-stats">
          <div className="stats-grid">
            <div className="stat-card primary">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{totalSolicitudes}</div>
                <div className="stat-label">Total Solicitudes</div>
              </div>
            </div>

            <div className="stat-card warning">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{contarPorEstado('pendiente')}</div>
                <div className="stat-label">Pendientes</div>
              </div>
            </div>

            <div className="stat-card info">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{contarPorEstado('procesando')}</div>
                <div className="stat-label">Procesando</div>
              </div>
            </div>

            <div className="stat-card success">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{contarPorEstado('cotizado')}</div>
                <div className="stat-label">Cotizadas</div>
              </div>
            </div>

            <div className="stat-card danger">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{contarPorEstado('rechazado')}</div>
                <div className="stat-label">Rechazadas</div>
              </div>
            </div>
          </div>
        </div>

        {/* Sección de solicitudes moderna */}
        <div className="requests-content">
          <div className="requests-header-section">
            <div className="requests-title-section">
              <h2>Lista de Solicitudes</h2>
              <p>Gestiona las cotizaciones y estados de las solicitudes</p>
            </div>
            <div className="requests-filters">
              <div className="filter-container">
                <select
                  value={filtroEstado}
                  onChange={(e) => setFiltroEstado(e.target.value)}
                  className="filter-select"
                >
                  <option value="todos">Todos los estados</option>
                  <option value="pendiente">Pendientes</option>
                  <option value="procesando">Procesando</option>
                  <option value="cotizado">Cotizadas</option>
                  <option value="rechazado">Rechazadas</option>
                </select>
              </div>
            </div>
          </div>

          <div className="requests-list">
            {loading ? (
              <div className="loading-state">
                <div className="loading-spinner">
                  <div className="spinner"></div>
                </div>
                <h3>Cargando solicitudes...</h3>
                <p>Obteniendo información de las cotizaciones</p>
              </div>
            ) : error ? (
              <div className="error-state">
                <div className="error-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <h3>Error al cargar solicitudes</h3>
                <p>{error}</p>
                <button
                  className="retry-button"
                  onClick={loadSolicitudes}
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="23 4 23 10 17 10"></polyline>
                    <polyline points="1 20 1 14 7 14"></polyline>
                    <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                  </svg>
                  Reintentar
                </button>
              </div>
            ) : solicitudes.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                </div>
                <h3>No hay solicitudes</h3>
                <p>
                  {filtroEstado !== 'todos'
                    ? `No hay solicitudes en estado "${filtroEstado}".`
                    : 'No hay solicitudes de cotización registradas.'
                  }
                </p>
              </div>
            ) : (
              <div className="modern-solicitudes-grid">
                {solicitudes.map((solicitud) => {
                  const resumen = obtenerResumenSolicitud(solicitud);

                  return (
                    <div key={solicitud.id} className="modern-solicitud-card">
                      {/* Header de la solicitud */}
                      <div className="solicitud-card-header">
                        <div className="solicitud-info">
                          <div className="solicitud-id">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                              <polyline points="14,2 14,8 20,8"></polyline>
                            </svg>
                            <span>#{solicitud.id}</span>
                            {solicitud.solicitud_grupo_id && (
                              <span className="grupo-id">Grupo: {solicitud.solicitud_grupo_id}</span>
                            )}
                          </div>
                          <div className="solicitud-date">{formatDate(solicitud.fecha_solicitud)}</div>
                        </div>
                        <span
                          className={`estado-badge modern ${solicitud.estado}`}
                        >
                          {solicitud.estado.charAt(0).toUpperCase() + solicitud.estado.slice(1)}
                        </span>
                      </div>

                      {/* Tipo de solicitud */}
                      <div className="solicitud-type">
                        {resumen.esCarrito ? (
                          <div className="type-badge carrito">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <circle cx="9" cy="21" r="1"></circle>
                              <circle cx="20" cy="21" r="1"></circle>
                              <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            <span>Carrito ({resumen.totalProductos} productos)</span>
                          </div>
                        ) : (
                          <div className="type-badge individual">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                            </svg>
                            <span>Producto Individual</span>
                          </div>
                        )}
                      </div>

                      {/* Información del cliente */}
                      <div className="solicitud-client-info">
                        <div className="client-header">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                          <span>Cliente</span>
                        </div>
                        <div className="client-details">
                          <div className="client-email">{solicitud.cliente_email}</div>
                          {solicitud.cliente_whatsapp && (
                            <div className="client-phone">
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                              </svg>
                              {solicitud.cliente_whatsapp}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Información del producto/productos */}
                      <div className="solicitud-product-info">
                        {resumen.esCarrito ? (
                          <div className="carrito-summary">
                            <div className="carrito-header">
                              <span>Productos en carrito:</span>
                            </div>
                            <div className="carrito-products-preview">
                              {resumen.productos.slice(0, 2).map((prod: any, index: number) => (
                                <div key={index} className="product-preview-item">
                                  <span className="product-name">{prod.modelo}</span>
                                  <span className="product-qty">x{prod.cantidad_solicitada}</span>
                                </div>
                              ))}
                              {resumen.productos.length > 2 && (
                                <div className="more-products">
                                  +{resumen.productos.length - 2} más...
                                </div>
                              )}
                            </div>
                          </div>
                        ) : (
                          <div className="individual-product">
                            <div className="product-name">{solicitud.modelo}</div>
                            <div className="product-details">
                              <span className="product-brand">{solicitud.marca}</span>
                              <span className="product-alm">ALM: {solicitud.numero_almacen}</span>
                              {solicitud.estante && (
                                <span className="product-shelf">Estante: {solicitud.estante}</span>
                              )}
                            </div>
                            <div className="product-quantity">
                              <span>Cantidad: {solicitud.cantidad_solicitada}</span>
                              <span className={`stock-info ${solicitud.cantidad_disponible > 0 ? 'available' : 'unavailable'}`}>
                                Stock: {solicitud.cantidad_disponible || 0}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Resumen financiero */}
                      <div className="solicitud-financial-summary">
                        <div className="financial-header">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <line x1="12" y1="1" x2="12" y2="23"></line>
                            <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                          </svg>
                          <span>Resumen de Cotización</span>
                        </div>
                        <div className="financial-details">
                          <div className="financial-row">
                            <span>Subtotal:</span>
                            <span>{formatPrice(resumen.subtotal)}</span>
                          </div>
                          <div className="financial-row">
                            <span>IVA (16%):</span>
                            <span>{formatPrice(resumen.totalConIVA - resumen.subtotal)}</span>
                          </div>
                          <div className="financial-row total">
                            <span>Total:</span>
                            <span className="total-amount">{formatPrice(resumen.totalConIVA)}</span>
                          </div>
                        </div>
                        {resumen.productosConAlerta > 0 && (
                          <div className="stock-alert">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                              <line x1="12" y1="9" x2="12" y2="13"></line>
                              <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            {resumen.productosConAlerta} producto{resumen.productosConAlerta > 1 ? 's' : ''} con alerta de stock
                          </div>
                        )}
                      </div>

                      {/* Acciones */}
                      <div className="solicitud-card-actions">
                        <button
                          className="modern-action-btn view"
                          onClick={() => handleOpenModal(solicitud, 'view')}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                          </svg>
                          Ver Detalles
                        </button>
                        <button
                          className="modern-action-btn edit"
                          onClick={() => handleOpenModal(solicitud, 'edit')}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                          Editar
                        </button>
                        <button
                          className="modern-action-btn delete"
                          onClick={() => handleOpenModal(solicitud, 'delete')}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                          </svg>
                          Eliminar
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Modales */}
        <SolicitudModals
          solicitud={selectedSolicitud}
          modalType={modalType}
          onClose={handleCloseModal}
          onUpdate={handleUpdateSolicitud}
        />
      </div>
  );
}
