'use client';

import { useState } from 'react';
import { FaWhatsapp, FaEnvelope, FaMapMarkerAlt, FaPhone, FaClock, FaShoppingCart, FaTools, FaInfoCircle, FaArrowRight, FaBuilding } from 'react-icons/fa';

export default function ContactPage() {
  const [activeTab, setActiveTab] = useState('general');

  // Información de contacto
  const contactInfo = {
    general: {
      title: 'Información General',
      description: 'Consultas generales sobre servicios y productos',
      address: 'Fray <PERSON> 1713, Jardín <PERSON>, 64820 Monterrey, N.L.',
      phone: '+52 8183589075',
      email: '<EMAIL>',
      hours: 'Lunes a Viernes de 9:00 AM a 6:00 PM',
      whatsapp: null,
      icon: '📋',
      color: '#0056a6'
    },
    services: {
      title: 'Servicios de Automatización',
      description: 'Contratación de servicios especializados',
      whatsapp: '+5218182803296',
      email: '<EMAIL>',
      contact: '<PERSON>',
      department: '<PERSON>erente de Servicios',
      icon: '🔧',
      color: '#10b981'
    },
    products: {
      title: 'Compra de Productos',
      description: 'Materiales y equipos industriales',
      whatsapp: '+5218187043546',
      email: '<EMAIL>',
      contact: 'Esteban Carrera',
      department: 'Gerente de Ventas',
      icon: '🛒',
      color: '#f7941d'
    }
  };

  // Función para abrir WhatsApp
  const openWhatsApp = (number: string, message: string) => {
    const formattedNumber = number.replace(/\D/g, '');
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/${formattedNumber}?text=${encodedMessage}`, '_blank');
  };

  return (
    <div className="modern-contact-page">
      {/* Hero Section */}
      <div className="contact-hero">
        <div className="contact-hero-content">
          <div className="hero-badge">
            <span className="badge-icon">📞</span>
            <span>Contáctanos</span>
          </div>
          <h1 className="contact-hero-title">
            Estamos <span className="title-highlight">Aquí</span> para Ayudarte
          </h1>
          <p className="contact-hero-description">
            Conecta con nuestro equipo especializado en automatización industrial.
            Encuentra la solución perfecta para tu proyecto.
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">25+</span>
              <span className="stat-label">Años</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">24/7</span>
              <span className="stat-label">Soporte</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">100%</span>
              <span className="stat-label">Satisfacción</span>
            </div>
          </div>
        </div>
        <div className="hero-decoration">
          <div className="decoration-circle decoration-circle-1"></div>
          <div className="decoration-circle decoration-circle-2"></div>
          <div className="decoration-circle decoration-circle-3"></div>
        </div>
      </div>

      {/* Contact Options Section */}
      <div className="contact-options-section">
        <div className="contact-options-container">
          <h2 className="options-title">¿Cómo Podemos Ayudarte?</h2>
          <div className="contact-options-grid">
            {Object.entries(contactInfo).map(([key, info]) => (
              <button
                key={key}
                className={`modern-contact-option ${activeTab === key ? 'active' : ''}`}
                onClick={() => setActiveTab(key)}
                style={{ '--option-color': info.color } as React.CSSProperties}
              >
                <div className="option-icon">{info.icon}</div>
                <h3 className="option-title">{info.title}</h3>
                <p className="option-description">{info.description}</p>
                <div className="option-arrow">
                  <FaArrowRight />
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Details Section */}
      <div className="contact-details-section">
        <div className="contact-details-container">
          <div className="contact-details-grid">
            {/* Contact Information Card */}
            <div className="contact-info-modern">
              <div className="contact-card-modern">
                <div className="contact-card-header-modern">
                  <div
                    className="contact-icon-modern"
                    style={{ backgroundColor: contactInfo[activeTab as keyof typeof contactInfo].color }}
                  >
                    {contactInfo[activeTab as keyof typeof contactInfo].icon}
                  </div>
                  <div className="contact-header-text">
                    <h2 className="contact-card-title">{contactInfo[activeTab as keyof typeof contactInfo].title}</h2>
                    <p className="contact-card-subtitle">{contactInfo[activeTab as keyof typeof contactInfo].description}</p>
                  </div>
                </div>

                <div className="contact-methods">
                  {/* Información general */}
                  {activeTab === 'general' && (
                    <>
                      <div className="contact-method">
                        <div className="method-icon">
                          <FaMapMarkerAlt />
                        </div>
                        <div className="method-content">
                          <h3>Dirección</h3>
                          <p>{contactInfo.general.address}</p>
                        </div>
                      </div>
                      <div className="contact-method">
                        <div className="method-icon">
                          <FaPhone />
                        </div>
                        <div className="method-content">
                          <h3>Teléfono</h3>
                          <p>{contactInfo.general.phone}</p>
                        </div>
                      </div>
                      <div className="contact-method">
                        <div className="method-icon">
                          <FaEnvelope />
                        </div>
                        <div className="method-content">
                          <h3>Email</h3>
                          <a href={`mailto:${contactInfo.general.email}`} className="contact-link-modern">
                            {contactInfo.general.email}
                          </a>
                        </div>
                      </div>
                      <div className="contact-method">
                        <div className="method-icon">
                          <FaClock />
                        </div>
                        <div className="method-content">
                          <h3>Horario</h3>
                          <p>{contactInfo.general.hours}</p>
                        </div>
                      </div>
                    </>
                  )}

                  {/* Información específica para servicios y productos */}
                  {(activeTab === 'services' || activeTab === 'products') && (
                    <>
                      <div className="contact-person">
                        <div className="person-icon">
                          <FaBuilding />
                        </div>
                        <div className="person-info">
                          <h3>
                            {activeTab === 'services' ? contactInfo.services.contact :
                             activeTab === 'products' ? contactInfo.products.contact :
                             'Contacto General'}
                          </h3>
                          <p>
                            {activeTab === 'services' ? contactInfo.services.department :
                             activeTab === 'products' ? contactInfo.products.department :
                             'Departamento General'}
                          </p>
                        </div>
                      </div>

                      <div className="contact-method">
                        <div className="method-icon whatsapp">
                          <FaWhatsapp />
                        </div>
                        <div className="method-content">
                          <h3>WhatsApp</h3>
                          <button
                            className="whatsapp-btn-modern"
                            onClick={() => {
                              const whatsappNumber = activeTab === 'services' ? contactInfo.services.whatsapp :
                                                    activeTab === 'products' ? contactInfo.products.whatsapp : '';
                              if (whatsappNumber) {
                                openWhatsApp(
                                  whatsappNumber,
                                  `Hola, estoy interesado en ${activeTab === 'services' ? 'contratar sus servicios' : 'adquirir productos'} de ECCSA Automation.`
                                );
                              }
                            }}
                          >
                            <FaWhatsapp />
                            <span>Contactar por WhatsApp</span>
                          </button>
                          <p className="phone-number">
                            {activeTab === 'services' ? contactInfo.services.whatsapp :
                             activeTab === 'products' ? contactInfo.products.whatsapp : ''}
                          </p>
                        </div>
                      </div>

                      <div className="contact-method">
                        <div className="method-icon">
                          <FaEnvelope />
                        </div>
                        <div className="method-content">
                          <h3>Email</h3>
                          <a
                            href={`mailto:${activeTab === 'services' ? contactInfo.services.email :
                                           activeTab === 'products' ? contactInfo.products.email : ''}`}
                            className="contact-link-modern"
                          >
                            {activeTab === 'services' ? contactInfo.services.email :
                             activeTab === 'products' ? contactInfo.products.email : ''}
                          </a>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Map Section */}
            <div className="contact-map-modern">
              <div className="map-header">
                <h2>Nuestra Ubicación</h2>
                <p>Visítanos en nuestras oficinas en Monterrey</p>
              </div>
              <div className="map-container-modern">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d14398.87731376815!2d-100.9657417!3d25.5784722!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0xc57be0cd03d2d96f!2sECCSA%20Automation!5e0!3m2!1ses-419!2smx!4v1651245625889!5m2!1ses-419!2smx"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
