import { NextRequest, NextResponse } from 'next/server';
import {
  getAllAlmacenProducts,
  createAlmacenProduct,
  searchAlmacenProducts,
  getLowStockProducts,
  getAlmacenStats,
  getAlmacenProductByNumber,
  getMostSoldProduct,
  getRecentProducts,
  getHighStockProducts,
  getBestSellingProducts
} from '@/eccsa_back/lib/almacen';

// GET handler to fetch all almacen products or search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const lowStock = searchParams.get('lowStock');
    const stats = searchParams.get('stats');
    const dashboard = searchParams.get('dashboard');
    const mostSold = searchParams.get('mostSold');
    const recent = searchParams.get('recent');
    const highStock = searchParams.get('highStock');
    const bestSelling = searchParams.get('bestSelling');

    // Return dashboard data (stats + most sold + recent products)
    if (dashboard === 'true') {
      const [statistics, mostSoldProduct, recentProducts] = await Promise.all([
        getAlmacenStats(),
        getMostSoldProduct(),
        getRecentProducts(5)
      ]);

      return NextResponse.json({
        stats: statistics,
        mostSold: mostSoldProduct,
        recent: recentProducts
      });
    }

    // Return statistics
    if (stats === 'true') {
      const statistics = await getAlmacenStats();
      return NextResponse.json({ stats: statistics });
    }

    // Return most sold product
    if (mostSold === 'true') {
      const product = await getMostSoldProduct();
      return NextResponse.json({ mostSold: product });
    }

    // Return recent products
    if (recent === 'true') {
      const limit = parseInt(searchParams.get('limit') || '5');
      const products = await getRecentProducts(limit);
      return NextResponse.json({ recent: products });
    }

    // Return low stock products
    if (lowStock === 'true') {
      const products = await getLowStockProducts();
      return NextResponse.json({ products });
    }

    // Return high stock products (for featured products)
    if (highStock === 'true') {
      const limit = parseInt(searchParams.get('limit') || '4');
      const products = await getHighStockProducts(limit);
      return NextResponse.json({ products });
    }

    // Return best selling products (for featured products)
    if (bestSelling === 'true') {
      const limit = parseInt(searchParams.get('limit') || '4');
      const products = await getBestSellingProducts(limit);
      return NextResponse.json({ products });
    }

    // Search products
    if (search) {
      const products = await searchAlmacenProducts(search);
      return NextResponse.json({ products });
    }

    // Return all products
    const products = await getAllAlmacenProducts();
    return NextResponse.json({ products });
  } catch (error) {
    console.error('Error fetching almacen products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch almacen products' },
      { status: 500 }
    );
  }
}

// POST handler to create a new almacen product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();



    // Validate required fields
    if (!body.numero_almacen || !body.modelo_existente) {
      return NextResponse.json(
        { error: 'Número de almacén y modelo existente son requeridos' },
        { status: 400 }
      );
    }

    // Check if numero_almacen already exists
    const existingProduct = await getAlmacenProductByNumber(body.numero_almacen);
    if (existingProduct) {
      return NextResponse.json(
        { error: `El número de almacén "${body.numero_almacen}" ya existe. Por favor, use un número diferente.` },
        { status: 400 }
      );
    }

    // Check if modelo_existente already exists
    const existingModel = await searchAlmacenProducts(body.modelo_existente);
    const modelExists = existingModel.find(product =>
      product.modelo_existente.toLowerCase() === body.modelo_existente.toLowerCase()
    );
    if (modelExists) {
      return NextResponse.json(
        { error: `El modelo "${body.modelo_existente}" ya existe. Por favor, use un modelo diferente.` },
        { status: 400 }
      );
    }

    const productId = await createAlmacenProduct({
      numero_almacen: body.numero_almacen,
      estante: body.estante,
      modelo_existente: body.modelo_existente,
      descripcion: body.descripcion,
      precio_venta: body.precio_venta,
      precio_ml: body.precio_ml,
      vendidos: body.vendidos,
      cantidad_nuevo: body.cantidad_nuevo,
      minimo: body.minimo,
      maximo: body.maximo,
      pedir_cantidad: body.pedir_cantidad,
      precio_us: body.precio_us,
      precio_mx: body.precio_mx,
      impuesto: body.impuesto,
      codigo_sat: body.codigo_sat,
      nota: body.nota,
      proveedor: body.proveedor,
      marcas: body.marcas,
      tiempo_entrega_proveedor: body.tiempo_entrega_proveedor,
      fecha_pedido: body.fecha_pedido,
      fecha_recibido: body.fecha_recibido,
      activo: body.activo,
      Url_imagen: body.Url_imagen,
      Datos_importantes_Descripcion_muestra: body.Datos_importantes_Descripcion_muestra,
      tiempo_de_Entrega: body.tiempo_de_Entrega,
    });

    if (!productId) {
      return NextResponse.json(
        { error: 'Failed to create almacen product' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, productId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating almacen product:', error);
    return NextResponse.json(
      { error: 'Failed to create almacen product' },
      { status: 500 }
    );
  }
}
