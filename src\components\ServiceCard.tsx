'use client';

import ImageWithFallback from './ImageWithFallback';
import { IconType } from 'react-icons';

interface ServiceCardProps {
  name: string;
  description: string;
  icon: React.ElementType | string;
  imageSrc?: string;
}

export default function ServiceCard({ name, description, icon, imageSrc }: ServiceCardProps) {
  const defaultImage = '/images/logos/logo_largo.png'; // Imagen por defecto

  // Render the icon based on its type
  const renderIcon = () => {
    if (typeof icon === 'string') {
      // If it's a string (emoji), render it directly
      return <span className="modern-service-icon">{icon}</span>;
    } else {
      // If it's a React component (icon), render it with props
      const IconComponent = icon as IconType;
      return (
        <span className="modern-service-icon">
          <IconComponent size={30} />
        </span>
      );
    }
  };

  return (
    <div className="modern-service-card">
      <div className="modern-service-image">
        <ImageWithFallback
          src={imageSrc || defaultImage}
          fallbackSrc={defaultImage}
          alt={name}
          width={0}
          height={0}
          sizes="100vw"
          containerClassName="modern-service-img-container"
        />
        <div className="service-overlay">
          {renderIcon()}
        </div>
      </div>

      <div className="modern-service-content">
        <h3 className="modern-service-title">{name}</h3>
        <p className="modern-service-description">{description}</p>
        <button className="modern-service-button">
          <span>Más información</span>
        </button>
      </div>
    </div>
  );
}
