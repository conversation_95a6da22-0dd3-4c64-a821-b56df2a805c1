import { NextRequest, NextResponse } from 'next/server';
import { UserModel, User } from '@/eccsa_back/models/User';
import { hashPassword } from '@/eccsa_back/lib/password';

/**
 * GET /api/users/[id]
 * Obtiene un usuario por su ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    const user = await UserModel.getById(id);

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, data: user });
  } catch (error) {
    console.error(`Error al obtener usuario con ID ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error al obtener usuario',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/users/[id]
 * Actualiza un usuario completo
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const userData = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    // Validaciones básicas
    if (!userData.nombre || !userData.nombre_usuario) {
      return NextResponse.json(
        { error: 'Nombre y usuario son requeridos' },
        { status: 400 }
      );
    }

    // Verificar si el usuario existe
    const existingUser = await UserModel.getById(id);

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Verificar si el username ya existe (si es diferente al actual)
    if (userData.nombre_usuario !== existingUser.nombre_usuario) {
      const userWithSameUsername = await UserModel.getByUsername(userData.nombre_usuario);
      if (userWithSameUsername && userWithSameUsername.id !== id) {
        return NextResponse.json(
          { error: 'El nombre de usuario ya está en uso' },
          { status: 409 }
        );
      }
    }

    // Preparar datos para actualizar
    const updateData = {
      nombre: userData.nombre,
      nombre_usuario: userData.nombre_usuario,
      correo: userData.correo || null,
      telefono: userData.telefono || null,
      nivel_ingeniero: userData.nivel_ingeniero !== undefined ? userData.nivel_ingeniero : 4,
      descripcion: userData.descripcion || null,
      foto_usuario: userData.foto_usuario || null,
      activo: userData.activo !== undefined ? userData.activo : true
    };

    const updated = await UserModel.update(id, updateData);

    if (!updated) {
      return NextResponse.json(
        { error: 'No se pudo actualizar el usuario' },
        { status: 500 }
      );
    }

    // Obtener el usuario actualizado
    const updatedUser = await UserModel.getById(id);

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'Error al obtener usuario actualizado' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      user: updatedUser,
      message: 'Usuario actualizado exitosamente'
    });

  } catch (error) {
    console.error(`Error al actualizar usuario con ID ${params.id}:`, error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('Duplicate entry')) {
      return NextResponse.json(
        { error: 'El nombre de usuario ya está en uso' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/[id]
 * Elimina un usuario permanentemente de la base de datos
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    // Verificar si el usuario existe
    const existingUser = await UserModel.getById(id);

    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Eliminar el usuario permanentemente de la base de datos
    const deleted = await UserModel.deleteForever(id);

    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'No se pudo eliminar el usuario' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Usuario eliminado permanentemente de la base de datos'
    });
  } catch (error) {
    console.error(`Error al eliminar usuario con ID ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error al eliminar usuario',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
