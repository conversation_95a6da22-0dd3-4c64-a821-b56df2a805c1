'use client';

import React, { useState } from 'react';

export default function ProyectosPage() {
  const [selectedCategory, setSelectedCategory] = useState('Todos');

  const projects = [
    {
      id: 1,
      title: 'Automatización de Planta Manufacturera',
      category: 'Automatización Industrial',
      description: 'Implementación completa de sistema de automatización para optimización de procesos de manufactura con tecnología PLC Siemens.',
      image: '/images/defaults/Automatización-Industrial.jpg',
      status: 'Completado',
      duration: '6 meses',
      client: 'Industria Manufacturera SA',
      technologies: ['Siemens S7-1500', 'WinCC SCADA', 'Profinet', 'Safety Integrated'],
      results: [
        'Incremento del 35% en productividad',
        'Reducción del 40% en tiempos de parada',
        'Mejora del 25% en calidad del producto',
        'ROI alcanzado en 18 meses'
      ]
    },
    {
      id: 2,
      title: 'Sistema SCADA para Tratamiento de Aguas',
      category: 'Sistemas SCADA',
      description: 'Desarrollo e implementación de sistema SCADA para monitoreo y control en tiempo real de planta de tratamiento de aguas residuales.',
      image: '/images/defaults/SCADA.jpg',
      status: 'Completado',
      duration: '4 meses',
      client: 'Servicios Municipales',
      technologies: ['Wonderware InTouch', 'Allen-Bradley PLC', 'Ethernet/IP', 'Historian'],
      results: [
        'Monitoreo 24/7 automatizado',
        'Reducción del 50% en costos operativos',
        'Cumplimiento normativo mejorado',
        'Alertas tempranas implementadas'
      ]
    },
    {
      id: 3,
      title: 'Instrumentación de Procesos Químicos',
      category: 'Instrumentación',
      description: 'Instalación y calibración de instrumentos de medición y control para procesos químicos con alta precisión y seguridad.',
      image: '/images/defaults/instrumentacion.jpg',
      status: 'En Progreso',
      duration: '8 meses',
      client: 'Petroquímica Industrial',
      technologies: ['Endress+Hauser', 'Rosemount', 'HART Protocol', 'SIL 2/3'],
      results: [
        'Precisión de medición mejorada',
        'Seguridad funcional implementada',
        'Mantenimiento predictivo habilitado',
        'Trazabilidad completa'
      ]
    },
    {
      id: 4,
      title: 'Centro de Control de Motores (CCM)',
      category: 'Control de Motores',
      description: 'Diseño e instalación de centro de control de motores para planta de procesamiento con variadores de frecuencia integrados.',
      image: '/images/defaults/motores.jpg',
      status: 'Completado',
      duration: '3 meses',
      client: 'Procesadora de Alimentos',
      technologies: ['ABB ACS880', 'Schneider Electric', 'Modbus TCP', 'PowerSuite'],
      results: [
        'Eficiencia energética mejorada 30%',
        'Control preciso de velocidad',
        'Mantenimiento simplificado',
        'Diagnósticos avanzados'
      ]
    },
    {
      id: 5,
      title: 'Red Industrial Ethernet',
      category: 'Comunicaciones',
      description: 'Implementación de red industrial Ethernet para integración de sistemas de automatización distribuidos.',
      image: '/images/defaults/redes.jpg',
      status: 'Completado',
      duration: '2 meses',
      client: 'Complejo Industrial',
      technologies: ['Cisco Industrial', 'Profinet', 'OPC UA', 'Cybersecurity'],
      results: [
        'Comunicación en tiempo real',
        'Integración de sistemas legacy',
        'Seguridad cibernética mejorada',
        'Escalabilidad futura'
      ]
    },
    {
      id: 6,
      title: 'Sistema de Seguridad Funcional',
      category: 'Seguridad Industrial',
      description: 'Implementación de sistema de seguridad funcional SIL 3 para protección de personal y equipos en planta química.',
      image: '/images/defaults/seguridad.jpg',
      status: 'En Progreso',
      duration: '5 meses',
      client: 'Química Especializada',
      technologies: ['Pilz PSS 4000', 'Safety Bus', 'TÜV Certified', 'HAZOP'],
      results: [
        'Certificación SIL 3 alcanzada',
        'Reducción de riesgos operativos',
        'Cumplimiento normativo total',
        'Protección integral implementada'
      ]
    }
  ];

  const categories = ['Todos', ...Array.from(new Set(projects.map(project => project.category)))];

  const filteredProjects = selectedCategory === 'Todos'
    ? projects
    : projects.filter(project => project.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completado':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'En Progreso':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Planificado':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div style={{
      marginTop: '0px',
      backgroundColor: '#f8fafc',
      minHeight: '100vh'
    }}>
        {/* Hero Section */}
        <div style={{
          background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
          color: 'white',
          padding: '4rem 2rem 3rem 2rem',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Decorative elements */}
          <div style={{
            position: 'absolute',
            top: '-50px',
            right: '-50px',
            width: '200px',
            height: '200px',
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '50%',
            zIndex: 1
          }}></div>
          <div style={{
            position: 'absolute',
            bottom: '-30px',
            left: '-30px',
            width: '150px',
            height: '150px',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '50%',
            zIndex: 1
          }}></div>

          <div style={{
            maxWidth: '1200px',
            margin: '0 auto',
            textAlign: 'center',
            position: 'relative',
            zIndex: 2
          }}>
            {/* Badge */}
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: 'rgba(255,255,255,0.2)',
              padding: '0.5rem 1rem',
              borderRadius: '25px',
              marginBottom: '1.5rem',
              fontSize: '0.9rem',
              fontWeight: '600'
            }}>
              <span>🏗️</span>
              <span>Casos de Éxito</span>
            </div>

            <h1 style={{
              fontSize: '3rem',
              marginBottom: '1rem',
              fontWeight: 'bold',
              lineHeight: '1.2'
            }}>
              Nuestros <span style={{ color: '#f7941d' }}>Proyectos</span>
            </h1>

            <p style={{
              fontSize: '1.3rem',
              opacity: '0.9',
              maxWidth: '700px',
              margin: '0 auto 2rem auto',
              lineHeight: '1.6'
            }}>
              Descubre algunos de los proyectos más destacados que hemos realizado para nuestros clientes.
              Cada proyecto representa nuestro compromiso con la excelencia y la innovación en automatización industrial.
            </p>

            {/* Stats */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '3rem',
              flexWrap: 'wrap',
              marginTop: '2rem'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: '#f7941d',
                  marginBottom: '0.5rem'
                }}>
                  500+
                </div>
                <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                  Proyectos Completados
                </div>
              </div>

              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: '#f7941d',
                  marginBottom: '0.5rem'
                }}>
                  25+
                </div>
                <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                  Años de Experiencia
                </div>
              </div>

              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  color: '#f7941d',
                  marginBottom: '0.5rem'
                }}>
                  100+
                </div>
                <div style={{ fontSize: '1rem', opacity: '0.8' }}>
                  Clientes Satisfechos
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <div style={{
          backgroundColor: 'white',
          padding: '2.5rem 2rem',
          margin: '-2rem auto 3rem auto',
          borderRadius: '20px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          maxWidth: '1200px',
          position: 'relative',
          zIndex: 10,
          border: '1px solid rgba(0,86,166,0.1)'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <div style={{
              width: '50px',
              height: '50px',
              backgroundColor: '#0056a6',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.5rem'
            }}>
              🔍
            </div>
            <div>
              <h3 style={{
                fontSize: '1.5rem',
                color: '#0056a6',
                margin: '0',
                fontWeight: 'bold'
              }}>
                Filtrar por Categoría
              </h3>
              <p style={{
                fontSize: '1rem',
                color: '#666',
                margin: '0.25rem 0 0 0'
              }}>
                Explora nuestros proyectos por área de especialización
              </p>
            </div>
          </div>

          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '1rem',
            justifyContent: 'center',
            marginBottom: '2rem'
          }}>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  border: '2px solid #0056a6',
                  backgroundColor: selectedCategory === category ? '#0056a6' : 'transparent',
                  color: selectedCategory === category ? 'white' : '#0056a6',
                  fontSize: '0.9rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  outline: 'none'
                }}
                onMouseEnter={(e) => {
                  if (selectedCategory !== category) {
                    e.currentTarget.style.backgroundColor = '#0056a6';
                    e.currentTarget.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedCategory !== category) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#0056a6';
                  }
                }}
              >
                {category}
              </button>
            ))}
          </div>

          <div style={{
            textAlign: 'center',
            padding: '1rem',
            backgroundColor: '#f0f9ff',
            borderRadius: '12px',
            border: '2px solid #e0f2fe'
          }}>
            <span style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#0056a6',
              marginRight: '0.5rem'
            }}>
              {filteredProjects.length}
            </span>
            <span style={{
              fontSize: '1rem',
              color: '#64748b'
            }}>
              {filteredProjects.length === 1 ? 'proyecto encontrado' : 'proyectos encontrados'}
            </span>
          </div>
        </div>

        {/* Projects Grid */}
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 2rem'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(380px, 1fr))',
            gap: '2rem',
            marginBottom: '3rem'
          }}>
            {filteredProjects.map((project) => (
              <div key={project.id} style={{
                backgroundColor: 'white',
                borderRadius: '20px',
                overflow: 'hidden',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
                border: '1px solid #f1f5f9',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                height: '100%'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.12)';
                e.currentTarget.style.borderColor = '#e2e8f0';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.08)';
                e.currentTarget.style.borderColor = '#f1f5f9';
              }}>
                {/* Project Image */}
                <div style={{
                  height: '250px',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                  padding: '1.5rem'
                }}>
                  <img
                    src={project.image}
                    alt={project.title}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'cover',
                      borderRadius: '8px',
                      transition: 'transform 0.4s ease'
                    }}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'scale(1.05)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'scale(1)';
                    }}
                  />

                  {/* Status Badge */}
                  <div style={{
                    position: 'absolute',
                    top: '1rem',
                    right: '1rem',
                    padding: '0.375rem 0.75rem',
                    borderRadius: '20px',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    background: project.status === 'Completado'
                      ? 'linear-gradient(135deg, #10b981, #059669)'
                      : project.status === 'En Progreso'
                      ? 'linear-gradient(135deg, #3b82f6, #1d4ed8)'
                      : 'linear-gradient(135deg, #f59e0b, #d97706)',
                    color: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    zIndex: 10
                  }}>
                    {project.status}
                  </div>
                </div>

                {/* Project Content */}
                <div style={{ padding: '1.5rem', flex: 1, display: 'flex', flexDirection: 'column' }}>
                  {/* Project Header */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '1rem'
                  }}>
                    <div style={{
                      display: 'inline-block',
                      padding: '0.375rem 1rem',
                      background: 'linear-gradient(135deg, #0056a6, #003366)',
                      color: 'white',
                      borderRadius: '25px',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      {project.category}
                    </div>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      color: '#64748b',
                      fontSize: '0.85rem',
                      fontWeight: '500'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                      </svg>
                      {project.duration}
                    </div>
                  </div>

                  {/* Project Title */}
                  <h3 style={{
                    fontSize: '1.3rem',
                    fontWeight: '700',
                    color: '#1e293b',
                    marginBottom: '0.75rem',
                    lineHeight: '1.4'
                  }}>
                    {project.title}
                  </h3>

                  {/* Project Description */}
                  <p style={{
                    fontSize: '0.9rem',
                    color: '#64748b',
                    marginBottom: '1.5rem',
                    lineHeight: '1.6',
                    flex: 1
                  }}>
                    {project.description}
                  </p>

                  {/* Client Info */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '1.5rem',
                    padding: '0.75rem',
                    backgroundColor: '#f8fafc',
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0'
                  }}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    <span style={{
                      fontSize: '0.9rem',
                      color: '#374151',
                      fontWeight: '500'
                    }}>
                      Cliente: {project.client}
                    </span>
                  </div>

                  {/* Technologies */}
                  <div style={{ marginBottom: '1.5rem' }}>
                    <h4 style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#374151',
                      marginBottom: '0.75rem'
                    }}>
                      Tecnologías Utilizadas:
                    </h4>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.5rem'
                    }}>
                      {project.technologies.map((tech, index) => (
                        <span key={index} style={{
                          padding: '0.25rem 0.75rem',
                          backgroundColor: '#e0f2fe',
                          color: '#0369a1',
                          borderRadius: '15px',
                          fontSize: '0.8rem',
                          fontWeight: '500',
                          border: '1px solid #bae6fd'
                        }}>
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Results */}
                  <div style={{ marginBottom: '1.5rem' }}>
                    <h4 style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#374151',
                      marginBottom: '0.75rem'
                    }}>
                      Resultados Obtenidos:
                    </h4>
                    <ul style={{
                      listStyle: 'none',
                      padding: 0,
                      margin: 0
                    }}>
                      {project.results.map((result, index) => (
                        <li key={index} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          marginBottom: '0.5rem',
                          fontSize: '0.85rem',
                          color: '#374151'
                        }}>
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#10b981" strokeWidth="2">
                            <polyline points="20,6 9,17 4,12"></polyline>
                          </svg>
                          {result}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Project Actions */}
                  <div style={{
                    display: 'flex',
                    gap: '0.75rem',
                    marginTop: 'auto'
                  }}>
                    <button style={{
                      flex: 1,
                      padding: '0.75rem 1rem',
                      background: 'linear-gradient(135deg, #0056a6, #003366)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      fontSize: '0.85rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem',
                      boxShadow: '0 4px 12px rgba(0,86,166,0.3)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,86,166,0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,86,166,0.3)';
                    }}>
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                      Ver Detalles
                    </button>

                    <button style={{
                      padding: '0.75rem',
                      backgroundColor: 'transparent',
                      color: '#0056a6',
                      border: '2px solid #0056a6',
                      borderRadius: '12px',
                      fontSize: '0.85rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      minWidth: '50px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#0056a6';
                      e.currentTarget.style.color = 'white';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#0056a6';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                    title="Consultar Proyecto Similar">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div style={{
          background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
          color: 'white',
          padding: '4rem 2rem',
          margin: '4rem 0 0 0',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Decorative elements */}
          <div style={{
            position: 'absolute',
            top: '-30px',
            right: '-30px',
            width: '150px',
            height: '150px',
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '50%',
            zIndex: 1
          }}></div>

          <div style={{
            maxWidth: '800px',
            margin: '0 auto',
            textAlign: 'center',
            position: 'relative',
            zIndex: 2
          }}>
            {/* Icon */}
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 2rem auto'
            }}>
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
              </svg>
            </div>

            <h3 style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              marginBottom: '1.5rem',
              lineHeight: '1.2'
            }}>
              ¿Tienes un <span style={{ color: '#f7941d' }}>proyecto</span> en mente?
            </h3>

            <p style={{
              fontSize: '1.2rem',
              opacity: '0.9',
              marginBottom: '2.5rem',
              lineHeight: '1.6',
              maxWidth: '600px',
              margin: '0 auto 2.5rem auto'
            }}>
              Nuestro equipo de expertos está listo para convertir tu visión en realidad.
              Desarrollamos soluciones personalizadas que superan las expectativas.
            </p>

            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              flexWrap: 'wrap'
            }}>
              <button style={{
                padding: '1rem 2rem',
                background: 'linear-gradient(135deg, #f7941d, #e67e22)',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 4px 12px rgba(247,148,29,0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(247,148,29,0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(247,148,29,0.3)';
              }}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
                Iniciar Proyecto
              </button>

              <button style={{
                padding: '1rem 2rem',
                backgroundColor: 'transparent',
                color: 'white',
                border: '2px solid white',
                borderRadius: '12px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'white';
                e.currentTarget.style.color = '#0056a6';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'white';
                e.currentTarget.style.transform = 'translateY(0)';
              }}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                Solicitar Cotización
              </button>
            </div>
          </div>
        </div>

        {/* CSS for animations and responsive design */}
        <style jsx>{`
          @media (max-width: 768px) {
            .hero-stats {
              flex-direction: column !important;
              gap: 2rem !important;
            }

            .projects-grid {
              grid-template-columns: 1fr !important;
              gap: 1.5rem !important;
            }

            .cta-actions {
              flex-direction: column !important;
              align-items: center !important;
            }
          }

          @media (max-width: 480px) {
            .hero-section {
              padding: 2rem 1rem !important;
            }

            .hero-title {
              font-size: 2rem !important;
            }

            .filters-section {
              margin: -1rem auto 2rem auto !important;
              padding: 1.5rem 1rem !important;
            }

            .project-card {
              margin: 0 auto !important;
              max-width: 350px !important;
            }
          }
        `}</style>
    </div>
  );
}
