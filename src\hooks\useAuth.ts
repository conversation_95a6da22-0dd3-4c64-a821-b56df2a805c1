'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export interface User {
  id: number;
  username: string;
  name: string;
  role: string;
  role_level: number;
  email: string;
  phone?: string;
  photo?: string;
  description?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isLoggingOut: boolean;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    isLoggingOut: false
  });
  const router = useRouter();

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'GET',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.authenticated && data.user) {
          setAuthState({
            user: data.user,
            isLoading: false,
            isAuthenticated: true,
            isLoggingOut: false
          });
        } else {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            isLoggingOut: false
          });
        }
      } else {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
          isLoggingOut: false
        });
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        isLoggingOut: false
      });
    }
  };

  const login = async (username: string, password: string) => {
    try {
      console.log('Starting login process for:', username);
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('Login successful for:', data.user.name);

        // Update auth state immediately
        setAuthState({
          user: data.user,
          isLoading: false,
          isAuthenticated: true,
          isLoggingOut: false
        });

        return { success: true, user: data.user };
      } else {
        console.log('Login failed:', data.error);
        return { success: false, error: data.error || 'Error de autenticación' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Error de conexión' };
    }
  };

  const logout = async () => {
    try {
      console.log('Starting logout process...');
      // Establecer estado de logout
      setAuthState(prev => ({
        ...prev,
        isLoggingOut: true
      }));

      // Llamar a la API de logout
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });

      // Pequeña pausa para mostrar el estado de logout
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      console.log('Logout complete, forcing page reload...');

      // Forzar recarga completa de la página para limpiar todo el estado
      // Esto asegura que no quede ningún estado residual en memoria
      window.location.replace('/eccsa/admin/login');
    }
  };

  const requireAuth = () => {
    if (!authState.isLoading && !authState.isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  };

  const updateUser = (updatedUser: Partial<User>) => {
    setAuthState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...updatedUser } : null
    }));
  };

  return {
    ...authState,
    login,
    logout,
    requireAuth,
    checkAuthStatus,
    updateUser
  };
}
