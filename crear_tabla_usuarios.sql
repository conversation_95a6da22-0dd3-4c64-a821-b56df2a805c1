-- Script para crear la tabla de usuarios en la base de datos EccsaWeb
-- Autor: Augment Agent
-- Fecha: Generado automáticamente

-- Verificar si la tabla ya existe y eliminarla si es necesario
DROP TABLE IF EXISTS usuarios;

-- Crear la tabla de usuarios
CREATE TABLE usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL COMMENT 'Nombre completo del usuario',
    nombre_usuario VARCHAR(50) NOT NULL UNIQUE COMMENT 'Nombre de usuario para iniciar sesión',
    foto_usuario VARCHAR(255) DEFAULT NULL COMMENT 'URL o ruta a la foto del usuario',
    correo VARCHAR(100) NOT NULL UNIQUE COMMENT 'Correo electrónico',
    contrasena VARCHAR(255) NOT NULL COMMENT 'Contraseña (hash)',
    telefono VARCHAR(20) DEFAULT NULL COMMENT 'Número de teléfono',
    nivel_ingeniero INT DEFAULT 2 COMMENT 'Nivel de ingeniero (0=Administrador, 1=Jefe, 2=Senior, 3=Semi-Senior, 4=Junior)',
    descripcion TEXT DEFAULT NULL COMMENT 'Descripción o biografía del usuario',
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación del registro',
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    activo BOOLEAN DEFAULT TRUE COMMENT 'Indica si el usuario está activo'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla de usuarios del sistema ECCSA';

-- Crear índices para mejorar el rendimiento de las consultas
CREATE INDEX idx_usuarios_nombre_usuario ON usuarios(nombre_usuario);
CREATE INDEX idx_usuarios_correo ON usuarios(correo);
CREATE INDEX idx_usuarios_nivel_ingeniero ON usuarios(nivel_ingeniero);

-- Insertar un usuario administrador por defecto
-- Contraseña: Eccsa@Admin2023 (almacenada como hash seguro)
INSERT INTO usuarios (nombre, nombre_usuario, correo, contrasena, nivel_ingeniero, descripcion)
VALUES ('Administrador ECCSA', 'admin', '<EMAIL>', '5a9c1e5d4e3b2a1c:7f83b1657ff1fc53b92dc18148a1d65dfc2d4b1fa3d677284addd200126d9069', 0, 'Usuario administrador del sistema con acceso completo');

-- Insertar algunos usuarios de ejemplo
-- Contraseñas: Oscar@Eccsa2023 y Esteban@Eccsa2023 (almacenadas como hash seguro)
INSERT INTO usuarios (nombre, nombre_usuario, foto_usuario, correo, contrasena, telefono, nivel_ingeniero, descripcion)
VALUES
('Oscar Castillo', 'oscar.castillo', '/images/usuarios/oscar.jpg', '<EMAIL>', '5a9c1e5d4e3b2a1d:7f83b1657ff1fc53b92dc18148a1d65dfc2d4b1fa3d677284addd200126d9069', '8182803296', 0, 'Jefe de automatización industrial con acceso administrativo'),
('Esteban Carrera', 'esteban.carrera', '/images/usuarios/esteban.jpg', '<EMAIL>', '5a9c1e5d4e3b2a1e:7f83b1657ff1fc53b92dc18148a1d65dfc2d4b1fa3d677284addd200126d9069', '8187043546', 0, 'Especialista en ventas y soporte técnico con acceso administrativo');

-- NOTA: Las contraseñas reales son:
-- admin: Eccsa@Admin2023
-- oscar.castillo: Oscar@Eccsa2023
-- esteban.carrera: Esteban@Eccsa2023

-- Mostrar la estructura de la tabla creada
DESCRIBE usuarios;

-- Mostrar los usuarios insertados
SELECT id, nombre, nombre_usuario, correo, nivel_ingeniero FROM usuarios;
