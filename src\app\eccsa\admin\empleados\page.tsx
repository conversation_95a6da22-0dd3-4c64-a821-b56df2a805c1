'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import EditUserModal from '@/components/admin/EditUserModal';
import { NotificationModal, DeleteModal, FormModal } from '@/components/admin/ModernModals';
import { FaEdit, FaUserTimes, FaUserCheck, FaSync, FaUserPlus, FaTrash } from 'react-icons/fa';

interface Usuario {
  id: number;
  nombre: string;
  nombre_usuario: string;
  correo?: string;
  telefono?: string;
  nivel_ingeniero: number;
  descripcion?: string;
  foto_usuario?: string;
  fecha_creacion?: string;
  activo: boolean;
}

// Mapeo de niveles a nombres
const ROLE_NAMES = {
  0: 'Administrador',
  1: 'Jefe',
  2: 'Ingeniero Senior',
  3: 'Ingeniero Junior',
  4: 'Ingeniero de Apoyo',
  5: 'Practicante'
};

export default function EmpleadosPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [filteredUsuarios, setFilteredUsuarios] = useState<Usuario[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNivel, setSelectedNivel] = useState('');
  const [selectedEstado, setSelectedEstado] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState<Usuario | null>(null);

  // Estados para modales modernos
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<Usuario | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [userToToggle, setUserToToggle] = useState<Usuario | null>(null);
  const [toggleLoading, setToggleLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalMessage, setModalMessage] = useState('');

  const [error, setError] = useState('');

  // Verificar permisos - solo administradores (0) y jefes (1)
  const hasPermission = user && (user.role_level === 0 || user.role_level === 1);

  // Redirect if not authenticated or no permission
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    } else if (!isLoading && isAuthenticated && !hasPermission) {
      router.push('/eccsa/admin');
    }
  }, [isAuthenticated, isLoading, hasPermission, router]);

  // Cargar usuarios de la base de datos
  const loadUsuarios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/users');
      const data = await response.json();

      if (response.ok && data.success) {
        const usuariosData = data.data || [];
        setUsuarios(usuariosData);
        setFilteredUsuarios(usuariosData);
      } else {
        setError(data.error || 'Error al cargar usuarios');
      }
    } catch (error) {
      console.error('Error loading usuarios:', error);
      setError('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (hasPermission) {
      loadUsuarios();
    }
  }, [hasPermission]);

  // Filtrar usuarios
  useEffect(() => {
    let filtered = usuarios.filter(usuario => {
      const matchesSearch =
        usuario.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        usuario.nombre_usuario.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (usuario.correo && usuario.correo.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesNivel = !selectedNivel || usuario.nivel_ingeniero.toString() === selectedNivel;
      const matchesEstado = !selectedEstado ||
        (selectedEstado === 'activo' && usuario.activo) ||
        (selectedEstado === 'inactivo' && !usuario.activo);

      return matchesSearch && matchesNivel && matchesEstado;
    });

    setFilteredUsuarios(filtered);
  }, [usuarios, searchTerm, selectedNivel, selectedEstado]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRoleName = (nivel: number): string => {
    return ROLE_NAMES[nivel as keyof typeof ROLE_NAMES] || 'Desconocido';
  };

  const getRoleBadgeClass = (nivel: number): string => {
    switch (nivel) {
      case 0: return 'admin-badge-danger';   // Administrador
      case 1: return 'admin-badge-warning';  // Jefe
      case 2: return 'admin-badge-info';     // Ingeniero Senior
      case 3: return 'admin-badge-primary';  // Ingeniero Junior
      case 4: return 'admin-badge-secondary'; // Ingeniero de Apoyo
      case 5: return 'admin-badge-light';    // Practicante
      default: return 'admin-badge-secondary';
    }
  };

  const getGlassmorphismRoleBadgeClass = (nivel: number): string => {
    switch (nivel) {
      case 0: return 'admin';     // Administrador
      case 1: return 'manager';   // Jefe
      case 2: return 'senior';    // Ingeniero Senior
      case 3: return 'junior';    // Ingeniero Junior
      case 4: return 'support';   // Ingeniero de Apoyo
      case 5: return 'intern';    // Practicante
      default: return 'support';
    }
  };

  const getEstadoBadgeClass = (activo: boolean): string => {
    return activo ? 'admin-badge-success' : 'admin-badge-danger';
  };

  // Funciones para mostrar notificaciones
  const showSuccessNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowSuccessModal(true);
  };

  const showErrorNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowErrorModal(true);
  };

  // Función para iniciar cambio de estado de usuario
  const handleToggleStatus = (usuario: Usuario) => {
    setUserToToggle(usuario);
    setShowToggleModal(true);
  };

  // Función para confirmar cambio de estado
  const confirmToggleStatus = async () => {
    if (!userToToggle) return;

    try {
      setToggleLoading(true);
      const response = await fetch(`/api/users/${userToToggle.id}/toggle-status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        // Actualizar la lista de usuarios
        setUsuarios(prev => prev.map(u =>
          u.id === userToToggle.id ? { ...u, activo: !u.activo } : u
        ));
        setFilteredUsuarios(prev => prev.map(u =>
          u.id === userToToggle.id ? { ...u, activo: !u.activo } : u
        ));

        setShowToggleModal(false);
        setUserToToggle(null);

        showSuccessNotification(
          'Estado Actualizado',
          `Usuario ${userToToggle.nombre} ${userToToggle.activo ? 'desactivado' : 'activado'} exitosamente.`
        );
      } else {
        showErrorNotification(
          'Error al Cambiar Estado',
          data.error || 'Error al cambiar el estado del usuario'
        );
      }
    } catch (error) {
      console.error('Error:', error);
      showErrorNotification(
        'Error de Conexión',
        'No se pudo conectar con el servidor. Por favor, intenta nuevamente.'
      );
    } finally {
      setToggleLoading(false);
    }
  };

  // Función para manejar la actualización de usuario desde el modal
  const handleUserUpdate = (updatedUser: Usuario) => {
    setUsuarios(prev => prev.map(u =>
      u.id === updatedUser.id ? updatedUser : u
    ));
    setFilteredUsuarios(prev => prev.map(u =>
      u.id === updatedUser.id ? updatedUser : u
    ));
    showSuccessNotification(
      'Usuario Actualizado',
      'Los cambios han sido guardados exitosamente.'
    );
  };

  // Función para manejar la creación de usuario desde el modal
  const handleUserCreate = (newUser: Usuario) => {
    setUsuarios(prev => [...prev, newUser]);
    setFilteredUsuarios(prev => [...prev, newUser]);
    showSuccessNotification(
      'Usuario Creado',
      'El nuevo usuario ha sido agregado exitosamente al sistema.'
    );
  };

  // Función para iniciar eliminación de usuario
  const handleDeleteUser = (usuario: Usuario) => {
    // Verificar que no sea el último administrador
    if (usuario.nivel_ingeniero === 0) {
      const adminCount = usuarios.filter(u => u.nivel_ingeniero === 0 && u.activo).length;
      if (adminCount <= 1) {
        showErrorNotification(
          'Operación No Permitida',
          'No se puede eliminar el último administrador del sistema. Debe haber al menos un administrador activo en todo momento.'
        );
        return;
      }
    }

    setUserToDelete(usuario);
    setShowDeleteModal(true);
  };

  // Función para confirmar eliminación
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setDeleteLoading(true);
      const response = await fetch(`/api/users/${userToDelete.id}/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        // Remover el usuario de la lista
        setUsuarios(prev => prev.filter(u => u.id !== userToDelete.id));
        setFilteredUsuarios(prev => prev.filter(u => u.id !== userToDelete.id));

        setShowDeleteModal(false);
        setUserToDelete(null);

        showSuccessNotification(
          'Usuario Eliminado',
          `Usuario ${userToDelete.nombre} eliminado exitosamente del sistema.`
        );
      } else {
        showErrorNotification(
          'Error al Eliminar',
          data.error || 'Error al eliminar el usuario'
        );
      }
    } catch (error) {
      console.error('Error:', error);
      showErrorNotification(
        'Error de Conexión',
        'No se pudo conectar con el servidor. Por favor, intenta nuevamente.'
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Don't render if no permission
  if (!hasPermission) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem',
        color: '#dc2626'
      }}>
        No tienes permisos para acceder a esta sección.
      </div>
    );
  }

  return (
    <div className="glassmorphism-dashboard">
      {/* Header */}
      <div className="glassmorphism-dashboard-header">
        <h1 className="glassmorphism-dashboard-title">Gestión de Empleados</h1>
        <p className="glassmorphism-dashboard-subtitle">
          Administrar personal y recursos humanos
        </p>
        <div className="glassmorphism-dashboard-actions">
          <button
            className="glassmorphism-refresh-button"
            onClick={loadUsuarios}
            disabled={loading}
          >
            <FaSync className="w-5 h-5" />
            {loading ? 'Actualizando...' : 'Actualizar'}
          </button>
          <button
            className="glassmorphism-refresh-button"
            onClick={() => {
              setEditingUser(null);
              setShowModal(true);
            }}
            style={{
              background: 'rgba(59, 130, 246, 0.15)',
              borderColor: 'rgba(59, 130, 246, 0.3)',
              color: '#3b82f6'
            }}
          >
            <FaUserPlus className="w-5 h-5" />
            Agregar Usuario
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="glassmorphism-card">
        <div className="glassmorphism-card-content">
          <div className="admin-filters">
            <div className="admin-filter-group">
              <input
                type="text"
                placeholder="Buscar usuarios..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="glassmorphism-input"
                style={{
                  width: '100%',
                  padding: '0.875rem 1rem',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  borderRadius: '12px',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  color: '#1e293b',
                  fontSize: '0.875rem',
                  transition: 'all 0.3s ease'
                }}
              />
            </div>
            <div className="admin-filter-group">
              <select
                value={selectedNivel}
                onChange={(e) => setSelectedNivel(e.target.value)}
                className="glassmorphism-select"
                style={{
                  width: '100%',
                  padding: '0.875rem 1rem',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  borderRadius: '12px',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  color: '#1e293b',
                  fontSize: '0.875rem',
                  transition: 'all 0.3s ease'
                }}
              >
                <option value="">Todos los niveles</option>
                <option value="0">Administrador</option>
                <option value="1">Jefe</option>
                <option value="2">Ingeniero Senior</option>
                <option value="3">Ingeniero Junior</option>
                <option value="4">Ingeniero de Apoyo</option>
                <option value="5">Practicante</option>
              </select>
            </div>
            <div className="admin-filter-group">
              <select
                value={selectedEstado}
                onChange={(e) => setSelectedEstado(e.target.value)}
                className="glassmorphism-select"
                style={{
                  width: '100%',
                  padding: '0.875rem 1rem',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  borderRadius: '12px',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  color: '#1e293b',
                  fontSize: '0.875rem',
                  transition: 'all 0.3s ease'
                }}
              >
                <option value="">Todos los estados</option>
                <option value="activo">Activo</option>
                <option value="inactivo">Inactivo</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Usuarios Table */}
      <div className="glassmorphism-card">
        <div className="glassmorphism-card-header">
          <h2 className="glassmorphism-card-title">
            Usuarios ({filteredUsuarios.length})
          </h2>
        </div>
        <div className="glassmorphism-card-content">
          {error && (
            <div className="glassmorphism-error">
              <p className="glassmorphism-error-text">{error}</p>
            </div>
          )}
          {loading ? (
            <div className="glassmorphism-loading">
              <div className="glassmorphism-spinner"></div>
              <p className="glassmorphism-loading-text">Cargando usuarios...</p>
            </div>
          ) : filteredUsuarios.length === 0 ? (
            <div className="glassmorphism-empty">
              <div className="glassmorphism-empty-icon">
                <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="glassmorphism-empty-title">No se encontraron usuarios</h3>
              <p className="glassmorphism-empty-text">No hay usuarios que coincidan con los filtros aplicados.</p>
            </div>
          ) : (
            <div className="glassmorphism-table-container">
              <table className="glassmorphism-table">
                <thead>
                  <tr>
                    <th>Usuario</th>
                    <th>Correo</th>
                    <th>Teléfono</th>
                    <th>Nivel</th>
                    <th>Estado</th>
                    <th>Fecha Creación</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsuarios.map((usuario) => (
                    <tr key={usuario.id}>
                      <td>
                        <div className="glassmorphism-employee-info">
                          <div className="glassmorphism-employee-name">{usuario.nombre}</div>
                          <div className="glassmorphism-employee-username">@{usuario.nombre_usuario}</div>
                          {usuario.descripcion && (
                            <div className="glassmorphism-employee-description">{usuario.descripcion}</div>
                          )}
                        </div>
                      </td>
                      <td>{usuario.correo || 'No especificado'}</td>
                      <td>{usuario.telefono || 'No especificado'}</td>
                      <td>
                        <span className={`glassmorphism-badge ${getGlassmorphismRoleBadgeClass(usuario.nivel_ingeniero)}`}>
                          {getRoleName(usuario.nivel_ingeniero)}
                        </span>
                      </td>
                      <td>
                        <span className={`glassmorphism-badge ${usuario.activo ? 'active' : 'inactive'}`}>
                          {usuario.activo ? 'Activo' : 'Inactivo'}
                        </span>
                      </td>
                      <td className="glassmorphism-table-date">
                        {usuario.fecha_creacion ? formatDate(usuario.fecha_creacion) : 'No disponible'}
                      </td>
                      <td>
                        <div className="glassmorphism-table-actions">
                          <button
                            onClick={() => {
                              setEditingUser(usuario);
                              setShowModal(true);
                            }}
                            className="glassmorphism-action-btn edit"
                            title="Editar usuario"
                          >
                            <FaEdit />
                          </button>
                          <button
                            onClick={() => handleToggleStatus(usuario)}
                            className={`glassmorphism-action-btn ${usuario.activo ? 'delete' : 'success'}`}
                            title={usuario.activo ? 'Desactivar usuario' : 'Activar usuario'}
                          >
                            {usuario.activo ? (
                              <FaUserTimes />
                            ) : (
                              <FaUserCheck />
                            )}
                          </button>
                          <button
                            onClick={() => handleDeleteUser(usuario)}
                            className="glassmorphism-action-btn delete"
                            title="Eliminar usuario permanentemente"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modal para editar/agregar usuario */}
      <EditUserModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingUser(null);
        }}
        user={editingUser}
        onUserUpdate={handleUserUpdate}
        onUserCreate={handleUserCreate}
      />

      {/* Modal de Eliminación Moderno */}
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setUserToDelete(null);
        }}
        onConfirm={confirmDeleteUser}
        loading={deleteLoading}
        title="Eliminar Usuario"
        itemName={userToDelete ? userToDelete.nombre : ''}
        itemDetails={userToDelete ? `Usuario: ${userToDelete.nombre}\nNivel: ${getRoleName(userToDelete.nivel_ingeniero)}\nCorreo: ${userToDelete.correo || 'No especificado'}\nEstado: ${userToDelete.activo ? 'Activo' : 'Inactivo'}` : ''}
        warningMessage="Esta acción eliminará permanentemente este usuario del sistema. Si solo quieres desactivar el usuario temporalmente, usa el botón de activar/desactivar."
      />

      {/* Modal de Cambio de Estado */}
      <DeleteModal
        isOpen={showToggleModal}
        onClose={() => {
          setShowToggleModal(false);
          setUserToToggle(null);
        }}
        onConfirm={confirmToggleStatus}
        loading={toggleLoading}
        title={userToToggle?.activo ? "Desactivar Usuario" : "Activar Usuario"}
        itemName={userToToggle ? userToToggle.nombre : ''}
        itemDetails={userToToggle ? `Usuario: ${userToToggle.nombre}\nNivel: ${getRoleName(userToToggle.nivel_ingeniero)}\nEstado actual: ${userToToggle.activo ? 'Activo' : 'Inactivo'}` : ''}
        warningMessage={userToToggle?.activo ? "Esta acción desactivará temporalmente el acceso del usuario al sistema." : "Esta acción reactivará el acceso del usuario al sistema."}
      />

      {/* Modales de Notificación Modernos */}
      {showSuccessModal && (
        <NotificationModal
          type="success"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowSuccessModal(false)}
        />
      )}

      {showErrorModal && (
        <NotificationModal
          type="error"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}
    </div>
  );
}
