/**
 * Script para cargar dinámicamente recursos JavaScript y CSS
 * Esta solución evita problemas de MIME type cargando los recursos como texto plano
 * y luego aplicándolos manualmente
 */
(function() {
  // Configuración
  var config = {
    debug: true,
    retryCount: 3,
    retryDelay: 1000,
    timeout: 10000
  };
  
  // Función para imprimir mensajes de depuración
  function log(message, type) {
    if (config.debug) {
      var prefix = '[DynamicLoader]';
      
      switch (type) {
        case 'error':
          console.error(prefix + ' Error: ' + message);
          break;
        case 'warn':
          console.warn(prefix + ' Warning: ' + message);
          break;
        case 'success':
          console.log(prefix + ' Success: ' + message);
          break;
        default:
          console.log(prefix + ' Info: ' + message);
      }
    }
  }
  
  // Función para cargar un archivo como texto plano
  function loadAsText(url, callback, errorCallback, attempt) {
    attempt = attempt || 1;
    
    var xhr = new XMLHttpRequest();
    var timeoutId = setTimeout(function() {
      xhr.abort();
      if (attempt < config.retryCount) {
        log('Timeout loading ' + url + '. Retrying (' + (attempt + 1) + '/' + config.retryCount + ')...', 'warn');
        setTimeout(function() {
          loadAsText(url, callback, errorCallback, attempt + 1);
        }, config.retryDelay);
      } else {
        log('Failed to load ' + url + ' after ' + config.retryCount + ' attempts.', 'error');
        if (errorCallback) errorCallback(new Error('Timeout loading ' + url));
      }
    }, config.timeout);
    
    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4) {
        clearTimeout(timeoutId);
        
        if (xhr.status === 200) {
          log('Successfully loaded ' + url, 'success');
          callback(xhr.responseText);
        } else {
          if (attempt < config.retryCount) {
            log('Failed to load ' + url + ' (status: ' + xhr.status + '). Retrying (' + (attempt + 1) + '/' + config.retryCount + ')...', 'warn');
            setTimeout(function() {
              loadAsText(url, callback, errorCallback, attempt + 1);
            }, config.retryDelay);
          } else {
            log('Failed to load ' + url + ' after ' + config.retryCount + ' attempts.', 'error');
            if (errorCallback) errorCallback(new Error('Failed to load ' + url + ' (status: ' + xhr.status + ')'));
          }
        }
      }
    };
    
    try {
      xhr.open('GET', url, true);
      xhr.send();
    } catch (e) {
      clearTimeout(timeoutId);
      log('Exception while loading ' + url + ': ' + e.message, 'error');
      if (errorCallback) errorCallback(e);
    }
  }
  
  // Función para cargar un script JavaScript
  function loadScript(url, callback, errorCallback) {
    log('Loading script: ' + url);
    
    loadAsText(url, function(content) {
      try {
        // Crear un elemento script
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.text = content;
        
        // Agregar el script al documento
        document.head.appendChild(script);
        
        log('Script applied: ' + url, 'success');
        if (callback) callback();
      } catch (e) {
        log('Error applying script ' + url + ': ' + e.message, 'error');
        if (errorCallback) errorCallback(e);
      }
    }, errorCallback);
  }
  
  // Función para cargar una hoja de estilo CSS
  function loadStyle(url, callback, errorCallback) {
    log('Loading style: ' + url);
    
    loadAsText(url, function(content) {
      try {
        // Crear un elemento style
        var style = document.createElement('style');
        style.type = 'text/css';
        
        // Agregar el contenido CSS
        if (style.styleSheet) {
          // IE
          style.styleSheet.cssText = content;
        } else {
          // Otros navegadores
          style.appendChild(document.createTextNode(content));
        }
        
        // Agregar el estilo al documento
        document.head.appendChild(style);
        
        log('Style applied: ' + url, 'success');
        if (callback) callback();
      } catch (e) {
        log('Error applying style ' + url + ': ' + e.message, 'error');
        if (errorCallback) errorCallback(e);
      }
    }, errorCallback);
  }
  
  // Función para cargar múltiples recursos en secuencia
  function loadSequential(resources, onComplete, onError) {
    var index = 0;
    
    function loadNext() {
      if (index >= resources.length) {
        if (onComplete) onComplete();
        return;
      }
      
      var resource = resources[index++];
      var loader = resource.type === 'script' ? loadScript : loadStyle;
      
      loader(resource.url, loadNext, function(error) {
        log('Error loading resource: ' + error.message, 'error');
        if (onError) onError(error);
        loadNext(); // Continuar con el siguiente recurso incluso si hay un error
      });
    }
    
    loadNext();
  }
  
  // Función para cargar múltiples recursos en paralelo
  function loadParallel(resources, onComplete, onError) {
    var count = resources.length;
    var errors = [];
    
    if (count === 0) {
      if (onComplete) onComplete();
      return;
    }
    
    resources.forEach(function(resource) {
      var loader = resource.type === 'script' ? loadScript : loadStyle;
      
      loader(resource.url, function() {
        count--;
        if (count === 0) {
          if (errors.length > 0) {
            if (onError) onError(new Error('Some resources failed to load'));
          } else {
            if (onComplete) onComplete();
          }
        }
      }, function(error) {
        errors.push(error);
        count--;
        if (count === 0) {
          if (onError) onError(new Error('Some resources failed to load'));
        }
      });
    });
  }
  
  // Función para reemplazar los enlaces a recursos externos con carga dinámica
  function replaceResourceLinks() {
    var scripts = [];
    var styles = [];
    
    // Recopilar scripts
    document.querySelectorAll('script[src]').forEach(function(script) {
      var src = script.getAttribute('src');
      if (src && !src.startsWith('data:') && !script.hasAttribute('data-dynamic-loaded')) {
        scripts.push({
          url: src,
          type: 'script',
          element: script
        });
        script.setAttribute('data-dynamic-loaded', 'true');
      }
    });
    
    // Recopilar estilos
    document.querySelectorAll('link[rel="stylesheet"]').forEach(function(link) {
      var href = link.getAttribute('href');
      if (href && !href.startsWith('data:') && !link.hasAttribute('data-dynamic-loaded')) {
        styles.push({
          url: href,
          type: 'style',
          element: link
        });
        link.setAttribute('data-dynamic-loaded', 'true');
      }
    });
    
    // Cargar estilos primero, luego scripts
    loadParallel(styles, function() {
      loadSequential(scripts, function() {
        log('All resources loaded successfully', 'success');
        // Disparar evento de que todos los recursos se han cargado
        document.dispatchEvent(new Event('DynamicLoaderComplete'));
      }, function(error) {
        log('Error loading resources: ' + error.message, 'error');
      });
    }, function(error) {
      log('Error loading styles: ' + error.message, 'error');
      // Continuar con los scripts incluso si hay errores en los estilos
      loadSequential(scripts, function() {
        log('Scripts loaded successfully', 'success');
        document.dispatchEvent(new Event('DynamicLoaderComplete'));
      }, function(error) {
        log('Error loading scripts: ' + error.message, 'error');
      });
    });
  }
  
  // Iniciar el proceso cuando el DOM esté listo
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', replaceResourceLinks);
  } else {
    replaceResourceLinks();
  }
  
  // Exponer funciones públicas
  window.DynamicLoader = {
    loadScript: loadScript,
    loadStyle: loadStyle,
    loadSequential: loadSequential,
    loadParallel: loadParallel
  };
})();
