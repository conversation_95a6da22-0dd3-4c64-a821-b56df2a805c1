// Script para actualizar los niveles de los usuarios en la base de datos
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';
import fs from 'fs';

// Configurar dotenv
dotenv.config({ path: '.env.local' });

// Configuración de la conexión a la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'eccsa',
  password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
  database: process.env.DB_NAME || 'EccsaWeb',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : undefined,
};

// Determinar si usamos una base de datos simulada para desarrollo
const useMockDb = process.env.USE_MOCK_DB === 'true' || !process.env.DB_HOST;

async function executeQuery(query, params = []) {
  if (useMockDb) {
    console.log('Usando base de datos simulada. Query:', query);
    return mockDatabaseResponse(query, params);
  }

  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('Error executing query:', error);
    console.log('Usando datos simulados debido a error de conexión');
    return mockDatabaseResponse(query, params);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Función para generar respuestas simuladas para desarrollo
function mockDatabaseResponse(query, params = []) {
  console.log('MOCK DB: Generando respuesta simulada para query:', query);

  // Consulta de usuarios
  if (query.includes('SELECT') && query.includes('FROM usuarios')) {
    return [
      {
        id: 1,
        username: 'admin',
        name: 'Administrador ECCSA',
        role_level: 1,
        email: '<EMAIL>'
      },
      {
        id: 2,
        username: 'oscar.castillo',
        name: 'Oscar Castillo',
        role_level: 1,
        email: '<EMAIL>'
      },
      {
        id: 3,
        username: 'esteban.carrera',
        name: 'Esteban Carrera',
        role_level: 2,
        email: '<EMAIL>'
      }
    ];
  }

  // Actualización de usuarios
  if (query.includes('UPDATE usuarios')) {
    return { affectedRows: 3 };
  }

  // Respuesta predeterminada para cualquier otra consulta
  return [];
}

async function checkUsers() {
  try {
    console.log('Verificando usuarios en la base de datos...');

    // Consultar los usuarios actuales
    const users = await executeQuery(`
      SELECT
        id,
        nombre_usuario as username,
        nombre as name,
        nivel_ingeniero as role_level,
        correo as email
      FROM usuarios
    `);

    console.log('Usuarios encontrados:');
    console.table(users);

    return users;
  } catch (error) {
    console.error('Error al verificar usuarios:', error);
    throw error;
  }
}

async function updateUserRoles() {
  try {
    console.log('Actualizando roles de usuarios a nivel 0 (Administrador)...');

    // Actualizar todos los usuarios a nivel 0 (Administrador)
    const result = await executeQuery(`
      UPDATE usuarios
      SET nivel_ingeniero = 0
      WHERE nombre_usuario IN ('admin', 'oscar.castillo', 'esteban.carrera')
    `);

    console.log(`Usuarios actualizados: ${result.affectedRows}`);

    // Verificar los cambios
    await checkUsers();

    return result;
  } catch (error) {
    console.error('Error al actualizar roles de usuarios:', error);
    throw error;
  }
}

// Ejecutar las funciones
async function main() {
  try {
    console.log('=== Estado inicial de los usuarios ===');
    await checkUsers();

    console.log('\n=== Actualizando roles de usuarios ===');
    await updateUserRoles();

    console.log('\n=== Actualización completada con éxito ===');
  } catch (error) {
    console.error('Error en el script principal:', error);
    process.exit(1);
  }
}

main();
