/**
 * Script para generar una versión estática del sitio que funcione sin JavaScript
 * Esta es una solución radical para problemas de MIME type en servidores restrictivos
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const copyFileAsync = promisify(fs.copyFile);
const mkdirAsync = promisify(fs.mkdir);

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  fg: {
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m',
  }
};

// Función para imprimir mensajes con formato
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  
  switch (type) {
    case 'info':
      console.log(`${colors.fg.blue}[${timestamp}] INFO:${colors.reset} ${message}`);
      break;
    case 'success':
      console.log(`${colors.fg.green}[${timestamp}] ÉXITO:${colors.reset} ${message}`);
      break;
    case 'warning':
      console.log(`${colors.fg.yellow}[${timestamp}] ADVERTENCIA:${colors.reset} ${message}`);
      break;
    case 'error':
      console.log(`${colors.fg.red}[${timestamp}] ERROR:${colors.reset} ${message}`);
      break;
  }
}

// Función para encontrar todos los archivos HTML en un directorio
function findHtmlFiles(dir) {
  const htmlFiles = [];
  
  function searchDir(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        searchDir(filePath);
      } else if (file.endsWith('.html')) {
        htmlFiles.push(filePath);
      }
    }
  }
  
  searchDir(dir);
  return htmlFiles;
}

// Función para leer un archivo
async function readFile(filePath) {
  try {
    return await readFileAsync(filePath, 'utf8');
  } catch (error) {
    log(`Error al leer el archivo ${filePath}: ${error.message}`, 'error');
    return null;
  }
}

// Función para simplificar el HTML
async function simplifyHtml(htmlContent) {
  // Eliminar scripts que no sean inline
  let modifiedContent = htmlContent.replace(/<script[^>]+src=["'][^"']+["'][^>]*><\/script>/g, '');
  
  // Eliminar atributos de carga diferida (lazy loading)
  modifiedContent = modifiedContent.replace(/\s+loading=["']lazy["']/g, '');
  
  // Eliminar atributos de data-reactroot y similares
  modifiedContent = modifiedContent.replace(/\s+data-reactroot[^>]*/g, '');
  
  // Eliminar comentarios HTML
  modifiedContent = modifiedContent.replace(/<!--[\s\S]*?-->/g, '');
  
  // Agregar meta tag para evitar que el navegador intente cargar JavaScript
  modifiedContent = modifiedContent.replace(/<head>/i, '<head>\n  <meta name="robots" content="noindex">\n  <meta http-equiv="Content-Security-Policy" content="script-src \'none\'">');
  
  // Agregar mensaje de versión estática
  modifiedContent = modifiedContent.replace(/<body[^>]*>/i, '$&\n  <div style="background-color: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 4px; text-align: center;">Esta es una versión estática del sitio. Algunas funcionalidades pueden no estar disponibles.</div>');
  
  return modifiedContent;
}

// Función para copiar imágenes y otros recursos estáticos
async function copyStaticAssets(sourceDir, targetDir) {
  // Crear el directorio de destino si no existe
  if (!fs.existsSync(targetDir)) {
    await mkdirAsync(targetDir, { recursive: true });
  }
  
  // Leer el contenido del directorio de origen
  const files = fs.readdirSync(sourceDir);
  
  // Copiar cada archivo o directorio
  for (const file of files) {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);
    
    const stat = fs.statSync(sourcePath);
    
    if (stat.isDirectory()) {
      // Si es un directorio, llamar recursivamente a esta función
      await copyStaticAssets(sourcePath, targetPath);
    } else {
      // Si es un archivo, copiarlo si es una imagen u otro recurso estático
      const ext = path.extname(file).toLowerCase();
      if (['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.ico', '.ttf', '.woff', '.woff2', '.eot', '.css'].includes(ext)) {
        try {
          await copyFileAsync(sourcePath, targetPath);
          log(`Archivo ${sourcePath} copiado correctamente`, 'success');
        } catch (error) {
          log(`Error al copiar el archivo ${sourcePath}: ${error.message}`, 'error');
        }
      }
    }
  }
}

// Función principal
async function generateStaticVersion(sourceDir, targetDir) {
  log(`Generando versión estática del sitio de ${sourceDir} a ${targetDir}...`, 'info');
  
  // Encontrar todos los archivos HTML
  const htmlFiles = findHtmlFiles(sourceDir);
  log(`Se encontraron ${htmlFiles.length} archivos HTML`, 'info');
  
  // Procesar cada archivo HTML
  for (const htmlFile of htmlFiles) {
    // Calcular la ruta relativa del archivo HTML
    const relativePath = path.relative(sourceDir, htmlFile);
    const targetFile = path.join(targetDir, relativePath);
    
    // Crear el directorio de destino si no existe
    const targetFileDir = path.dirname(targetFile);
    if (!fs.existsSync(targetFileDir)) {
      await mkdirAsync(targetFileDir, { recursive: true });
    }
    
    log(`Procesando ${relativePath}...`, 'info');
    
    // Leer el contenido del archivo HTML
    const htmlContent = await readFile(htmlFile);
    
    if (htmlContent) {
      // Simplificar el HTML
      const simplifiedContent = await simplifyHtml(htmlContent);
      
      // Guardar el archivo modificado
      try {
        await writeFileAsync(targetFile, simplifiedContent, 'utf8');
        log(`Archivo ${relativePath} modificado correctamente`, 'success');
      } catch (error) {
        log(`Error al guardar el archivo ${relativePath}: ${error.message}`, 'error');
      }
    }
  }
  
  // Copiar imágenes y otros recursos estáticos
  await copyStaticAssets(sourceDir, targetDir);
  
  log('Generación de versión estática completada', 'success');
}

// Exportar la función principal
module.exports = generateStaticVersion;
