'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  totalProducts: number;
  totalSolicitudes: number;
  totalUsers: number;
  lowStockProducts: number;
  recentActivities: Array<{
    id: number;
    type: string;
    description: string;
    timestamp: string;
    user?: string;
  }>;
}

export default function EccsaAdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();
  const { user, isLoading: authLoading, isAuthenticated, logout, requireAuth } = useAuth();

  // Verificar autenticación al cargar el componente
  useEffect(() => {
    if (!authLoading) {
      requireAuth();
    }
  }, [authLoading, requireAuth]);

  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      // Cargar estadísticas del almacén
      const almacenResponse = await fetch('/api/almacen?stats=true');
      const almacenData = await almacenResponse.json();

      // Cargar estadísticas de solicitudes
      const solicitudesResponse = await fetch('/api/solicitudes?stats=true');
      const solicitudesData = await solicitudesResponse.json();

      // Cargar estadísticas de usuarios
      const usersResponse = await fetch('/api/users?stats=true');
      const usersData = await usersResponse.json();

      // Combinar datos
      const dashboardStats: DashboardStats = {
        totalProducts: almacenData.stats?.total_productos || 0,
        totalSolicitudes: solicitudesData.stats?.total || 0,
        totalUsers: usersData.stats?.total || 0,
        lowStockProducts: almacenData.stats?.productos_stock_bajo || 0,
        recentActivities: [
          {
            id: 1,
            type: 'product_added',
            description: 'Nuevo producto agregado al almacén',
            timestamp: new Date().toISOString(),
            user: user?.name || 'Admin'
          },
          {
            id: 2,
            type: 'solicitud_received',
            description: 'Nueva solicitud de cotización recibida',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            user: 'Cliente'
          },
          {
            id: 3,
            type: 'stock_low',
            description: 'Producto con stock bajo detectado',
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            user: 'Sistema'
          }
        ]
      };

      setStats(dashboardStats);
    } catch (err) {
      setError('Error al cargar datos del dashboard');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [loadDashboardData, user]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-MX');
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'product_added':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      case 'solicitud_received':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'stock_low':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  // Mostrar loading mientras se verifica la autenticación
  if (authLoading) {
    return (
      <div className="modern-dashboard-page">
        <div className="modern-admin-loading">
          <div className="modern-admin-spinner"></div>
          <p>Verificando autenticación...</p>
        </div>
      </div>
    );
  }

  // Si no está autenticado, no mostrar nada (el hook redirigirá)
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="glassmorphism-dashboard">
      <div className="glassmorphism-dashboard-header">
        <h1 className="glassmorphism-dashboard-title">Panel Ejecutivo ECCSA</h1>
        <p className="glassmorphism-dashboard-subtitle">
          Sistema de Gestión Empresarial - Automatización Industrial
        </p>
        <div className="glassmorphism-dashboard-actions">
          <button
            className="glassmorphism-refresh-button"
            onClick={loadDashboardData}
            disabled={loading}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {loading ? 'Actualizando...' : 'Actualizar'}
          </button>
        </div>
      </div>

      {loading ? (
        <div className="glassmorphism-loading">
          <div className="glassmorphism-spinner"></div>
          <p className="glassmorphism-loading-text">Cargando dashboard...</p>
        </div>
      ) : error ? (
        <div className="glassmorphism-error">
          <p className="glassmorphism-error-text">{error}</p>
          <button
            className="glassmorphism-error-button"
            onClick={loadDashboardData}
          >
            Reintentar
          </button>
        </div>
      ) : stats ? (
        <>
          {/* Estadísticas Ejecutivas */}
          <div className="glassmorphism-stats-grid">
            <div className="glassmorphism-stat-card">
              <div className="glassmorphism-stat-icon">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <div className="glassmorphism-stat-content">
                <div className="glassmorphism-stat-title">Inventario Total</div>
                <div className="glassmorphism-stat-value">{stats.totalProducts.toLocaleString()}</div>
                <div className="glassmorphism-stat-change positive">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                  </svg>
                  +12% este mes
                </div>
              </div>
            </div>

            <div className="glassmorphism-stat-card">
              <div className="glassmorphism-stat-icon">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="glassmorphism-stat-content">
                <div className="glassmorphism-stat-title">Solicitudes Activas</div>
                <div className="glassmorphism-stat-value">{stats.totalSolicitudes.toLocaleString()}</div>
                <div className="glassmorphism-stat-change positive">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                  </svg>
                  +8% esta semana
                </div>
              </div>
            </div>

            <div className="glassmorphism-stat-card">
              <div className="glassmorphism-stat-icon">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="glassmorphism-stat-content">
                <div className="glassmorphism-stat-title">Personal Activo</div>
                <div className="glassmorphism-stat-value">{stats.totalUsers.toLocaleString()}</div>
                <div className="glassmorphism-stat-change positive">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                  </svg>
                  +2 nuevos
                </div>
              </div>
            </div>

            <div className="glassmorphism-stat-card">
              <div className="glassmorphism-stat-icon">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="glassmorphism-stat-content">
                <div className="glassmorphism-stat-title">Alertas Críticas</div>
                <div className="glassmorphism-stat-value">{stats.lowStockProducts.toLocaleString()}</div>
                <div className="glassmorphism-stat-change negative">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
                  </svg>
                  Requiere atención
                </div>
              </div>
            </div>
          </div>

          {/* Centro de Control Ejecutivo */}
          <div className="glassmorphism-card">
            <div className="glassmorphism-card-header">
              <h2 className="glassmorphism-card-title">Centro de Control Ejecutivo</h2>
            </div>
            <div className="glassmorphism-card-content">
              <div className="glassmorphism-actions-grid">
                <a href="/eccsa/admin/almacen" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Gestión de Almacén</div>
                    <div className="glassmorphism-action-description">Control integral del inventario, productos y stock en tiempo real</div>
                  </div>
                </a>
                <a href="/eccsa/admin/precios" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Sistema de Precios</div>
                    <div className="glassmorphism-action-description">Gestión avanzada de cotizaciones, márgenes y estrategias de precios</div>
                  </div>
                </a>
                <a href="/eccsa/admin/solicitudes" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Centro de Solicitudes</div>
                    <div className="glassmorphism-action-description">Administración completa de solicitudes y atención al cliente</div>
                  </div>
                </a>
                <a href="/eccsa/admin/empleados" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Recursos Humanos</div>
                    <div className="glassmorphism-action-description">Gestión estratégica del talento humano y estructura organizacional</div>
                  </div>
                </a>
                <a href="/eccsa/admin/database" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Centro de Datos</div>
                    <div className="glassmorphism-action-description">Monitoreo avanzado y administración de la infraestructura de datos</div>
                  </div>
                </a>
                <a href="/eccsa/admin/reports" className="glassmorphism-action-button">
                  <div className="glassmorphism-action-icon">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div className="glassmorphism-action-content">
                    <div className="glassmorphism-action-title">Reportes Ejecutivos</div>
                    <div className="glassmorphism-action-description">Análisis avanzado y reportes estratégicos para toma de decisiones</div>
                  </div>
                </a>
              </div>
            </div>
          </div>

          {/* Actividades recientes */}
          <div className="glassmorphism-card">
            <div className="glassmorphism-card-header">
              <h2 className="glassmorphism-card-title">Actividades Recientes</h2>
            </div>
            <div className="glassmorphism-card-content">
              <div className="glassmorphism-activity-list">
                {stats.recentActivities.map((activity) => (
                  <div key={activity.id} className="glassmorphism-activity-item">
                    <div className="glassmorphism-activity-icon">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="glassmorphism-activity-content">
                      <div className="glassmorphism-activity-text">{activity.description}</div>
                      <div className="glassmorphism-activity-time">
                        {formatDate(activity.timestamp)} - {activity.user}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="glassmorphism-empty">
          <div className="glassmorphism-empty-icon">
            <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="glassmorphism-empty-title">No hay datos disponibles</h3>
          <p className="glassmorphism-empty-text">No se pudieron cargar las estadísticas del dashboard.</p>
        </div>
      )}
    </div>
  );
}
