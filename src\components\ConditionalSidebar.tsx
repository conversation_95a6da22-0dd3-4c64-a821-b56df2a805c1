'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import AdminSidebar from '@/components/admin/AdminSidebar';
import { useEffect, useState } from 'react';

export default function ConditionalSidebar() {
  const pathname = usePathname();
  const { user, isAuthenticated, logout, isLoading, updateUser } = useAuth();
  const [shouldShowSidebar, setShouldShowSidebar] = useState(false);

  // Detectar si es una página admin (pero no login)
  const isAdminPage = pathname.startsWith('/eccsa/admin') && pathname !== '/eccsa/admin/login';

  // Actualizar estado de sidebar cuando cambie la autenticación
  useEffect(() => {
    if (isAdminPage && !isLoading && isAuthenticated && user) {
      setShouldShowSidebar(true);
    } else {
      setShouldShowSidebar(false);
    }
  }, [isAdminPage, isAuthenticated, user, isLoading, pathname]);

  // Solo mostrar sidebar si estamos en admin y el usuario está autenticado
  if (shouldShowSidebar && user) {
    return (
      <AdminSidebar
        user={user}
        onLogout={logout}
        onUserUpdate={updateUser}
      />
    );
  }

  return null;
}
