'use client';

import { useState } from 'react';
import Image from 'next/image';
import ModernBrandsSlider from './ModernBrandsSlider';
import {
  FaWrench,
  FaLaptopCode,
  FaMobileAlt,
  FaHammer,
  FaExchangeAlt,
  FaTrophy,
  FaUserTie,
  FaCogs,
  FaHandshake,
  FaShieldAlt,
  FaGlobe,
  FaHeadset,
  FaGraduationCap,
  FaRocket,
  FaArrowRight,
  FaPlay,
  FaCheckCircle,
  FaIndustry,
  FaChartLine
} from 'react-icons/fa';

// Interfaces
interface Service {
  name: string;
  description: string;
  icon: React.ElementType;
  imageSrc: string;
  features: string[];
}

interface Stat {
  number: string;
  label: string;
  icon: React.ElementType;
}

interface Brand {
  src: string;
  alt: string;
}

export default function ModernDashboard() {
  const [services] = useState<Service[]>([
    {
      name: 'Automatización Industrial',
      description: 'Implementamos soluciones de automatización que optimizan sus procesos productivos, mejoran la eficiencia y reducen costos operativos.',
      icon: FaWrench,
      imageSrc: '/images/defaults/Automatización-Industrial.jpg',
      features: ['Sistemas robustos y escalables', 'Tecnología de punta', 'ROI garantizado']
    },
    {
      name: 'Programación de PLC',
      description: 'Desarrollamos software para control industrial adaptado a sus necesidades específicas con todas las marcas principales.',
      icon: FaLaptopCode,
      imageSrc: '/images/defaults/PROGRAMACION-PLC.jpeg',
      features: ['Todas las marcas', 'Funcionamiento óptimo', 'Soporte completo']
    },
    {
      name: 'Sistemas SCADA',
      description: 'Diseñamos sistemas de supervisión, control y adquisición de datos para monitoreo en tiempo real.',
      icon: FaMobileAlt,
      imageSrc: '/images/defaults/sistema-scada-gas-natual-eisenberg-monterrey.jpg',
      features: ['Tiempo real', 'Mejor toma de decisiones', 'Interfaz intuitiva']
    },
    {
      name: 'Mantenimiento Industrial',
      description: 'Servicios de mantenimiento preventivo y correctivo para garantizar el funcionamiento óptimo de sus sistemas.',
      icon: FaHammer,
      imageSrc: '/images/defaults/Mantenimiento-Industrial.jpg',
      features: ['Preventivo y correctivo', 'Mínimo downtime', 'Vida útil extendida']
    }
  ]);

  const [stats] = useState<Stat[]>([
    { number: '25+', label: 'Años de Experiencia', icon: FaTrophy },
    { number: '500+', label: 'Proyectos Completados', icon: FaRocket },
    { number: '100+', label: 'Clientes Satisfechos', icon: FaHandshake },
    { number: '24/7', label: 'Soporte Técnico', icon: FaHeadset }
  ]);

  const [brands] = useState<Brand[]>([
    { src: '/images/Marcas/1.png', alt: 'Siemens' },
    { src: '/images/Marcas/2.png', alt: 'Allen Bradley' },
    { src: '/images/Marcas/3.png', alt: 'Schneider Electric' },
    { src: '/images/Marcas/4.png', alt: 'ABB' },
    { src: '/images/Marcas/5.png', alt: 'Omron' },
    { src: '/images/Marcas/6.png', alt: 'Mitsubishi' },
    { src: '/images/Marcas/7.png', alt: 'Honeywell' },
    { src: '/images/Marcas/8.png', alt: 'Emerson' }
  ]);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-blue-600/20 rounded-full text-blue-200 text-sm font-medium">
                  <FaIndustry className="mr-2" />
                  Líderes en Automatización Industrial
                </div>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Transformamos su
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
                    {' '}Industria
                  </span>
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed">
                  Más de 25 años desarrollando soluciones de automatización industrial
                  con tecnología de vanguardia y los más altos estándares internacionales.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                  Conocer Servicios
                  <FaArrowRight className="ml-2" />
                </button>
                <button className="inline-flex items-center px-8 py-4 border-2 border-white/20 hover:border-white/40 rounded-lg font-semibold transition-all duration-300">
                  <FaPlay className="mr-2" />
                  Ver Demo
                </button>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-2xl blur-3xl"></div>
              <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <Image
                  src="/images/logos/logo_pequeno.png"
                  alt="ECCSA Automation"
                  width={300}
                  height={300}
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4 group-hover:bg-blue-600 transition-colors duration-300">
                  <stat.icon className="w-8 h-8 text-blue-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Brands Slider */}
      <ModernBrandsSlider
        brands={brands}
        speed={3}
        title="Nuestros Socios Tecnológicos"
      />

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Nuestros Servicios
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Ofrecemos una amplia gama de servicios especializados para satisfacer
              todas sus necesidades de automatización industrial
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden">
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={service.imageSrc}
                    alt={service.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg">
                      <service.icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{service.name}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>

                  <div className="space-y-3 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center">
                        <FaCheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-2 transition-all duration-300">
                    Más información
                    <FaArrowRight className="ml-2" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-600 text-sm font-medium">
                  Desde 1999
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                  Quiénes Somos
                </h2>
                <p className="text-xl text-gray-600 leading-relaxed">
                  <strong className="text-blue-600">ECCSA Automation</strong> desarrolla soluciones en automatización
                  para procesos industriales desde 1999, utilizando la mejor tecnología y los más exigentes
                  estándares internacionales.
                </p>
              </div>

              <div className="space-y-6 text-gray-600 leading-relaxed">
                <p>
                  Nuestra compañía tiene como propósito primordial: Integrar sistemas de control
                  para aplicarlos en la industria moderna, proporcionando a nuestros clientes
                  altos niveles de productividad y calidad con la mejor relación costo-beneficio.
                </p>
                <p>
                  Nuestras alianzas estratégicas con los líderes mundiales en equipos de automatización
                  industrial nos permiten brindar un alto nivel de soporte tecnológico.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">ISO 9001</div>
                  <div className="text-gray-600">Certificación de Calidad</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">24/7</div>
                  <div className="text-gray-600">Soporte Técnico</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-cyan-600/10 rounded-2xl blur-3xl"></div>
              <div className="relative bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 shadow-2xl">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                    <FaIndustry className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">500+</div>
                    <div className="text-gray-600">Proyectos</div>
                  </div>
                  <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                    <FaChartLine className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">98%</div>
                    <div className="text-gray-600">Satisfacción</div>
                  </div>
                  <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                    <FaGlobe className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">15+</div>
                    <div className="text-gray-600">Países</div>
                  </div>
                  <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                    <FaTrophy className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">25+</div>
                    <div className="text-gray-600">Años</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-cyan-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              ¿Por qué Elegir ECCSA?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Nuestro compromiso con la excelencia nos distingue en el mercado de automatización industrial
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: FaShieldAlt,
                title: 'Garantía Total',
                description: 'Garantía en todos nuestros servicios y productos, asegurando máxima calidad y confiabilidad.',
                color: 'blue'
              },
              {
                icon: FaHeadset,
                title: 'Soporte 24/7',
                description: 'Equipo de soporte técnico disponible para resolver cualquier incidencia de manera rápida.',
                color: 'green'
              },
              {
                icon: FaGraduationCap,
                title: 'Capacitación',
                description: 'Programas de capacitación para que su personal aproveche al máximo nuestras soluciones.',
                color: 'purple'
              },
              {
                icon: FaRocket,
                title: 'Innovación',
                description: 'Nos mantenemos a la vanguardia para ofrecer siempre las soluciones más avanzadas.',
                color: 'orange'
              }
            ].map((feature, index) => (
              <div key={index} className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <div className={`inline-flex items-center justify-center w-16 h-16 bg-${feature.color}-100 rounded-2xl mb-6 group-hover:bg-${feature.color}-600 transition-colors duration-300`}>
                  <feature.icon className={`w-8 h-8 text-${feature.color}-600 group-hover:text-white transition-colors duration-300`} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Industrias que Atendemos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Colaboramos con empresas líderes en diversos sectores industriales
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { icon: FaTrophy, title: 'Industria Automotriz', description: 'Principales armadoras y proveedores del sector automotriz en México.' },
              { icon: FaUserTie, title: 'Sector Manufacturero', description: 'Optimización de procesos para empresas manufactureras de diversos sectores.' },
              { icon: FaCogs, title: 'Industria Alimentaria', description: 'Soluciones que cumplen con estrictos estándares de la industria alimentaria.' },
              { icon: FaHandshake, title: 'Sector Energético', description: 'Proyectos para empresas líderes en generación y distribución de energía.' },
              { icon: FaShieldAlt, title: 'Industria Farmacéutica', description: 'Automatización de procesos críticos para el sector farmacéutico y de salud.' },
              { icon: FaGlobe, title: 'Empresas Multinacionales', description: 'Corporaciones globales que confían en nuestra experiencia y profesionalismo.' }
            ].map((industry, index) => (
              <div key={index} className="group bg-gray-50 hover:bg-white rounded-2xl p-8 transition-all duration-300 hover:shadow-xl border border-transparent hover:border-blue-100">
                <div className="inline-flex items-center justify-center w-14 h-14 bg-blue-100 rounded-xl mb-6 group-hover:bg-blue-600 transition-colors duration-300">
                  <industry.icon className="w-7 h-7 text-blue-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{industry.title}</h3>
                <p className="text-gray-600 leading-relaxed">{industry.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-cyan-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-4xl font-bold">
              ¿Listo para Transformar su Industria?
            </h2>
            <p className="text-xl text-blue-100">
              Contáctenos hoy mismo y descubra cómo nuestras soluciones de automatización
              pueden optimizar sus procesos y aumentar su productividad.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="inline-flex items-center px-8 py-4 bg-white text-blue-600 hover:bg-gray-100 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                Solicitar Cotización
                <FaArrowRight className="ml-2" />
              </button>
              <button className="inline-flex items-center px-8 py-4 border-2 border-white/20 hover:border-white/40 rounded-lg font-semibold transition-all duration-300">
                <FaHeadset className="mr-2" />
                Contactar Ahora
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
