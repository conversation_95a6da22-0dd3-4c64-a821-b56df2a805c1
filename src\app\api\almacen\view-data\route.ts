import { NextResponse } from 'next/server';
import { executeQuery } from '@/eccsa_back/lib/db';

export async function POST() {
  try {
    console.log('Consultando datos de la tabla almacén...');
    
    // Primero verificar si la tabla existe
    const tableCheck = await executeQuery({
      query: "SHOW TABLES LIKE 'almacen'"
    });
    
    if (!Array.isArray(tableCheck) || tableCheck.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'La tabla almacén no existe',
        tableExists: false
      });
    }
    
    // Obtener la estructura de la tabla
    const structure = await executeQuery({
      query: 'DESCRIBE almacen'
    });
    
    // Obtener todos los datos de la tabla
    const allData = await executeQuery({
      query: 'SELECT * FROM almacen ORDER BY id ASC'
    });
    
    // Contar registros
    const countResult = await executeQuery({
      query: 'SELECT COUNT(*) as total FROM almacen'
    });
    
    const totalRecords = Array.isArray(countResult) && countResult.length > 0 ? countResult[0].total : 0;
    
    return NextResponse.json({
      success: true,
      message: 'Datos de la tabla almacén obtenidos exitosamente',
      tableExists: true,
      totalRecords,
      structure,
      data: allData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error consultando tabla almacén:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      message: 'Error al consultar la tabla almacén'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to view almacen data',
    endpoint: '/api/almacen/view-data',
    method: 'POST'
  });
}
