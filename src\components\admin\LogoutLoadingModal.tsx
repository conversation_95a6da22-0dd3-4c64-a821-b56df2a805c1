'use client';

import React from 'react';
import { FaSignOutAlt } from 'react-icons/fa';

interface LogoutLoadingModalProps {
  isVisible: boolean;
  message?: string;
}

export default function LogoutLoadingModal({ 
  isVisible, 
  message = 'Cerrando sesión...' 
}: LogoutLoadingModalProps) {
  if (!isVisible) return null;

  return (
    <div className="logout-loading-overlay">
      <div className="logout-loading-modal">
        <div className="logout-loading-content">
          {/* Animated Icon */}
          <div className="logout-loading-icon">
            <div className="icon-container">
              <FaSignOutAlt className="logout-icon" />
            </div>
            <div className="loading-ring"></div>
          </div>

          {/* Message */}
          <div className="logout-loading-message">
            <h3>Cerrando Sesión</h3>
            <p>{message}</p>
          </div>

          {/* Progress Bar */}
          <div className="logout-progress-bar">
            <div className="progress-fill"></div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .logout-loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(10px);
          z-index: 10001;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .logout-loading-modal {
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border-radius: 24px;
          padding: 3rem 2.5rem;
          text-align: center;
          box-shadow: 
            0 25px 60px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.2);
          max-width: 420px;
          width: 90%;
          animation: modalSlideIn 0.4s ease-out;
        }

        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-30px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .logout-loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 2rem;
        }

        .logout-loading-icon {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .icon-container {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 2rem;
          box-shadow: 
            0 8px 25px rgba(239, 68, 68, 0.4),
            0 0 0 4px rgba(239, 68, 68, 0.1);
          z-index: 2;
          position: relative;
          animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            box-shadow: 
              0 8px 25px rgba(239, 68, 68, 0.4),
              0 0 0 4px rgba(239, 68, 68, 0.1);
          }
          50% {
            transform: scale(1.05);
            box-shadow: 
              0 12px 35px rgba(239, 68, 68, 0.5),
              0 0 0 8px rgba(239, 68, 68, 0.2);
          }
        }

        .loading-ring {
          position: absolute;
          width: 100px;
          height: 100px;
          border: 3px solid rgba(239, 68, 68, 0.2);
          border-top: 3px solid #ef4444;
          border-radius: 50%;
          animation: spin 1.5s linear infinite;
          z-index: 1;
        }

        @keyframes spin {
          0% { 
            transform: rotate(0deg);
          }
          100% { 
            transform: rotate(360deg);
          }
        }

        .logout-icon {
          animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-3px);
          }
        }

        .logout-loading-message {
          text-align: center;
        }

        .logout-loading-message h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1.75rem;
          font-weight: 700;
          color: #1a202c;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .logout-loading-message p {
          margin: 0;
          color: #64748b;
          font-size: 1.1rem;
          font-weight: 500;
        }

        .logout-progress-bar {
          width: 100%;
          height: 6px;
          background: rgba(239, 68, 68, 0.1);
          border-radius: 3px;
          overflow: hidden;
          position: relative;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #ef4444, #dc2626, #ef4444);
          background-size: 200% 100%;
          border-radius: 3px;
          animation: progressFlow 2s ease-in-out infinite;
        }

        @keyframes progressFlow {
          0% {
            width: 0%;
            background-position: 0% 50%;
          }
          50% {
            width: 70%;
            background-position: 100% 50%;
          }
          100% {
            width: 100%;
            background-position: 200% 50%;
          }
        }

        @media (max-width: 480px) {
          .logout-loading-modal {
            padding: 2.5rem 2rem;
            margin: 1rem;
            max-width: none;
          }

          .icon-container {
            width: 70px;
            height: 70px;
            font-size: 1.75rem;
          }

          .loading-ring {
            width: 90px;
            height: 90px;
          }

          .logout-loading-message h3 {
            font-size: 1.5rem;
          }

          .logout-loading-message p {
            font-size: 1rem;
          }

          .logout-loading-content {
            gap: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
}
