// Script para corregir nombres de archivos con caracteres especiales
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  fg: {
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m',
    cyan: '\x1b[36m'
  }
};

console.log(`${colors.fg.cyan}${colors.bright}=== Corrigiendo nombres de archivos con caracteres especiales ===${colors.reset}\n`);

// Directorios de origen y destino
const rootDir = path.join(__dirname, '..', '..');
const outputDir = path.join(rootDir, 'out');

// Verificar si el directorio de salida existe
if (!fs.existsSync(outputDir)) {
  console.log(`${colors.fg.yellow}El directorio 'out' no existe. Ejecutando build primero...${colors.reset}`);
  try {
    execSync('npm run build', { stdio: 'inherit' });
  } catch (error) {
    console.error(`${colors.fg.red}Error al ejecutar build: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Función para normalizar nombres de archivos
function normalizeFileName(fileName) {
  // Reemplazar caracteres especiales
  return fileName
    .replace(/á/g, 'a')
    .replace(/é/g, 'e')
    .replace(/í/g, 'i')
    .replace(/ó/g, 'o')
    .replace(/ú/g, 'u')
    .replace(/ñ/g, 'n')
    .replace(/Á/g, 'A')
    .replace(/É/g, 'E')
    .replace(/Í/g, 'I')
    .replace(/Ó/g, 'O')
    .replace(/Ú/g, 'U')
    .replace(/Ñ/g, 'N')
    .replace(/ü/g, 'u')
    .replace(/Ü/g, 'U')
    .replace(/ç/g, 'c')
    .replace(/Ç/g, 'C')
    .replace(/[^\w\s.-]/g, '_'); // Reemplazar otros caracteres especiales con guión bajo
}

// Función para procesar un directorio recursivamente
function processDirectory(dirPath, relativePath = '') {
  // Leer contenido del directorio
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  // Procesar cada archivo/directorio
  for (const entry of entries) {
    const entryPath = path.join(dirPath, entry.name);
    const entryRelativePath = path.join(relativePath, entry.name);
    
    if (entry.isDirectory()) {
      // Procesar subdirectorios recursivamente
      processDirectory(entryPath, entryRelativePath);
    } else {
      // Verificar si el nombre del archivo tiene caracteres especiales
      const normalizedName = normalizeFileName(entry.name);
      
      if (normalizedName !== entry.name) {
        const newPath = path.join(dirPath, normalizedName);
        const newRelativePath = path.join(relativePath, normalizedName);
        
        console.log(`${colors.fg.yellow}Renombrando: ${entryRelativePath} -> ${newRelativePath}${colors.reset}`);
        
        try {
          // Crear una copia con el nuevo nombre
          fs.copyFileSync(entryPath, newPath);
          
          // Crear un archivo de redirección HTML para el nombre original
          const redirectContent = `<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="refresh" content="0;url=${normalizedName}">
  <title>Redireccionando...</title>
</head>
<body>
  <p>Redireccionando a <a href="${normalizedName}">${normalizedName}</a>...</p>
</body>
</html>`;
          
          fs.writeFileSync(entryPath, redirectContent);
          
          console.log(`${colors.fg.green}✓ Archivo copiado y redirección creada${colors.reset}`);
        } catch (error) {
          console.error(`${colors.fg.red}Error al renombrar archivo: ${error.message}${colors.reset}`);
        }
      }
    }
  }
}

// Archivos específicos a verificar
const specificFiles = [
  { path: 'images/logos/logo_pequeño.png', normalizedPath: 'images/logos/logo_pequeno.png' },
  { path: 'images/defaults/Automatización-Industrial.jpg', normalizedPath: 'images/defaults/Automatizacion-Industrial.jpg' }
];

// Verificar y corregir archivos específicos
console.log(`${colors.fg.blue}Verificando archivos específicos...${colors.reset}`);

for (const file of specificFiles) {
  const filePath = path.join(outputDir, file.path);
  const normalizedPath = path.join(outputDir, file.normalizedPath);
  
  if (fs.existsSync(filePath)) {
    console.log(`${colors.fg.green}✓ Archivo encontrado: ${file.path}${colors.reset}`);
    
    // Crear una copia con nombre normalizado
    try {
      // Asegurarse de que el directorio de destino exista
      const normalizedDir = path.dirname(normalizedPath);
      if (!fs.existsSync(normalizedDir)) {
        fs.mkdirSync(normalizedDir, { recursive: true });
      }
      
      // Copiar el archivo
      fs.copyFileSync(filePath, normalizedPath);
      console.log(`${colors.fg.green}✓ Creada copia normalizada: ${file.normalizedPath}${colors.reset}`);
    } catch (error) {
      console.error(`${colors.fg.red}Error al crear copia normalizada: ${error.message}${colors.reset}`);
    }
  } else {
    console.error(`${colors.fg.red}✗ Archivo no encontrado: ${file.path}${colors.reset}`);
  }
}

// Procesar todos los directorios de imágenes
console.log(`\n${colors.fg.blue}Procesando directorios de imágenes...${colors.reset}`);
const imagesDir = path.join(outputDir, 'images');

if (fs.existsSync(imagesDir)) {
  processDirectory(imagesDir, 'images');
} else {
  console.error(`${colors.fg.red}✗ Directorio de imágenes no encontrado: ${imagesDir}${colors.reset}`);
}

// Crear un archivo de mapeo para referencias en el código
console.log(`\n${colors.fg.blue}Creando archivo de mapeo...${colors.reset}`);

const mappingContent = `// Mapeo de nombres de archivos con caracteres especiales
// Este archivo se genera automáticamente
window.ECCSA_FILE_MAPPING = {
  "/images/logos/logo_pequeño.png": "/images/logos/logo_pequeno.png",
  "/images/defaults/Automatización-Industrial.jpg": "/images/defaults/Automatizacion-Industrial.jpg"
};`;

const mappingPath = path.join(outputDir, 'file-mapping.js');
fs.writeFileSync(mappingPath, mappingContent);
console.log(`${colors.fg.green}✓ Archivo de mapeo creado: file-mapping.js${colors.reset}`);

// Crear un script para cargar el mapeo
const loaderScript = `// Script para cargar el mapeo de archivos
document.addEventListener('DOMContentLoaded', function() {
  // Cargar el archivo de mapeo
  var script = document.createElement('script');
  script.src = '/file-mapping.js';
  script.onload = function() {
    // Reemplazar URLs en imágenes
    if (window.ECCSA_FILE_MAPPING) {
      var images = document.querySelectorAll('img');
      images.forEach(function(img) {
        var src = img.getAttribute('src');
        if (src && window.ECCSA_FILE_MAPPING[src]) {
          img.setAttribute('src', window.ECCSA_FILE_MAPPING[src]);
        }
      });
    }
  };
  document.head.appendChild(script);
});`;

const loaderPath = path.join(outputDir, 'file-mapping-loader.js');
fs.writeFileSync(loaderPath, loaderScript);
console.log(`${colors.fg.green}✓ Script de carga creado: file-mapping-loader.js${colors.reset}`);

// Agregar el script de carga a index.html
const indexPath = path.join(outputDir, 'index.html');
if (fs.existsSync(indexPath)) {
  let indexContent = fs.readFileSync(indexPath, 'utf8');
  
  // Verificar si el script ya está incluido
  if (!indexContent.includes('file-mapping-loader.js')) {
    // Insertar el script antes de </head>
    indexContent = indexContent.replace('</head>', '<script src="/file-mapping-loader.js"></script></head>');
    fs.writeFileSync(indexPath, indexContent);
    console.log(`${colors.fg.green}✓ Script de carga agregado a index.html${colors.reset}`);
  } else {
    console.log(`${colors.fg.yellow}El script de carga ya está incluido en index.html${colors.reset}`);
  }
} else {
  console.warn(`${colors.fg.yellow}⚠️ No se encontró index.html${colors.reset}`);
}

console.log(`\n${colors.fg.cyan}${colors.bright}=== Proceso completado ===${colors.reset}`);
console.log(`${colors.fg.green}Se han creado copias normalizadas de los archivos con caracteres especiales.${colors.reset}`);
console.log(`${colors.fg.yellow}Recuerde incluir los archivos file-mapping.js y file-mapping-loader.js en el ZIP para cPanel.${colors.reset}`);
