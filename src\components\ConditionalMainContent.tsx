'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useSidebar } from '@/contexts/SidebarContext';

interface ConditionalMainContentProps {
  children: React.ReactNode;
}

export default function ConditionalMainContent({ children }: ConditionalMainContentProps) {
  const pathname = usePathname();
  const { isAuthenticated, user } = useAuth();

  // Detect if it's an admin page
  const isAdminPage = pathname.startsWith('/eccsa/admin') || pathname.startsWith('/admin');
  const isLoginPage = pathname === '/eccsa/admin/login';
  const isAdminDashboard = isAdminPage && !isLoginPage && isAuthenticated && user;

  // Get sidebar state if available (only for admin pages)
  let sidebarState = null;
  try {
    if (isAdminDashboard) {
      sidebarState = useSidebar();
    }
  } catch (error) {
    // useSidebar hook not available, continue without it
  }


  // Admin dashboard with glassmorphism sidebar
  if (isAdminDashboard) {
    const sidebarCollapsed = sidebarState?.isCollapsed || false;
    return (
      <main className={`glassmorphism-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        {children}
      </main>
    );
  }
  
  // Admin login page - full screen
  if (isLoginPage) {
    return (
      <main style={{
        minHeight: '100vh',
        width: '100%',
        overflowX: 'hidden'
      }}
      className="login-main-content"
      >
        {children}
      </main>
    );
  }
  
  // Public pages - with navbar padding
  return (
    <main style={{
      minHeight: 'calc(100vh - 92px)',
      paddingTop: '92px',
      width: '100%',
      overflowX: 'hidden'
    }}
    className="main-content"
    >
      {children}
    </main>
  );
}
