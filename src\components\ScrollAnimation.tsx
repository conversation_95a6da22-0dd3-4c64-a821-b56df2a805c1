'use client';

import React from 'react';
import { useScrollAnimation, useStaggerAnimation, useCounterAnimation, useParallaxScroll } from '@/hooks/useScrollAnimation';

interface ScrollAnimationProps {
  children: React.ReactNode;
  animation?: 'fade-in' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' |
             'scale-up' | 'rotate-in' | 'bounce-in' | 'flip-in-x' | 'flip-in-y' |
             'zoom-in' | 'zoom-out' | 'blur-in';
  delay?: number;
  threshold?: number;
  className?: string;
  triggerOnce?: boolean;
}

export function ScrollAnimation({
  children,
  animation = 'slide-up',
  delay = 0,
  threshold = 0.1,
  className = '',
  triggerOnce = true
}: ScrollAnimationProps) {
  const { elementRef, isVisible } = useScrollAnimation({
    threshold,
    delay,
    triggerOnce
  });

  return (
    <div
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={`${animation} ${isVisible ? 'visible' : ''} ${className}`}
    >
      {children}
    </div>
  );
}

interface StaggerAnimationProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  animation?: string;
  className?: string;
}

export function StaggerAnimation({
  children,
  staggerDelay = 100,
  animation = 'stagger-item',
  className = ''
}: StaggerAnimationProps) {
  const { containerRef, visibleItems } = useStaggerAnimation(children.length, staggerDelay);

  return (
    <div ref={containerRef as React.RefObject<HTMLDivElement>} className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={`${animation} ${visibleItems[index] ? 'visible' : ''}`}
        >
          {child}
        </div>
      ))}
    </div>
  );
}

interface CounterAnimationProps {
  endValue: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
  className?: string;
}

export function CounterAnimation({
  endValue,
  duration = 2000,
  suffix = '',
  prefix = '',
  className = ''
}: CounterAnimationProps) {
  const { elementRef, count } = useCounterAnimation(endValue, duration);

  return (
    <span ref={elementRef as React.RefObject<HTMLSpanElement>} className={`counter-animate ${className}`}>
      {prefix}{count.toLocaleString()}{suffix}
    </span>
  );
}

interface ParallaxProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
}

export function Parallax({ children, speed = 0.5, className = '' }: ParallaxProps) {
  const { elementRef, offset } = useParallaxScroll(speed);

  return (
    <div
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={`parallax-element ${className}`}
      style={{ transform: `translateY(${offset}px)` }}
    >
      {children}
    </div>
  );
}

interface TextRevealProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export function TextReveal({ children, delay = 0, className = '' }: TextRevealProps) {
  const { elementRef, isVisible } = useScrollAnimation({ delay, threshold: 0.8 });

  return (
    <div
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={`text-reveal ${isVisible ? 'visible' : ''} ${className}`}
    >
      <div className="text-reveal-inner">
        {children}
      </div>
    </div>
  );
}

interface LineDrawProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}

export function LineDraw({ children, delay = 0, className = '' }: LineDrawProps) {
  const { elementRef, isVisible } = useScrollAnimation({ delay, threshold: 0.8 });

  return (
    <div
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={`line-draw ${isVisible ? 'visible' : ''} ${className}`}
    >
      {children}
    </div>
  );
}

// Componente para animaciones de sección completa
interface SectionAnimationProps {
  children: React.ReactNode;
  className?: string;
  headerAnimation?: string;
  contentAnimation?: string;
  staggerChildren?: boolean;
  staggerDelay?: number;
}

export function SectionAnimation({
  children,
  className = '',
  headerAnimation = 'slide-up',
  contentAnimation = 'slide-up',
  staggerChildren = false,
  staggerDelay = 100
}: SectionAnimationProps) {
  if (staggerChildren && Array.isArray(children)) {
    return (
      <div className={className}>
        <StaggerAnimation staggerDelay={staggerDelay}>
          {children}
        </StaggerAnimation>
      </div>
    );
  }

  return (
    <ScrollAnimation animation={contentAnimation as any} className={className}>
      {children}
    </ScrollAnimation>
  );
}

// Hook personalizado para animaciones complejas
export function useComplexAnimation() {
  const fadeIn = useScrollAnimation({ threshold: 0.1 });
  const slideUp = useScrollAnimation({ threshold: 0.2, delay: 200 });
  const scaleUp = useScrollAnimation({ threshold: 0.3, delay: 400 });

  return {
    fadeIn,
    slideUp,
    scaleUp
  };
}

export default ScrollAnimation;
