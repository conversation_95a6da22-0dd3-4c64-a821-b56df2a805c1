'use client';

import { useState } from 'react';

interface Product {
  id: number;
  name: string;
  category: string;
  description: string;
  price: string;
  image: string;
  brand: string;
  stock: number;
  almacenNumber: string;
  deliveryTime?: string;
}

interface SolicitudCotizacionModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
}

export default function SolicitudCotizacionModal({
  isOpen,
  onClose,
  product
}: SolicitudCotizacionModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    whatsapp: '',
    cantidad: 1
  });
  const [loading, setLoading] = useState(false);
  const [alertaStock, setAlertaStock] = useState(false);
  const [mensaje, setMensaje] = useState('');
  const [error, setError] = useState('');

  if (!isOpen || !product) return null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Verificar stock cuando cambie la cantidad
    if (name === 'cantidad') {
      const cantidad = parseInt(value) || 0;
      setAlertaStock(cantidad > product.stock);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMensaje('');

    try {
      // Validaciones
      if (!formData.email) {
        setError('El email es obligatorio');
        setLoading(false);
        return;
      }

      if (formData.cantidad <= 0) {
        setError('La cantidad debe ser mayor a 0');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/solicitudes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          numero_almacen: product.almacenNumber,
          cantidad_solicitada: formData.cantidad,
          cliente_email: formData.email,
          cliente_whatsapp: formData.whatsapp || null
        })
      });

      const data = await response.json();

      if (data.success) {
        let mensaje = '¡Solicitud enviada exitosamente!';

        if (data.solicitud_id) {
          mensaje += ` ID de solicitud: #${data.solicitud_id}`;
        }

        if (data.alerta_stock) {
          mensaje += ' ⚠️ Nota: Stock limitado, se cotizará con tiempo de entrega extendido.';
        }

        mensaje += ' Nos pondremos en contacto contigo pronto.';

        setMensaje(mensaje);
        setTimeout(() => {
          onClose();
          setFormData({ email: '', whatsapp: '', cantidad: 1 });
          setMensaje('');
          setAlertaStock(false);
        }, 3000);
      } else {
        setError(data.error || 'Error al enviar la solicitud');
      }
    } catch (error) {
      console.error('Error:', error);
      setError('Error de conexión. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    setFormData({ email: '', whatsapp: '', cantidad: 1 });
    setError('');
    setMensaje('');
    setAlertaStock(false);
  };

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content solicitud-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Solicitar Cotización</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-body">
          {/* Información del producto */}
          <div className="product-info-section">
            <h3>Información del Producto</h3>
            <div className="product-details">
              <p><strong>Modelo:</strong> {product.name}</p>
              <p><strong>Marca:</strong> {product.brand}</p>
              <p><strong>Descripción:</strong> {product.description}</p>
              <p><strong>Stock disponible:</strong> {product.stock} unidades</p>
              {product.deliveryTime && (
                <p><strong>Tiempo de entrega:</strong> {product.deliveryTime}</p>
              )}
            </div>
          </div>

          {/* Formulario */}
          <form onSubmit={handleSubmit} className="solicitud-form">
            <div className="form-group">
              <label htmlFor="email">
                Email <span className="required">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label htmlFor="whatsapp">WhatsApp (opcional)</label>
              <input
                type="tel"
                id="whatsapp"
                name="whatsapp"
                value={formData.whatsapp}
                onChange={handleInputChange}
                placeholder="+52 81 1234 5678"
              />
            </div>

            <div className="form-group">
              <label htmlFor="cantidad">
                Cantidad <span className="required">*</span>
              </label>
              <input
                type="number"
                id="cantidad"
                name="cantidad"
                value={formData.cantidad}
                onChange={handleInputChange}
                min="1"
                required
              />
            </div>

            {/* Alerta de stock */}
            {alertaStock && (
              <div className="stock-alert">
                ⚠️ No hay suficiente material disponible. Se le cotizará con mayor tiempo de entrega de lo que dice la tarjeta.
              </div>
            )}

            {/* Mensajes */}
            {error && <div className="error-message">{error}</div>}
            {mensaje && <div className="success-message">{mensaje}</div>}

            <div className="modal-actions">
              <button type="button" onClick={handleClose} className="btn-secondary">
                Cancelar
              </button>
              <button type="submit" disabled={loading} className="btn-primary">
                {loading ? 'Enviando...' : 'Enviar Solicitud'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
