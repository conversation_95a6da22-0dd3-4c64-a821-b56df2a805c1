/**
 * @type {import('next').NextConfig}
 * Next.js configuration optimized for static export
 */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  // Image configuration
  images: {
    domains: [
      'via.placeholder.com',
      '5.imimg.com',
      'm.media-amazon.com',
      'images.unsplash.com',
      'cdn.shopify.com',
      'i.imgur.com',
      'picsum.photos',
      'example.com'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '5.imimg.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'm.media-amazon.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.shopify.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'i.imgur.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        pathname: '**',
      },
      {
        protocol: 'http',
        hostname: 'example.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
        pathname: '**',
      },
    ],
    // Required for static export
    unoptimized: true,
    // Improve image loading performance
    deviceSizes: [120, 240, 640, 750, 828, 1080, 1200],
    imageSizes: [16, 32, 48, 64, 96, 120, 128, 256],
    formats: ['image/webp'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Performance optimizations
  experimental: {
    scrollRestoration: true,
    // Optimizaciones para evitar problemas de MIME type en cPanel
    // Inlinear CSS directamente en el HTML para evitar solicitudes separadas
    optimizeCss: true,
    // Cargar JS de manera más eficiente
    optimizePackageImports: ['react-icons'],
    // Comprimir mejor el código
    turbotrace: {
      logLevel: 'error',
    },
    // Mejorar la carga de imágenes
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB'],
  },

  // Configuración específica para CSS
  sassOptions: {
    includePaths: ['./src/styles'],
  },

  // Configuración para inlinear CSS en HTML
  // Esto ayuda a evitar problemas de MIME type con archivos CSS
  // al incluir los estilos directamente en el HTML
  optimizeFonts: true,

  // Reduce bundle size in production
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },

  // Static export configuration
  output: 'export',
  trailingSlash: true,

  // Optimize for static hosting
  poweredByHeader: false,
};

module.exports = nextConfig;
