'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  FaWrench,
  FaLaptopCode,
  FaMobileAlt,
  FaHammer,
  FaArrowRight,
  FaCheckCircle,
  FaIndustry,
  FaChartLine,
  FaPhone,
  FaEnvelope,
  FaShieldAlt,
  FaHeadset,
  FaGraduationCap,
  FaRocket,
  FaTrophy,
  FaUserTie,
  FaCogs,
  FaHandshake,
  FaGlobe
} from 'react-icons/fa';

export default function SimpleLandingPage() {
  const services = [
    {
      name: 'Automatización Industrial',
      description: 'Implementamos soluciones de automatización que optimizan sus procesos productivos, mejoran la eficiencia y reducen costos operativos.',
      icon: FaWrench,
      imageSrc: '/images/defaults/Automatización-Industrial.jpg',
      features: ['Sistemas robustos y escalables', 'Tecnología de punta', 'ROI garantizado']
    },
    {
      name: 'Programación de PLC',
      description: 'Desarrollamos software para control industrial adaptado a sus necesidades específicas con todas las marcas principales.',
      icon: FaLaptopCode,
      imageSrc: '/images/defaults/PROGRAMACION-PLC.jpeg',
      features: ['Todas las marcas', 'Funcionamiento óptimo', 'Soporte completo']
    },
    {
      name: 'Sistemas SCADA',
      description: 'Diseñamos sistemas de supervisión, control y adquisición de datos para monitoreo en tiempo real.',
      icon: FaMobileAlt,
      imageSrc: '/images/defaults/sistema-scada-gas-natual-eisenberg-monterrey.jpg',
      features: ['Tiempo real', 'Mejor toma de decisiones', 'Interfaz intuitiva']
    },
    {
      name: 'Mantenimiento Industrial',
      description: 'Servicios de mantenimiento preventivo y correctivo para garantizar el funcionamiento óptimo de sus sistemas.',
      icon: FaHammer,
      imageSrc: '/images/defaults/Mantenimiento-Industrial.jpg',
      features: ['Preventivo y correctivo', 'Mínimo downtime', 'Vida útil extendida']
    }
  ];

  const brands = [
    { src: '/images/Marcas/1.png', alt: 'Siemens' },
    { src: '/images/Marcas/2.png', alt: 'Allen Bradley' },
    { src: '/images/Marcas/3.png', alt: 'Schneider Electric' },
    { src: '/images/Marcas/4.png', alt: 'ABB' },
    { src: '/images/Marcas/5.png', alt: 'Omron' },
    { src: '/images/Marcas/6.png', alt: 'Mitsubishi' },
    { src: '/images/Marcas/7.png', alt: 'Honeywell' },
    { src: '/images/Marcas/8.png', alt: 'Emerson' }
  ];

  const companies = [
    { src: '/images/empresar/1.png', alt: 'Empresa 1' },
    { src: '/images/empresar/2.png', alt: 'Empresa 2' },
    { src: '/images/empresar/3.png', alt: 'Empresa 3' },
    { src: '/images/empresar/4.png', alt: 'Empresa 4' },
    { src: '/images/empresar/5.png', alt: 'Empresa 5' },
    { src: '/images/empresar/6.png', alt: 'Empresa 6' },
    { src: '/images/empresar/7.png', alt: 'Empresa 7' },
    { src: '/images/empresar/8.png', alt: 'Empresa 8' }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800 text-white overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gray-800"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-blue-600 bg-opacity-20 rounded-full text-blue-200 text-sm font-medium">
                  <FaIndustry className="mr-2" />
                  Líderes en Automatización Industrial desde 1999
                </div>

                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Transformamos su
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
                    Industria
                  </span>
                  con Tecnología
                </h1>

                <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
                  Más de 25 años desarrollando soluciones de automatización industrial
                  con tecnología de vanguardia y los más altos estándares internacionales.
                  Proporcionamos a nuestros clientes altos niveles de productividad y calidad.
                </p>
              </div>

              <div className="grid grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">25+</div>
                  <div className="text-sm text-gray-400">Años de Experiencia</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">500+</div>
                  <div className="text-sm text-gray-400">Proyectos Completados</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400">100+</div>
                  <div className="text-sm text-gray-400">Clientes Satisfechos</div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/services"
                  className="inline-flex items-center justify-center px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Conocer Servicios
                  <FaArrowRight className="ml-2" />
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-white border-opacity-20 hover:border-opacity-40 hover:bg-white hover:bg-opacity-10 rounded-lg font-semibold transition-all duration-300"
                >
                  <FaPhone className="mr-2" />
                  Contactar Ahora
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 from-opacity-20 to-cyan-600 to-opacity-20 rounded-3xl blur-3xl"></div>
              <div className="relative bg-white bg-opacity-10 rounded-3xl p-8 border border-white border-opacity-20 shadow-2xl">
                <div className="text-center space-y-4">
                  <Image
                    src="/images/logos/logo_pequeno.png"
                    alt="ECCSA Automation"
                    width={250}
                    height={250}
                    className="w-full h-auto max-w-xs mx-auto"
                    priority
                  />
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white mb-2">ECCSA Automation</h3>
                    <p className="text-gray-300 text-sm">Soluciones Industriales de Vanguardia</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0">
          <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 120L1440 120L1440 0C1440 0 1200 80 720 80C240 80 0 0 0 0L0 120Z" fill="white"/>
          </svg>
        </div>
      </section>

      {/* Brands Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              Nuestros Socios Tecnológicos
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Trabajamos con las marcas líderes en tecnología industrial para ofrecerle
              las mejores soluciones del mercado
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {brands.map((brand, index) => (
              <div
                key={index}
                className="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group"
              >
                <div className="relative w-20 h-12 grayscale group-hover:grayscale-0 transition-all duration-300">
                  <Image
                    src={brand.src}
                    alt={brand.alt}
                    fill
                    className="object-contain"
                    sizes="80px"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-600 text-sm font-medium mb-4">
              Nuestros Servicios
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Soluciones Integrales de Automatización
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Ofrecemos una amplia gama de servicios especializados para satisfacer
              todas sus necesidades de automatización industrial
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100">
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={service.imageSrc}
                    alt={service.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black from-opacity-60 via-black via-opacity-20 to-transparent"></div>
                  <div className="absolute bottom-6 left-6">
                    <div className="inline-flex items-center justify-center w-14 h-14 bg-blue-600 rounded-xl shadow-lg">
                      <service.icon className="w-7 h-7 text-white" />
                    </div>
                  </div>
                </div>

                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{service.name}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>

                  <div className="space-y-3 mb-8">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center">
                        <FaCheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    href="/services"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-2 transition-all duration-300"
                  >
                    Más información
                    <FaArrowRight className="ml-2" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-600 text-sm font-medium">
                  Desde 1999
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                  Quiénes Somos
                </h2>
                <p className="text-xl text-gray-600 leading-relaxed">
                  <strong className="text-blue-600">ECCSA Automation</strong> desarrolla soluciones en automatización
                  para procesos industriales desde 1999, utilizando la mejor tecnología y los más exigentes
                  estándares internacionales.
                </p>
              </div>

              <div className="space-y-6 text-gray-600 leading-relaxed">
                <p>
                  Nuestra compañía tiene como propósito primordial: <strong>Integrar sistemas de control
                  para aplicarlos en la industria moderna</strong>, proporcionando a nuestros clientes
                  altos niveles de productividad y calidad con la mejor relación costo-beneficio.
                </p>
                <p>
                  Nuestras alianzas estratégicas con los líderes mundiales en equipos de automatización
                  industrial nos permiten brindar un alto nivel de soporte tecnológico.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">ISO 9001</div>
                  <div className="text-gray-600">Certificación de Calidad</div>
                </div>
                <div className="text-center p-6 bg-white rounded-xl shadow-lg">
                  <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
                  <div className="text-gray-600">Soporte Técnico</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 from-opacity-10 to-cyan-600 to-opacity-10 rounded-3xl blur-3xl"></div>
              <div className="relative bg-white rounded-3xl p-8 shadow-2xl">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
                    <FaIndustry className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">500+</div>
                    <div className="text-gray-600">Proyectos</div>
                  </div>
                  <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
                    <FaChartLine className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">98%</div>
                    <div className="text-gray-600">Satisfacción</div>
                  </div>
                  <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
                    <FaGlobe className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">15+</div>
                    <div className="text-gray-600">Países</div>
                  </div>
                  <div className="text-center p-6 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl">
                    <FaTrophy className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
                    <div className="text-2xl font-bold text-gray-900">25+</div>
                    <div className="text-gray-600">Años</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-blue-700 to-cyan-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gray-800"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-5xl font-bold">
                ¿Listo para Transformar su Industria?
              </h2>
              <p className="text-xl text-blue-100 leading-relaxed">
                Contáctenos hoy mismo y descubra cómo nuestras soluciones de automatización
                pueden optimizar sus procesos, aumentar su productividad y reducir sus costos operativos.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-white text-blue-600 hover:bg-gray-100 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Solicitar Cotización
                <FaArrowRight className="ml-2" />
              </Link>
              <Link
                href="tel:+52-81-8000-0000"
                className="inline-flex items-center px-8 py-4 border-2 border-white border-opacity-20 hover:border-opacity-40 hover:bg-white hover:bg-opacity-10 rounded-lg font-semibold transition-all duration-300"
              >
                <FaPhone className="mr-2" />
                Llamar Ahora
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-8 py-4 border-2 border-white border-opacity-20 hover:border-opacity-40 hover:bg-white hover:bg-opacity-10 rounded-lg font-semibold transition-all duration-300"
              >
                <FaEnvelope className="mr-2" />
                Enviar Email
              </Link>
            </div>

            <div className="pt-8 border-t border-white border-opacity-20">
              <p className="text-blue-200 text-sm">
                Respuesta garantizada en menos de 24 horas • Consulta inicial gratuita
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
