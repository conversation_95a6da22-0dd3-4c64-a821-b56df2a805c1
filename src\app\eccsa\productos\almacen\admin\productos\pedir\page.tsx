'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

export default function ProductosPedirPage() {
  const [loading, setLoading] = useState(false);
  const [productos, setProductos] = useState([]);
  const [error, setError] = useState('');

  useEffect(() => {
    // Aquí se cargarían los productos a pedir
    // Por ahora, dejamos un array vacío
  }, []);

  return (
    <AuthGuard>
      <div className="modern-admin-page">
        <div className="modern-admin-header">
          <h1 className="modern-admin-title">Productos Pedidos</h1>
          <button className="modern-admin-button modern-admin-button-primary">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Generar Orden de Compra
          </button>
        </div>

        <div className="modern-admin-card">
          <div className="modern-admin-card-header">
            <h2 className="modern-admin-card-title">Lista de Productos Pedidos</h2>
            <div className="modern-admin-filters">
              <input
                type="text"
                placeholder="Buscar producto..."
                className="modern-admin-search"
              />
              <select className="modern-admin-select">
                <option value="">Todos los proveedores</option>
                <option value="proveedor1">Proveedor 1</option>
                <option value="proveedor2">Proveedor 2</option>
                <option value="proveedor3">Proveedor 3</option>
              </select>
            </div>
          </div>

          <div className="modern-admin-card-content">
            {loading ? (
              <div className="modern-admin-loading">
                <div className="modern-admin-spinner"></div>
                <p>Cargando productos...</p>
              </div>
            ) : error ? (
              <div className="modern-admin-error">
                <p>{error}</p>
                <button
                  className="modern-admin-button modern-admin-button-secondary"
                  onClick={() => {
                    setError('');
                    // Recargar productos
                  }}
                >
                  Reintentar
                </button>
              </div>
            ) : productos.length === 0 ? (
              <div className="modern-admin-empty">
                <div className="modern-admin-empty-icon">
                  <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                  </svg>
                </div>
                <h3 className="modern-admin-empty-title">No hay productos pedidos</h3>
                <p className="modern-admin-empty-text">Agrega productos a la lista de pedidos para generar órdenes de compra.</p>
              </div>
            ) : (
              <div className="modern-admin-table-container">
                <table className="modern-admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Nombre</th>
                      <th>Proveedor</th>
                      <th>Stock Actual</th>
                      <th>Stock Mínimo</th>
                      <th>Cantidad Pedida</th>
                      <th>Precio Unitario</th>
                      <th>Total</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td colSpan={9} className="modern-admin-table-empty">
                        No hay productos para mostrar
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        <div className="modern-admin-card">
          <div className="modern-admin-card-header">
            <h2 className="modern-admin-card-title">Resumen de Pedido</h2>
          </div>
          <div className="modern-admin-card-content">
            <div className="modern-admin-summary">
              <div className="modern-admin-summary-item">
                <span className="modern-admin-summary-label">Total de Productos:</span>
                <span className="modern-admin-summary-value">0</span>
              </div>
              <div className="modern-admin-summary-item">
                <span className="modern-admin-summary-label">Subtotal:</span>
                <span className="modern-admin-summary-value">$0.00</span>
              </div>
              <div className="modern-admin-summary-item">
                <span className="modern-admin-summary-label">IVA (16%):</span>
                <span className="modern-admin-summary-value">$0.00</span>
              </div>
              <div className="modern-admin-summary-item modern-admin-summary-total">
                <span className="modern-admin-summary-label">Total:</span>
                <span className="modern-admin-summary-value">$0.00</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
