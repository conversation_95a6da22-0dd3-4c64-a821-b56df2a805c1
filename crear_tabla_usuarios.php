<?php
/**
 * Script para crear la tabla de usuarios en la base de datos EccsaWeb
 * 
 * Este script lee el archivo SQL y lo ejecuta en la base de datos
 */

// Configuración de la base de datos
$host = 'localhost';
$usuario = 'eccsa';
$contrasena = 'eccsa?web?Admin';
$base_datos = 'EccsaWeb';

// Conectar a la base de datos
try {
    $conexion = new PDO("mysql:host=$host;dbname=$base_datos", $usuario, $contrasena);
    $conexion->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Conexión exitosa a la base de datos.\n";
} catch (PDOException $e) {
    die("Error de conexión: " . $e->getMessage() . "\n");
}

// Leer el archivo SQL
try {
    $sql = file_get_contents('crear_tabla_usuarios.sql');
    if ($sql === false) {
        throw new Exception("No se pudo leer el archivo SQL.");
    }
} catch (Exception $e) {
    die("Error al leer el archivo SQL: " . $e->getMessage() . "\n");
}

// Ejecutar las consultas SQL
try {
    // Dividir el archivo SQL en consultas individuales
    $consultas = explode(';', $sql);
    
    // Ejecutar cada consulta
    foreach ($consultas as $consulta) {
        $consulta = trim($consulta);
        if (!empty($consulta)) {
            $conexion->exec($consulta);
            echo "Consulta ejecutada con éxito: " . substr($consulta, 0, 50) . "...\n";
        }
    }
    
    echo "La tabla de usuarios ha sido creada exitosamente.\n";
} catch (PDOException $e) {
    die("Error al ejecutar las consultas SQL: " . $e->getMessage() . "\n");
}

// Verificar que la tabla se haya creado correctamente
try {
    $resultado = $conexion->query("SHOW TABLES LIKE 'usuarios'");
    if ($resultado->rowCount() > 0) {
        echo "Verificación: La tabla 'usuarios' existe en la base de datos.\n";
        
        // Mostrar la estructura de la tabla
        $resultado = $conexion->query("DESCRIBE usuarios");
        echo "\nEstructura de la tabla 'usuarios':\n";
        echo "------------------------------------\n";
        echo "Campo | Tipo | Nulo | Clave | Predeterminado | Extra\n";
        echo "------------------------------------\n";
        while ($fila = $resultado->fetch(PDO::FETCH_ASSOC)) {
            echo $fila['Field'] . " | " . $fila['Type'] . " | " . $fila['Null'] . " | " . $fila['Key'] . " | " . $fila['Default'] . " | " . $fila['Extra'] . "\n";
        }
        
        // Mostrar los usuarios insertados
        $resultado = $conexion->query("SELECT id, nombre, nombre_usuario, correo, nivel_ingeniero FROM usuarios");
        echo "\nUsuarios insertados:\n";
        echo "------------------------------------\n";
        echo "ID | Nombre | Usuario | Correo | Nivel\n";
        echo "------------------------------------\n";
        while ($fila = $resultado->fetch(PDO::FETCH_ASSOC)) {
            echo $fila['id'] . " | " . $fila['nombre'] . " | " . $fila['nombre_usuario'] . " | " . $fila['correo'] . " | " . $fila['nivel_ingeniero'] . "\n";
        }
    } else {
        echo "Error: La tabla 'usuarios' no se creó correctamente.\n";
    }
} catch (PDOException $e) {
    echo "Error al verificar la tabla: " . $e->getMessage() . "\n";
}

// Cerrar la conexión
$conexion = null;
echo "\nProceso completado.\n";
?>
