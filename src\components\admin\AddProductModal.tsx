'use client';

import { useState, useEffect } from 'react';
import { FaTimes, FaSave, FaCalculator } from 'react-icons/fa';

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProductCreate: (product: any) => void;
  editingProduct?: any;
}

interface ProductForm {
  numero_almacen: string;
  estante: string;
  modelo_existente: string;
  descripcion: string;
  precio_venta: number;
  precio_ml: number;
  cantidad_nuevo: number;
  minimo: number;
  maximo: number;
  pedir_cantidad: number;
  precio_us: number;
  precio_mx: number;
  impuesto: number;
  codigo_sat: string;
  nota: string;
  proveedor: string;
  marcas: string;
  tiempo_entrega_proveedor: string;
  fecha_pedido: string;
  fecha_recibido: string;
  activo: boolean;
  Url_imagen: string;
  Datos_importantes_Descripcion_muestra: string;
  tiempo_de_Entrega: string;
}

export default function AddProductModal({ isOpen, onClose, onProductCreate, editingProduct }: AddProductModalProps) {
  const [formData, setFormData] = useState<ProductForm>({
    numero_almacen: '',
    estante: '',
    modelo_existente: '',
    descripcion: '',
    precio_venta: 0,
    precio_ml: 0,
    cantidad_nuevo: 0,
    minimo: 0,
    maximo: 0,
    pedir_cantidad: 0,
    precio_us: 0,
    precio_mx: 0,
    impuesto: 0,
    codigo_sat: '',
    nota: '',
    proveedor: '',
    marcas: '',
    tiempo_entrega_proveedor: '',
    fecha_pedido: '',
    fecha_recibido: '',
    activo: true,
    Url_imagen: '',
    Datos_importantes_Descripcion_muestra: '',
    tiempo_de_Entrega: ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const isEditing = !!editingProduct;

  // Cargar datos del producto cuando se está editando
  useEffect(() => {
    if (editingProduct && isOpen) {
      setFormData({
        numero_almacen: editingProduct.numero_almacen || '',
        estante: editingProduct.estante || '',
        modelo_existente: editingProduct.modelo_existente || '',
        descripcion: editingProduct.descripcion || '',
        precio_venta: editingProduct.precio_venta || 0,
        precio_ml: editingProduct.precio_ml || 0,
        cantidad_nuevo: editingProduct.cantidad_nuevo || 0,
        minimo: editingProduct.minimo || 0,
        maximo: editingProduct.maximo || 0,
        pedir_cantidad: editingProduct.pedir_cantidad || 0,
        precio_us: editingProduct.precio_us || 0,
        precio_mx: editingProduct.precio_mx || 0,
        impuesto: editingProduct.impuesto || 0,
        codigo_sat: editingProduct.codigo_sat || '',
        nota: editingProduct.nota || '',
        proveedor: editingProduct.proveedor || '',
        marcas: editingProduct.marcas || '',
        tiempo_entrega_proveedor: editingProduct.tiempo_entrega_proveedor || '',
        fecha_pedido: editingProduct.fecha_pedido ? editingProduct.fecha_pedido.split('T')[0] : '',
        fecha_recibido: editingProduct.fecha_recibido ? editingProduct.fecha_recibido.split('T')[0] : '',
        activo: editingProduct.activo !== false,
        Url_imagen: editingProduct.Url_imagen || '',
        Datos_importantes_Descripcion_muestra: editingProduct.Datos_importantes_Descripcion_muestra || '',
        tiempo_de_Entrega: editingProduct.tiempo_de_Entrega || ''
      });
    } else if (!editingProduct && isOpen) {
      // Reset form para nuevo producto
      setFormData({
        numero_almacen: '',
        estante: '',
        modelo_existente: '',
        descripcion: '',
        precio_venta: 0,
        precio_ml: 0,
        cantidad_nuevo: 0,
        minimo: 0,
        maximo: 0,
        pedir_cantidad: 0,
        precio_us: 0,
        precio_mx: 0,
        impuesto: 0,
        codigo_sat: '',
        nota: '',
        proveedor: '',
        marcas: '',
        tiempo_entrega_proveedor: '',
        fecha_pedido: '',
        fecha_recibido: '',
        activo: true,
        Url_imagen: '',
        Datos_importantes_Descripcion_muestra: '',
        tiempo_de_Entrega: ''
      });
    }
  }, [editingProduct, isOpen]);

  // Calcular precio_mx cuando cambia precio_us
  useEffect(() => {
    if (formData.precio_us > 0) {
      const precio_mx = formData.precio_us * 20; // USD a MXN (20 pesos por dólar)
      setFormData(prev => ({ ...prev, precio_mx }));
    }
  }, [formData.precio_us]);

  // Calcular precio_venta cuando cambian precio_mx o impuesto
  useEffect(() => {
    if (formData.precio_mx > 0) {
      // Fórmula: precio_mx * 1.21 * 1.25 * 1.16 + 100 + impuestos
      const precio_venta = (formData.precio_mx * 1.21 * 1.25 * 1.16) + 100 + formData.impuesto;
      setFormData(prev => ({ ...prev, precio_venta: Math.round(precio_venta * 100) / 100 }));
    }
  }, [formData.precio_mx, formData.impuesto]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : 
               type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               value
    }));

    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Campos requeridos
    if (!formData.numero_almacen.trim()) {
      newErrors.numero_almacen = 'El número de almacén es requerido';
    }
    if (!formData.modelo_existente.trim()) {
      newErrors.modelo_existente = 'El modelo existente es requerido';
    }
    if (!formData.descripcion.trim()) {
      newErrors.descripcion = 'La descripción es requerida';
    }
    if (!formData.marcas.trim()) {
      newErrors.marcas = 'La marca es requerida';
    }
    if (!formData.proveedor.trim()) {
      newErrors.proveedor = 'El proveedor es requerido';
    }

    // Validaciones numéricas
    if (formData.cantidad_nuevo < 0) {
      newErrors.cantidad_nuevo = 'La cantidad no puede ser negativa';
    }
    if (formData.minimo < 0) {
      newErrors.minimo = 'El mínimo no puede ser negativo';
    }
    if (formData.maximo < 0) {
      newErrors.maximo = 'El máximo no puede ser negativo';
    }
    if (formData.minimo > formData.maximo && formData.maximo > 0) {
      newErrors.maximo = 'El máximo debe ser mayor al mínimo';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const url = isEditing ? `/api/almacen/${editingProduct.id}` : '/api/almacen';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          fecha_creacion: isEditing ? editingProduct.fecha_creacion : new Date().toISOString(),
          fecha_actualizacion: new Date().toISOString()
        }),
      });

      const data = await response.json();

      if (response.ok) {
        onProductCreate(data);
        onClose();
      } else {
        setErrors({ general: data.error || `Error al ${isEditing ? 'actualizar' : 'crear'} el producto` });
      }
    } catch (error) {
      console.error('Error:', error);
      setErrors({ general: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        width: '95vw',
        maxWidth: '1400px',
        height: '95vh',
        backgroundColor: 'white',
        borderRadius: '16px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #0056a6 0%, #003d75 100%)',
          color: 'white',
          padding: '1.5rem 2rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            margin: 0,
            display: 'flex',
            alignItems: 'center'
          }}>
            <FaSave style={{ marginRight: '12px' }} />
            {isEditing ? 'Editar Producto' : 'Agregar Nuevo Producto'}
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              color: 'white',
              padding: '8px',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
            disabled={loading}
          >
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          overflowY: 'auto',
          padding: '2rem'
        }}>
          <form id="product-form" onSubmit={handleSubmit}>
            {errors.general && (
              <div style={{
                background: 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)',
                border: '2px solid #f87171',
                color: '#dc2626',
                padding: '1rem',
                borderRadius: '12px',
                marginBottom: '2rem',
                fontWeight: '600'
              }}>
                {errors.general}
              </div>
            )}

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
              gap: '1.5rem'
            }}>
            {/* Información Básica */}
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              border: '1px solid #e2e8f0',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#0056a6',
                margin: '0 0 1.5rem 0',
                paddingBottom: '0.75rem',
                borderBottom: '2px solid #0056a6'
              }}>Información Básica</h3>

              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Número de Almacén <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <input
                  type="text"
                  name="numero_almacen"
                  value={formData.numero_almacen}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: `2px solid ${errors.numero_almacen ? '#dc2626' : '#d1d5db'}`,
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: ALM006"
                  disabled={loading}
                />
                {errors.numero_almacen && (
                  <div style={{
                    color: '#dc2626',
                    fontSize: '0.75rem',
                    marginTop: '0.5rem',
                    fontWeight: '600',
                    background: '#fef2f2',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    borderLeft: '3px solid #dc2626'
                  }}>
                    ⚠ {errors.numero_almacen}
                  </div>
                )}
              </div>

              {/* Modelo Existente */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Modelo Existente <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <input
                  type="text"
                  name="modelo_existente"
                  value={formData.modelo_existente}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: `2px solid ${errors.modelo_existente ? '#dc2626' : '#d1d5db'}`,
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Nombre del producto"
                  disabled={loading}
                />
                {errors.modelo_existente && (
                  <div style={{
                    color: '#dc2626',
                    fontSize: '0.75rem',
                    marginTop: '0.5rem',
                    fontWeight: '600',
                    background: '#fef2f2',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    borderLeft: '3px solid #dc2626'
                  }}>
                    ⚠ {errors.modelo_existente}
                  </div>
                )}
              </div>

              {/* Descripción */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Descripción <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <textarea
                  name="descripcion"
                  value={formData.descripcion}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: `2px solid ${errors.descripcion ? '#dc2626' : '#d1d5db'}`,
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white',
                    minHeight: '80px',
                    resize: 'vertical'
                  }}
                  placeholder="Descripción detallada del producto"
                  rows={3}
                  disabled={loading}
                />
                {errors.descripcion && (
                  <div style={{
                    color: '#dc2626',
                    fontSize: '0.75rem',
                    marginTop: '0.5rem',
                    fontWeight: '600',
                    background: '#fef2f2',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    borderLeft: '3px solid #dc2626'
                  }}>
                    ⚠ {errors.descripcion}
                  </div>
                )}
              </div>

              {/* Estante */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Estante
                </label>
                <input
                  type="text"
                  name="estante"
                  value={formData.estante}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: A1-01"
                  disabled={loading}
                />
              </div>

              {/* Marca */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Marca <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <input
                  type="text"
                  name="marcas"
                  value={formData.marcas}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: `2px solid ${errors.marcas ? '#dc2626' : '#d1d5db'}`,
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: Siemens, ABB, Schneider"
                  disabled={loading}
                />
                {errors.marcas && (
                  <div style={{
                    color: '#dc2626',
                    fontSize: '0.75rem',
                    marginTop: '0.5rem',
                    fontWeight: '600',
                    background: '#fef2f2',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    borderLeft: '3px solid #dc2626'
                  }}>
                    ⚠ {errors.marcas}
                  </div>
                )}
              </div>

              {/* Proveedor */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Proveedor <span style={{ color: '#dc2626' }}>*</span>
                </label>
                <input
                  type="text"
                  name="proveedor"
                  value={formData.proveedor}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: `2px solid ${errors.proveedor ? '#dc2626' : '#d1d5db'}`,
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Nombre del proveedor"
                  disabled={loading}
                />
                {errors.proveedor && (
                  <div style={{
                    color: '#dc2626',
                    fontSize: '0.75rem',
                    marginTop: '0.5rem',
                    fontWeight: '600',
                    background: '#fef2f2',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    borderLeft: '3px solid #dc2626'
                  }}>
                    ⚠ {errors.proveedor}
                  </div>
                )}
              </div>
            </div>

            {/* Precios */}
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              border: '1px solid #e2e8f0',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#0056a6',
                margin: '0 0 1.5rem 0',
                paddingBottom: '0.75rem',
                borderBottom: '2px solid #0056a6',
                display: 'flex',
                alignItems: 'center'
              }}>
                <FaCalculator style={{ marginRight: '8px' }} />
                Precios y Costos
              </h3>

              {/* Precio USD */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Precio USD
                </label>
                <input
                  type="number"
                  name="precio_us"
                  value={formData.precio_us}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  disabled={loading}
                />
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginTop: '0.5rem',
                  fontStyle: 'italic',
                  background: '#f9fafb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  borderLeft: '3px solid #0056a6'
                }}>
                  Al llenar este campo se calculará automáticamente el precio MX
                </div>
              </div>

              {/* Precio MX */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Precio MX
                </label>
                <input
                  type="number"
                  name="precio_mx"
                  value={formData.precio_mx}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  disabled={loading}
                />
              </div>

              {/* Impuestos */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Impuestos (Monto en pesos)
                </label>
                <input
                  type="number"
                  name="impuesto"
                  value={formData.impuesto}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  disabled={loading}
                />
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginTop: '0.5rem',
                  fontStyle: 'italic',
                  background: '#f9fafb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  borderLeft: '3px solid #0056a6'
                }}>
                  Monto en pesos, no porcentaje
                </div>
              </div>

              {/* Precio de Venta Calculado */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Precio de Venta (Calculado)
                </label>
                <input
                  type="number"
                  name="precio_venta"
                  value={formData.precio_venta}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #0056a6',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                    color: '#0056a6',
                    fontWeight: '600',
                    cursor: 'not-allowed'
                  }}
                  disabled
                />
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginTop: '0.5rem',
                  fontStyle: 'italic',
                  background: '#f9fafb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  borderLeft: '3px solid #0056a6'
                }}>
                  Fórmula: (Precio MX × 1.21 × 1.25 × 1.16) + 100 + Impuestos
                </div>
              </div>

              {/* Precio MercadoLibre */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Precio MercadoLibre
                </label>
                <input
                  type="number"
                  name="precio_ml"
                  value={formData.precio_ml}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  disabled={loading}
                />
              </div>

              {/* Cantidad */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Cantidad
                </label>
                <input
                  type="number"
                  name="cantidad_nuevo"
                  value={formData.cantidad_nuevo}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0"
                  min="0"
                  disabled={loading}
                />
              </div>

              {/* Mínimo */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Mínimo
                </label>
                <input
                  type="number"
                  name="minimo"
                  value={formData.minimo}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0"
                  min="0"
                  disabled={loading}
                />
              </div>

              {/* Máximo */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Máximo
                </label>
                <input
                  type="number"
                  name="maximo"
                  value={formData.maximo}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0"
                  min="0"
                  disabled={loading}
                />
              </div>

              {/* Pedir Cantidad */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Pedir Cantidad
                </label>
                <input
                  type="number"
                  name="pedir_cantidad"
                  value={formData.pedir_cantidad}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="0"
                  min="0"
                  disabled={loading}
                />
              </div>
            </div>

            {/* Información Adicional */}
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              border: '1px solid #e2e8f0',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#0056a6',
                margin: '0 0 1.5rem 0',
                paddingBottom: '0.75rem',
                borderBottom: '2px solid #0056a6'
              }}>Información Adicional</h3>

              {/* Código SAT */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Código SAT
                </label>
                <input
                  type="text"
                  name="codigo_sat"
                  value={formData.codigo_sat}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: 85371099"
                  disabled={loading}
                />
              </div>

              {/* Tiempo Entrega Proveedor */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Tiempo Entrega Proveedor
                </label>
                <input
                  type="text"
                  name="tiempo_entrega_proveedor"
                  value={formData.tiempo_entrega_proveedor}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: 2-3 semanas"
                  disabled={loading}
                />
              </div>

              {/* Tiempo de Entrega (Para /productos) */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Tiempo de Entrega (Para /productos)
                </label>
                <input
                  type="text"
                  name="tiempo_de_Entrega"
                  value={formData.tiempo_de_Entrega}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="Ej: 1-2 semanas"
                  disabled={loading}
                />
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginTop: '0.5rem',
                  fontStyle: 'italic',
                  background: '#f9fafb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  borderLeft: '3px solid #0056a6'
                }}>
                  Este tiempo aparece en la página de productos
                </div>
              </div>

              {/* Fecha Pedido Proveedor */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Fecha Pedido Proveedor
                </label>
                <input
                  type="date"
                  name="fecha_pedido"
                  value={formData.fecha_pedido}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  disabled={loading}
                />
              </div>

              {/* Fecha Recibido */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Fecha Recibido
                </label>
                <input
                  type="date"
                  name="fecha_recibido"
                  value={formData.fecha_recibido}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  disabled={loading}
                />
              </div>

              {/* URL Imagen */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  URL Imagen
                </label>
                <input
                  type="url"
                  name="Url_imagen"
                  value={formData.Url_imagen}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white'
                  }}
                  placeholder="https://ejemplo.com/imagen.jpg"
                  disabled={loading}
                />
              </div>

              {/* Descripción para Productos */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Descripción para Productos
                </label>
                <textarea
                  name="Datos_importantes_Descripcion_muestra"
                  value={formData.Datos_importantes_Descripcion_muestra}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white',
                    minHeight: '80px',
                    resize: 'vertical'
                  }}
                  placeholder="Esta descripción aparece en la página de productos"
                  rows={3}
                  disabled={loading}
                />
                <div style={{
                  fontSize: '0.75rem',
                  color: '#6b7280',
                  marginTop: '0.5rem',
                  fontStyle: 'italic',
                  background: '#f9fafb',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  borderLeft: '3px solid #0056a6'
                }}>
                  Esta es la descripción que se muestra en /productos
                </div>
              </div>

              {/* Nota */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '0.75rem',
                  fontSize: '0.875rem'
                }}>
                  Nota
                </label>
                <textarea
                  name="nota"
                  value={formData.nota}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.875rem 1rem',
                    border: '2px solid #d1d5db',
                    borderRadius: '10px',
                    fontSize: '0.875rem',
                    background: 'white',
                    minHeight: '60px',
                    resize: 'vertical'
                  }}
                  placeholder="Notas internas del producto"
                  rows={2}
                  disabled={loading}
                />
              </div>

              {/* Producto Activo */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '600',
                  color: '#1f2937',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    name="activo"
                    checked={formData.activo}
                    onChange={handleInputChange}
                    style={{
                      marginRight: '0.5rem',
                      width: '1rem',
                      height: '1rem',
                      accentColor: '#0056a6'
                    }}
                    disabled={loading}
                  />
                  Producto Activo
                </label>
              </div>
            </div>
          </div>
          </form>
        </div>

        {/* Footer */}
        <div style={{
          background: '#f8fafc',
          padding: '1.5rem 2rem',
          borderTop: '1px solid #e2e8f0',
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '1rem'
        }}>
          <button
            type="button"
            onClick={onClose}
            style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontWeight: '500',
              fontSize: '0.875rem',
              cursor: 'pointer',
              border: 'none',
              minWidth: '120px',
              background: '#e2e8f0',
              color: '#374151'
            }}
            disabled={loading}
          >
            Cancelar
          </button>
          <button
            type="submit"
            form="product-form"
            style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontWeight: '500',
              fontSize: '0.875rem',
              cursor: 'pointer',
              border: 'none',
              minWidth: '120px',
              background: '#0056a6',
              color: 'white'
            }}
            disabled={loading}
          >
            {loading ? (isEditing ? 'Actualizando...' : 'Guardando...') : (isEditing ? 'Actualizar Producto' : 'Guardar Producto')}
          </button>
        </div>
      </div>
    </div>
  );
}
