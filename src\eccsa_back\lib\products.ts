import { executeQuery } from './db';
import { Product, Category } from '../types/database';

/**
 * Get all products from the database
 */
export async function getAllProducts(): Promise<Product[]> {
  try {
    const query = `
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.name ASC
    `;
    
    const products = await executeQuery({ query }) as Product[];
    return products;
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

/**
 * Get a product by ID
 */
export async function getProductById(id: number): Promise<Product | null> {
  try {
    const query = `
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `;
    
    const products = await executeQuery({ query, values: [id] }) as Product[];
    
    if (products.length === 0) {
      return null;
    }
    
    return products[0];
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);
    return null;
  }
}

/**
 * Create a new product
 */
export async function createProduct(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<number | null> {
  try {
    const query = `
      INSERT INTO products (name, description, price, category_id, image_url, brand, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    const result = await executeQuery({
      query,
      values: [
        product.name,
        product.description,
        product.price || null,
        product.category_id,
        product.image_url || null,
        product.brand || null,
      ],
    }) as any;
    
    return result.insertId;
  } catch (error) {
    console.error('Error creating product:', error);
    return null;
  }
}

/**
 * Update an existing product
 */
export async function updateProduct(id: number, product: Partial<Product>): Promise<boolean> {
  try {
    // Build the SET part of the query dynamically based on provided fields
    const updates: string[] = [];
    const values: any[] = [];
    
    if (product.name !== undefined) {
      updates.push('name = ?');
      values.push(product.name);
    }
    
    if (product.description !== undefined) {
      updates.push('description = ?');
      values.push(product.description);
    }
    
    if (product.price !== undefined) {
      updates.push('price = ?');
      values.push(product.price);
    }
    
    if (product.category_id !== undefined) {
      updates.push('category_id = ?');
      values.push(product.category_id);
    }
    
    if (product.image_url !== undefined) {
      updates.push('image_url = ?');
      values.push(product.image_url);
    }
    
    if (product.brand !== undefined) {
      updates.push('brand = ?');
      values.push(product.brand);
    }
    
    // Always update the updated_at timestamp
    updates.push('updated_at = NOW()');
    
    // If no fields to update, return false
    if (updates.length === 0) {
      return false;
    }
    
    const query = `
      UPDATE products
      SET ${updates.join(', ')}
      WHERE id = ?
    `;
    
    // Add the ID to the values array
    values.push(id);
    
    const result = await executeQuery({ query, values }) as any;
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error(`Error updating product with ID ${id}:`, error);
    return false;
  }
}

/**
 * Delete a product
 */
export async function deleteProduct(id: number): Promise<boolean> {
  try {
    const query = 'DELETE FROM products WHERE id = ?';
    
    const result = await executeQuery({ query, values: [id] }) as any;
    
    return result.affectedRows > 0;
  } catch (error) {
    console.error(`Error deleting product with ID ${id}:`, error);
    return false;
  }
}

/**
 * Get all categories
 */
export async function getAllCategories(): Promise<Category[]> {
  try {
    const query = 'SELECT * FROM categories ORDER BY name ASC';
    
    const categories = await executeQuery({ query }) as Category[];
    
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}
