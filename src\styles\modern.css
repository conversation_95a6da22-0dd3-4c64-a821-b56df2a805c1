/* Modern CSS Utilities for ECCSA Web */
/* Complementa Tailwind CSS con utilidades específicas del proyecto */

/* Solo utilidades CSS personalizadas, sin Tailwind base para evitar conflictos */

/* Variables CSS personalizadas para ECCSA */
@layer base {
  :root {
    /* Colores ECCSA ya definidos en Tailwind config */
    --eccsa-primary: #0056a6;
    --eccsa-primary-light: #00a0e3;
    --eccsa-primary-dark: #003366;
    --eccsa-accent: #f7941d;
    --eccsa-red: #e31e24;

    /* Espaciado fluido */
    --space-fluid-xs: clamp(0.5rem, 1vw, 0.75rem);
    --space-fluid-sm: clamp(0.75rem, 1.5vw, 1rem);
    --space-fluid-md: clamp(1rem, 2vw, 1.5rem);
    --space-fluid-lg: clamp(1.5rem, 3vw, 2rem);
    --space-fluid-xl: clamp(2rem, 4vw, 3rem);
    --space-fluid-2xl: clamp(3rem, 6vw, 4rem);
    --space-fluid-3xl: clamp(4rem, 8vw, 6rem);

    /* Tipografía fluida */
    --text-fluid-xs: clamp(0.75rem, 1.5vw, 0.875rem);
    --text-fluid-sm: clamp(0.875rem, 1.75vw, 1rem);
    --text-fluid-base: clamp(1rem, 2vw, 1.125rem);
    --text-fluid-lg: clamp(1.125rem, 2.25vw, 1.25rem);
    --text-fluid-xl: clamp(1.25rem, 2.5vw, 1.5rem);
    --text-fluid-2xl: clamp(1.5rem, 3vw, 2rem);
    --text-fluid-3xl: clamp(1.875rem, 3.75vw, 2.5rem);
    --text-fluid-4xl: clamp(2.25rem, 4.5vw, 3rem);
    --text-fluid-5xl: clamp(3rem, 6vw, 4rem);
  }

  /* Reset mejorado */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Mejoras de accesibilidad */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    html {
      scroll-behavior: auto;
    }
  }
}

/* Componentes personalizados */
@layer components {
  /* Contenedor responsivo mejorado */
  .container-fluid {
    width: 100%;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-fluid-md);
    padding-right: var(--space-fluid-md);
  }

  /* Grid responsivo intrínseco */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
    gap: var(--space-fluid-lg);
  }

  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(min(100%, 300px), 1fr));
    gap: var(--space-fluid-lg);
  }

  /* Tipografía fluida */
  .text-fluid {
    font-size: var(--text-fluid-base);
    line-height: 1.6;
  }

  .heading-fluid {
    font-size: var(--text-fluid-2xl);
    line-height: 1.2;
    font-weight: 700;
  }

  /* Botones modernos */
  .btn-modern {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn-modern bg-eccsa-primary text-white hover:bg-eccsa-primary-dark focus:ring-eccsa-primary shadow-eccsa hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply btn-modern bg-eccsa-gray-600 text-white hover:bg-eccsa-gray-700 focus:ring-eccsa-gray-500;
  }

  .btn-accent {
    @apply btn-modern bg-eccsa-accent text-white hover:bg-eccsa-accent-dark focus:ring-eccsa-accent shadow-accent hover:-translate-y-0.5;
  }

  .btn-outline {
    @apply btn-modern border-2 border-eccsa-primary text-eccsa-primary hover:bg-eccsa-primary hover:text-white focus:ring-eccsa-primary;
  }

  /* Tarjetas modernas */
  .card-modern {
    @apply bg-white rounded-xl shadow-soft hover:shadow-strong transition-all duration-300 hover:-translate-y-1;
  }

  .card-interactive {
    @apply card-modern cursor-pointer hover:shadow-strong hover:-translate-y-2;
  }

  /* Efectos de hover modernos */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-strong;
  }

  /* Gradientes ECCSA */
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.eccsa.primary'), theme('colors.eccsa.primary-light'));
  }

  .gradient-accent {
    background: linear-gradient(135deg, theme('colors.eccsa.accent'), theme('colors.eccsa.accent-light'));
  }

  /* Animaciones de entrada */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Utilidades de texto */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Utilidades de layout */
  .aspect-ratio-16-9 {
    aspect-ratio: 16 / 9;
  }

  .aspect-ratio-4-3 {
    aspect-ratio: 4 / 3;
  }

  .aspect-ratio-1-1 {
    aspect-ratio: 1 / 1;
  }

  /* Utilidades de scroll */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .scroll-snap-x {
    scroll-snap-type: x mandatory;
  }

  .scroll-snap-y {
    scroll-snap-type: y mandatory;
  }

  .scroll-snap-start {
    scroll-snap-align: start;
  }

  .scroll-snap-center {
    scroll-snap-align: center;
  }
}

/* Utilidades adicionales */
@layer utilities {
  /* Espaciado fluido */
  .p-fluid-xs { padding: var(--space-fluid-xs); }
  .p-fluid-sm { padding: var(--space-fluid-sm); }
  .p-fluid-md { padding: var(--space-fluid-md); }
  .p-fluid-lg { padding: var(--space-fluid-lg); }
  .p-fluid-xl { padding: var(--space-fluid-xl); }
  .p-fluid-2xl { padding: var(--space-fluid-2xl); }
  .p-fluid-3xl { padding: var(--space-fluid-3xl); }

  .px-fluid-xs { padding-left: var(--space-fluid-xs); padding-right: var(--space-fluid-xs); }
  .px-fluid-sm { padding-left: var(--space-fluid-sm); padding-right: var(--space-fluid-sm); }
  .px-fluid-md { padding-left: var(--space-fluid-md); padding-right: var(--space-fluid-md); }
  .px-fluid-lg { padding-left: var(--space-fluid-lg); padding-right: var(--space-fluid-lg); }
  .px-fluid-xl { padding-left: var(--space-fluid-xl); padding-right: var(--space-fluid-xl); }
  .px-fluid-2xl { padding-left: var(--space-fluid-2xl); padding-right: var(--space-fluid-2xl); }
  .px-fluid-3xl { padding-left: var(--space-fluid-3xl); padding-right: var(--space-fluid-3xl); }

  .py-fluid-xs { padding-top: var(--space-fluid-xs); padding-bottom: var(--space-fluid-xs); }
  .py-fluid-sm { padding-top: var(--space-fluid-sm); padding-bottom: var(--space-fluid-sm); }
  .py-fluid-md { padding-top: var(--space-fluid-md); padding-bottom: var(--space-fluid-md); }
  .py-fluid-lg { padding-top: var(--space-fluid-lg); padding-bottom: var(--space-fluid-lg); }
  .py-fluid-xl { padding-top: var(--space-fluid-xl); padding-bottom: var(--space-fluid-xl); }
  .py-fluid-2xl { padding-top: var(--space-fluid-2xl); padding-bottom: var(--space-fluid-2xl); }
  .py-fluid-3xl { padding-top: var(--space-fluid-3xl); padding-bottom: var(--space-fluid-3xl); }

  /* Márgenes fluidos */
  .m-fluid-xs { margin: var(--space-fluid-xs); }
  .m-fluid-sm { margin: var(--space-fluid-sm); }
  .m-fluid-md { margin: var(--space-fluid-md); }
  .m-fluid-lg { margin: var(--space-fluid-lg); }
  .m-fluid-xl { margin: var(--space-fluid-xl); }
  .m-fluid-2xl { margin: var(--space-fluid-2xl); }
  .m-fluid-3xl { margin: var(--space-fluid-3xl); }

  /* Tipografía fluida */
  .text-fluid-xs { font-size: var(--text-fluid-xs); }
  .text-fluid-sm { font-size: var(--text-fluid-sm); }
  .text-fluid-base { font-size: var(--text-fluid-base); }
  .text-fluid-lg { font-size: var(--text-fluid-lg); }
  .text-fluid-xl { font-size: var(--text-fluid-xl); }
  .text-fluid-2xl { font-size: var(--text-fluid-2xl); }
  .text-fluid-3xl { font-size: var(--text-fluid-3xl); }
  .text-fluid-4xl { font-size: var(--text-fluid-4xl); }
  .text-fluid-5xl { font-size: var(--text-fluid-5xl); }

  /* Utilidades de truncado */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Utilidades de backdrop */
  .backdrop-blur-xs { backdrop-filter: blur(2px); }
  .backdrop-blur-sm { backdrop-filter: blur(4px); }
  .backdrop-blur-md { backdrop-filter: blur(8px); }
  .backdrop-blur-lg { backdrop-filter: blur(16px); }
  .backdrop-blur-xl { backdrop-filter: blur(24px); }
}
