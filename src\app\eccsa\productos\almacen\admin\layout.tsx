import type { Metadata } from 'next';
import '@/app/admin-styles.css';
import AdminLayoutComponent from '@/components/AdminLayout';

export const metadata: Metadata = {
  title: 'ECCSA - Área de Administración',
  description: 'Panel de administración para ECCSA Automation',
  icons: [
    { rel: 'icon', url: '/images/logos/logo_pequeno.png' },
    { rel: 'apple-touch-icon', url: '/images/logos/logo_pequeno.png' },
    { rel: 'shortcut icon', url: '/images/logos/logo_pequeno.png' }
  ],
};

// Layout para la ruta de administración - incluye sidebar en todas las páginas
export default function AdminRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body>
        <div className="admin-layout-container" style={{ backgroundColor: '#f5f7fa', minHeight: '100vh', color: '#333333' }}>
          <AdminLayoutComponent>
            {children}
          </AdminLayoutComponent>
        </div>
      </body>
    </html>
  );
}
