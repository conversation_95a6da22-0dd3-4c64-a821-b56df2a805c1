'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import AddProductModal from '@/components/admin/AddProductModal';
import { NotificationModal, DeleteModal } from '@/components/admin/ModernModals';
import { FaEdit, FaTrash, FaSync, FaPlus, FaSearch, FaFilter, FaBoxes } from 'react-icons/fa';

interface Product {
  id: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  minimo?: number;
  maximo?: number;
  pedir_cantidad?: number;
  precio_us?: number;
  precio_mx?: number;
  impuesto?: number;
  codigo_sat?: string;
  nota?: string;
  proveedor?: string;
  marcas?: string;
  tiempo_entrega_proveedor?: string;
  fecha_pedido?: Date | string;
  fecha_recibido?: Date | string;
  fecha_creacion?: Date;
  fecha_actualizacion?: Date;
  activo?: boolean;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

interface StockByBrand {
  [brand: string]: {
    total: number;
    inStock: number;
    outOfStock: number;
    lowStock: number;
  };
}

interface OrdersBySupplier {
  [supplier: string]: {
    totalOrders: number;
    totalQuantity: number;
    pending: number;
    received: number;
  };
}

interface PullDataStats {
  totalProducts: number;
  activeProducts: number;
  inactiveProducts: number;
  productsWithStock: number;
  productsOutOfStock: number;
  productsLowStock: number;
  productsWithOrders: number;
  totalOrderedQuantity: number;
  pendingOrders: number;
  receivedOrders: number;
  uniqueBrands: number;
  uniqueSuppliers: number;
  totalInventoryValue: number;
  averagePrice: number;
  lastUpdated: string;
  stockByBrand: StockByBrand;
  ordersBySupplier: OrdersBySupplier;
  recentActivity: any[];
}

export default function AlmacenPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [brands, setBrands] = useState<string[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showPullModal, setShowPullModal] = useState(false);
  const [pullLoading, setPullLoading] = useState(false);
  const [pullData, setPullData] = useState<PullDataStats | null>(null);

  // Estados para modales de notificación
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalMessage, setModalMessage] = useState('');

  // Estados para paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Load products
  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/almacen');
      const data = await response.json();

      if (response.ok) {
        const productsData = data.products || [];
        setProducts(productsData);
        setFilteredProducts(productsData);

        // Extract unique brands
        const brandsArray = productsData.map((p: Product) => p.marcas).filter(Boolean) as string[];
        const uniqueBrands = Array.from(new Set(brandsArray));
        setBrands(uniqueBrands.sort());
      } else {
        console.error('Error response:', data);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProducts();
  }, []);

  // Filter products
  useEffect(() => {
    let filtered = products.filter(product => {
      const matchesSearch =
        product.modelo_existente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.numero_almacen.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (product.marcas && product.marcas.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (product.descripcion && product.descripcion.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesBrand = !selectedBrand || product.marcas === selectedBrand;

      return matchesSearch && matchesBrand;
    });

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedBrand]);

  // Lógica de paginación
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  // Funciones de paginación
  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Reset página cuando cambia la búsqueda o filtro
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedBrand]);

  // Filtrar productos con pedidos (pedir_cantidad > 0)
  const productsWithOrders = products.filter(product =>
    product.pedir_cantidad && product.pedir_cantidad > 0
  );

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  // Funciones para mostrar notificaciones
  const showSuccessNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowSuccessModal(true);
  };

  const showErrorNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowErrorModal(true);
  };

  const getStockStatus = (cantidad: number | undefined, minimo: number | undefined, cantidadPedida: number | undefined): string => {
    if (!cantidad || cantidad === 0) return 'No Hay';
    if (cantidadPedida && cantidadPedida > 0) return 'Pedido Realizado';
    if (!minimo) return 'Normal';
    if (cantidad < minimo) return 'Pedir Material';
    return 'Normal';
  };

  // Función para manejar la creación de productos
  const handleProductCreate = (newProduct: any) => {
    // Recargar la lista de productos
    loadProducts();
    showSuccessNotification(
      'Producto Agregado',
      'El nuevo producto ha sido agregado exitosamente al almacén.'
    );
  };

  // Función para manejar la edición de productos
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowEditModal(true);
  };

  // Función para manejar la actualización de productos
  const handleProductUpdate = () => {
    setShowEditModal(false);
    setEditingProduct(null);
    loadProducts();
    showSuccessNotification(
      'Producto Actualizado',
      'El producto ha sido actualizado exitosamente.'
    );
  };

  // Función para confirmar eliminación
  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  // Función para eliminar producto
  const handleDeleteProduct = async () => {
    if (!productToDelete) return;

    try {
      setDeleteLoading(true);
      const response = await fetch(`/api/almacen/${productToDelete.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Recargar productos después de eliminar
        await loadProducts();
        setShowDeleteModal(false);
        setProductToDelete(null);
        showSuccessNotification(
          'Producto Eliminado',
          'El producto ha sido eliminado exitosamente del almacén.'
        );
      } else {
        const errorData = await response.json();
        console.error('Error al eliminar producto:', errorData);
        showErrorNotification(
          'Error al Eliminar',
          'No se pudo eliminar el producto: ' + (errorData.message || 'Error desconocido')
        );
      }
    } catch (error) {
      console.error('Error al eliminar producto:', error);
      showErrorNotification(
        'Error al Eliminar',
        'Error al eliminar el producto. Por favor, intenta de nuevo.'
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  // Función para cancelar eliminación
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setProductToDelete(null);
  };

  // Función para hacer pull y obtener detalles
  const handlePullData = async () => {
    try {
      setPullLoading(true);

      // Recargar productos
      await loadProducts();

      // Calcular estadísticas detalladas
      const stats: PullDataStats = {
        totalProducts: products.length,
        activeProducts: products.filter(p => p.activo !== false).length,
        inactiveProducts: products.filter(p => p.activo === false).length,
        productsWithStock: products.filter(p => (p.cantidad_nuevo || 0) > 0).length,
        productsOutOfStock: products.filter(p => (p.cantidad_nuevo || 0) === 0).length,
        productsLowStock: products.filter(p => {
          const cantidad = p.cantidad_nuevo || 0;
          const minimo = p.minimo || 0;
          return cantidad > 0 && cantidad < minimo;
        }).length,
        productsWithOrders: products.filter(p => (p.pedir_cantidad || 0) > 0).length,
        totalOrderedQuantity: products.reduce((sum, p) => sum + (p.pedir_cantidad || 0), 0),
        pendingOrders: products.filter(p => (p.pedir_cantidad || 0) > 0 && !p.fecha_recibido).length,
        receivedOrders: products.filter(p => (p.pedir_cantidad || 0) > 0 && p.fecha_recibido).length,
        uniqueBrands: Array.from(new Set(products.map(p => p.marcas).filter(Boolean))).length,
        uniqueSuppliers: Array.from(new Set(products.map(p => p.proveedor).filter(Boolean))).length,
        totalInventoryValue: products.reduce((sum, p) => sum + ((p.precio_venta || 0) * (p.cantidad_nuevo || 0)), 0),
        averagePrice: products.length > 0 ? products.reduce((sum, p) => sum + (p.precio_venta || 0), 0) / products.length : 0,
        lastUpdated: new Date().toLocaleString('es-MX'),
        stockByBrand: {} as StockByBrand,
        ordersBySupplier: {} as OrdersBySupplier,
        recentActivity: []
      };

      // Agrupar stock por marca
      products.forEach(product => {
        if (product.marcas) {
          if (!stats.stockByBrand[product.marcas]) {
            stats.stockByBrand[product.marcas] = {
              total: 0,
              inStock: 0,
              outOfStock: 0,
              lowStock: 0
            };
          }
          stats.stockByBrand[product.marcas].total++;
          if ((product.cantidad_nuevo || 0) === 0) {
            stats.stockByBrand[product.marcas].outOfStock++;
          } else if ((product.cantidad_nuevo || 0) < (product.minimo || 0)) {
            stats.stockByBrand[product.marcas].lowStock++;
          } else {
            stats.stockByBrand[product.marcas].inStock++;
          }
        }
      });

      // Agrupar pedidos por proveedor
      products.filter(p => (p.pedir_cantidad || 0) > 0).forEach(product => {
        if (product.proveedor) {
          if (!stats.ordersBySupplier[product.proveedor]) {
            stats.ordersBySupplier[product.proveedor] = {
              totalOrders: 0,
              totalQuantity: 0,
              pending: 0,
              received: 0
            };
          }
          stats.ordersBySupplier[product.proveedor].totalOrders++;
          stats.ordersBySupplier[product.proveedor].totalQuantity += (product.pedir_cantidad || 0);
          if (product.fecha_recibido) {
            stats.ordersBySupplier[product.proveedor].received++;
          } else {
            stats.ordersBySupplier[product.proveedor].pending++;
          }
        }
      });

      setPullData(stats);

    } catch (error) {
      console.error('Error al hacer pull:', error);
      alert('Error al obtener los datos. Por favor, intenta de nuevo.');
    } finally {
      setPullLoading(false);
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="admin-page-container">
      {/* Header */}
      <div className="admin-page-header">
        <div className="admin-header-content">
          <div className="admin-header-text">
            <h1 className="admin-page-title">Gestión de Almacén</h1>
            <p className="admin-page-subtitle">
              Administrar productos del inventario ECCSA
            </p>
          </div>
          <div className="admin-header-actions">
            <button
              className="modern-admin-button modern-admin-button-info"
              onClick={() => setShowPullModal(true)}
            >
              <FaSync className="w-4 h-4 mr-2" />
              Pull & Detalles
            </button>
            <button
              className="modern-admin-button modern-admin-button-secondary"
              onClick={loadProducts}
              disabled={loading}
            >
              <FaSync className="w-5 h-5 mr-2" />
              {loading ? 'Actualizando...' : 'Actualizar'}
            </button>
            <button
              className="modern-admin-button modern-admin-button-primary"
              onClick={() => setShowAddModal(true)}
            >
              <FaPlus className="w-5 h-5 mr-2" />
              Agregar Producto
            </button>
          </div>
        </div>
      </div>

      {/* Filtros y Búsqueda */}
      <div className="admin-filters-card">
        <div className="admin-filters-content">
          <div className="admin-filters-grid">
            <div className="admin-filter-item">
              <div className="admin-search-container">
                <FaSearch className="admin-search-icon" />
                <input
                  type="text"
                  placeholder="Buscar por código, modelo, marca, descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="admin-search-input"
                />
              </div>
            </div>
            <div className="admin-filter-item">
              <div className="admin-select-container">
                <FaFilter className="admin-select-icon" />
                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="admin-select-input"
                >
                  <option value="">Todas las marcas</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="admin-filter-item">
              <div className="admin-stats-display">
                <FaBoxes className="w-5 h-5 mr-2 text-blue-600" />
                <span className="text-sm font-medium text-gray-600">
                  {filteredProducts.length} productos
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabla de Productos */}
      <div className="admin-table-card">
        <div className="admin-table-header">
          <h2 className="admin-table-title">
            Productos de Almacén ({filteredProducts.length})
          </h2>
        </div>
        <div className="admin-table-content">
          {loading ? (
            <div className="admin-loading-container">
              <div className="admin-loading-spinner"></div>
              <p className="admin-loading-text">Cargando productos...</p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="admin-empty-state">
              <div className="admin-empty-icon">
                <FaBoxes className="w-16 h-16" />
              </div>
              <h3 className="admin-empty-title">No se encontraron productos</h3>
              <p className="admin-empty-text">No hay productos que coincidan con los filtros aplicados.</p>
            </div>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Código</th>
                    <th>Modelo</th>
                    <th>Marca</th>
                    <th>Estante</th>
                    <th>Stock</th>
                    <th>Cantidad Pedida</th>
                    <th>Precio Venta</th>
                    <th>Precio ML</th>
                    <th>Estado</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {currentProducts.map((product) => (
                    <tr key={product.id}>
                      <td className="admin-table-code">
                        <strong>{product.numero_almacen}</strong>
                      </td>
                      <td className="admin-table-model">
                        <div className="admin-product-name">{product.modelo_existente}</div>
                        {product.descripcion && (
                          <div className="admin-product-description">
                            {product.descripcion.length > 50
                              ? `${product.descripcion.substring(0, 50)}...`
                              : product.descripcion}
                          </div>
                        )}
                      </td>
                      <td>{product.marcas || '-'}</td>
                      <td>{product.estante || '-'}</td>
                      <td className="admin-table-quantity">
                        <span className="admin-stock-number">
                          {product.cantidad_nuevo || 0}
                        </span>
                      </td>
                      <td className="admin-table-quantity">
                        <span className="admin-pedido-number">
                          {product.pedir_cantidad || 0}
                        </span>
                      </td>
                      <td className="admin-table-price">{formatPrice(product.precio_venta)}</td>
                      <td className="admin-table-price">{formatPrice(product.precio_ml)}</td>
                      <td>
                        <span className={`admin-status-badge ${getStockStatus(product.cantidad_nuevo, product.minimo, product.pedir_cantidad).toLowerCase().replace(' ', '-')}`}>
                          {getStockStatus(product.cantidad_nuevo, product.minimo, product.pedir_cantidad)}
                        </span>
                      </td>
                      <td>
                        <div className="admin-table-actions">
                          <button
                            onClick={() => handleEditProduct(product)}
                            className="admin-action-btn edit"
                            title="Editar producto"
                          >
                            <FaEdit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(product)}
                            className="admin-action-btn danger"
                            title="Eliminar producto"
                          >
                            <FaTrash className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Paginación */}
        {!loading && filteredProducts.length > 0 && totalPages > 1 && (
          <div className="admin-pagination-container">
            <div className="admin-pagination-info">
              <span className="admin-pagination-text">
                Mostrando {startIndex + 1} - {Math.min(endIndex, filteredProducts.length)} de {filteredProducts.length} productos
              </span>
            </div>
            <div className="admin-pagination-controls">
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                className="admin-pagination-button admin-pagination-button-prev"
              >
                ← Anterior
              </button>

              <div className="admin-pagination-numbers">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => goToPage(page)}
                    className={`admin-pagination-number ${currentPage === page ? 'active' : ''}`}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="admin-pagination-button admin-pagination-button-next"
              >
                Siguiente →
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Tabla de Materiales Pedidos */}
      <div className="admin-table-card" style={{ marginTop: '2rem' }}>
        <div className="admin-table-header">
          <h2 className="admin-table-title">
            Materiales Pedidos ({productsWithOrders.length})
          </h2>
          <p className="admin-table-subtitle">
            Productos con cantidad pedida mayor a 0
          </p>
        </div>
        <div className="admin-table-content">
          {loading ? (
            <div className="admin-loading-container">
              <div className="admin-loading-spinner"></div>
              <p className="admin-loading-text">Cargando pedidos...</p>
            </div>
          ) : productsWithOrders.length === 0 ? (
            <div className="admin-empty-state">
              <div className="admin-empty-icon">
                <FaBoxes className="w-16 h-16" />
              </div>
              <h3 className="admin-empty-title">No hay materiales pedidos</h3>
              <p className="admin-empty-description">
                No se encontraron productos con cantidad pedida
              </p>
            </div>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Código</th>
                    <th>Modelo</th>
                    <th>Cantidad Pedida</th>
                    <th>Proveedor</th>
                    <th>Tiempo de Entrega</th>
                    <th>Fecha Pedido</th>
                    <th>Fecha Recibido</th>
                    <th>Estado</th>
                  </tr>
                </thead>
                <tbody>
                  {productsWithOrders.map((product) => (
                    <tr key={`order-${product.id}`}>
                      <td className="admin-table-code">
                        <strong>{product.numero_almacen}</strong>
                      </td>
                      <td className="admin-table-model">
                        <div className="admin-product-name">
                          {product.modelo_existente}
                        </div>
                        <div className="admin-product-description">
                          {product.marcas && `Marca: ${product.marcas}`}
                        </div>
                      </td>
                      <td className="admin-table-quantity">
                        <span className="admin-order-quantity">
                          {product.pedir_cantidad}
                        </span>
                      </td>
                      <td className="admin-table-supplier">
                        <span className="admin-supplier-name">
                          {product.proveedor || 'No especificado'}
                        </span>
                      </td>
                      <td className="admin-table-delivery">
                        <span className="admin-delivery-time">
                          {product.tiempo_entrega_proveedor || 'No especificado'}
                        </span>
                      </td>
                      <td className="admin-table-date">
                        <span className="admin-order-date">
                          {product.fecha_pedido ?
                            new Date(product.fecha_pedido).toLocaleDateString('es-MX') :
                            'No registrada'
                          }
                        </span>
                      </td>
                      <td className="admin-table-date">
                        <span className="admin-received-date">
                          {product.fecha_recibido ?
                            new Date(product.fecha_recibido).toLocaleDateString('es-MX') :
                            'Pendiente'
                          }
                        </span>
                      </td>
                      <td>
                        <span className={`admin-order-status-badge ${product.fecha_recibido ? 'received' : 'pending'}`}>
                          {product.fecha_recibido ? 'Recibido' : 'Pendiente'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modal para agregar producto */}
      <AddProductModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onProductCreate={handleProductCreate}
      />

      {/* Modal para editar producto */}
      {editingProduct && (
        <AddProductModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingProduct(null);
          }}
          onProductCreate={handleProductUpdate}
          editingProduct={editingProduct}
        />
      )}

      {/* Modal de Eliminación Moderno */}
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleDeleteProduct}
        loading={deleteLoading}
        title="Eliminar Producto"
        itemName={productToDelete ? `${productToDelete.modelo_existente} (${productToDelete.numero_almacen})` : ''}
        itemDetails={productToDelete ? `Código: ${productToDelete.numero_almacen}\nModelo: ${productToDelete.modelo_existente}\nMarca: ${productToDelete.marcas || 'Sin marca'}` : ''}
        warningMessage="Esta acción eliminará permanentemente este producto del almacén."
      />

      {/* Modales de Notificación */}
      {showSuccessModal && (
        <NotificationModal
          type="success"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowSuccessModal(false)}
        />
      )}

      {showErrorModal && (
        <NotificationModal
          type="error"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}

      {/* Modal de Pull y Detalles */}
      {showPullModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 99999,
          padding: '20px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            maxWidth: '1200px',
            width: '95%',
            maxHeight: '90vh',
            overflow: 'hidden',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Header */}
            <div style={{
              background: 'linear-gradient(135deg, #0056a6 0%, #003d75 100%)',
              color: 'white',
              padding: '1.5rem 2rem',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                margin: 0,
                display: 'flex',
                alignItems: 'center'
              }}>
                <FaSync style={{ marginRight: '12px' }} />
                Pull & Análisis de Datos
              </h2>
              <button
                onClick={() => setShowPullModal(false)}
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  border: 'none',
                  color: 'white',
                  padding: '8px',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                ✕
              </button>
            </div>

            {/* Content */}
            <div style={{
              flex: 1,
              overflowY: 'auto',
              padding: '2rem'
            }}>
              {!pullData ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem'
                }}>
                  <div style={{
                    fontSize: '4rem',
                    marginBottom: '1rem'
                  }}>📊</div>
                  <h3 style={{
                    fontSize: '1.5rem',
                    color: '#374151',
                    marginBottom: '1rem'
                  }}>
                    Análisis de Datos del Almacén
                  </h3>
                  <p style={{
                    color: '#6b7280',
                    marginBottom: '2rem'
                  }}>
                    Haz clic en "Ejecutar Pull" para sincronizar y obtener estadísticas detalladas
                  </p>
                  <button
                    onClick={handlePullData}
                    disabled={pullLoading}
                    style={{
                      background: pullLoading ? '#93c5fd' : 'linear-gradient(135deg, #0056a6 0%, #003d75 100%)',
                      color: 'white',
                      border: 'none',
                      padding: '1rem 2rem',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      fontWeight: '600',
                      cursor: pullLoading ? 'not-allowed' : 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      margin: '0 auto'
                    }}
                  >
                    <FaSync className={pullLoading ? 'animate-spin' : ''} />
                    {pullLoading ? 'Ejecutando Pull...' : 'Ejecutar Pull'}
                  </button>
                </div>
              ) : (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '1.5rem'
                }}>
                  {/* Estadísticas Generales */}
                  <div style={{
                    background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    border: '1px solid #0ea5e9'
                  }}>
                    <h4 style={{
                      color: '#0369a1',
                      marginBottom: '1rem',
                      fontSize: '1.125rem',
                      fontWeight: '600'
                    }}>📈 Estadísticas Generales</h4>
                    <div style={{ display: 'grid', gap: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Total de Productos:</span>
                        <strong>{pullData.totalProducts}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Productos Activos:</span>
                        <strong style={{ color: '#059669' }}>{pullData.activeProducts}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Productos Inactivos:</span>
                        <strong style={{ color: '#dc2626' }}>{pullData.inactiveProducts}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Marcas Únicas:</span>
                        <strong>{pullData.uniqueBrands}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Proveedores Únicos:</span>
                        <strong>{pullData.uniqueSuppliers}</strong>
                      </div>
                    </div>
                  </div>

                  {/* Estado del Inventario */}
                  <div style={{
                    background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    border: '1px solid #22c55e'
                  }}>
                    <h4 style={{
                      color: '#166534',
                      marginBottom: '1rem',
                      fontSize: '1.125rem',
                      fontWeight: '600'
                    }}>📦 Estado del Inventario</h4>
                    <div style={{ display: 'grid', gap: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Con Stock:</span>
                        <strong style={{ color: '#059669' }}>{pullData.productsWithStock}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Sin Stock:</span>
                        <strong style={{ color: '#dc2626' }}>{pullData.productsOutOfStock}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Stock Bajo:</span>
                        <strong style={{ color: '#d97706' }}>{pullData.productsLowStock}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Valor Total:</span>
                        <strong>${pullData.totalInventoryValue.toLocaleString('es-MX')}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Precio Promedio:</span>
                        <strong>${pullData.averagePrice.toFixed(2)}</strong>
                      </div>
                    </div>
                  </div>

                  {/* Pedidos */}
                  <div style={{
                    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    border: '1px solid #f59e0b'
                  }}>
                    <h4 style={{
                      color: '#92400e',
                      marginBottom: '1rem',
                      fontSize: '1.125rem',
                      fontWeight: '600'
                    }}>🛒 Estado de Pedidos</h4>
                    <div style={{ display: 'grid', gap: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Productos con Pedidos:</span>
                        <strong>{pullData.productsWithOrders}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Cantidad Total Pedida:</span>
                        <strong>{pullData.totalOrderedQuantity}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Pedidos Pendientes:</span>
                        <strong style={{ color: '#d97706' }}>{pullData.pendingOrders}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Pedidos Recibidos:</span>
                        <strong style={{ color: '#059669' }}>{pullData.receivedOrders}</strong>
                      </div>
                    </div>
                  </div>

                  {/* Información del Sistema */}
                  <div style={{
                    background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    border: '1px solid #6b7280'
                  }}>
                    <h4 style={{
                      color: '#374151',
                      marginBottom: '1rem',
                      fontSize: '1.125rem',
                      fontWeight: '600'
                    }}>⚙️ Información del Sistema</h4>
                    <div style={{ display: 'grid', gap: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Última Actualización:</span>
                        <strong>{pullData.lastUpdated}</strong>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Estado del Sistema:</span>
                        <strong style={{ color: '#059669' }}>✅ Operativo</strong>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div style={{
              background: '#f8fafc',
              padding: '1.5rem 2rem',
              borderTop: '1px solid #e2e8f0',
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '1rem'
            }}>
              {pullData && (
                <button
                  onClick={handlePullData}
                  disabled={pullLoading}
                  style={{
                    background: pullLoading ? '#93c5fd' : '#0056a6',
                    color: 'white',
                    border: 'none',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '8px',
                    fontWeight: '500',
                    cursor: pullLoading ? 'not-allowed' : 'pointer'
                  }}
                >
                  {pullLoading ? 'Actualizando...' : 'Actualizar Datos'}
                </button>
              )}
              <button
                onClick={() => setShowPullModal(false)}
                style={{
                  background: '#e5e7eb',
                  color: '#374151',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
