# 🏭 ECCSA Web - Sistema Integral de Gestión Industrial

<div align="center">

![ECCSA Logo](public/images/logos/logo_largo.png)

**Sistema web completo para ECCSA (Empresa de Control y Comunicación S.A.)**
*Automatización Industrial • Gestión de Almacén • Control de Inventarios*

[![Next.js](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18-blue?style=for-the-badge&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-orange?style=for-the-badge&logo=mysql)](https://www.mysql.com/)

</div>

---

## 🌟 **Características Principales**

### 🎯 **Sistema Completo de Gestión**
- **🏪 Sitio Web Corporativo** - Presentación profesional de servicios y productos
- **📦 Sistema de Almacén** - Gestión completa de inventarios con estados en tiempo real
- **👥 Panel de Administración** - Control total con diferentes niveles de usuario
- **🔐 Autenticación Segura** - Sistema de login con roles y permisos
- **📱 Diseño Responsive** - Optimizado para móviles, tablets y desktop

### ⚡ **Tecnologías de Vanguardia**
- **Framework**: Next.js 14 con App Router
- **Frontend**: React 18 + TypeScript
- **Base de Datos**: MySQL 8.0 con conexión optimizada
- **Estilos**: CSS personalizado sin frameworks externos
- **API**: RESTful APIs con validación completa
- **Seguridad**: Autenticación JWT y validación de datos

---

## 🚀 **Inicio Rápido**

### 📋 **Requisitos del Sistema**
```bash
Node.js >= 18.0.0
MySQL >= 8.0
npm >= 9.0.0
```

### ⚙️ **Instalación**

1. **Clonar el repositorio**
```bash
git clone https://github.com/tu-usuario/eccsa-web.git
cd eccsa-web
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
# Crear archivo .env.local
cp .env.example .env.local
```

4. **Configurar base de datos**
```env
# .env.local
DB_HOST=localhost
DB_USER=eccsa
DB_PASSWORD=eccsa?web?Admin
DB_NAME=EccsaWeb
```

5. **Crear base de datos**
```bash
# Ejecutar scripts SQL incluidos
mysql -u root -p < database-setup.sql
mysql -u root -p < crear_tabla_usuarios.sql
mysql -u root -p < crear_tabla_almacen.sql
```

6. **Iniciar servidor de desarrollo**
```bash
# Comando especial para Windows
powershell -executionPolicy Bypass "npx next dev"

# O comando estándar
npm run dev
```

7. **Acceder al sistema**
```
🌐 Sitio web: http://localhost:3000
🔧 Admin: http://localhost:3000/eccsa/productos/almacen/admin
👤 Login: admin / eccsa2023
```

---

## 📊 **Sistema de Almacén**

### 🎯 **Funcionalidades Principales**

#### **📦 Gestión de Inventarios**
- ✅ **CRUD Completo** - Crear, leer, actualizar y eliminar productos
- ✅ **Estados Visuales** - Sistema de colores para estado de stock
- ✅ **Búsqueda Avanzada** - Por número, modelo, marca, proveedor, código SAT
- ✅ **Validación de Duplicados** - Previene números de almacén repetidos
- ✅ **Cálculo Automático** - Precios de venta con fórmula personalizada

#### **🎨 Estados de Stock**
| Color | Estado | Condición | Descripción |
|-------|--------|-----------|-------------|
| 🔵 **Azul** | Ya se pidió | `pedir_cantidad > 0` | Producto ya ordenado al proveedor |
| 🔴 **Rojo** | Sin stock | `cantidad = 0` | Producto agotado |
| 🟡 **Amarillo** | Por agotarse | `cantidad ≤ mínimo` | Stock crítico |
| 🟢 **Verde** | En stock | `cantidad > mínimo` | Stock normal |

#### **💰 Cálculo de Precios**
```javascript
// Fórmula automática de precio de venta
precio_venta = (precio_mx × 1.2 × 1.25 × 1.16) + 100 + impuesto

// Conversión automática USD → MXN
precio_mx = precio_us × tipo_cambio (18.50)
```

### 🗃️ **Estructura de Base de Datos**

#### **Tabla `almacen` (25 columnas)**
```sql
CREATE TABLE almacen (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_almacen VARCHAR(50) NOT NULL UNIQUE,
    estante VARCHAR(100),
    modelo_existente VARCHAR(200) NOT NULL,
    descripcion TEXT,
    precio_venta DECIMAL(12, 2),
    precio_ml DECIMAL(12, 2),
    vendidos INT DEFAULT 0,
    cantidad_nuevo INT DEFAULT 0,
    minimo INT DEFAULT 0,
    maximo INT DEFAULT 0,
    pedir_cantidad INT DEFAULT 0,
    precio_us DECIMAL(12, 2),
    precio_mx DECIMAL(12, 2),
    impuesto DECIMAL(5, 2) DEFAULT 16.00,
    codigo_sat VARCHAR(50),
    nota TEXT,
    proveedor VARCHAR(200),
    marcas VARCHAR(200),           -- ← NUEVA COLUMNA
    tiempo_entrega_proveedor VARCHAR(100),
    fecha_pedido DATE,
    fecha_recibido DATE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    activo BOOLEAN DEFAULT TRUE
);
```

---

## 🏗️ **Arquitectura del Proyecto**

### 📁 **Estructura de Directorios**
```
eccsa-web/
├── 📄 README.md                    # Documentación principal
├── 📄 README_ALMACEN.md           # Documentación del almacén
├── 🗄️ database-setup.sql          # Setup inicial de BD
├── 🗄️ crear_tabla_almacen.sql     # Tabla de almacén
├── 🗄️ crear_tabla_usuarios.sql    # Tabla de usuarios
├── 📦 src/
│   ├── 🎯 app/                     # Next.js App Router
│   │   ├── 🌐 api/                 # API Routes
│   │   │   ├── almacen/            # APIs del almacén
│   │   │   ├── users/              # APIs de usuarios
│   │   │   └── database/           # APIs de base de datos
│   │   ├── 🏢 eccsa/               # Panel de administración
│   │   │   └── productos/almacen/admin/
│   │   ├── 📱 components/          # Componentes React
│   │   ├── 🎨 globals.css          # Estilos globales
│   │   └── 📄 page.tsx             # Página principal
│   ├── 🧩 components/              # Componentes reutilizables
│   │   ├── Navbar.tsx              # Barra de navegación
│   │   ├── Footer.tsx              # Pie de página
│   │   ├── AdminSidebar.tsx        # Sidebar del admin
│   │   └── AuthGuard.tsx           # Protección de rutas
│   ├── 🔧 eccsa_back/              # Backend y lógica
│   │   ├── lib/                    # Librerías principales
│   │   │   ├── db.ts               # Conexión a BD
│   │   │   ├── almacen.ts          # Lógica del almacén
│   │   │   └── auth.ts             # Autenticación
│   │   ├── types/                  # Tipos TypeScript
│   │   └── models/                 # Modelos de datos
│   └── 🎨 styles/                  # Estilos CSS
└── 📦 public/                      # Archivos estáticos
    ├── 🖼️ images/                  # Imágenes del sitio
    │   ├── logos/                  # Logos de ECCSA
    │   ├── Slider/                 # Imágenes del slider
    │   ├── Marcas/                 # Logos de marcas
    │   └── services/               # Imágenes de servicios
    └── 📄 favicon.ico              # Icono del sitio
```

### 🔗 **APIs Principales**

#### **📦 Sistema de Almacén**
```typescript
GET    /api/almacen              // Obtener todos los productos
POST   /api/almacen              // Crear nuevo producto
GET    /api/almacen/[id]         // Obtener producto por ID
PUT    /api/almacen/[id]         // Actualizar producto
DELETE /api/almacen/[id]         // Eliminar producto
GET    /api/almacen?search=term  // Buscar productos
GET    /api/almacen?stats=true   // Estadísticas del almacén
```

#### **👥 Sistema de Usuarios**
```typescript
POST   /api/users/login          // Iniciar sesión
POST   /api/users/logout         // Cerrar sesión
GET    /api/users/profile        // Obtener perfil
PUT    /api/users/profile        // Actualizar perfil
GET    /api/users                // Listar usuarios (admin)
```

#### **🗄️ Base de Datos**
```typescript
GET    /api/database/test        // Probar conexión
GET    /api/database/stats       // Estadísticas de BD
POST   /api/almacen/create-table // Crear tabla almacén
```

---

## 👥 **Sistema de Usuarios**

### 🔐 **Niveles de Acceso**
| Nivel | Rol | Permisos |
|-------|-----|----------|
| **0** | 👑 Administrador | Acceso total al sistema |
| **1** | 👨‍💼 Manager | Gestión de productos y usuarios |
| **2** | 👨‍🔧 Supervisor | Gestión de productos |
| **3** | 👨‍💻 Operador | Solo lectura |
| **4** | 👁️ Visualizador | Solo visualización |

### 👤 **Usuarios Predeterminados**
```javascript
// Administradores
oscar.castillo    // Nivel 0 - Admin total
esteban.carrera   // Nivel 0 - Admin total
admin            // Nivel 0 - Admin sistema

// Contraseña temporal: eccsa2023
```

---

## 🌐 **Sitio Web Corporativo**

### 📄 **Páginas Principales**
- **🏠 Inicio** (`/`) - Página principal con slider y servicios
- **🔧 Servicios** (`/services`) - Catálogo de servicios industriales
- **📦 Productos** (`/products`) - Catálogo de productos
- **🏗️ Proyectos** (`/projects`) - Portafolio de proyectos realizados
- **👥 Clientes** (`/clients`) - Testimonios y casos de éxito
- **📞 Contacto** (`/contact`) - Información de contacto
- **📊 Reportes** (`/reports`) - Reportes y documentación

### 🎨 **Características de Diseño**
- ✅ **Responsive Design** - Adaptable a todos los dispositivos
- ✅ **Slider Parallax** - Efectos visuales modernos
- ✅ **Navegación Sticky** - Barra de navegación fija
- ✅ **Dark Mode** - Modo oscuro opcional
- ✅ **WhatsApp Integration** - Botón de contacto directo
- ✅ **Optimización SEO** - Meta tags y estructura optimizada

---

## 🛠️ **Scripts y Comandos**

### 📜 **Scripts de Desarrollo**
```bash
# Desarrollo
npm run dev                    # Servidor de desarrollo
npm run dev:turbo             # Con Turbopack (experimental)

# Producción
npm run build                 # Construir para producción
npm run start                 # Servidor de producción
npm run export                # Exportar sitio estático

# Calidad de Código
npm run lint                  # Linter ESLint
npm run lint:fix              # Corregir errores automáticamente
npm run type-check            # Verificar tipos TypeScript

# Base de Datos
npm run db:setup              # Configurar base de datos
npm run db:migrate            # Ejecutar migraciones
npm run db:seed               # Poblar con datos de ejemplo
```

### 🔧 **Scripts Personalizados**
```bash
# Windows (PowerShell)
powershell -executionPolicy Bypass "npx next dev"

# Crear tabla de almacén
node crear_tabla_almacen.js

# Crear tabla de usuarios
node crear_tabla_usuarios.js

# Probar conexión a BD
node test_db_connection.js
```

---

## 🚀 **Despliegue**

### 🖥️ **Desarrollo Local**
```bash
# Clonar y configurar
git clone [repo-url]
cd eccsa-web
npm install
cp .env.example .env.local

# Configurar base de datos
mysql -u root -p < database-setup.sql

# Iniciar desarrollo
npm run dev
```

### 🌐 **Producción (cPanel)**
```bash
# Construir proyecto
npm run build

# Crear ZIP para cPanel
npm run export
zip -r eccsa-web.zip out/

# Subir a cPanel y extraer en /pagina
```

### ☁️ **VPS (Ubuntu + Nginx)**
```bash
# Instalar dependencias
sudo apt update
sudo apt install nodejs npm nginx mysql-server

# Configurar PM2
npm install -g pm2
pm2 start ecosystem.config.js

# Configurar Nginx
sudo nano /etc/nginx/sites-available/eccsa
sudo ln -s /etc/nginx/sites-available/eccsa /etc/nginx/sites-enabled/

# SSL con Certbot
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d tu-dominio.com
```

---

## 📊 **Datos de Ejemplo**

### 📦 **Productos de Almacén**
```sql
-- Ejemplos incluidos en la base de datos
ALM-001: SIMATIC S7-1200 CPU 1214C (SIEMENS)
ALM-002: SINAMICS G120C 0.75kW (SIEMENS)
ALM-003: SICK WT100-P132 (SICK)
ALM-004: Phoenix Contact QUINT-PS (Phoenix Contact)
ALM-005: Schneider Electric ATV320 (Schneider Electric)
```

### 🏭 **Marcas Principales**
- **SIEMENS** - Automatización industrial
- **SICK** - Sensores y sistemas de seguridad
- **Phoenix Contact** - Conexiones industriales
- **Schneider Electric** - Gestión de energía
- **ABB** - Robótica y automatización

---

## 🔧 **Configuración Avanzada**

### 🗄️ **Variables de Entorno**
```env
# Base de datos
DB_HOST=localhost
DB_USER=eccsa
DB_PASSWORD=eccsa?web?Admin
DB_NAME=EccsaWeb

# Autenticación
JWT_SECRET=tu_jwt_secret_muy_seguro
SESSION_SECRET=tu_session_secret

# APIs externas
EXCHANGE_RATE_API=tu_api_key
WHATSAPP_API=tu_whatsapp_token

# Configuración
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://tu-dominio.com
```

### ⚙️ **Configuración de MySQL**
```sql
-- Crear usuario y base de datos
CREATE DATABASE EccsaWeb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'eccsa'@'localhost' IDENTIFIED BY 'eccsa?web?Admin';
GRANT ALL PRIVILEGES ON EccsaWeb.* TO 'eccsa'@'localhost';
FLUSH PRIVILEGES;
```

---

## 🧪 **Testing y Calidad**

### ✅ **Funcionalidades Probadas**
- ✅ **CRUD de Almacén** - Crear, leer, actualizar, eliminar productos
- ✅ **Validación de Duplicados** - Números de almacén únicos
- ✅ **Cálculo de Precios** - Fórmulas automáticas
- ✅ **Estados de Stock** - Colores y lógica correcta
- ✅ **Búsqueda Avanzada** - Por múltiples campos
- ✅ **Autenticación** - Login y permisos
- ✅ **Responsive Design** - Móviles y desktop
- ✅ **APIs RESTful** - Todas las endpoints

### 🔍 **Herramientas de Calidad**
```bash
# Linting
npm run lint

# Type checking
npm run type-check

# Pruebas de API
curl -X GET http://localhost:3000/api/almacen
curl -X POST http://localhost:3000/api/almacen -H "Content-Type: application/json" -d '{"numero_almacen":"ALM-999","modelo_existente":"Producto Test"}'
```

---

## 📞 **Información de Contacto**

### 🏢 **ECCSA - Empresa de Control y Comunicación S.A.**

**📍 Dirección:**
Fray Luis de León 1713, Jardín Español
64820 Monterrey, Nuevo León, México

**📞 Contacto:**
- **Teléfono:** 81 8358 9075
- **Horario:** Lunes a Viernes, 8:00 AM - 6:30 PM

**💬 WhatsApp:**
- **Servicios:** +52 81 8280 3296 (<EMAIL>)
- **Compras:** +52 81 8704 3546 (<EMAIL>)

**🌐 Web:**
- **Sitio:** https://www.ecc-sa.com.mx
- **Email:** <EMAIL>

**🏆 Certificaciones:**
- ✅ **25 años de experiencia** en automatización industrial
- ✅ **SIEMENS Approved Partner** oficial
- ✅ **ISO 9001:2015** en gestión de calidad

---

## 📄 **Licencia y Derechos**

```
Copyright © 2024 ECCSA - Empresa de Control y Comunicación S.A.
Todos los derechos reservados.

Este software es propiedad exclusiva de ECCSA y está protegido por
las leyes de derechos de autor. No se permite la reproducción,
distribución o modificación sin autorización expresa por escrito.
```

---

<div align="center">

**🚀 Desarrollado con ❤️ para ECCSA**
*Automatización Industrial • Innovación • Excelencia*

[![Next.js](https://img.shields.io/badge/Powered%20by-Next.js-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/Built%20with-React-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/Written%20in-TypeScript-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)

</div>
