'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProductImage from '@/components/ProductImage';

interface CartItem {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  marcas?: string;
  Url_imagen?: string;
  cantidad: number;
  stock_disponible: number;
}

export default function Cart() {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState<CartItem[]>([]);
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();

  // Manejar hidratación del cliente
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Cargar carrito desde localStorage
  useEffect(() => {
    if (isClient) {
      loadCartFromStorage();
    }
  }, [isClient]);

  // Escuchar eventos para abrir el carrito y actualizar contenido
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleOpenCart = () => setIsOpen(true);
      const handleCartUpdate = () => {
        loadCartFromStorage();
      };

      window.addEventListener('openCart', handleOpenCart);
      window.addEventListener('cartUpdated', handleCartUpdate);

      return () => {
        window.removeEventListener('openCart', handleOpenCart);
        window.removeEventListener('cartUpdated', handleCartUpdate);
      };
    }
  }, [isClient]);

  const loadCartFromStorage = () => {
    const savedCart = localStorage.getItem('eccsa-cart');
    if (savedCart) {
      try {
        const cartItems = JSON.parse(savedCart);
        setItems(cartItems);
      } catch (error) {
        console.error('Error loading cart:', error);
        setItems([]);
      }
    }
  };

  const saveCartToStorage = (newItems: CartItem[]) => {
    localStorage.setItem('eccsa-cart', JSON.stringify(newItems));
    setItems(newItems);
    // Disparar evento para actualizar el contador en el navbar de manera inmediata
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  const removeItem = (id: number) => {
    const newItems = items.filter(item => item.id !== id);
    saveCartToStorage(newItems);
  };

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(id);
      return;
    }

    const newItems = items.map(item =>
      item.id === id
        ? { ...item, cantidad: Math.min(newQuantity, item.stock_disponible) }
        : item
    );
    saveCartToStorage(newItems);
  };

  const clearCart = () => {
    saveCartToStorage([]);
  };

  const closeCart = () => {
    setIsOpen(false);
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.cantidad, 0);
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => {
      const price = item.precio_ml || 0;
      return total + (price * item.cantidad);
    }, 0);
  };

  const getTotalWithTax = () => {
    const subtotal = getTotalPrice();
    return subtotal * 1.16; // 16% IVA
  };

  const formatPrice = (price: number) => {
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
    return formattedPrice;
  };

  const handleQuantityChange = (id: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(id);
    } else {
      updateQuantity(id, newQuantity);
    }
  };

  const handleViewCart = () => {
    if (items.length === 0) {
      alert('El carrito está vacío. Agrega productos antes de ver el carrito.');
      return;
    }
    setIsOpen(false);
    router.push('/cart');
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="cart-overlay" onClick={closeCart}></div>

      {/* Cart Panel */}
      <div className="cart-panel">
        <div className="cart-header">
          <h2>Carrito de Compras</h2>
          <button className="cart-close" onClick={closeCart}>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="cart-content">
          {items.length === 0 ? (
            <div className="cart-empty">
              <div className="cart-empty-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
              </div>
              <h3>Tu carrito está vacío</h3>
              <p>Agrega productos para comenzar tu cotización</p>
            </div>
          ) : (
            <>
              <div className="cart-items">
                {items.map((item) => (
                  <div key={item.id} className="cart-item">
                    <div className="cart-item-image">
                      {/* Usar img normal para URLs de base de datos, ProductImage para imágenes locales */}
                      {item.Url_imagen && (item.Url_imagen.startsWith('http') || item.Url_imagen.startsWith('https')) ? (
                        <img
                          src={item.Url_imagen}
                          alt={item.modelo_existente}
                          className="cart-item-img"
                          loading="lazy"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain'
                          }}
                          onError={(e) => {
                            // Si la imagen externa falla, usar imagen por defecto
                            (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                          }}
                        />
                      ) : (
                        <ProductImage
                          src={item.Url_imagen || "/images/logos/logo_pequeno.png"}
                          alt={item.modelo_existente}
                          width={60}
                          height={60}
                          className="cart-item-img"
                        />
                      )}
                    </div>

                    <div className="cart-item-details">
                      <h4 className="cart-item-name">{item.modelo_existente}</h4>
                      <p className="cart-item-brand">{item.marcas || 'Sin marca'}</p>
                      <p className="cart-item-code">ALM: {item.numero_almacen}</p>
                      {item.precio_ml && (
                        <p className="cart-item-price">{formatPrice(item.precio_ml)}</p>
                      )}
                    </div>

                    <div className="cart-item-controls">
                      <div className="quantity-controls">
                        <button
                          className="quantity-btn"
                          onClick={() => handleQuantityChange(item.id, item.cantidad - 1)}
                        >
                          -
                        </button>
                        <span className="quantity-display">{item.cantidad}</span>
                        <button
                          className="quantity-btn"
                          onClick={() => handleQuantityChange(item.id, item.cantidad + 1)}
                          disabled={item.cantidad >= item.stock_disponible}
                        >
                          +
                        </button>
                      </div>

                      <button
                        className="remove-item-btn"
                        onClick={() => removeItem(item.id)}
                        title="Eliminar del carrito"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                          <line x1="10" y1="11" x2="10" y2="17"></line>
                          <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                      </button>
                    </div>

                    {item.precio_ml && (
                      <div className="cart-item-total">
                        {formatPrice(item.precio_ml * item.cantidad)}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="cart-summary">
                <div className="cart-totals">
                  <div className="cart-total-row">
                    <span>Subtotal ({getTotalItems()} {getTotalItems() === 1 ? 'producto' : 'productos'}):</span>
                    <span className="cart-subtotal">{formatPrice(getTotalPrice())}</span>
                  </div>
                  <div className="cart-total-row">
                    <span>IVA (16%):</span>
                    <span className="cart-tax">{formatPrice(getTotalWithTax() - getTotalPrice())}</span>
                  </div>
                  <div className="cart-total-row cart-final-total">
                    <span>Total:</span>
                    <span className="cart-total-price">{formatPrice(getTotalWithTax())}</span>
                  </div>
                </div>

                <div className="cart-actions">
                  <button className="cart-clear-btn" onClick={clearCart}>
                    Vaciar Carrito
                  </button>
                  <button className="cart-checkout-btn" onClick={handleViewCart}>
                    Ver Carrito
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

    </>
  );
}
