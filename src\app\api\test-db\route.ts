import { NextResponse } from 'next/server';
import { executeQuery } from '@/eccsa_back/lib/db';

export async function GET() {
  try {
    console.log('Testing database connection...');
    
    // Test basic connection
    const testQuery = await executeQuery({
      query: 'SELECT 1 as test'
    });
    
    console.log('Basic connection test result:', testQuery);
    
    // Check if almacen table exists
    const tableCheck = await executeQuery({
      query: "SHOW TABLES LIKE 'almacen'"
    });
    
    console.log('Table check result:', tableCheck);
    
    // If table exists, get some data
    let sampleData = null;
    if (Array.isArray(tableCheck) && tableCheck.length > 0) {
      try {
        sampleData = await executeQuery({
          query: 'SELECT id, numero_almacen, modelo_existente, precio_venta FROM almacen LIMIT 3'
        });
        console.log('Sample data:', sampleData);
      } catch (dataError) {
        console.log('Error getting sample data:', dataError);
      }
    }
    
    return NextResponse.json({
      success: true,
      connection: testQuery,
      tableExists: Array.isArray(tableCheck) && tableCheck.length > 0,
      tableCheck,
      sampleData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
