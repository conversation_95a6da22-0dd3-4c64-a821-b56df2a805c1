'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { FaEdit, FaTrash, FaSync, FaPlus, FaDownload, FaSearch, FaFilter, FaUpload } from 'react-icons/fa';
import * as XLSX from 'xlsx';

interface PrecioItem {
  id: number;
  partida: number;
  cantidad: number;
  descripcion: string;
  modelo: string;
  precio_compra_dls?: number;
  precio_compra: number;
  precio_total_compra: number;
  factor: number;
  precio_venta_unitario: number;
  precio_venta_total: number;
  ganancia: number;
  marca: string;
  proveedor?: string;
  tiempo_entrega?: string;
  fecha_cotizacion: string;
  stock: string;
  folio_cotizacion?: string;
  serie_familia?: string;
  responsable?: string;
}

export default function PreciosPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [precios, setPrecios] = useState<PrecioItem[]>([]);
  const [filteredPrecios, setFilteredPrecios] = useState<PrecioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Estados para modales
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPrecio, setSelectedPrecio] = useState<PrecioItem | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Estados para notificaciones
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [modalTitle, setModalTitle] = useState('');

  // Estado para actualización masiva de precios
  const [updatingPrices, setUpdatingPrices] = useState(false);
  const [importingFile, setImportingFile] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  // Estado para precio del dólar
  const [precioDolar, setPrecioDolar] = useState(20);

  // Estados para marcas y proveedores
  const [marcas, setMarcas] = useState<string[]>([]);
  const [proveedores, setProveedores] = useState<string[]>([]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Load precios
  const loadPrecios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/precios');
      const data = await response.json();

      if (response.ok && data.success) {
        const preciosData = data.precios || [];
        setPrecios(preciosData);
        setFilteredPrecios(preciosData);

        // Extract unique brands
        const marcasArray = preciosData.map((p: PrecioItem) => p.marca).filter(Boolean) as string[];
        const uniqueBrands = Array.from(new Set(marcasArray));
        setBrands(uniqueBrands.sort());
        setMarcas(uniqueBrands.sort());

        // Extract unique proveedores
        const proveedoresArray = preciosData.map((p: PrecioItem) => p.proveedor).filter(Boolean) as string[];
        const uniqueProveedores = Array.from(new Set(proveedoresArray));
        setProveedores(uniqueProveedores.sort());
      }
    } catch (error) {
      console.error('Error loading precios:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPrecios();
  }, []);

  // Filter precios
  useEffect(() => {
    let filtered = precios.filter(precio => {
      const matchesSearch =
        precio.marca.toLowerCase().includes(searchTerm.toLowerCase()) ||
        precio.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
        precio.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (precio.proveedor && precio.proveedor.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesBrand = !selectedBrand || precio.marca === selectedBrand;

      return matchesSearch && matchesBrand;
    });

    setFilteredPrecios(filtered);
  }, [precios, searchTerm, selectedBrand]);

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  // Funciones para notificaciones
  const showSuccessNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowSuccessModal(true);
  };

  const showErrorNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowErrorModal(true);
  };

  // Función para recalcular todos los precios con el nuevo valor del dólar (solo en frontend)
  const updateAllPricesWithNewDollar = () => {
    if (precios.length === 0) {
      showErrorNotification(
        'Sin Precios',
        'No hay precios en la lista para actualizar.'
      );
      return;
    }

    try {
      setUpdatingPrices(true);

      // Simular un pequeño delay para mostrar el loading
      setTimeout(() => {
        // Recalcular todos los precios con el nuevo valor del dólar
        const updatedPrices = precios.map(precio => {
          const precio_compra_dls = parseFloat(String(precio.precio_compra_dls || '0'));
          const cantidad = parseInt(String(precio.cantidad || '1'));
          const factor = parseFloat(String(precio.factor || '1.20'));

          // Recalcular solo los valores que dependen del dólar
          const precio_compra = precio_compra_dls * precioDolar;
          const precio_total_compra = precio_compra * cantidad;
          const precio_venta_unitario = precio_compra * factor;
          const precio_venta_total = precio_venta_unitario * cantidad;
          const ganancia = precio_venta_total - precio_total_compra;

          return {
            ...precio, // Mantener todos los datos originales
            precio_compra,
            precio_total_compra,
            precio_venta_unitario,
            precio_venta_total,
            ganancia
          };
        });

        // Actualizar los estados locales con los nuevos valores calculados
        setPrecios(updatedPrices);
        setFilteredPrecios(updatedPrices.filter(precio => {
          const matchesSearch = searchTerm === '' ||
            precio.marca?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            precio.modelo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            precio.descripcion?.toLowerCase().includes(searchTerm.toLowerCase());

          const matchesBrand = selectedBrand === '' || precio.marca === selectedBrand;

          return matchesSearch && matchesBrand;
        }));

        setUpdatingPrices(false);

        showSuccessNotification(
          'Precios Recalculados',
          `Se recalcularon ${updatedPrices.length} precios con el nuevo valor del dólar ($${precioDolar.toFixed(2)} MXN). Los cambios son solo visuales y no se han guardado en la base de datos.`
        );
      }, 800); // Delay de 800ms para mostrar el loading

    } catch (error) {
      console.error('Error al recalcular precios:', error);
      setUpdatingPrices(false);
      showErrorNotification(
        'Error al Recalcular',
        'No se pudieron recalcular los precios. Por favor, intenta de nuevo.'
      );
    }
  };

  // Funciones para manejar acciones
  const handleAddPrecio = () => {
    setShowAddModal(true);
  };

  const handleAddSubmit = async (formData: any) => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/precios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          responsable: user?.name || user?.username || 'Usuario',
          precio_dolar: precioDolar
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await loadPrecios(); // Recargar la lista
        setShowAddModal(false);
        showSuccessNotification(
          'Precio Agregado',
          'El nuevo precio ha sido agregado exitosamente a la lista.'
        );
      } else {
        throw new Error(data.error || 'Error al agregar precio');
      }
    } catch (error) {
      console.error('Error al agregar precio:', error);
      showErrorNotification(
        'Error al Agregar',
        'No se pudo agregar el precio. Por favor, verifica los datos e intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const handleEditPrecio = (precio: PrecioItem) => {
    setSelectedPrecio(precio);
    setShowEditModal(true);
  };

  const handleDeletePrecio = (precio: PrecioItem) => {
    setSelectedPrecio(precio);
    setShowDeleteModal(true);
  };

  const handleEditSubmit = async (formData: any) => {
    if (!selectedPrecio) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/precios/${selectedPrecio.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          responsable: user?.name || user?.username || 'Usuario',
          precio_dolar: precioDolar
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await loadPrecios(); // Recargar la lista
        setShowEditModal(false);
        setSelectedPrecio(null);
        showSuccessNotification(
          'Precio Actualizado',
          'El precio ha sido actualizado exitosamente.'
        );
      } else {
        throw new Error(data.error || 'Error al actualizar precio');
      }
    } catch (error) {
      console.error('Error al actualizar precio:', error);
      showErrorNotification(
        'Error al Actualizar',
        'No se pudo actualizar el precio. Por favor, verifica los datos e intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!selectedPrecio) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/precios/${selectedPrecio.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await loadPrecios(); // Recargar la lista
        setShowDeleteModal(false);
        setSelectedPrecio(null);
        showSuccessNotification(
          'Precio Eliminado',
          'El precio ha sido eliminado exitosamente.'
        );
      } else {
        throw new Error(data.error || 'Error al eliminar precio');
      }
    } catch (error) {
      console.error('Error al eliminar precio:', error);
      showErrorNotification(
        'Error al Eliminar',
        'No se pudo eliminar el precio. Por favor, intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Función para exportar a Excel
  const exportToExcel = () => {
    if (selectedItems.length === 0) {
      showErrorNotification(
        'Sin Selección',
        'Selecciona al menos un precio para exportar.'
      );
      return;
    }

    try {
      // Filtrar solo los precios seleccionados
      const selectedPrices = precios.filter(precio => selectedItems.includes(precio.id));

      // Preparar los datos para Excel
      const excelData = selectedPrices.map((precio, index) => ({
        'No.': index + 1,
        'Marca': precio.marca || '',
        'Modelo': precio.modelo || '',
        'Descripción': precio.descripcion || '',
        'Proveedor': precio.proveedor || 'No especificado',
        'Fecha Cotización': precio.fecha_cotizacion
          ? new Date(precio.fecha_cotizacion).toLocaleDateString('es-MX', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
            })
          : 'No especificada',
        'Folio Cotización': precio.folio_cotizacion || '',
        'Serie/Familia': precio.serie_familia || '',
        'Cantidad': precio.cantidad || 1,
        'Precio USD': precio.precio_compra_dls ? `$${parseFloat(String(precio.precio_compra_dls)).toFixed(2)}` : '$0.00',
        'Precio Compra MXN': precio.precio_compra ? `$${parseFloat(String(precio.precio_compra)).toFixed(2)}` : '$0.00',
        'Precio Total Compra': precio.precio_total_compra ? `$${parseFloat(String(precio.precio_total_compra)).toFixed(2)}` : '$0.00',
        'Factor %': typeof precio.factor === 'number'
          ? `${((precio.factor - 1) * 100).toFixed(0)}%`
          : `${((parseFloat(precio.factor || '1.20') - 1) * 100).toFixed(0)}%`,
        'Precio Venta Unitario': precio.precio_venta_unitario ? `$${parseFloat(String(precio.precio_venta_unitario)).toFixed(2)}` : '$0.00',
        'Precio Venta Total': precio.precio_venta_total ? `$${parseFloat(String(precio.precio_venta_total)).toFixed(2)}` : '$0.00',
        'Ganancia': precio.ganancia ? `$${parseFloat(String(precio.ganancia)).toFixed(2)}` : '$0.00',
        'Tiempo Entrega': precio.tiempo_entrega || '',
        'Stock': precio.stock || '',
        'Responsable': precio.responsable || ''
      }));

      // Crear el libro de trabajo
      const workbook = XLSX.utils.book_new();

      // Crear la hoja de trabajo
      const worksheet = XLSX.utils.json_to_sheet(excelData);

      // Configurar el ancho de las columnas
      const columnWidths = [
        { wch: 5 },   // No.
        { wch: 15 },  // Marca
        { wch: 20 },  // Modelo
        { wch: 30 },  // Descripción
        { wch: 20 },  // Proveedor
        { wch: 15 },  // Fecha Cotización
        { wch: 15 },  // Folio Cotización
        { wch: 15 },  // Serie/Familia
        { wch: 10 },  // Cantidad
        { wch: 12 },  // Precio USD
        { wch: 15 },  // Precio Compra MXN
        { wch: 18 },  // Precio Total Compra
        { wch: 10 },  // Factor %
        { wch: 18 },  // Precio Venta Unitario
        { wch: 18 },  // Precio Venta Total
        { wch: 15 },  // Ganancia
        { wch: 15 },  // Tiempo Entrega
        { wch: 10 },  // Stock
        { wch: 15 }   // Responsable
      ];

      worksheet['!cols'] = columnWidths;

      // Aplicar estilo verde a la fila de encabezados (fila 1)
      const headerStyle = {
        fill: {
          fgColor: { rgb: "10B981" } // Verde similar al usado en la aplicación
        },
        font: {
          bold: true,
          color: { rgb: "FFFFFF" } // Texto blanco
        },
        alignment: {
          horizontal: "center",
          vertical: "center"
        },
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } }
        }
      };

      // Obtener las columnas (A, B, C, etc.)
      const headerColumns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S'];

      // Aplicar el estilo a cada celda del encabezado
      headerColumns.forEach(col => {
        const cellRef = col + '1'; // Fila 1 (encabezados)
        if (worksheet[cellRef]) {
          worksheet[cellRef].s = headerStyle;
        }
      });

      // Agregar la hoja al libro
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Precios');

      // Generar el nombre del archivo con fecha y hora
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
      const filename = `Precios_ECCSA_${timestamp}.xlsx`;

      // Descargar el archivo
      XLSX.writeFile(workbook, filename);

      // Mostrar notificación de éxito
      showSuccessNotification(
        'Exportación Exitosa',
        `Se exportaron ${selectedPrices.length} precios al archivo "${filename}".`
      );

    } catch (error) {
      console.error('Error al exportar a Excel:', error);
      showErrorNotification(
        'Error de Exportación',
        'No se pudo generar el archivo Excel. Por favor, intenta de nuevo.'
      );
    }
  };

  // Función para importar archivo Excel
  const handleImportExcel = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar que sea un archivo Excel
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    if (!validTypes.includes(file.type)) {
      showErrorNotification(
        'Archivo Inválido',
        'Por favor selecciona un archivo Excel (.xlsx o .xls).'
      );
      return;
    }

    setImportingFile(true);

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // Obtener la primera hoja
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convertir a JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

        // Validar que tenga datos
        if (jsonData.length < 2) {
          throw new Error('El archivo debe tener al menos una fila de encabezados y una fila de datos.');
        }

        // Procesar los datos
        await processImportedData(jsonData);

      } catch (error) {
        console.error('Error al procesar archivo:', error);
        showErrorNotification(
          'Error de Importación',
          error instanceof Error ? error.message : 'No se pudo procesar el archivo Excel.'
        );
      } finally {
        setImportingFile(false);
        // Limpiar el input
        event.target.value = '';
      }
    };

    reader.readAsArrayBuffer(file);
  };

  // Función para limpiar valores monetarios del Excel
  const cleanMoneyValue = (value: any): number => {
    if (!value) return 0;
    const stringValue = String(value).trim();
    // Remover símbolos de moneda, espacios, comas y convertir a número
    const cleanValue = stringValue
      .replace(/[$\s,]/g, '')  // Remover $, espacios y comas
      .replace(/[^\d.-]/g, '') // Mantener solo dígitos, punto y guión
      .trim();

    const numValue = parseFloat(cleanValue);
    return isNaN(numValue) ? 0 : numValue;
  };

  // Función para limpiar porcentajes
  const cleanPercentageValue = (value: any): number => {
    if (!value) return 1.20; // Factor por defecto
    const stringValue = String(value).trim();
    const cleanValue = stringValue.replace('%', '').trim();
    const numValue = parseFloat(cleanValue);

    if (isNaN(numValue)) return 1.20;

    // Si viene como porcentaje (ej: 25), convertir a decimal (ej: 1.25)
    return numValue > 10 ? (numValue / 100) + 1 : numValue;
  };

  // Función para procesar los datos importados
  const processImportedData = async (data: any[][]) => {
    try {
      const headers = data[0] as string[];
      const rows = data.slice(1);

      // Mapear las columnas esperadas (exactamente como están en tu Excel)
      const expectedColumns = {
        'Marca': ['marca', 'brand'],
        'Partida': ['partida'],
        'Modelo': ['modelo', 'model'],
        'Descripción': ['descripcion', 'description', 'descripción'],
        'Proveedor': ['proveedor', 'supplier', 'provider', 'provedor'],
        'Cantidad': ['cantidad', 'quantity', 'qty', 'cant'],
        'Precio USD': ['precio usd', 'precio_usd', 'price usd', 'precio dls', 'precio_compra_dls', 'p.com.uni (dls)', 'p.com.uni(dls)', 'p com uni dls', 'p.com.uni (dls)'],
        'Precio Compra Unitario': ['precio compra unitario', 'p.com.uni.', 'p com uni', 'precio_compra', 'p.com.uni.'],
        'Precio Compra Total': ['precio compra total', 'p. com total', 'p com total', 'precio_total_compra', 'p. com total'],
        'Factor %': ['factor', 'factor %', 'factor%', 'margen'],
        'Precio Venta Unitario': ['precio venta unitario', 'p. vta. unitario', 'p vta unitario', 'precio_venta_unitario', 'p. vta. unitario'],
        'Precio Venta Total': ['precio venta total', 'p. vta. total', 'p vta total', 'precio_venta_total', 'p. vta. total'],
        'Ganancia': ['ganancia', 'profit'],
        'Tiempo Entrega': ['tiempo entrega', 'tiempo_entrega', 'delivery time', 'tiempo de entrega'],
        'Stock': ['stock', 'inventory'],
        'Folio Cotización': ['folio cotización', 'folio_cotizacion', 'folio', 'folio cotizacion'],
        'Serie/Familia': ['serie/familia', 'serie_familia', 'serie', 'familia'],
        'Fecha Cotización': ['fecha cotización', 'fecha_cotizacion', 'fecha de cotizacion', 'fecha cotizacion'],
        'Responsable': ['responsable', 'responsible']
      };

      // Mapeo directo para tus columnas específicas (exactamente como aparecen en tu Excel)
      const directMapping: { [key: string]: string } = {
        'Marca': 'Marca',
        'PARTIDA': 'Partida',
        'Cant': 'Cantidad',
        'Descripción': 'Descripción',
        'Modelo': 'Modelo',
        'P.Com.Uni (Dls)': 'Precio USD',
        'P.Com.Uni.': 'Precio Compra Unitario',
        'P. Com Total': 'Precio Compra Total',
        'Factor': 'Factor %',
        'P. Vta. Unitario': 'Precio Venta Unitario',
        'P. Vta. Total': 'Precio Venta Total',
        'Ganancia': 'Ganancia',
        'Provedor': 'Proveedor',
        'Tiempo de Entrega': 'Tiempo Entrega',
        'FECHA DE COTIZACION': 'Fecha Cotización',
        'STOCK': 'Stock',
        'FOLIO COTIZACION': 'Folio Cotización',
        'SERIE/FAMILIA': 'Serie/Familia',
        'RESPONSABLE': 'Responsable'
      };

      // Encontrar índices de columnas usando mapeo directo primero
      const columnMap: { [key: string]: number } = {};

      // Primero intentar mapeo directo
      headers.forEach((header, index) => {
        const headerTrimmed = header.trim();
        if (directMapping[headerTrimmed]) {
          columnMap[directMapping[headerTrimmed]] = index;
        }
      });

      // Si no se encontraron con mapeo directo, usar el mapeo flexible
      if (Object.keys(columnMap).length === 0) {
        Object.entries(expectedColumns).forEach(([key, variations]) => {
          const index = headers.findIndex(header => {
            const headerLower = header.toLowerCase().trim();
            return variations.some(variation => {
              const variationLower = variation.toLowerCase().trim();
              return headerLower === variationLower || headerLower.includes(variationLower);
            });
          });
          if (index !== -1) {
            columnMap[key] = index;
          }
        });
      }

      // Debug: mostrar qué columnas se encontraron
      console.log('Headers encontrados:', headers);
      console.log('Mapeo de columnas:', columnMap);
      console.log('Columnas requeridas encontradas:', {
        'Marca': columnMap['Marca'] !== undefined,
        'Modelo': columnMap['Modelo'] !== undefined,
        'Descripción': columnMap['Descripción'] !== undefined
      });

      // Validar columnas requeridas
      const requiredColumns = ['Marca', 'Modelo', 'Descripción'];
      const missingColumns = requiredColumns.filter(col => !(col in columnMap));

      if (missingColumns.length > 0) {
        throw new Error(`Faltan las columnas requeridas: ${missingColumns.join(', ')}`);
      }

      // Procesar cada fila
      const processedData = [];
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];

        // Saltar filas vacías
        if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
          continue;
        }

        try {
          // Obtener valores básicos
          const marca = String(row[columnMap['Marca']] || '').trim();
          const modelo = String(row[columnMap['Modelo']] || '').trim();
          const descripcion = String(row[columnMap['Descripción']] || '').trim();
          const proveedor = String(row[columnMap['Proveedor']] || '').trim();
          const cantidad = parseInt(String(row[columnMap['Cantidad']] || '1')) || 1;
          const partida = parseInt(String(row[columnMap['Partida']] || (i + 1))) || (i + 1);

          // Obtener precios usando las funciones de limpieza
          const precio_compra_dls = cleanMoneyValue(row[columnMap['Precio USD']]);

          // Verificar si ya vienen calculados en el Excel
          const precio_compra_excel = cleanMoneyValue(row[columnMap['Precio Compra Unitario']]);
          const precio_total_compra_excel = cleanMoneyValue(row[columnMap['Precio Compra Total']]);
          const precio_venta_unitario_excel = cleanMoneyValue(row[columnMap['Precio Venta Unitario']]);
          const precio_venta_total_excel = cleanMoneyValue(row[columnMap['Precio Venta Total']]);
          const ganancia_excel = cleanMoneyValue(row[columnMap['Ganancia']]);

          // Factor usando función de limpieza
          const factorDecimal = cleanPercentageValue(row[columnMap['Factor %']]);

          // Usar valores del Excel si están disponibles, sino calcular
          const precio_compra = precio_compra_excel > 0 ? precio_compra_excel : (precio_compra_dls * precioDolar);
          const precio_total_compra = precio_total_compra_excel > 0 ? precio_total_compra_excel : (precio_compra * cantidad);
          const precio_venta_unitario = precio_venta_unitario_excel > 0 ? precio_venta_unitario_excel : (precio_compra * factorDecimal);
          const precio_venta_total = precio_venta_total_excel > 0 ? precio_venta_total_excel : (precio_venta_unitario * cantidad);
          const ganancia = ganancia_excel > 0 ? ganancia_excel : (precio_venta_total - precio_total_compra);

          // Obtener fecha de cotización
          let fecha_cotizacion = new Date().toISOString().split('T')[0];
          if (columnMap['Fecha Cotización'] !== undefined && row[columnMap['Fecha Cotización']]) {
            const fechaExcel = row[columnMap['Fecha Cotización']];
            if (fechaExcel) {
              try {
                // Intentar parsear la fecha del Excel
                const parsedDate = new Date(fechaExcel);
                if (!isNaN(parsedDate.getTime())) {
                  fecha_cotizacion = parsedDate.toISOString().split('T')[0];
                }
              } catch (e) {
                // Si no se puede parsear, usar fecha actual
              }
            }
          }

          // Obtener responsable
          let responsable = user?.name || user?.username || 'Usuario';
          if (columnMap['Responsable'] !== undefined && row[columnMap['Responsable']]) {
            const responsableExcel = String(row[columnMap['Responsable']] || '').trim();
            if (responsableExcel) {
              responsable = responsableExcel;
            }
          }

          const precioData = {
            marca,
            modelo,
            descripcion,
            proveedor,
            cantidad,
            partida,
            precio_compra_dls,
            precio_compra,
            precio_total_compra,
            factor: factorDecimal,
            precio_venta_unitario,
            precio_venta_total,
            ganancia,
            tiempo_entrega: String(row[columnMap['Tiempo Entrega']] || '').trim(),
            stock: String(row[columnMap['Stock']] || '').trim(),
            folio_cotizacion: String(row[columnMap['Folio Cotización']] || '').trim(),
            serie_familia: String(row[columnMap['Serie/Familia']] || '').trim(),
            fecha_cotizacion,
            responsable
          };

          // Validar datos mínimos
          if (!precioData.marca || !precioData.modelo || !precioData.descripcion) {
            errorCount++;
            continue;
          }

          processedData.push(precioData);
          successCount++;

        } catch (error) {
          console.error(`Error en fila ${i + 2}:`, error);
          errorCount++;
        }
      }

      if (processedData.length === 0) {
        throw new Error('No se encontraron datos válidos para importar.');
      }

      // Enviar datos al servidor
      await saveImportedData(processedData, successCount, errorCount);

    } catch (error) {
      throw error;
    }
  };

  // Función para guardar los datos importados
  const saveImportedData = async (data: any[], successCount: number, errorCount: number) => {
    try {
      const response = await fetch('/api/precios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'bulk_import',
          data: data
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al guardar los datos');
      }

      // Recargar la lista de precios
      await loadPrecios();

      showSuccessNotification(
        'Importación Exitosa',
        `Se importaron ${successCount} precios correctamente.${errorCount > 0 ? ` ${errorCount} filas tuvieron errores y se omitieron.` : ''}`
      );

    } catch (error) {
      console.error('Error al guardar datos importados:', error);
      throw new Error('No se pudieron guardar los datos en la base de datos.');
    }
  };

  const handleSelectItem = (id: number) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="admin-page-container">
      {/* Header */}
      <div className="admin-page-header">
        <div className="admin-header-content">
          <div className="admin-header-text">
            <h1 className="admin-page-title">Lista de Precios</h1>
            <p className="admin-page-subtitle">
              Gestión de precios y cotizaciones de productos ECCSA
            </p>
          </div>
          <div className="admin-header-actions">
            <button
              className="modern-admin-button modern-admin-button-secondary"
              onClick={loadPrecios}
              disabled={loading}
            >
              <FaSync className="w-5 h-5 mr-2" />
              {loading ? 'Actualizando...' : 'Actualizar'}
            </button>

            {/* Botón de Importar Excel */}
            <div style={{ position: 'relative' }}>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleImportExcel}
                disabled={importingFile}
                style={{
                  position: 'absolute',
                  opacity: 0,
                  width: '100%',
                  height: '100%',
                  cursor: importingFile ? 'not-allowed' : 'pointer'
                }}
                id="excel-import"
              />
              <button
                className={`modern-admin-button ${importingFile ? 'modern-admin-button-disabled' : 'modern-admin-button-success'}`}
                disabled={importingFile}
                style={{
                  background: importingFile
                    ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                    : 'linear-gradient(135deg, #059669, #10b981)',
                  pointerEvents: 'none' // El input maneja los clicks
                }}
              >
                {importingFile ? (
                  <>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid rgba(255,255,255,0.3)',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      marginRight: '0.5rem'
                    }}></div>
                    Importando...
                  </>
                ) : (
                  <>
                    <FaUpload className="w-5 h-5 mr-2" />
                    Importar Excel
                  </>
                )}
              </button>
            </div>

            <button
              className="modern-admin-button modern-admin-button-primary"
              onClick={handleAddPrecio}
            >
              <FaPlus className="w-5 h-5 mr-2" />
              Agregar Precio
            </button>
          </div>
        </div>
      </div>

      {/* Configuración del Dólar */}
      <div className="admin-filters-card" style={{ marginBottom: '1rem' }}>
        <div className="admin-filters-content">
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '1rem',
            padding: '0.5rem 0'
          }}>
            {/* Primera fila: Título y configuración del dólar */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              flexWrap: 'wrap'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                background: 'linear-gradient(135deg, #059669, #10b981)',
                color: 'white',
                padding: '0.75rem 1rem',
                borderRadius: '12px',
                fontWeight: '600'
              }}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                <span>Valor del Dólar (USD):</span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <span style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  color: '#059669'
                }}>
                  $
                </span>
                <input
                  type="number"
                  value={precioDolar}
                  onChange={(e) => setPrecioDolar(parseFloat(e.target.value) || 20)}
                  min="1"
                  max="100"
                  step="0.01"
                  style={{
                    width: '120px',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    textAlign: 'center',
                    background: '#f8fafc',
                    color: '#1e293b'
                  }}
                />
                <span style={{
                  fontSize: '1rem',
                  color: '#64748b',
                  fontWeight: '500'
                }}>
                  MXN
                </span>
              </div>
            </div>

            {/* Segunda fila: Botón y descripción */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              flexWrap: 'wrap'
            }}>
              <button
                onClick={updateAllPricesWithNewDollar}
                disabled={updatingPrices || precios.length === 0}
                style={{
                  padding: '0.875rem 1.5rem',
                  background: updatingPrices
                    ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                    : precios.length === 0
                    ? 'linear-gradient(135deg, #9ca3af, #6b7280)'
                    : 'linear-gradient(135deg, #f59e0b, #d97706)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: updatingPrices || precios.length === 0 ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  whiteSpace: 'nowrap',
                  boxShadow: precios.length > 0 && !updatingPrices ? '0 2px 8px rgba(245,158,11,0.3)' : 'none'
                }}
                onMouseEnter={(e) => {
                  if (!updatingPrices && precios.length > 0) {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(245,158,11,0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = precios.length > 0 && !updatingPrices ? '0 2px 8px rgba(245,158,11,0.3)' : 'none';
                }}
              >
                {updatingPrices ? (
                  <>
                    <div style={{
                      width: '18px',
                      height: '18px',
                      border: '2px solid rgba(255,255,255,0.3)',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    Actualizando Precios...
                  </>
                ) : precios.length === 0 ? (
                  <>
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="15" y1="9" x2="9" y2="15"></line>
                      <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    Sin Precios para Actualizar
                  </>
                ) : (
                  <>
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M1 4v6h6"></path>
                      <path d="M23 20v-6h-6"></path>
                      <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                    </svg>
                    Recalcular Precios MXN ({precios.length})
                  </>
                )}
              </button>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontStyle: 'italic',
                flex: 1,
                minWidth: '250px'
              }}>
                {precios.length === 0
                  ? 'Agrega algunos precios primero para poder usar la función de recálculo.'
                  : `Recalculará los "Precio Compra MXN" de los ${precios.length} precios usando el nuevo valor del dólar ($${precioDolar.toFixed(2)} MXN). Solo actualiza la vista, no guarda en base de datos.`
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filtros y Búsqueda */}
      <div className="admin-filters-card">
        <div className="admin-filters-content">
          <div className="admin-filters-grid">
            <div className="admin-filter-item">
              <div className="admin-search-container">
                <FaSearch className="admin-search-icon" />
                <input
                  type="text"
                  placeholder="Buscar por marca, modelo, descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="admin-search-input"
                />
              </div>
            </div>
            <div className="admin-filter-item">
              <div className="admin-select-container">
                <FaFilter className="admin-select-icon" />
                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="admin-select-input"
                >
                  <option value="">Todas las marcas</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="admin-filter-item">
              <button
                onClick={exportToExcel}
                disabled={selectedItems.length === 0}
                className={`modern-admin-button ${selectedItems.length === 0 ? 'modern-admin-button-disabled' : 'modern-admin-button-secondary'}`}
              >
                <FaDownload className="w-5 h-5 mr-2" />
                Exportar Excel ({selectedItems.length})
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabla de Precios */}
      <div className="admin-table-card">
        <div className="admin-table-header">
          <h2 className="admin-table-title">
            Precios de Cotización ({filteredPrecios.length})
          </h2>
        </div>
        <div className="admin-table-content">
          {loading ? (
            <div className="admin-loading-container">
              <div className="admin-loading-spinner"></div>
              <p className="admin-loading-text">Cargando precios...</p>
            </div>
          ) : filteredPrecios.length === 0 ? (
            <div className="admin-empty-state">
              <div className="admin-empty-icon">
                <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="admin-empty-title">No se encontraron precios</h3>
              <p className="admin-empty-text">No hay precios que coincidan con los filtros aplicados.</p>
            </div>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>
                      <input
                        type="checkbox"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems(filteredPrecios.map(p => p.id));
                          } else {
                            setSelectedItems([]);
                          }
                        }}
                        checked={selectedItems.length === filteredPrecios.length && filteredPrecios.length > 0}
                      />
                    </th>
                    <th>Marca</th>
                    <th>Modelo</th>
                    <th>Descripción</th>
                    <th>Proveedor</th>
                    <th>Fecha Cotización</th>
                    <th>Cantidad</th>
                    <th>Precio Compra</th>
                    <th>Factor %</th>
                    <th>Precio Venta</th>
                    <th>Ganancias</th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPrecios.map((precio) => (
                    <tr key={precio.id}>
                      <td>
                        <input
                          type="checkbox"
                          checked={selectedItems.includes(precio.id)}
                          onChange={() => handleSelectItem(precio.id)}
                        />
                      </td>
                      <td className="admin-table-brand">{precio.marca}</td>
                      <td className="admin-table-model">{precio.modelo}</td>
                      <td className="admin-table-description" title={precio.descripcion}>
                        {precio.descripcion.length > 30
                          ? `${precio.descripcion.substring(0, 30)}...`
                          : precio.descripcion}
                      </td>
                      <td className="admin-table-supplier">
                        {precio.proveedor || 'No especificado'}
                      </td>
                      <td className="admin-table-date">
                        {precio.fecha_cotizacion
                          ? new Date(precio.fecha_cotizacion).toLocaleDateString('es-MX', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit'
                            })
                          : 'No especificada'}
                      </td>
                      <td className="admin-table-quantity">{precio.cantidad}</td>
                      <td className="admin-table-price">{formatPrice(precio.precio_total_compra)}</td>
                      <td className="admin-table-factor">
                        <span style={{
                          background: 'linear-gradient(135deg, #10b981, #059669)',
                          color: 'white',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '6px',
                          fontSize: '0.875rem',
                          fontWeight: '600'
                        }}>
                          {typeof precio.factor === 'number'
                            ? `${((precio.factor - 1) * 100).toFixed(0)}%`
                            : `${((parseFloat(precio.factor || '1.20') - 1) * 100).toFixed(0)}%`}
                        </span>
                      </td>
                      <td className="admin-table-price">{formatPrice(precio.precio_venta_total)}</td>
                      <td className="admin-table-profit">
                        <span style={{
                          color: typeof precio.ganancia === 'number' && precio.ganancia > 0
                            ? '#059669'
                            : typeof precio.ganancia === 'string' && parseFloat(precio.ganancia) > 0
                            ? '#059669'
                            : '#dc2626',
                          fontWeight: '600'
                        }}>
                          {formatPrice(precio.ganancia)}
                        </span>
                      </td>
                      <td>
                        <div className="admin-table-actions">
                          <button
                            onClick={() => handleEditPrecio(precio)}
                            className="admin-action-btn edit"
                            title="Editar precio"
                          >
                            <FaEdit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeletePrecio(precio)}
                            className="admin-action-btn danger"
                            title="Eliminar precio"
                          >
                            <FaTrash className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Agregar Precio */}
      {showAddModal && (
        <AddPrecioModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSubmit={handleAddSubmit}
          loading={actionLoading}
          precioDolar={precioDolar}
          marcas={marcas}
          proveedores={proveedores}
        />
      )}

      {/* Modal de Notificación de Éxito */}
      {showSuccessModal && (
        <NotificationModal
          type="success"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowSuccessModal(false)}
        />
      )}

      {/* Modal de Notificación de Error */}
      {showErrorModal && (
        <NotificationModal
          type="error"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}

      {/* Modal de Editar Precio */}
      {showEditModal && selectedPrecio && (
        <EditPrecioModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedPrecio(null);
          }}
          onSubmit={handleEditSubmit}
          loading={actionLoading}
          precioDolar={precioDolar}
          marcas={marcas}
          proveedores={proveedores}
          precio={selectedPrecio}
        />
      )}

      {/* Modal de Eliminar Precio */}
      {showDeleteModal && selectedPrecio && (
        <DeletePrecioModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedPrecio(null);
          }}
          onConfirm={confirmDelete}
          loading={actionLoading}
          precio={selectedPrecio}
        />
      )}
    </div>
  );
}

// Modal de Notificación
interface NotificationModalProps {
  type: 'success' | 'error';
  title: string;
  message: string;
  onClose: () => void;
}

function NotificationModal({ type, title, message, onClose }: NotificationModalProps) {
  const isSuccess = type === 'success';

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '2rem',
        maxWidth: '400px',
        width: '90%',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        textAlign: 'center'
      }}>
        <div style={{
          width: '64px',
          height: '64px',
          borderRadius: '50%',
          backgroundColor: isSuccess ? '#10b981' : '#ef4444',
          margin: '0 auto 1.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {isSuccess ? (
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
          ) : (
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
          )}
        </div>

        <h3 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#1e293b',
          margin: '0 0 1rem 0'
        }}>
          {title}
        </h3>

        <p style={{
          color: '#64748b',
          margin: '0 0 2rem 0',
          lineHeight: '1.6'
        }}>
          {message}
        </p>

        <button
          onClick={onClose}
          style={{
            backgroundColor: isSuccess ? '#10b981' : '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '0.75rem 2rem',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'none';
          }}
        >
          Entendido
        </button>
      </div>
    </div>
  );
}

// Modal para agregar precio
interface AddPrecioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  loading: boolean;
  precioDolar: number;
  marcas: string[];
  proveedores: string[];
}

function AddPrecioModal({ isOpen, onClose, onSubmit, loading, precioDolar, marcas, proveedores }: AddPrecioModalProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    marca: '',
    partida: 1,
    cantidad: 1,
    descripcion: '',
    modelo: '',
    precio_compra_dls: 0,
    precio_compra: 0,
    precio_total_compra: 0,
    factor: 1.20,
    precio_venta_unitario: 0,
    precio_venta_total: 0,
    ganancia: 0,
    proveedor: '',
    tiempo_entrega: '',
    fecha_cotizacion: new Date().toISOString().split('T')[0],
    stock: '',
    folio_cotizacion: '',
    serie_familia: '',
    responsable: ''
  });

  // Estados para controlar si se está escribiendo texto libre
  const [isCustomMarca, setIsCustomMarca] = useState(false);
  const [isCustomProveedor, setIsCustomProveedor] = useState(false);

  // Auto-completar responsable cuando se abre el modal
  useEffect(() => {
    if (isOpen) {
      setFormData(prev => ({
        ...prev,
        responsable: user?.name || user?.username || 'Usuario'
      }));
    }
  }, [isOpen, user]);

  // Calcular precios automáticamente
  const calculatePrices = (data: any) => {
    const precio_compra = data.precio_compra_dls * precioDolar;
    const precio_total_compra = precio_compra * data.cantidad;
    const precio_venta_unitario = precio_compra * data.factor;
    const precio_venta_total = precio_venta_unitario * data.cantidad;
    const ganancia = precio_venta_total - precio_total_compra;

    return {
      ...data,
      precio_compra,
      precio_total_compra,
      precio_venta_unitario,
      precio_venta_total,
      ganancia
    };
  };

  const handleInputChange = (field: string, value: any) => {
    const newData = { ...formData, [field]: value };
    const calculatedData = calculatePrices(newData);
    setFormData(calculatedData);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.marca || !formData.modelo || !formData.descripcion) {
      alert('Por favor completa todos los campos obligatorios');
      return;
    }
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #10b981, #059669)',
          color: 'white',
          padding: '1.5rem 2rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#ffffff',
                margin: '0 0 0.5rem 0'
              }}>
                Agregar Nuevo Precio
              </h2>
              <p style={{
                fontSize: '1rem',
                color: 'rgba(255, 255, 255, 0.8)',
                margin: 0
              }}>
                Precio del dólar: ${precioDolar.toFixed(2)} MXN
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: loading ? '#9ca3af' : 'rgba(255, 255, 255, 0.8)',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '8px',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
            }}
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div style={{
          padding: '2rem',
          maxHeight: 'calc(90vh - 200px)',
          overflowY: 'auto'
        }}>
          <form onSubmit={handleSubmit}>
            {/* Información básica */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Marca *
                </label>
                <div style={{ position: 'relative' }}>
                  {isCustomMarca ? (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <input
                        type="text"
                        value={formData.marca}
                        onChange={(e) => handleInputChange('marca', e.target.value)}
                        placeholder="Escribir nueva marca"
                        required
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #10b981',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomMarca(false);
                          handleInputChange('marca', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          backgroundColor: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Lista
                      </button>
                    </div>
                  ) : (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <select
                        value={formData.marca}
                        onChange={(e) => handleInputChange('marca', e.target.value)}
                        required
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      >
                        <option value="">Seleccionar marca</option>
                        {marcas.map(marca => (
                          <option key={marca} value={marca}>{marca}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomMarca(true);
                          handleInputChange('marca', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #10b981',
                          borderRadius: '8px',
                          backgroundColor: '#10b981',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Nueva
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Modelo *
                </label>
                <input
                  type="text"
                  value={formData.modelo}
                  onChange={(e) => handleInputChange('modelo', e.target.value)}
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Cantidad *
                </label>
                <input
                  type="number"
                  value={formData.cantidad}
                  onChange={(e) => handleInputChange('cantidad', Number(e.target.value))}
                  min="1"
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Precio en USD *
                </label>
                <input
                  type="number"
                  value={formData.precio_compra_dls}
                  onChange={(e) => handleInputChange('precio_compra_dls', Number(e.target.value))}
                  step="0.01"
                  min="0"
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #f59e0b',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : '#fef3c7',
                    color: '#92400e',
                    fontWeight: '600',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Descripción */}
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Descripción *
              </label>
              <textarea
                value={formData.descripcion}
                onChange={(e) => handleInputChange('descripcion', e.target.value)}
                required
                disabled={loading}
                rows={3}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* Campos adicionales de información */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Fecha de Cotización *
                </label>
                <input
                  type="date"
                  value={formData.fecha_cotizacion}
                  onChange={(e) => handleInputChange('fecha_cotizacion', e.target.value)}
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Folio de Cotización
                </label>
                <input
                  type="text"
                  value={formData.folio_cotizacion}
                  onChange={(e) => handleInputChange('folio_cotizacion', e.target.value)}
                  placeholder="ej: COT-2024-001"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Serie/Familia
                </label>
                <input
                  type="text"
                  value={formData.serie_familia}
                  onChange={(e) => handleInputChange('serie_familia', e.target.value)}
                  placeholder="ej: S7-1200, ATV320"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Responsable
                </label>
                <input
                  type="text"
                  value={formData.responsable}
                  onChange={(e) => handleInputChange('responsable', e.target.value)}
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: '#f0f9ff',
                    color: '#0369a1',
                    fontWeight: '600',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Precios calculados */}
            <div style={{
              background: '#f8fafc',
              border: '2px solid #e2e8f0',
              borderRadius: '12px',
              padding: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <h3 style={{
                fontSize: '1.1rem',
                fontWeight: '600',
                color: '#374151',
                margin: '0 0 1rem 0'
              }}>
                Precios Calculados
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Precio Compra MXN</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#059669' }}>
                    ${formData.precio_compra.toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Total Compra</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#059669' }}>
                    ${formData.precio_total_compra.toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Precio Venta Unitario</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#0ea5e9' }}>
                    ${formData.precio_venta_unitario.toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Total Venta</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#0ea5e9' }}>
                    ${formData.precio_venta_total.toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Ganancia</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#dc2626' }}>
                    ${formData.ganancia.toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Factor</label>
                  <input
                    type="number"
                    value={formData.factor}
                    onChange={(e) => handleInputChange('factor', Number(e.target.value))}
                    step="0.01"
                    min="1"
                    disabled={loading}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '1rem',
                      backgroundColor: loading ? '#f9fafb' : 'white'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Campos adicionales */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1.5rem',
              marginBottom: '2rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Proveedor
                </label>
                <div style={{ position: 'relative' }}>
                  {isCustomProveedor ? (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <input
                        type="text"
                        value={formData.proveedor}
                        onChange={(e) => handleInputChange('proveedor', e.target.value)}
                        placeholder="Escribir nuevo proveedor"
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #10b981',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomProveedor(false);
                          handleInputChange('proveedor', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          backgroundColor: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Lista
                      </button>
                    </div>
                  ) : (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <select
                        value={formData.proveedor}
                        onChange={(e) => handleInputChange('proveedor', e.target.value)}
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      >
                        <option value="">Seleccionar proveedor</option>
                        {proveedores.map(proveedor => (
                          <option key={proveedor} value={proveedor}>{proveedor}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomProveedor(true);
                          handleInputChange('proveedor', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #10b981',
                          borderRadius: '8px',
                          backgroundColor: '#10b981',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Nuevo
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Stock
                </label>
                <select
                  value={formData.stock}
                  onChange={(e) => handleInputChange('stock', e.target.value)}
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Seleccionar estado</option>
                  <option value="En Stock">En Stock</option>
                  <option value="Bajo Stock">Bajo Stock</option>
                  <option value="Sin Stock">Sin Stock</option>
                  <option value="Por Pedir">Por Pedir</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Tiempo de Entrega
                </label>
                <input
                  type="text"
                  value={formData.tiempo_entrega}
                  onChange={(e) => handleInputChange('tiempo_entrega', e.target.value)}
                  placeholder="ej: 2-3 semanas"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Botones */}
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'flex-end',
              paddingTop: '1rem',
              borderTop: '1px solid #e2e8f0'
            }}>
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  backgroundColor: 'white',
                  color: '#64748b',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease'
                }}
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={loading}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  background: loading ? '#9ca3af' : 'linear-gradient(135deg, #10b981, #059669)',
                  color: 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease'
                }}
              >
                {loading ? 'Guardando...' : 'Agregar Precio'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Modal para editar precio
interface EditPrecioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  loading: boolean;
  precioDolar: number;
  marcas: string[];
  proveedores: string[];
  precio: PrecioItem;
}

function EditPrecioModal({ isOpen, onClose, onSubmit, loading, precioDolar, marcas, proveedores, precio }: EditPrecioModalProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    marca: '',
    partida: 1,
    cantidad: 1,
    descripcion: '',
    modelo: '',
    precio_compra_dls: 0,
    precio_compra: 0,
    precio_total_compra: 0,
    factor: 1.20,
    precio_venta_unitario: 0,
    precio_venta_total: 0,
    ganancia: 0,
    proveedor: '',
    tiempo_entrega: '',
    fecha_cotizacion: new Date().toISOString().split('T')[0],
    stock: '',
    folio_cotizacion: '',
    serie_familia: '',
    responsable: ''
  });

  // Estados para controlar si se está escribiendo texto libre
  const [isCustomMarca, setIsCustomMarca] = useState(false);
  const [isCustomProveedor, setIsCustomProveedor] = useState(false);

  // Cargar datos del precio cuando se abre el modal
  useEffect(() => {
    if (isOpen && precio) {
      setFormData({
        marca: precio.marca || '',
        partida: parseInt(String(precio.partida || '1')),
        cantidad: parseInt(String(precio.cantidad || '1')),
        descripcion: precio.descripcion || '',
        modelo: precio.modelo || '',
        precio_compra_dls: parseFloat(String(precio.precio_compra_dls || '0')),
        precio_compra: parseFloat(String(precio.precio_compra || '0')),
        precio_total_compra: parseFloat(String(precio.precio_total_compra || '0')),
        factor: parseFloat(String(precio.factor || '1.20')),
        precio_venta_unitario: parseFloat(String(precio.precio_venta_unitario || '0')),
        precio_venta_total: parseFloat(String(precio.precio_venta_total || '0')),
        ganancia: parseFloat(String(precio.ganancia || '0')),
        proveedor: precio.proveedor || '',
        tiempo_entrega: precio.tiempo_entrega || '',
        fecha_cotizacion: precio.fecha_cotizacion || new Date().toISOString().split('T')[0],
        stock: precio.stock || '',
        folio_cotizacion: precio.folio_cotizacion || '',
        serie_familia: precio.serie_familia || '',
        responsable: precio.responsable || user?.name || user?.username || 'Usuario'
      });

      // Verificar si marca y proveedor están en las listas existentes
      setIsCustomMarca(!marcas.includes(precio.marca || ''));
      setIsCustomProveedor(!proveedores.includes(precio.proveedor || ''));
    }
  }, [isOpen, precio, user, marcas, proveedores]);

  // Calcular precios automáticamente
  const calculatePrices = (data: any) => {
    const precio_compra_dls = parseFloat(data.precio_compra_dls || '0');
    const cantidad = parseInt(data.cantidad || '1');
    const factor = parseFloat(data.factor || '1.20');

    const precio_compra = precio_compra_dls * precioDolar;
    const precio_total_compra = precio_compra * cantidad;
    const precio_venta_unitario = precio_compra * factor;
    const precio_venta_total = precio_venta_unitario * cantidad;
    const ganancia = precio_venta_total - precio_total_compra;

    return {
      ...data,
      precio_compra,
      precio_total_compra,
      precio_venta_unitario,
      precio_venta_total,
      ganancia
    };
  };

  const handleInputChange = (field: string, value: any) => {
    const newData = { ...formData, [field]: value };
    const calculatedData = calculatePrices(newData);
    setFormData(calculatedData);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.marca || !formData.modelo || !formData.descripcion) {
      alert('Por favor completa todos los campos obligatorios');
      return;
    }
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #0ea5e9, #0284c7)',
          color: 'white',
          padding: '1.5rem 2rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#ffffff',
                margin: '0 0 0.5rem 0'
              }}>
                Editar Precio
              </h2>
              <p style={{
                fontSize: '1rem',
                color: 'rgba(255, 255, 255, 0.8)',
                margin: 0
              }}>
                {precio.marca} - {precio.modelo} | Precio del dólar: ${precioDolar.toFixed(2)} MXN
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: loading ? '#9ca3af' : 'rgba(255, 255, 255, 0.8)',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '8px',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
            }}
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div style={{
          padding: '2rem',
          maxHeight: 'calc(90vh - 200px)',
          overflowY: 'auto'
        }}>
          <form onSubmit={handleSubmit}>
            {/* Información básica */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Marca *
                </label>
                <div style={{ position: 'relative' }}>
                  {isCustomMarca ? (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <input
                        type="text"
                        value={formData.marca}
                        onChange={(e) => handleInputChange('marca', e.target.value)}
                        placeholder="Escribir nueva marca"
                        required
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #0ea5e9',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomMarca(false);
                          handleInputChange('marca', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          backgroundColor: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Lista
                      </button>
                    </div>
                  ) : (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <select
                        value={formData.marca}
                        onChange={(e) => handleInputChange('marca', e.target.value)}
                        required
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      >
                        <option value="">Seleccionar marca</option>
                        {marcas.map(marca => (
                          <option key={marca} value={marca}>{marca}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomMarca(true);
                          handleInputChange('marca', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #0ea5e9',
                          borderRadius: '8px',
                          backgroundColor: '#0ea5e9',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Nueva
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Modelo *
                </label>
                <input
                  type="text"
                  value={formData.modelo}
                  onChange={(e) => handleInputChange('modelo', e.target.value)}
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Cantidad *
                </label>
                <input
                  type="number"
                  value={formData.cantidad}
                  onChange={(e) => handleInputChange('cantidad', Number(e.target.value))}
                  min="1"
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Precio en USD *
                </label>
                <input
                  type="number"
                  value={formData.precio_compra_dls}
                  onChange={(e) => handleInputChange('precio_compra_dls', Number(e.target.value))}
                  step="0.01"
                  min="0"
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #f59e0b',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : '#fef3c7',
                    color: '#92400e',
                    fontWeight: '600',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Descripción */}
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Descripción *
              </label>
              <textarea
                value={formData.descripcion}
                onChange={(e) => handleInputChange('descripcion', e.target.value)}
                required
                disabled={loading}
                rows={3}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* Campos adicionales de información */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Fecha de Cotización *
                </label>
                <input
                  type="date"
                  value={formData.fecha_cotizacion}
                  onChange={(e) => handleInputChange('fecha_cotizacion', e.target.value)}
                  required
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Folio de Cotización
                </label>
                <input
                  type="text"
                  value={formData.folio_cotizacion}
                  onChange={(e) => handleInputChange('folio_cotizacion', e.target.value)}
                  placeholder="ej: COT-2024-001"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Serie/Familia
                </label>
                <input
                  type="text"
                  value={formData.serie_familia}
                  onChange={(e) => handleInputChange('serie_familia', e.target.value)}
                  placeholder="ej: S7-1200, ATV320"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Responsable
                </label>
                <input
                  type="text"
                  value={formData.responsable}
                  onChange={(e) => handleInputChange('responsable', e.target.value)}
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: '#e0f2fe',
                    color: '#0369a1',
                    fontWeight: '600',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Precios calculados */}
            <div style={{
              background: '#f0f9ff',
              border: '2px solid #0ea5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              marginBottom: '1.5rem'
            }}>
              <h3 style={{
                fontSize: '1.1rem',
                fontWeight: '600',
                color: '#374151',
                margin: '0 0 1rem 0'
              }}>
                Precios Calculados
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem'
              }}>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Precio Compra MXN</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#059669' }}>
                    ${typeof formData.precio_compra === 'number'
                      ? formData.precio_compra.toFixed(2)
                      : parseFloat(formData.precio_compra || '0').toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Total Compra</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#059669' }}>
                    ${typeof formData.precio_total_compra === 'number'
                      ? formData.precio_total_compra.toFixed(2)
                      : parseFloat(formData.precio_total_compra || '0').toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Precio Venta Unitario</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#0ea5e9' }}>
                    ${typeof formData.precio_venta_unitario === 'number'
                      ? formData.precio_venta_unitario.toFixed(2)
                      : parseFloat(formData.precio_venta_unitario || '0').toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Total Venta</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#0ea5e9' }}>
                    ${typeof formData.precio_venta_total === 'number'
                      ? formData.precio_venta_total.toFixed(2)
                      : parseFloat(formData.precio_venta_total || '0').toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Ganancia</label>
                  <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#dc2626' }}>
                    ${typeof formData.ganancia === 'number'
                      ? formData.ganancia.toFixed(2)
                      : parseFloat(formData.ganancia || '0').toFixed(2)}
                  </div>
                </div>
                <div>
                  <label style={{ fontSize: '0.875rem', color: '#64748b' }}>Factor</label>
                  <input
                    type="number"
                    value={formData.factor}
                    onChange={(e) => handleInputChange('factor', Number(e.target.value))}
                    step="0.01"
                    min="1"
                    disabled={loading}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '1rem',
                      backgroundColor: loading ? '#f9fafb' : 'white'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Campos adicionales */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1.5rem',
              marginBottom: '2rem'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Proveedor
                </label>
                <div style={{ position: 'relative' }}>
                  {isCustomProveedor ? (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <input
                        type="text"
                        value={formData.proveedor}
                        onChange={(e) => handleInputChange('proveedor', e.target.value)}
                        placeholder="Escribir nuevo proveedor"
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #0ea5e9',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomProveedor(false);
                          handleInputChange('proveedor', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          backgroundColor: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Lista
                      </button>
                    </div>
                  ) : (
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <select
                        value={formData.proveedor}
                        onChange={(e) => handleInputChange('proveedor', e.target.value)}
                        disabled={loading}
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #e2e8f0',
                          borderRadius: '8px',
                          fontSize: '1rem',
                          backgroundColor: loading ? '#f9fafb' : 'white',
                          boxSizing: 'border-box'
                        }}
                      >
                        <option value="">Seleccionar proveedor</option>
                        {proveedores.map(proveedor => (
                          <option key={proveedor} value={proveedor}>{proveedor}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => {
                          setIsCustomProveedor(true);
                          handleInputChange('proveedor', '');
                        }}
                        disabled={loading}
                        style={{
                          padding: '0.75rem',
                          border: '2px solid #0ea5e9',
                          borderRadius: '8px',
                          backgroundColor: '#0ea5e9',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '0.875rem'
                        }}
                      >
                        Nuevo
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Stock
                </label>
                <select
                  value={formData.stock}
                  onChange={(e) => handleInputChange('stock', e.target.value)}
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Seleccionar estado</option>
                  <option value="En Stock">En Stock</option>
                  <option value="Bajo Stock">Bajo Stock</option>
                  <option value="Sin Stock">Sin Stock</option>
                  <option value="Por Pedir">Por Pedir</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: '600',
                  color: '#374151',
                  fontSize: '0.875rem'
                }}>
                  Tiempo de Entrega
                </label>
                <input
                  type="text"
                  value={formData.tiempo_entrega}
                  onChange={(e) => handleInputChange('tiempo_entrega', e.target.value)}
                  placeholder="ej: 2-3 semanas"
                  disabled={loading}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    backgroundColor: loading ? '#f9fafb' : 'white',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>

            {/* Botones */}
            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'flex-end',
              paddingTop: '1rem',
              borderTop: '1px solid #e2e8f0'
            }}>
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  backgroundColor: 'white',
                  color: '#64748b',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease'
                }}
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={loading}
                style={{
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  background: loading ? '#9ca3af' : 'linear-gradient(135deg, #0ea5e9, #0284c7)',
                  color: 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.3s ease'
                }}
              >
                {loading ? 'Actualizando...' : 'Actualizar Precio'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Modal para eliminar precio
interface DeletePrecioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
  precio: PrecioItem;
}

function DeletePrecioModal({ isOpen, onClose, onConfirm, loading, precio }: DeletePrecioModalProps) {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        width: '100%',
        maxWidth: '500px',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #ef4444, #dc2626)',
          color: 'white',
          padding: '1.5rem 2rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                <line x1="10" y1="11" x2="10" y2="17"></line>
                <line x1="14" y1="11" x2="14" y2="17"></line>
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#ffffff',
                margin: '0 0 0.5rem 0'
              }}>
                Eliminar Precio
              </h2>
              <p style={{
                fontSize: '1rem',
                color: 'rgba(255, 255, 255, 0.8)',
                margin: 0
              }}>
                Esta acción no se puede deshacer
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: loading ? '#9ca3af' : 'rgba(255, 255, 255, 0.8)',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '8px',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
            }}
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: '2rem' }}>
          <div style={{
            textAlign: 'center',
            marginBottom: '2rem'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              backgroundColor: '#fee2e2',
              margin: '0 auto 1.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#dc2626" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
            </div>

            <h3 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#1e293b',
              margin: '0 0 1rem 0'
            }}>
              ¿Estás seguro?
            </h3>

            <p style={{
              color: '#64748b',
              margin: '0 0 1.5rem 0',
              lineHeight: '1.6'
            }}>
              Estás a punto de eliminar el siguiente precio:
            </p>

            <div style={{
              background: '#f8fafc',
              border: '2px solid #e2e8f0',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'left',
              marginBottom: '1.5rem'
            }}>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Marca:</strong> {precio.marca}
              </div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Modelo:</strong> {precio.modelo}
              </div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Descripción:</strong> {precio.descripcion.length > 50
                  ? `${precio.descripcion.substring(0, 50)}...`
                  : precio.descripcion}
              </div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Precio Total:</strong> ${typeof precio.precio_venta_total === 'number'
                  ? precio.precio_venta_total.toFixed(2)
                  : parseFloat(precio.precio_venta_total || '0').toFixed(2)}
              </div>
              <div>
                <strong>Stock:</strong> {precio.stock}
              </div>
            </div>

            <p style={{
              color: '#dc2626',
              fontSize: '0.875rem',
              fontWeight: '600',
              margin: '0 0 2rem 0'
            }}>
              Esta acción eliminará permanentemente este precio de la base de datos.
            </p>
          </div>

          {/* Botones */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'center'
          }}>
            <button
              onClick={onClose}
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: '2px solid #e2e8f0',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                backgroundColor: 'white',
                color: '#64748b',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              Cancelar
            </button>
            <button
              onClick={onConfirm}
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                background: loading ? '#9ca3af' : 'linear-gradient(135deg, #ef4444, #dc2626)',
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              {loading ? 'Eliminando...' : 'Sí, Eliminar'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
