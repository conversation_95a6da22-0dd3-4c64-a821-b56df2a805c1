'use client';

import { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';
import { isValidUrl } from '@/utils';
import { IMAGES } from '@/constants';

interface ImageWithFallbackProps extends Omit<ImageProps, 'src'> {
  src: string;
  fallbackSrc?: string;
  containerClassName?: string;
}

/**
 * Image component with fallback support
 * Automatically handles image loading errors by showing a fallback image
 */
export default function ImageWithFallback({
  src,
  fallbackSrc = IMAGES.LOGO.SMALL,
  alt,
  containerClassName,
  ...rest
}: ImageWithFallbackProps) {
  const [imgSrc, setImgSrc] = useState<string>(src);

  // Validate URL on mount and when src changes
  useEffect(() => {
    // Always set the image source initially
    setImgSrc(src);

    // Only validate external URLs, not local paths
    if (src && !src.startsWith('/') && !isValidUrl(src)) {
      console.warn(`Invalid image URL: ${src}, but will try to load it anyway`);
    }
  }, [src]);

  // Handle image load error
  const handleError = () => {
    if (imgSrc !== fallbackSrc) {
      console.warn(`Image failed to load: ${imgSrc}, using fallback`);
      setImgSrc(fallbackSrc);
    }
  };

  return (
    <div
      className={`image-container ${containerClassName || ''}`}
      style={{ margin: 0, padding: 0, border: 'none', overflow: 'hidden' }}
    >
      <Image
        {...rest}
        src={imgSrc}
        alt={alt || 'Image'}
        onError={handleError}
        style={{
          ...rest.style,
          objectFit: 'cover',
          width: '100%',
          height: '100%',
          display: 'block',
          margin: 0,
          padding: 0,
          border: 'none'
        }}
      />
    </div>
  );
}
