'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

interface PrecioItem {
  id: number;
  marca: string;
  partida: number;
  cantidad: number;
  descripcion: string;
  modelo: string;
  precio_compra_dls?: number;
  precio_compra: number;
  precio_total_compra: number;
  factor: number;
  precio_venta_unitario: number;
  precio_venta_total: number;
  ganancia: number;
  proveedor?: string;
  tiempo_entrega?: string;
  fecha_cotizacion: string;
  stock: string;
  folio_cotizacion?: string;
  serie_familia?: string;
  responsable?: string;
}

export default function ListaPreciosPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Estados principales
  const [precios, setPrecios] = useState<PrecioItem[]>([]);
  const [filteredPrecios, setFilteredPrecios] = useState<PrecioItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Estados para filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMarca, setSelectedMarca] = useState('');
  const [selectedProveedor, setSelectedProveedor] = useState('');
  const [selectedStock, setSelectedStock] = useState('');

  // Estados para paginación
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Estado para precio del dólar
  const [precioDolar, setPrecioDolar] = useState(20);
  
  // Estados para modales
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPrecio, setSelectedPrecio] = useState<PrecioItem | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  
  // Estados para notificaciones
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [modalTitle, setModalTitle] = useState('');

  // Listas para filtros
  const [marcas, setMarcas] = useState<string[]>([]);
  const [proveedores, setProveedores] = useState<string[]>([]);

  // Verificar autenticación
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/eccsa/admin/login');
    }
  }, [user, authLoading, router]);

  // Cargar datos
  useEffect(() => {
    if (user) {
      loadPrecios();
    }
  }, [user]);

  // Aplicar filtros
  useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedMarca, selectedProveedor, selectedStock, precios]);

  const loadPrecios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/precios');
      const data = await response.json();

      if (response.ok && data.success) {
        const preciosData = data.precios || [];
        setPrecios(preciosData);
        setFilteredPrecios(preciosData);

        // Extraer marcas y proveedores únicos
        const marcasArray = preciosData.map((p: PrecioItem) => p.marca).filter(Boolean) as string[];
        const proveedoresArray = preciosData.map((p: PrecioItem) => p.proveedor).filter(Boolean) as string[];
        
        setMarcas(Array.from(new Set(marcasArray)).sort());
        setProveedores(Array.from(new Set(proveedoresArray)).sort());
      }
    } catch (error) {
      console.error('Error loading precios:', error);
      showErrorNotification('Error de Carga', 'No se pudieron cargar los precios');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = precios;

    // Filtro de búsqueda
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(precio =>
        precio.modelo?.toLowerCase().includes(term) ||
        precio.descripcion?.toLowerCase().includes(term) ||
        precio.marca?.toLowerCase().includes(term) ||
        precio.proveedor?.toLowerCase().includes(term) ||
        precio.folio_cotizacion?.toLowerCase().includes(term)
      );
    }

    // Filtro por marca
    if (selectedMarca) {
      filtered = filtered.filter(precio => precio.marca === selectedMarca);
    }

    // Filtro por proveedor
    if (selectedProveedor) {
      filtered = filtered.filter(precio => precio.proveedor === selectedProveedor);
    }

    // Filtro por stock
    if (selectedStock) {
      filtered = filtered.filter(precio => precio.stock === selectedStock);
    }

    setFilteredPrecios(filtered);
  };

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('es-MX');
  };

  // Funciones para modales de notificación
  const showSuccessNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowSuccessModal(true);
  };

  const showErrorNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowErrorModal(true);
  };

  // Funciones para manejar acciones
  const handleAddPrecio = () => {
    setShowAddModal(true);
  };

  const handleAddSubmit = async (formData: any) => {
    try {
      setActionLoading(true);
      const response = await fetch('/api/precios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          responsable: user?.name || user?.username || 'Usuario'
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await loadPrecios(); // Recargar la lista
        setShowAddModal(false);
        showSuccessNotification(
          'Precio Agregado',
          'El nuevo precio ha sido agregado exitosamente a la lista.'
        );
      } else {
        throw new Error(data.error || 'Error al agregar precio');
      }
    } catch (error) {
      console.error('Error al agregar precio:', error);
      showErrorNotification(
        'Error al Agregar',
        'No se pudo agregar el precio. Por favor, verifica los datos e intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const handleEditPrecio = (precio: PrecioItem) => {
    setSelectedPrecio(precio);
    setShowEditModal(true);
  };

  const handleDeletePrecio = (precio: PrecioItem) => {
    setSelectedPrecio(precio);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedPrecio) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/precios/${selectedPrecio.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setPrecios(prev => prev.filter(p => p.id !== selectedPrecio.id));
        setShowDeleteModal(false);
        setSelectedPrecio(null);
        showSuccessNotification(
          'Precio Eliminado',
          'El registro de precio ha sido eliminado exitosamente.'
        );
      } else {
        throw new Error(data.error || 'Error al eliminar precio');
      }
    } catch (error) {
      console.error('Error al eliminar precio:', error);
      showErrorNotification(
        'Error al Eliminar',
        'No se pudo eliminar el precio. Por favor, intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedMarca('');
    setSelectedProveedor('');
    setSelectedStock('');
    setCurrentPage(1);
  };

  // Funciones de paginación
  const totalPages = Math.ceil(filteredPrecios.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPrecios = filteredPrecios.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  if (authLoading || loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem',
        color: '#64748b'
      }}>
        Cargando lista de precios...
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '16px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
          marginBottom: '2rem',
          display: 'flex',
          alignItems: 'center',
          gap: '1.5rem'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #0056a6, #003366)',
            padding: '1rem',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <line x1="12" y1="1" x2="12" y2="23"></line>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </div>
          <div style={{ flex: 1 }}>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              color: '#1e293b',
              margin: '0 0 0.5rem 0'
            }}>
              Lista de Precios
            </h1>
            <p style={{ color: '#64748b', margin: '0', fontSize: '1rem' }}>
              Gestión de precios, márgenes y cotizaciones
            </p>
          </div>
          <button
            onClick={handleAddPrecio}
            style={{
              padding: '0.875rem 1.5rem',
              background: 'linear-gradient(135deg, #10b981, #059669)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(16,185,129,0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Agregar Precio
          </button>
        </div>

        {/* Filtros */}
        <div style={{
          backgroundColor: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          marginBottom: '2rem'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            alignItems: 'end'
          }}>
            {/* Búsqueda */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Buscar
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Modelo, descripción, marca..."
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'border-color 0.3s ease',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#0056a6'}
                onBlur={(e) => e.target.style.borderColor = '#e2e8f0'}
              />
            </div>

            {/* Filtro por Marca */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Marca
              </label>
              <select
                value={selectedMarca}
                onChange={(e) => setSelectedMarca(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: 'white',
                  cursor: 'pointer',
                  boxSizing: 'border-box'
                }}
              >
                <option value="">Todas las marcas</option>
                {marcas.map(marca => (
                  <option key={marca} value={marca}>{marca}</option>
                ))}
              </select>
            </div>

            {/* Filtro por Proveedor */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Proveedor
              </label>
              <select
                value={selectedProveedor}
                onChange={(e) => setSelectedProveedor(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: 'white',
                  cursor: 'pointer',
                  boxSizing: 'border-box'
                }}
              >
                <option value="">Todos los proveedores</option>
                {proveedores.map(proveedor => (
                  <option key={proveedor} value={proveedor}>{proveedor}</option>
                ))}
              </select>
            </div>

            {/* Precio del Dólar */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio USD (MXN)
              </label>
              <input
                type="number"
                value={precioDolar}
                onChange={(e) => setPrecioDolar(Number(e.target.value))}
                placeholder="20.00"
                step="0.01"
                min="1"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'border-color 0.3s ease',
                  boxSizing: 'border-box',
                  backgroundColor: '#fef3c7',
                  color: '#92400e',
                  fontWeight: '600'
                }}
                onFocus={(e) => e.target.style.borderColor = '#f59e0b'}
                onBlur={(e) => e.target.style.borderColor = '#e2e8f0'}
              />
            </div>

            {/* Botón limpiar filtros */}
            <div>
              <button
                onClick={clearFilters}
                style={{
                  padding: '0.75rem 1rem',
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  width: '100%'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                  e.currentTarget.style.borderColor = '#cbd5e1';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                  e.currentTarget.style.borderColor = '#e2e8f0';
                }}
              >
                Limpiar Filtros
              </button>
            </div>
          </div>
        </div>

        {/* Estadísticas */}
        <div style={{
          backgroundColor: 'white',
          padding: '1.5rem',
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          marginBottom: '2rem'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem'
          }}>
            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: '#f0f9ff',
              borderRadius: '8px',
              border: '1px solid #bae6fd'
            }}>
              <div style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#0369a1',
                marginBottom: '0.5rem'
              }}>
                {filteredPrecios.length}
              </div>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600'
              }}>
                Productos en Lista
              </div>
            </div>

            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: '#f0fdf4',
              borderRadius: '8px',
              border: '1px solid #bbf7d0'
            }}>
              <div style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#059669',
                marginBottom: '0.5rem'
              }}>
                {marcas.length}
              </div>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600'
              }}>
                Marcas Disponibles
              </div>
            </div>

            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: '#fefce8',
              borderRadius: '8px',
              border: '1px solid #fde047'
            }}>
              <div style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#ca8a04',
                marginBottom: '0.5rem'
              }}>
                {proveedores.length}
              </div>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600'
              }}>
                Proveedores Activos
              </div>
            </div>

            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: '#fef2f2',
              borderRadius: '8px',
              border: '1px solid #fecaca'
            }}>
              <div style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#dc2626',
                marginBottom: '0.5rem'
              }}>
                {formatPrice(filteredPrecios.reduce((sum, p) => sum + (p.ganancia || 0), 0))}
              </div>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600'
              }}>
                Ganancia Total Estimada
              </div>
            </div>
          </div>
        </div>

        {/* Tabla de Precios */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          overflow: 'hidden'
        }}>
          <div style={{
            padding: '1.5rem',
            borderBottom: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#1e293b',
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
              Lista de Precios ({filteredPrecios.length})
            </h2>

            <div style={{ display: 'flex', gap: '0.75rem' }}>
              <button
                onClick={() => console.log('Export Excel')}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#f0fdf4',
                  color: '#166534',
                  border: '1px solid #bbf7d0',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#dcfce7';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f0fdf4';
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                </svg>
                Exportar Excel
              </button>

              <button
                onClick={() => console.log('Export PDF')}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#fef2f2',
                  color: '#dc2626',
                  border: '1px solid #fecaca',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#fee2e2';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#fef2f2';
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                </svg>
                Exportar PDF
              </button>
            </div>
          </div>

          {currentPrecios.length === 0 ? (
            <div style={{
              padding: '3rem',
              textAlign: 'center',
              color: '#64748b'
            }}>
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" style={{ margin: '0 auto 1rem auto', opacity: 0.5 }}>
                <circle cx="11" cy="11" r="8"></circle>
                <path d="M21 21l-4.35-4.35"></path>
              </svg>
              <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1.2rem' }}>
                No se encontraron precios
              </h3>
              <p style={{ margin: 0 }}>
                {searchTerm || selectedMarca || selectedProveedor || selectedStock
                  ? 'Intenta ajustar los filtros de búsqueda'
                  : 'Agrega el primer precio a la lista'
                }
              </p>
            </div>
          ) : (
            <div style={{ overflowX: 'auto' }}>
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: '0.875rem'
              }}>
                <thead>
                  <tr style={{ backgroundColor: '#f8fafc' }}>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 0 1 0 2.828l-7 7a2 2 0 0 1-2.828 0l-7-7A1.994 1.994 0 0 1 2 12V7a5 5 0 0 1 5-5z"></path>
                        </svg>
                        Marca
                      </div>
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Modelo
                      </div>
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'left', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      Descripción
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'center', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4m-6 0V9a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2m-6 0h6"></path>
                        </svg>
                        Cantidad
                      </div>
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'right', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'flex-end' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <line x1="12" y1="1" x2="12" y2="23"></line>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                        Precio Compra
                      </div>
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'center', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      Factor
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'right', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      Precio Venta
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'right', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      Ganancia
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'center', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      Stock
                    </th>
                    <th style={{ padding: '1rem 0.75rem', textAlign: 'center', fontWeight: '600', color: '#374151', borderBottom: '1px solid #e2e8f0' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="3"></circle>
                          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        Acciones
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {currentPrecios.map((precio, index) => (
                    <tr key={precio.id} style={{
                      backgroundColor: index % 2 === 0 ? 'white' : '#f8fafc',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#f1f5f9';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = index % 2 === 0 ? 'white' : '#f8fafc';
                    }}
                    >
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0' }}>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          backgroundColor: '#f0f9ff',
                          color: '#0369a1',
                          borderRadius: '20px',
                          fontSize: '0.75rem',
                          fontWeight: '600'
                        }}>
                          {precio.marca}
                        </span>
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', fontWeight: '600', color: '#1e293b' }}>
                        {precio.modelo}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', color: '#64748b', maxWidth: '200px' }}>
                        {precio.descripcion && precio.descripcion.length > 50
                          ? `${precio.descripcion.substring(0, 50)}...`
                          : precio.descripcion}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'center', fontWeight: '600' }}>
                        {precio.cantidad}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'right', fontWeight: '600', color: '#dc2626' }}>
                        {formatPrice(precio.precio_total_compra)}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'center' }}>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          backgroundColor: '#fef3c7',
                          color: '#92400e',
                          borderRadius: '4px',
                          fontSize: '0.75rem',
                          fontWeight: '600'
                        }}>
                          {precio.factor}x
                        </span>
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'right', fontWeight: '600', color: '#059669' }}>
                        {formatPrice(precio.precio_venta_total)}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'right', fontWeight: '600', color: '#0369a1' }}>
                        {formatPrice(precio.ganancia)}
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0', textAlign: 'center' }}>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          backgroundColor: '#f0fdf4',
                          color: '#166534',
                          borderRadius: '20px',
                          fontSize: '0.75rem',
                          fontWeight: '600'
                        }}>
                          {precio.stock}
                        </span>
                      </td>
                      <td style={{ padding: '1rem 0.75rem', borderBottom: '1px solid #e2e8f0' }}>
                        <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>
                          <button
                            onClick={() => handleEditPrecio(precio)}
                            style={{
                              backgroundColor: '#f0fdf4',
                              color: '#166534',
                              border: '1px solid #bbf7d0',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              borderRadius: '6px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#dcfce7';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#f0fdf4';
                            }}
                          >
                            <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Editar
                          </button>
                          <button
                            onClick={() => handleDeletePrecio(precio)}
                            style={{
                              backgroundColor: '#fef2f2',
                              color: '#dc2626',
                              border: '1px solid #fecaca',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              borderRadius: '6px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = '#fee2e2';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = '#fef2f2';
                            }}
                          >
                            <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Eliminar
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Paginación */}
        {filteredPrecios.length > itemsPerPage && (
          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '12px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
            marginTop: '1rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div style={{
              fontSize: '0.875rem',
              color: '#64748b'
            }}>
              Mostrando {startIndex + 1} - {Math.min(endIndex, filteredPrecios.length)} de {filteredPrecios.length} registros
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                style={{
                  padding: '0.5rem 0.75rem',
                  backgroundColor: currentPage === 1 ? '#f9fafb' : '#f1f5f9',
                  color: currentPage === 1 ? '#9ca3af' : '#374151',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== 1) {
                    e.currentTarget.style.backgroundColor = '#e2e8f0';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentPage !== 1) {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                  }
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
                Anterior
              </button>

              {/* Números de página */}
              <div style={{ display: 'flex', gap: '0.25rem' }}>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => goToPage(page)}
                    style={{
                      padding: '0.5rem 0.75rem',
                      backgroundColor: page === currentPage ? '#0056a6' : '#f1f5f9',
                      color: page === currentPage ? 'white' : '#374151',
                      border: '1px solid #e2e8f0',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      minWidth: '2.5rem'
                    }}
                    onMouseEnter={(e) => {
                      if (page !== currentPage) {
                        e.currentTarget.style.backgroundColor = '#e2e8f0';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (page !== currentPage) {
                        e.currentTarget.style.backgroundColor = '#f1f5f9';
                      }
                    }}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                style={{
                  padding: '0.5rem 0.75rem',
                  backgroundColor: currentPage === totalPages ? '#f9fafb' : '#f1f5f9',
                  color: currentPage === totalPages ? '#9ca3af' : '#374151',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
                onMouseEnter={(e) => {
                  if (currentPage !== totalPages) {
                    e.currentTarget.style.backgroundColor = '#e2e8f0';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentPage !== totalPages) {
                    e.currentTarget.style.backgroundColor = '#f1f5f9';
                  }
                }}
              >
                Siguiente
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="9,18 15,12 9,6"></polyline>
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Modales de Notificación */}
        {showSuccessModal && (
          <NotificationModal
            type="success"
            title={modalTitle}
            message={modalMessage}
            onClose={() => setShowSuccessModal(false)}
          />
        )}

        {showErrorModal && (
          <NotificationModal
            type="error"
            title={modalTitle}
            message={modalMessage}
            onClose={() => setShowErrorModal(false)}
          />
        )}

        {/* Modal de Agregar Precio */}
        {showAddModal && (
          <AddPrecioModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
            onSubmit={handleAddSubmit}
            loading={actionLoading}
            precioDolar={precioDolar}
            marcas={marcas}
            proveedores={proveedores}
          />
        )}

        {/* Modal de Confirmación de Eliminación */}
        {showDeleteModal && selectedPrecio && (
          <DeleteConfirmModal
            precio={selectedPrecio}
            onClose={() => {
              setShowDeleteModal(false);
              setSelectedPrecio(null);
            }}
            onConfirm={confirmDelete}
            loading={actionLoading}
          />
        )}
      </div>
    </div>
  );
}

// Modal de Notificación
interface NotificationModalProps {
  type: 'success' | 'error';
  title: string;
  message: string;
  onClose: () => void;
}

function NotificationModal({ type, title, message, onClose }: NotificationModalProps) {
  const isSuccess = type === 'success';

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '500px',
        width: '100%',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative',
        textAlign: 'center'
      }}>
        {/* Icono */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: isSuccess
              ? 'linear-gradient(135deg, #10b981, #059669)'
              : 'linear-gradient(135deg, #ef4444, #dc2626)',
            padding: '1.5rem',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {isSuccess ? (
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <path d="M20 6L9 17l-5-5" />
              </svg>
            ) : (
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <circle cx="12" cy="12" r="10" />
                <line x1="15" y1="9" x2="9" y2="15" />
                <line x1="9" y1="9" x2="15" y2="15" />
              </svg>
            )}
          </div>
        </div>

        {/* Contenido */}
        <div style={{ marginBottom: '2rem' }}>
          <h2 style={{
            fontSize: '1.8rem',
            fontWeight: 'bold',
            color: '#1e293b',
            margin: '0 0 1rem 0'
          }}>
            {title}
          </h2>
          <p style={{
            fontSize: '1.1rem',
            color: '#64748b',
            lineHeight: '1.6',
            margin: 0
          }}>
            {message}
          </p>
        </div>

        {/* Botón */}
        <button
          onClick={onClose}
          style={{
            padding: '0.875rem 2rem',
            background: isSuccess
              ? 'linear-gradient(135deg, #10b981, #059669)'
              : 'linear-gradient(135deg, #ef4444, #dc2626)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            margin: '0 auto'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = isSuccess
              ? '0 4px 12px rgba(16,185,129,0.4)'
              : '0 4px 12px rgba(239,68,68,0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'none';
          }}
        >
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M20 6L9 17l-5-5" />
          </svg>
          Entendido
        </button>
      </div>
    </div>
  );
}

// Modal de confirmación para eliminar
interface DeleteConfirmModalProps {
  precio: PrecioItem;
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
}

function DeleteConfirmModal({ precio, onClose, onConfirm, loading }: DeleteConfirmModalProps) {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '500px',
        width: '100%',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #dc2626, #b91c1c)',
            padding: '1.5rem',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </div>
        </div>

        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <h2 style={{
            fontSize: '1.8rem',
            fontWeight: 'bold',
            color: '#1e293b',
            margin: '0 0 1rem 0'
          }}>
            Confirmar Eliminación
          </h2>
          <p style={{
            fontSize: '1.1rem',
            color: '#64748b',
            lineHeight: '1.6',
            margin: '0 0 1.5rem 0'
          }}>
            ¿Estás seguro de que deseas eliminar este precio?
          </p>

          {/* Información del precio */}
          <div style={{
            backgroundColor: '#fef2f2',
            padding: '1.5rem',
            borderRadius: '12px',
            border: '1px solid #fecaca',
            textAlign: 'left'
          }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1rem',
              fontSize: '0.9rem'
            }}>
              <div>
                <label style={{ color: '#64748b', fontWeight: '600' }}>Marca:</label>
                <p style={{ color: '#1e293b', margin: '0.25rem 0 0 0', fontWeight: '600' }}>
                  {precio.marca}
                </p>
              </div>
              <div>
                <label style={{ color: '#64748b', fontWeight: '600' }}>Modelo:</label>
                <p style={{ color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                  {precio.modelo}
                </p>
              </div>
              <div>
                <label style={{ color: '#64748b', fontWeight: '600' }}>Precio Venta:</label>
                <p style={{ color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                  {new Intl.NumberFormat('es-MX', {
                    style: 'currency',
                    currency: 'MXN'
                  }).format(precio.precio_venta_total)}
                </p>
              </div>
              <div>
                <label style={{ color: '#64748b', fontWeight: '600' }}>Stock:</label>
                <p style={{ color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                  {precio.stock}
                </p>
              </div>
            </div>
          </div>

          <p style={{
            fontSize: '0.9rem',
            color: '#dc2626',
            fontWeight: '600',
            margin: '1.5rem 0 0 0'
          }}>
            Esta acción no se puede deshacer.
          </p>
        </div>

        {/* Botones */}
        <div style={{
          display: 'flex',
          gap: '1rem',
          paddingTop: '1rem',
          borderTop: '1px solid #e2e8f0'
        }}>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              flex: 1,
              padding: '0.875rem',
              backgroundColor: loading ? '#f9fafb' : '#f1f5f9',
              color: loading ? '#9ca3af' : '#64748b',
              border: '2px solid #e2e8f0',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#e2e8f0';
                e.currentTarget.style.borderColor = '#cbd5e1';
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#f1f5f9';
                e.currentTarget.style.borderColor = '#e2e8f0';
              }
            }}
          >
            Cancelar
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            style={{
              flex: 1,
              padding: '0.875rem',
              background: loading
                ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                : 'linear-gradient(135deg, #dc2626, #b91c1c)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(220,38,38,0.4)';
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }
            }}
          >
            {loading ? (
              <>
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid rgba(255,255,255,0.3)',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                Eliminando...
              </>
            ) : (
              <>
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Sí, Eliminar
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

// Modal para agregar precio
interface AddPrecioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  loading: boolean;
  precioDolar: number;
  marcas: string[];
  proveedores: string[];
}

function AddPrecioModal({ isOpen, onClose, onSubmit, loading, precioDolar, marcas, proveedores }: AddPrecioModalProps) {
  const [formData, setFormData] = useState({
    marca: '',
    partida: 1,
    cantidad: 1,
    descripcion: '',
    modelo: '',
    precio_compra_dls: 0,
    precio_compra: 0,
    precio_total_compra: 0,
    factor: 1.20,
    precio_venta_unitario: 0,
    precio_venta_total: 0,
    ganancia: 0,
    proveedor: '',
    tiempo_entrega: '',
    fecha_cotizacion: new Date().toISOString().split('T')[0],
    stock: '',
    folio_cotizacion: '',
    serie_familia: ''
  });

  // Calcular precios automáticamente
  const calculatePrices = (data: any) => {
    const precio_compra = data.precio_compra_dls * precioDolar;
    const precio_total_compra = precio_compra * data.cantidad;
    const precio_venta_unitario = precio_compra * data.factor;
    const precio_venta_total = precio_venta_unitario * data.cantidad;
    const ganancia = precio_venta_total - precio_total_compra;

    return {
      ...data,
      precio_compra,
      precio_total_compra,
      precio_venta_unitario,
      precio_venta_total,
      ganancia
    };
  };

  const handleInputChange = (field: string, value: any) => {
    const newData = { ...formData, [field]: value };
    const calculatedData = calculatePrices(newData);
    setFormData(calculatedData);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '900px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #f1f5f9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'linear-gradient(135deg, #10b981, #059669)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#1e293b',
                margin: '0 0 0.5rem 0'
              }}>
                Agregar Nuevo Precio
              </h2>
              <p style={{
                fontSize: '1rem',
                color: '#64748b',
                margin: 0
              }}>
                Precio del dólar: ${precioDolar.toFixed(2)} MXN
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: loading ? '#9ca3af' : '#64748b',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '50%',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#f1f5f9';
                e.currentTarget.style.color = '#dc2626';
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#64748b';
              }
            }}
          >
            ✕
          </button>
        </div>

        {/* Formulario - Parte 1 */}
        <form onSubmit={handleSubmit}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            {/* Marca */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Marca *
              </label>
              <select
                value={formData.marca}
                onChange={(e) => handleInputChange('marca', e.target.value)}
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  boxSizing: 'border-box'
                }}
              >
                <option value="">Seleccionar marca</option>
                {marcas.map(marca => (
                  <option key={marca} value={marca}>{marca}</option>
                ))}
                <option value="nueva">+ Nueva marca</option>
              </select>
              {formData.marca === 'nueva' && (
                <input
                  type="text"
                  placeholder="Escribir nueva marca"
                  onChange={(e) => handleInputChange('marca', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    marginTop: '0.5rem',
                    boxSizing: 'border-box'
                  }}
                />
              )}
            </div>

            {/* Partida */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Partida
              </label>
              <input
                type="number"
                value={formData.partida}
                onChange={(e) => handleInputChange('partida', Number(e.target.value))}
                min="1"
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            {/* Cantidad */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Cantidad *
              </label>
              <input
                type="number"
                value={formData.cantidad}
                onChange={(e) => handleInputChange('cantidad', Number(e.target.value))}
                min="1"
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Modelo y Descripción */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 2fr',
            gap: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Modelo *
              </label>
              <input
                type="text"
                value={formData.modelo}
                onChange={(e) => handleInputChange('modelo', e.target.value)}
                placeholder="Ej: NSX100F"
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Descripción *
              </label>
              <input
                type="text"
                value={formData.descripcion}
                onChange={(e) => handleInputChange('descripcion', e.target.value)}
                placeholder="Descripción del producto"
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Precios */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio en Dólares (USD) *
              </label>
              <input
                type="number"
                value={formData.precio_compra_dls}
                onChange={(e) => handleInputChange('precio_compra_dls', Number(e.target.value))}
                step="0.01"
                min="0"
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #f59e0b',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : '#fef3c7',
                  color: '#92400e',
                  fontWeight: '600',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Factor de Ganancia *
              </label>
              <input
                type="number"
                value={formData.factor}
                onChange={(e) => handleInputChange('factor', Number(e.target.value))}
                step="0.01"
                min="1"
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio Compra (MXN)
              </label>
              <input
                type="text"
                value={`$${formData.precio_compra.toFixed(2)}`}
                disabled
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: '#f1f5f9',
                  color: '#64748b',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio Total Compra
              </label>
              <input
                type="text"
                value={`$${formData.precio_total_compra.toFixed(2)}`}
                disabled
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: '#fef2f2',
                  color: '#dc2626',
                  fontWeight: '600',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio Venta Unitario
              </label>
              <input
                type="text"
                value={`$${formData.precio_venta_unitario.toFixed(2)}`}
                disabled
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: '#f0fdf4',
                  color: '#059669',
                  fontWeight: '600',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Precio Venta Total
              </label>
              <input
                type="text"
                value={`$${formData.precio_venta_total.toFixed(2)}`}
                disabled
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: '#f0fdf4',
                  color: '#059669',
                  fontWeight: '600',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Ganancia
              </label>
              <input
                type="text"
                value={`$${formData.ganancia.toFixed(2)}`}
                disabled
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: '#f0f9ff',
                  color: '#0369a1',
                  fontWeight: '600',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Información adicional */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Proveedor
              </label>
              <select
                value={formData.proveedor}
                onChange={(e) => handleInputChange('proveedor', e.target.value)}
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  boxSizing: 'border-box'
                }}
              >
                <option value="">Seleccionar proveedor</option>
                {proveedores.map(proveedor => (
                  <option key={proveedor} value={proveedor}>{proveedor}</option>
                ))}
                <option value="nuevo">+ Nuevo proveedor</option>
              </select>
              {formData.proveedor === 'nuevo' && (
                <input
                  type="text"
                  placeholder="Escribir nuevo proveedor"
                  onChange={(e) => handleInputChange('proveedor', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    marginTop: '0.5rem',
                    boxSizing: 'border-box'
                  }}
                />
              )}
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Tiempo de Entrega
              </label>
              <input
                type="text"
                value={formData.tiempo_entrega}
                onChange={(e) => handleInputChange('tiempo_entrega', e.target.value)}
                placeholder="Ej: 15 días"
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Fecha de Cotización *
              </label>
              <input
                type="date"
                value={formData.fecha_cotizacion}
                onChange={(e) => handleInputChange('fecha_cotizacion', e.target.value)}
                required
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Stock
              </label>
              <select
                value={formData.stock}
                onChange={(e) => handleInputChange('stock', e.target.value)}
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  boxSizing: 'border-box'
                }}
              >
                <option value="">Seleccionar estado</option>
                <option value="Disponible">Disponible</option>
                <option value="Bajo Stock">Bajo Stock</option>
                <option value="Sin Stock">Sin Stock</option>
                <option value="Por Cotizar">Por Cotizar</option>
              </select>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Folio de Cotización
              </label>
              <input
                type="text"
                value={formData.folio_cotizacion}
                onChange={(e) => handleInputChange('folio_cotizacion', e.target.value)}
                placeholder="Ej: COT-2024-001"
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Serie/Familia
              </label>
              <input
                type="text"
                value={formData.serie_familia}
                onChange={(e) => handleInputChange('serie_familia', e.target.value)}
                placeholder="Ej: NSX Series"
                disabled={loading}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  boxSizing: 'border-box'
                }}
              />
            </div>
          </div>

          {/* Botones */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            paddingTop: '1.5rem',
            borderTop: '1px solid #e2e8f0'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                flex: 1,
                padding: '0.875rem',
                backgroundColor: loading ? '#f9fafb' : '#f1f5f9',
                color: loading ? '#9ca3af' : '#64748b',
                border: '2px solid #e2e8f0',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                  e.currentTarget.style.borderColor = '#cbd5e1';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                  e.currentTarget.style.borderColor = '#e2e8f0';
                }
              }}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              style={{
                flex: 2,
                padding: '0.875rem',
                background: loading
                  ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                  : 'linear-gradient(135deg, #10b981, #059669)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(16,185,129,0.4)';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {loading ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid rgba(255,255,255,0.3)',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Agregando...
                </>
              ) : (
                <>
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 6L9 17l-5-5" />
                  </svg>
                  Agregar Precio
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
