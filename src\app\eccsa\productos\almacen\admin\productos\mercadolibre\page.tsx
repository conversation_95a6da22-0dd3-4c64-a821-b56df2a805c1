'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

export default function MercadoLibrePage() {
  const [loading, setLoading] = useState(false);
  const [productos, setProductos] = useState([]);
  const [error, setError] = useState('');
  const [cuentaConectada, setCuentaConectada] = useState(false);

  useEffect(() => {
    // Aquí se verificaría si hay una cuenta de Mercado Libre conectada
    // Por ahora, asumimos que no hay cuenta conectada
    setCuentaConectada(false);

    // Aquí se cargarían los productos de Mercado Libre
    // Por ahora, dejamos un array vacío
  }, []);

  return (
    <AuthGuard>
      <div className="admin-header">
        <h1 className="admin-title">Mercado Libre</h1>
        {cuentaConectada ? (
          <button className="admin-button">Sincronizar Productos</button>
        ) : (
          <button className="admin-button">Conectar Cuenta</button>
        )}
      </div>

          {!cuentaConectada ? (
            <div className="admin-card">
              <div className="admin-card-header">
                <h2 className="admin-card-title">Conectar con Mercado Libre</h2>
              </div>
              <div className="admin-card-content">
                <div className="admin-connect-mercadolibre">
                  <img
                    src="https://images.seeklogo.com/logo-png/38/2/mercado-libre-logo-png_seeklogo-381070.png"
                    alt="Mercado Libre Logo"
                    className="admin-ml-logo"
                  />
                  <p>Para gestionar tus productos en Mercado Libre, necesitas conectar tu cuenta.</p>
                  <p>Al conectar tu cuenta, podrás:</p>
                  <ul>
                    <li>Sincronizar tu inventario con Mercado Libre</li>
                    <li>Publicar productos automáticamente</li>
                    <li>Gestionar pedidos desde un solo lugar</li>
                    <li>Actualizar precios y stock en tiempo real</li>
                  </ul>
                  <button className="admin-button admin-ml-button">
                    Conectar con Mercado Libre
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className="admin-card">
                <div className="admin-card-header">
                  <h2 className="admin-card-title">Productos en Mercado Libre</h2>
                  <div className="admin-card-actions">
                    <input
                      type="text"
                      placeholder="Buscar producto..."
                      className="admin-search-input"
                    />
                    <select className="admin-select">
                      <option value="todos">Todos los estados</option>
                      <option value="activo">Activo</option>
                      <option value="pausa">En pausa</option>
                      <option value="finalizado">Finalizado</option>
                    </select>
                  </div>
                </div>

                <div className="admin-card-content">
                  {loading ? (
                    <div className="admin-loading">
                      <div className="admin-loading-spinner"></div>
                      <p>Cargando productos...</p>
                    </div>
                  ) : error ? (
                    <div className="admin-error">
                      <p>{error}</p>
                      <button
                        className="admin-button"
                        onClick={() => {
                          setError('');
                          // Recargar productos
                        }}
                      >
                        Reintentar
                      </button>
                    </div>
                  ) : productos.length === 0 ? (
                    <div className="admin-empty">
                      <p>No hay productos publicados en Mercado Libre.</p>
                      <p>Publica productos para comenzar a vender en Mercado Libre.</p>
                    </div>
                  ) : (
                    <table className="admin-table">
                      <thead>
                        <tr>
                          <th>ID ML</th>
                          <th>Imagen</th>
                          <th>Título</th>
                          <th>Precio</th>
                          <th>Stock</th>
                          <th>Ventas</th>
                          <th>Estado</th>
                          <th>Acciones</th>
                        </tr>
                      </thead>
                      <tbody>
                        {/* Aquí se mostrarían los productos */}
                        <tr>
                          <td colSpan={8} className="admin-table-empty">
                            No hay productos para mostrar
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  )}
                </div>
              </div>

              <div className="admin-card mt-4">
                <div className="admin-card-header">
                  <h2 className="admin-card-title">Estadísticas de Ventas</h2>
                </div>
                <div className="admin-card-content">
                  <div className="admin-stats-grid">
                    <div className="admin-stat-card">
                      <div className="admin-stat-title">Ventas Totales</div>
                      <div className="admin-stat-value">0</div>
                    </div>
                    <div className="admin-stat-card">
                      <div className="admin-stat-title">Ingresos</div>
                      <div className="admin-stat-value">$0.00</div>
                    </div>
                    <div className="admin-stat-card">
                      <div className="admin-stat-title">Productos Activos</div>
                      <div className="admin-stat-value">0</div>
                    </div>
                    <div className="admin-stat-card">
                      <div className="admin-stat-title">Calificación</div>
                      <div className="admin-stat-value">N/A</div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
    </AuthGuard>
  );
}
