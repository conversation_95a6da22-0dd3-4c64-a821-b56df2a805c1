import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3306'),
};

export async function GET() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const [rows] = await connection.execute(`
      SELECT
        id,
        partida,
        cantidad,
        descripcion,
        modelo,
        precio_compra_dls,
        precio_compra,
        precio_total_compra,
        factor,
        precio_venta_unitario,
        precio_venta_total,
        ganancia,
        marca,
        proveedor,
        tiempo_entrega,
        fecha_cotizacion,
        stock,
        folio_cotizacion,
        serie_familia,
        responsable
      FROM lista_de_precio
      ORDER BY marca, modelo
    `);

    return NextResponse.json({
      success: true,
      precios: rows
    });

  } catch (error) {
    console.error('Error fetching precios:', error);
    return NextResponse.json(
      { success: false, error: 'Error al obtener precios' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function POST(request: NextRequest) {
  let connection;

  try {
    const body = await request.json();

    // Verificar si es una importación en lote
    if (body.type === 'bulk_import' && body.data) {
      return await handleBulkImport(body.data);
    }

    // Importación individual (código existente)
    const {
      partida = 1,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor,
      tiempo_entrega,
      fecha_cotizacion,
      stock,
      folio_cotizacion,
      serie_familia,
      responsable
    } = body;

    // Validaciones
    if (!cantidad || !descripcion || !modelo || !precio_compra || !factor || !marca || !fecha_cotizacion) {
      return NextResponse.json(
        { success: false, error: 'Faltan campos requeridos' },
        { status: 400 }
      );
    }

    connection = await mysql.createConnection(dbConfig);

    const [result] = await connection.execute(`
      INSERT INTO lista_de_precio (
        partida,
        cantidad,
        descripcion,
        modelo,
        precio_compra_dls,
        precio_compra,
        precio_total_compra,
        factor,
        precio_venta_unitario,
        precio_venta_total,
        ganancia,
        marca,
        proveedor,
        tiempo_entrega,
        fecha_cotizacion,
        stock,
        folio_cotizacion,
        serie_familia,
        responsable
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      partida,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls || null,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor || null,
      tiempo_entrega || null,
      fecha_cotizacion,
      stock,
      folio_cotizacion || null,
      serie_familia || null,
      responsable || null
    ]);

    return NextResponse.json({
      success: true,
      message: 'Precio agregado exitosamente',
      id: (result as any).insertId
    });

  } catch (error) {
    console.error('Error creating precio:', error);
    return NextResponse.json(
      { success: false, error: 'Error al crear precio' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Función para manejar importación en lote
async function handleBulkImport(data: any[]) {
  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);

    // Iniciar transacción
    await connection.beginTransaction();

    let successCount = 0;
    let errorCount = 0;

    for (const item of data) {
      try {
        await connection.execute(`
          INSERT INTO lista_de_precio (
            partida,
            cantidad,
            descripcion,
            modelo,
            precio_compra_dls,
            precio_compra,
            precio_total_compra,
            factor,
            precio_venta_unitario,
            precio_venta_total,
            ganancia,
            marca,
            proveedor,
            tiempo_entrega,
            fecha_cotizacion,
            stock,
            folio_cotizacion,
            serie_familia,
            responsable
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          item.partida || 1,
          item.cantidad,
          item.descripcion,
          item.modelo,
          item.precio_compra_dls || null,
          item.precio_compra,
          item.precio_total_compra,
          item.factor,
          item.precio_venta_unitario,
          item.precio_venta_total,
          item.ganancia,
          item.marca,
          item.proveedor || null,
          item.tiempo_entrega || null,
          item.fecha_cotizacion,
          item.stock || null,
          item.folio_cotizacion || null,
          item.serie_familia || null,
          item.responsable || null
        ]);

        successCount++;
      } catch (error) {
        console.error('Error inserting item:', error);
        errorCount++;
      }
    }

    // Confirmar transacción
    await connection.commit();

    return NextResponse.json({
      success: true,
      message: `Importación completada: ${successCount} exitosos, ${errorCount} errores`,
      successCount,
      errorCount
    });

  } catch (error) {
    // Revertir transacción en caso de error
    if (connection) {
      await connection.rollback();
    }

    console.error('Error in bulk import:', error);
    return NextResponse.json(
      { success: false, error: 'Error en la importación masiva' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function PUT(request: NextRequest) {
  let connection;

  try {
    const url = new URL(request.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID requerido' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      partida = 1,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor,
      tiempo_entrega,
      fecha_cotizacion,
      stock,
      folio_cotizacion,
      serie_familia,
      responsable
    } = body;

    // Validaciones
    if (!cantidad || !descripcion || !modelo || !precio_compra || !factor || !marca || !fecha_cotizacion) {
      return NextResponse.json(
        { success: false, error: 'Faltan campos requeridos' },
        { status: 400 }
      );
    }

    connection = await mysql.createConnection(dbConfig);

    const [result] = await connection.execute(`
      UPDATE lista_de_precio SET
        partida = ?,
        cantidad = ?,
        descripcion = ?,
        modelo = ?,
        precio_compra_dls = ?,
        precio_compra = ?,
        precio_total_compra = ?,
        factor = ?,
        precio_venta_unitario = ?,
        precio_venta_total = ?,
        ganancia = ?,
        marca = ?,
        proveedor = ?,
        tiempo_entrega = ?,
        fecha_cotizacion = ?,
        stock = ?,
        folio_cotizacion = ?,
        serie_familia = ?,
        responsable = ?
      WHERE id = ?
    `, [
      partida,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls || null,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor || null,
      tiempo_entrega || null,
      fecha_cotizacion,
      stock,
      folio_cotizacion || null,
      serie_familia || null,
      responsable || null,
      id
    ]);

    const updateResult = result as any;
    if (updateResult.affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Precio no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Precio actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating precio:', error);
    return NextResponse.json(
      { success: false, error: 'Error al actualizar precio' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function DELETE(request: NextRequest) {
  let connection;

  try {
    const url = new URL(request.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID requerido' },
        { status: 400 }
      );
    }

    connection = await mysql.createConnection(dbConfig);

    const [result] = await connection.execute(`
      DELETE FROM lista_de_precio WHERE id = ?
    `, [id]);

    const deleteResult = result as any;
    if (deleteResult.affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Precio no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Precio eliminado exitosamente'
    });

  } catch (error) {
    console.error('Error deleting precio:', error);
    return NextResponse.json(
      { success: false, error: 'Error al eliminar precio' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
