-- Script para crear la tabla de almacén en la base de datos EccsaWeb
--
-- Esta tabla almacenará toda la información del inventario y productos del almacén
-- Incluye información de precios, cantidades, proveedores y seguimiento

-- Eliminar la tabla si existe (para recrearla)
DROP TABLE IF EXISTS almacen;

-- Crear la tabla de almacén
CREATE TABLE almacen (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID único del producto en almacén',
    numero_almacen VARCHAR(50) NOT NULL UNIQUE COMMENT 'Número identificador en el almacén',
    estante VARCHAR(100) DEFAULT NULL COMMENT 'Ubicación del estante donde se encuentra',
    modelo_existente VARCHAR(200) NOT NULL COMMENT 'Modelo del producto existente',
    descripcion TEXT DEFAULT NULL COMMENT 'Descripción detallada del producto',
    precio_venta DECIMAL(12, 2) DEFAULT NULL COMMENT 'Precio de venta al público',
    precio_ml DECIMAL(12, 2) DEFAULT NULL COMMENT 'Precio en Mercado Libre',
    vendidos INT DEFAULT 0 COMMENT 'Cantidad total vendidos',
    cantidad_nuevo INT DEFAULT 0 COMMENT 'Cantidad de productos nuevos disponibles',
    minimo INT DEFAULT 0 COMMENT 'Cantidad mínima requerida en inventario',
    maximo INT DEFAULT 0 COMMENT 'Cantidad máxima permitida en inventario',
    pedir_cantidad INT DEFAULT 0 COMMENT 'Cantidad que se debe pedir al proveedor',
    precio_us DECIMAL(12, 2) DEFAULT NULL COMMENT 'Precio en dólares estadounidenses',
    precio_mx DECIMAL(12, 2) DEFAULT NULL COMMENT 'Precio en pesos mexicanos',
    impuesto DECIMAL(5, 2) DEFAULT 16.00 COMMENT 'Porcentaje de impuesto aplicable (IVA)',
    codigo_sat VARCHAR(50) DEFAULT NULL COMMENT 'Código del SAT para facturación',
    nota TEXT DEFAULT NULL COMMENT 'Notas adicionales sobre el producto',
    proveedor VARCHAR(200) DEFAULT NULL COMMENT 'Nombre del proveedor',
    marcas VARCHAR(200) DEFAULT NULL COMMENT 'Marca del producto',
    tiempo_entrega_proveedor VARCHAR(100) DEFAULT NULL COMMENT 'Tiempo de entrega del proveedor',
    fecha_pedido DATE DEFAULT NULL COMMENT 'Fecha cuando se realizó el pedido',
    fecha_recibido DATE DEFAULT NULL COMMENT 'Fecha cuando se recibió el producto',
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación del registro',
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    activo BOOLEAN DEFAULT TRUE COMMENT 'Indica si el producto está activo en el sistema'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla de inventario y almacén de productos ECCSA';

-- Crear índices para mejorar el rendimiento de las consultas
CREATE INDEX idx_almacen_numero ON almacen(numero_almacen);
CREATE INDEX idx_almacen_modelo ON almacen(modelo_existente);
CREATE INDEX idx_almacen_estante ON almacen(estante);
CREATE INDEX idx_almacen_proveedor ON almacen(proveedor);
CREATE INDEX idx_almacen_marcas ON almacen(marcas);
CREATE INDEX idx_almacen_activo ON almacen(activo);
CREATE INDEX idx_almacen_cantidad_nuevo ON almacen(cantidad_nuevo);
CREATE INDEX idx_almacen_fecha_pedido ON almacen(fecha_pedido);

-- Insertar algunos productos de ejemplo para pruebas
INSERT INTO almacen (
    numero_almacen,
    estante,
    modelo_existente,
    descripcion,
    precio_venta,
    precio_ml,
    vendidos,
    cantidad_nuevo,
    minimo,
    maximo,
    pedir_cantidad,
    precio_us,
    precio_mx,
    impuesto,
    codigo_sat,
    nota,
    proveedor,
    marcas,
    tiempo_entrega_proveedor
) VALUES
('ALM-001', 'A1-01', 'SIMATIC S7-1200 CPU 1214C', 'Controlador compacto SIMATIC S7-1200, CPU 1214C, DC/DC/DC, 14 DI/10 DO/2 AI', 15500.00, 16200.00, 5, 3, 2, 10, 0, 850.00, 15500.00, 16.00, '85371099', 'Producto estrella de automatización', 'SIEMENS México', 'SIEMENS', '2-3 semanas'),
('ALM-002', 'A1-02', 'SINAMICS G120C 0.75kW', 'Variador de frecuencia SINAMICS G120C, 0.75kW, 380-480V AC', 8900.00, 9200.00, 8, 5, 3, 15, 2, 485.00, 8900.00, 16.00, '85044082', 'Variador compacto para aplicaciones básicas', 'SIEMENS México', 'SIEMENS', '1-2 semanas'),
('ALM-003', 'B2-05', 'SICK WT100-P132', 'Sensor fotoeléctrico SICK WT100-P132, alcance 100m, PNP', 2850.00, 3100.00, 12, 8, 5, 20, 0, 155.00, 2850.00, 16.00, '85365019', 'Sensor de alta precisión para detección', 'SICK México', 'SICK', '3-4 semanas'),
('ALM-004', 'C3-10', 'Phoenix Contact QUINT-PS', 'Fuente de alimentación QUINT-PS/1AC/24DC/10, 24V DC, 10A', 4200.00, 4500.00, 3, 2, 1, 8, 3, 230.00, 4200.00, 16.00, '85044030', 'Fuente confiable para automatización', 'Phoenix Contact', 'Phoenix Contact', '2-3 semanas'),
('ALM-005', 'D1-03', 'Schneider Electric ATV320U15M2C', 'Variador de velocidad ATV320, 1.5kW, 220V monofásico', 6800.00, 7200.00, 6, 4, 2, 12, 1, 370.00, 6800.00, 16.00, '85044082', 'Variador para motores pequeños', 'Schneider Electric', 'Schneider Electric', '1-2 semanas');

-- Mostrar la estructura de la tabla creada
DESCRIBE almacen;

-- Mostrar los productos insertados
SELECT
    id,
    numero_almacen,
    estante,
    modelo_existente,
    precio_venta,
    cantidad_nuevo,
    proveedor
FROM almacen
ORDER BY numero_almacen;
