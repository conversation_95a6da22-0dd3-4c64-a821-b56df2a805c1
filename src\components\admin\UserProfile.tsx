'use client';

import { User } from '@/hooks/useAuth';

interface UserProfileProps {
  user: User;
  onLogout: () => void;
}

export default function UserProfile({ user, onLogout }: UserProfileProps) {
  const getRoleDisplayName = (role: string, roleLevel: number) => {
    switch (roleLevel) {
      case 0:
        return 'Administrador';
      case 1:
        return 'Jefe';
      case 2:
        return 'Senior';
      case 3:
        return 'Semi-Senior';
      case 4:
        return 'Junior';
      default:
        return role || 'Usuario';
    }
  };

  const getRoleColor = (roleLevel: number) => {
    switch (roleLevel) {
      case 0:
        return '#ef4444'; // Rojo para Administrador
      case 1:
        return '#f97316'; // Naranja para Jefe
      case 2:
        return '#eab308'; // Amarillo para Senior
      case 3:
        return '#22c55e'; // Verde para Semi-Senior
      case 4:
        return '#3b82f6'; // Azul para Junior
      default:
        return '#6b7280'; // G<PERSON> por defecto
    }
  };

  return (
    <div className="user-profile-container">
      <div className="user-profile-info">
        <div className="user-avatar">
          {user.photo ? (
            <img
              src={user.photo}
              alt={user.name}
              className="user-avatar-image"
            />
          ) : (
            <div className="user-avatar-placeholder">
              {user.name.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="user-details">
          <div className="user-name">{user.name}</div>
          <div className="user-username">@{user.username}</div>
          <div 
            className="user-role"
            style={{ color: getRoleColor(user.role_level) }}
          >
            {getRoleDisplayName(user.role, user.role_level)}
          </div>
        </div>
      </div>
      <button
        onClick={onLogout}
        className="logout-button"
        title="Cerrar Sesión"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
          <polyline points="16,17 21,12 16,7"></polyline>
          <line x1="21" y1="12" x2="9" y2="12"></line>
        </svg>
        Cerrar Sesión
      </button>

      <style jsx>{`
        .user-profile-container {
          display: flex;
          align-items: center;
          gap: 1.5rem;
          padding: 1rem;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-profile-info {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .user-avatar {
          position: relative;
        }

        .user-avatar-image {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-avatar-placeholder {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #0056a6, #f7941d);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 700;
          font-size: 1.2rem;
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-details {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
        }

        .user-name {
          font-weight: 600;
          font-size: 1rem;
          color: #1a202c;
          line-height: 1.2;
        }

        .user-username {
          font-size: 0.875rem;
          color: #64748b;
          line-height: 1.2;
        }

        .user-role {
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          line-height: 1.2;
        }

        .logout-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background: #ef4444;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 0.9rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .logout-button:hover {
          background: #dc2626;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        @media (max-width: 768px) {
          .user-profile-container {
            flex-direction: column;
            gap: 1rem;
            padding: 0.75rem;
          }

          .user-profile-info {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
          }

          .user-details {
            align-items: center;
          }

          .logout-button {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
}
