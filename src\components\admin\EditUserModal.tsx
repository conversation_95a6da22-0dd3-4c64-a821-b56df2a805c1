'use client';

import { useState, useEffect } from 'react';

interface Usuario {
  id: number;
  nombre: string;
  nombre_usuario: string;
  correo?: string;
  telefono?: string;
  nivel_ingeniero: number;
  descripcion?: string;
  foto_usuario?: string;
  activo: boolean;
}

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: Usuario | null;
  onUserUpdate: (updatedUser: Usuario) => void;
  onUserCreate?: (newUser: Usuario) => void;
}

const ROLE_NAMES = {
  0: 'Administrador',
  1: 'Jefe',
  2: 'Ingeniero Senior',
  3: 'Ingeniero Junior', 
  4: 'Ingeniero de Apoyo',
  5: 'Practicante'
};

export default function EditUserModal({ isOpen, onClose, user, onUserUpdate, onUserCreate }: EditUserModalProps) {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const [formData, setFormData] = useState({
    nombre: '',
    nombre_usuario: '',
    correo: '',
    telefono: '',
    nivel_ingeniero: 4,
    descripcion: '',
    foto_usuario: '',
    password: '',
    activo: true
  });

  const isEditing = !!user;

  // Reset form when modal opens/closes or user changes
  useEffect(() => {
    if (isOpen) {
      if (user) {
        // Modo edición
        setFormData({
          nombre: user.nombre || '',
          nombre_usuario: user.nombre_usuario || '',
          correo: user.correo || '',
          telefono: user.telefono || '',
          nivel_ingeniero: user.nivel_ingeniero || 4,
          descripcion: user.descripcion || '',
          foto_usuario: user.foto_usuario || '',
          password: '',
          activo: user.activo !== undefined ? user.activo : true
        });
      } else {
        // Modo creación
        setFormData({
          nombre: '',
          nombre_usuario: '',
          correo: '',
          telefono: '',
          nivel_ingeniero: 4,
          descripcion: '',
          foto_usuario: '',
          password: '',
          activo: true
        });
      }
      setMessage(null);
    }
  }, [isOpen, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validaciones
    if (!formData.nombre || !formData.nombre_usuario) {
      setMessage({ type: 'error', text: 'Nombre y usuario son requeridos' });
      return;
    }

    if (!isEditing && !formData.password) {
      setMessage({ type: 'error', text: 'La contraseña es requerida para nuevos usuarios' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      let response;
      let data;

      if (isEditing && user) {
        // Modo edición
        const updateData: any = { ...formData };
        if (!updateData.password) {
          delete updateData.password; // No enviar contraseña vacía en edición
        }

        response = await fetch(`/api/users/${user.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });
        data = await response.json();

        if (response.ok) {
          setMessage({ type: 'success', text: 'Usuario actualizado exitosamente' });
          onUserUpdate(data.user);
          setTimeout(() => {
            onClose();
          }, 1500);
        }
      } else {
        // Modo creación
        response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });
        data = await response.json();

        if (response.ok) {
          setMessage({ type: 'success', text: 'Usuario creado exitosamente' });
          if (onUserCreate) {
            onUserCreate(data.user);
          }
          setTimeout(() => {
            onClose();
          }, 1500);
        }
      }

      if (!response.ok) {
        setMessage({ type: 'error', text: data.error || 'Error al procesar la solicitud' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content large-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{isEditing ? 'Editar Usuario' : 'Agregar Usuario'}</h3>
          <button className="modal-close-btn" onClick={onClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {message && (
            <div className={`message ${message.type}`}>
              {message.text}
            </div>
          )}

          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="nombre">Nombre Completo *</label>
              <input
                type="text"
                id="nombre"
                value={formData.nombre}
                onChange={(e) => setFormData({ ...formData, nombre: e.target.value })}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="nombre_usuario">Usuario *</label>
              <input
                type="text"
                id="nombre_usuario"
                value={formData.nombre_usuario}
                onChange={(e) => setFormData({ ...formData, nombre_usuario: e.target.value })}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">
                Contraseña {isEditing ? '(dejar vacío para no cambiar)' : '*'}
              </label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required={!isEditing}
                className="form-input"
                placeholder={isEditing ? 'Nueva contraseña...' : 'Contraseña...'}
              />
            </div>

            <div className="form-group">
              <label htmlFor="correo">Correo Electrónico</label>
              <input
                type="email"
                id="correo"
                value={formData.correo}
                onChange={(e) => setFormData({ ...formData, correo: e.target.value })}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="telefono">Teléfono</label>
              <input
                type="tel"
                id="telefono"
                value={formData.telefono}
                onChange={(e) => setFormData({ ...formData, telefono: e.target.value })}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="nivel_ingeniero">Nivel de Ingeniero *</label>
              <select
                id="nivel_ingeniero"
                value={formData.nivel_ingeniero}
                onChange={(e) => setFormData({ ...formData, nivel_ingeniero: parseInt(e.target.value) })}
                required
                className="form-input"
              >
                <option value={0}>Administrador</option>
                <option value={1}>Jefe</option>
                <option value={2}>Ingeniero Senior</option>
                <option value={3}>Ingeniero Junior</option>
                <option value={4}>Ingeniero de Apoyo</option>
                <option value={5}>Practicante</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="foto_usuario">URL de Foto</label>
              <input
                type="url"
                id="foto_usuario"
                value={formData.foto_usuario}
                onChange={(e) => setFormData({ ...formData, foto_usuario: e.target.value })}
                className="form-input"
                placeholder="https://ejemplo.com/foto.jpg"
              />
            </div>

            <div className="form-group full-width">
              <label htmlFor="descripcion">Descripción</label>
              <textarea
                id="descripcion"
                value={formData.descripcion}
                onChange={(e) => setFormData({ ...formData, descripcion: e.target.value })}
                className="form-input"
                rows={3}
                placeholder="Descripción del usuario..."
              />
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.activo}
                  onChange={(e) => setFormData({ ...formData, activo: e.target.checked })}
                />
                <span className="checkbox-text">Usuario Activo</span>
              </label>
            </div>
          </div>
        </form>

        <div className="modal-footer">
          <button 
            type="button"
            className="modern-admin-button modern-admin-button-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </button>
          <button 
            type="submit"
            className="modern-admin-button modern-admin-button-primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Guardando...' : (isEditing ? 'Guardar Cambios' : 'Crear Usuario')}
          </button>
        </div>
      </div>
    </div>
  );
}
