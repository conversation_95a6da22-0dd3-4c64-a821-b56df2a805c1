'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';
import { DatabaseStatus } from '@/eccsa_back/types/database';

export default function DatabasePage() {
  const [loading, setLoading] = useState(true);
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const fetchDatabaseStatus = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/database/status');
        const data = await response.json();

        if (response.ok) {
          setDbStatus(data);
        } else {
          setError(data.error || 'Error al obtener el estado de la base de datos');
        }
      } catch (err) {
        setError('Error de conexión al servidor');
        console.error('Error fetching database status:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDatabaseStatus();
  }, [refreshKey]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <AuthGuard>
      <div className="modern-admin-page">
            <div className="admin-header">
              <h1 className="admin-title">Estado de la Base de Datos</h1>
              <button
                className="admin-refresh-button"
                onClick={handleRefresh}
                disabled={loading}
              >
                {loading ? 'Actualizando...' : 'Actualizar'}
              </button>
            </div>

            {loading ? (
              <div className="admin-loading">
                <div className="admin-loading-spinner"></div>
                <p>Cargando información de la base de datos...</p>
              </div>
            ) : error ? (
              <div className="admin-error">
                <h2>Error</h2>
                <p>{error}</p>
              </div>
            ) : dbStatus ? (
              <div className="database-status">
                <div className="database-section">
                  <h2 className="database-section-title">Información de Conexión</h2>
                  <div className="database-card">
                    <div className="database-connection-status">
                      <div className={`status-indicator ${dbStatus.connection.status === 'success' ? 'success' : 'error'}`}></div>
                      <span className="status-text">
                        {dbStatus.connection.status === 'success' ? 'Conectado' : 'Error de conexión'}
                      </span>
                    </div>

                    <div className="database-info-grid">
                      <div className="database-info-item">
                        <span className="info-label">Host:</span>
                        <span className="info-value">{dbStatus.connection.info.host}</span>
                      </div>
                      <div className="database-info-item">
                        <span className="info-label">Usuario:</span>
                        <span className="info-value">{dbStatus.connection.info.user}</span>
                      </div>
                      <div className="database-info-item">
                        <span className="info-label">Base de datos:</span>
                        <span className="info-value">{dbStatus.connection.info.database}</span>
                      </div>
                      <div className="database-info-item">
                        <span className="info-label">Puerto:</span>
                        <span className="info-value">{dbStatus.connection.info.port}</span>
                      </div>
                      <div className="database-info-item">
                        <span className="info-label">Modo:</span>
                        <span className={`info-value ${dbStatus.connection.info.usingMock ? 'mock-mode' : 'real-mode'}`}>
                          {dbStatus.connection.info.usingMock ?
                            'Base de datos simulada (desarrollo)' :
                            'Base de datos real (producción)'}
                        </span>
                      </div>
                    </div>

                    {dbStatus.connection.message && (
                      <div className="database-message">
                        <p>{dbStatus.connection.message}</p>
                      </div>
                    )}
                  </div>
                </div>

                {dbStatus.stats && (
                  <div className="database-section">
                    <h2 className="database-section-title">Tablas y Registros</h2>
                    <div className="database-card">
                      <table className="database-table">
                        <thead>
                          <tr>
                            <th>Tabla</th>
                            <th>Registros</th>
                          </tr>
                        </thead>
                        <tbody>
                          {dbStatus.stats.tables.map((table) => (
                            <tr key={table.name}>
                              <td>{table.name}</td>
                              <td>{table.count}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                <div className="database-section">
                  <h2 className="database-section-title">Cadena de Conexión</h2>
                  <div className="database-card">
                    <div className="connection-string">
                      <code>{dbStatus.connectionString}</code>
                    </div>
                    <p className="connection-note">
                      Esta es la cadena de conexión utilizada para conectar con la base de datos.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="admin-error">
                <p>No se pudo obtener información de la base de datos.</p>
              </div>
            )}
      </div>
    </AuthGuard>
  );
}
