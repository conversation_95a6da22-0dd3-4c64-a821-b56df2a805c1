/**
 * Utility functions for the Eccsa website
 */

/**
 * Format a phone number by removing non-digit characters
 * @param phoneNumber - The phone number to format
 * @returns Formatted phone number with only digits
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  return phoneNumber.replace(/\D/g, '');
};

/**
 * Create a WhatsApp URL with phone number and message
 * @param phoneNumber - The phone number to contact
 * @param message - The message to send
 * @returns WhatsApp URL
 */
export const createWhatsAppUrl = (phoneNumber: string, message: string): string => {
  const formattedPhone = formatPhoneNumber(phoneNumber);
  return `https://wa.me/${formattedPhone}?text=${encodeURIComponent(message)}`;
};

/**
 * Check if a string is a valid URL or a valid local path
 * @param url - The URL or path to validate
 * @returns Boolean indicating if the URL/path is valid
 */
export const isValidUrl = (url: string): boolean => {
  // Check if it's a local path starting with '/'
  if (url && url.startsWith('/')) {
    return true;
  }

  // Check if it's a valid URL
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Prefetch routes to improve navigation performance
 * @param routes - Array of routes to prefetch
 */
export const prefetchRoutes = (routes: string[]): void => {
  routes.forEach(route => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    document.head.appendChild(link);
  });
};

/**
 * Prevent body scroll
 * @param prevent - Boolean to indicate whether to prevent scroll
 */
export const preventBodyScroll = (prevent: boolean): void => {
  if (prevent) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
};

/**
 * Check if a path is an admin page
 * @param path - The path to check
 * @returns Boolean indicating if the path is an admin page
 */
export const isAdminPage = (path: string): boolean => {
  return path.includes('/eccsa/productos/almacen/admin') || path.includes('/admin');
};
