import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/eccsa_back/models/User';

/**
 * DELETE /api/users/[id]/delete
 * Elimina un usuario de la base de datos
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    // Verificar si el usuario existe
    const existingUser = await UserModel.getById(id);

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Verificar que no sea el último administrador
    if (existingUser.nivel_ingeniero === 0) {
      const allAdmins = await UserModel.getByRole(0);
      if (allAdmins.length <= 1) {
        return NextResponse.json(
          { error: 'No se puede eliminar el último administrador del sistema' },
          { status: 400 }
        );
      }
    }

    // Eliminar el usuario permanentemente
    const deleted = await UserModel.deleteForever(id);

    if (!deleted) {
      return NextResponse.json(
        { error: 'No se pudo eliminar el usuario' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Usuario ${existingUser.nombre} eliminado exitosamente`
    });

  } catch (error) {
    console.error(`Error al eliminar usuario con ID ${params.id}:`, error);

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
