import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3306'),
};

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let connection;
  
  try {
    const id = params.id;
    const body = await request.json();
    const {
      partida = 1,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor,
      tiempo_entrega,
      fecha_cotizacion,
      stock,
      folio_cotizacion,
      serie_familia,
      responsable
    } = body;

    // Validaciones
    if (!cantidad || !descripcion || !modelo || !precio_compra || !factor || !marca || !fecha_cotizacion) {
      return NextResponse.json(
        { success: false, error: 'Faltan campos requeridos' },
        { status: 400 }
      );
    }

    // Los cálculos ya vienen hechos del frontend, solo los validamos
    console.log('Actualizando precio ID:', id, {
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia
    });

    connection = await mysql.createConnection(dbConfig);
    
    const [result] = await connection.execute(`
      UPDATE lista_de_precio SET
        partida = ?,
        cantidad = ?,
        descripcion = ?,
        modelo = ?,
        precio_compra_dls = ?,
        precio_compra = ?,
        precio_total_compra = ?,
        factor = ?,
        precio_venta_unitario = ?,
        precio_venta_total = ?,
        ganancia = ?,
        marca = ?,
        proveedor = ?,
        tiempo_entrega = ?,
        fecha_cotizacion = ?,
        stock = ?,
        folio_cotizacion = ?,
        serie_familia = ?,
        responsable = ?
      WHERE id = ?
    `, [
      partida,
      cantidad,
      descripcion,
      modelo,
      precio_compra_dls || null,
      precio_compra,
      precio_total_compra,
      factor,
      precio_venta_unitario,
      precio_venta_total,
      ganancia,
      marca,
      proveedor || null,
      tiempo_entrega || null,
      fecha_cotizacion,
      stock,
      folio_cotizacion || null,
      serie_familia || null,
      responsable || null,
      id
    ]);

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Precio no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Precio actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating precio:', error);
    return NextResponse.json(
      { success: false, error: 'Error al actualizar precio' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let connection;
  
  try {
    const id = params.id;

    connection = await mysql.createConnection(dbConfig);
    
    const [result] = await connection.execute(
      'DELETE FROM lista_de_precio WHERE id = ?',
      [id]
    );

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Precio no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Precio eliminado exitosamente'
    });

  } catch (error) {
    console.error('Error deleting precio:', error);
    return NextResponse.json(
      { success: false, error: 'Error al eliminar precio' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
