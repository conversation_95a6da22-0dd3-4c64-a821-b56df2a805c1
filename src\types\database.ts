// User model
export interface User {
  id: number;
  username: string;
  password: string;
  email?: string;
  role: string;
  created_at: Date;
  updated_at: Date;
}

// Product model
export interface Product {
  id: number;
  name: string;
  description: string;
  price?: number;
  category_id: number;
  image_url?: string;
  brand?: string;
  created_at: Date;
  updated_at: Date;
}

// Category model
export interface Category {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

// Order model
export interface Order {
  id: number;
  user_id: number;
  status: string;
  total: number;
  created_at: Date;
  updated_at: Date;
}

// OrderItem model
export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  price: number;
  created_at: Date;
  updated_at: Date;
}
