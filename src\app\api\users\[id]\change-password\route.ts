import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import { verifyPassword, hashPassword } from '@/eccsa_back/lib/password';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { currentPassword, newPassword } = await request.json();
    const userId = parseInt(params.id);

    // Validations
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Contraseña actual y nueva contraseña son requeridas' },
        { status: 400 }
      );
    }

    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'La nueva contraseña debe tener al menos 8 caracteres' },
        { status: 400 }
      );
    }

    // Password strength validation
    const hasUpperCase = /[A-Z]/.test(newPassword);
    const hasLowerCase = /[a-z]/.test(newPassword);
    const hasNumbers = /\d/.test(newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      return NextResponse.json(
        { 
          error: 'La contraseña debe contener al menos: una mayúscula, una minúscula, un número y un carácter especial' 
        },
        { status: 400 }
      );
    }

    // Get current user data
    const userResult = await executeQuery({
      query: 'SELECT * FROM usuarios WHERE id = ?',
      values: [userId]
    });

    if (!Array.isArray(userResult) || userResult.length === 0) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const user = userResult[0] as any;

    // Verify current password
    const isCurrentPasswordValid = verifyPassword(user.contrasena, currentPassword);
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { error: 'La contraseña actual es incorrecta' },
        { status: 400 }
      );
    }

    // Check if new password is different from current
    const isSamePassword = verifyPassword(user.contrasena, newPassword);
    
    if (isSamePassword) {
      return NextResponse.json(
        { error: 'La nueva contraseña debe ser diferente a la actual' },
        { status: 400 }
      );
    }

    // Hash new password using the same method as the system
    const hashedNewPassword = hashPassword(newPassword);

    // Update password in database
    await executeQuery({
      query: 'UPDATE usuarios SET contrasena = ? WHERE id = ?',
      values: [hashedNewPassword, userId]
    });

    // Log password change for security audit
    console.log(`Password changed for user ID: ${userId} at ${new Date().toISOString()}`);

    return NextResponse.json(
      { 
        message: 'Contraseña actualizada exitosamente',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error changing password:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
