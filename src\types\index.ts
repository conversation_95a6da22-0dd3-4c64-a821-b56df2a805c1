/**
 * Common types used throughout the application
 */

/**
 * Image type for sliders and galleries
 */
export interface Image {
  src: string;
  alt: string;
  title?: string;
  description?: string;
  ctaLink?: string;
  ctaText?: string;
}

/**
 * Brand type for brand sliders
 */
export interface Brand {
  src: string;
  alt: string;
  url?: string;
}

/**
 * Service type for service cards
 */
export interface Service {
  id: string;
  title: string;
  description: string;
  icon?: string;
  image?: string;
}

/**
 * Product type for product listings
 */
export interface Product {
  id: string;
  name: string;
  description: string;
  price?: number;
  image?: string;
  brand?: string;
  category?: string;
}

/**
 * Project type for project showcase
 */
export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  client?: string;
  date?: string;
}

/**
 * Navigation link type
 */
export interface NavLink {
  href: string;
  label: string;
  icon?: React.ReactNode;
  isExternal?: boolean;
}

/**
 * Social media link type
 */
export interface SocialLink {
  href: string;
  label: string;
  icon: React.ComponentType;
}

/**
 * Contact information type
 */
export interface ContactInfo {
  address: string;
  phone: string;
  email: string;
  hours: string;
}

/**
 * User type for authentication
 */
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
}

/**
 * Authentication state
 */
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
