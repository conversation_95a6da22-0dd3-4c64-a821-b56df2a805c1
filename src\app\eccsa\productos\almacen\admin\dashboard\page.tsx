'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import './dashboard.css';

interface User {
  id?: number;
  username: string;
  name?: string;
  role?: string;
  role_level?: number;
  email?: string;
}

interface DashboardStats {
  total_productos: number;
  total_cantidad: number;
  total_vendidos: number;
  productos_stock_bajo: number;
  productos_sin_stock: number;
  valor_total_inventario: number;
}

interface MostSoldProduct {
  numero_almacen: string;
  modelo_existente: string;
  marcas: string;
  vendidos: number;
}

interface RecentProduct {
  numero_almacen: string;
  modelo_existente: string;
  marcas: string;
  fecha_creacion: string;
}

interface Alert {
  id: string;
  type: 'warning' | 'danger' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

interface Solicitud {
  id: number;
  estado: string;
  fecha_creacion: string;
  email_cliente: string;
  numero_almacen: string;
  modelo_existente?: string;
  marcas?: string;
}

interface Employee {
  id: number;
  nombre: string;
  nombre_usuario: string;
  correo: string;
  nivel_ingeniero: number;
  fecha_creacion: string;
}

interface ActivityStats {
  total_empleados: number;
  empleados_activos: number;
  solicitudes_pendientes: number;
  solicitudes_hoy: number;
  productos_agregados_hoy: number;
  ventas_hoy: number;
}

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [userData, setUserData] = useState<User | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [previousStats, setPreviousStats] = useState<DashboardStats | null>(null);
  const [mostSold, setMostSold] = useState<MostSoldProduct | null>(null);
  const [recentProducts, setRecentProducts] = useState<RecentProduct[]>([]);
  const [recentSolicitudes, setRecentSolicitudes] = useState<Solicitud[]>([]);
  const [recentEmployees, setRecentEmployees] = useState<Employee[]>([]);
  const [activityStats, setActivityStats] = useState<ActivityStats | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Cargar los datos del usuario desde localStorage
  useEffect(() => {
    const savedUserData = localStorage.getItem('eccsaUserData');
    if (savedUserData) {
      try {
        const parsedUserData = JSON.parse(savedUserData);
        setUserData(parsedUserData);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }, []);

  // Generar alertas basadas en cambios en los datos
  const generateAlerts = useCallback((newStats: DashboardStats, oldStats: DashboardStats | null) => {
    const newAlerts: Alert[] = [];

    if (oldStats) {
      // Alerta por productos sin stock
      if (newStats.productos_sin_stock > oldStats.productos_sin_stock) {
        newAlerts.push({
          id: `stock-out-${Date.now()}`,
          type: 'danger',
          title: 'Productos sin stock',
          message: `${newStats.productos_sin_stock - oldStats.productos_sin_stock} productos se han quedado sin stock`,
          timestamp: new Date(),
          read: false
        });
      }

      // Alerta por stock bajo
      if (newStats.productos_stock_bajo > oldStats.productos_stock_bajo) {
        newAlerts.push({
          id: `low-stock-${Date.now()}`,
          type: 'warning',
          title: 'Stock bajo detectado',
          message: `${newStats.productos_stock_bajo - oldStats.productos_stock_bajo} productos tienen stock bajo`,
          timestamp: new Date(),
          read: false
        });
      }

      // Alerta por nuevos productos
      if (newStats.total_productos > oldStats.total_productos) {
        newAlerts.push({
          id: `new-products-${Date.now()}`,
          type: 'success',
          title: 'Nuevos productos agregados',
          message: `${newStats.total_productos - oldStats.total_productos} productos han sido agregados al inventario`,
          timestamp: new Date(),
          read: false
        });
      }

      // Alerta por ventas
      if (newStats.total_vendidos > oldStats.total_vendidos) {
        newAlerts.push({
          id: `sales-${Date.now()}`,
          type: 'info',
          title: 'Nuevas ventas registradas',
          message: `${newStats.total_vendidos - oldStats.total_vendidos} productos han sido vendidos`,
          timestamp: new Date(),
          read: false
        });
      }
    }

    // Alertas por estado actual
    if (newStats.productos_sin_stock > 0) {
      newAlerts.push({
        id: `current-no-stock-${Date.now()}`,
        type: 'danger',
        title: 'Productos sin stock',
        message: `Hay ${newStats.productos_sin_stock} productos sin stock que requieren atención`,
        timestamp: new Date(),
        read: false
      });
    }

    if (newStats.productos_stock_bajo > 5) {
      newAlerts.push({
        id: `current-low-stock-${Date.now()}`,
        type: 'warning',
        title: 'Múltiples productos con stock bajo',
        message: `${newStats.productos_stock_bajo} productos tienen stock bajo`,
        timestamp: new Date(),
        read: false
      });
    }

    return newAlerts;
  }, []);

  // Cargar datos del dashboard
  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);

      // Cargar datos de múltiples fuentes en paralelo
      const [
        dashboardResponse,
        solicitudesResponse,
        empleadosResponse,
        solicitudesStatsResponse
      ] = await Promise.all([
        fetch('/api/almacen?dashboard=true'),
        fetch('/api/solicitudes?limit=5'),
        fetch('/api/users'),
        fetch('/api/solicitudes?stats=true')
      ]);

      // Procesar datos del almacén
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();

        // Guardar estadísticas anteriores antes de actualizar
        if (stats) {
          setPreviousStats(stats);
        }

        setStats(dashboardData.stats);
        setMostSold(dashboardData.mostSold);
        setRecentProducts(dashboardData.recent || []);

        // Generar alertas si hay cambios
        const newAlerts = generateAlerts(dashboardData.stats, stats);
        if (newAlerts.length > 0) {
          setAlerts(prev => [...newAlerts, ...prev].slice(0, 10));
        }
      }

      // Procesar solicitudes recientes
      if (solicitudesResponse.ok) {
        const solicitudesData = await solicitudesResponse.json();
        setRecentSolicitudes(solicitudesData.solicitudes || []);
      }

      // Procesar empleados recientes
      if (empleadosResponse.ok) {
        const empleadosData = await empleadosResponse.json();
        const employees = empleadosData.data || [];
        // Ordenar por fecha de creación y tomar los 5 más recientes
        const sortedEmployees = employees
          .sort((a: Employee, b: Employee) =>
            new Date(b.fecha_creacion).getTime() - new Date(a.fecha_creacion).getTime()
          )
          .slice(0, 5);
        setRecentEmployees(sortedEmployees);
      }

      // Calcular estadísticas de actividad
      const today = new Date().toISOString().split('T')[0];

      // Obtener datos adicionales para estadísticas
      const [productosHoyResponse, solicitudesHoyResponse] = await Promise.all([
        fetch(`/api/almacen?recent=true&limit=100`),
        fetch(`/api/solicitudes?limit=100`)
      ]);

      let activityData: ActivityStats = {
        total_empleados: 0,
        empleados_activos: 0,
        solicitudes_pendientes: 0,
        solicitudes_hoy: 0,
        productos_agregados_hoy: 0,
        ventas_hoy: 0
      };

      // Calcular estadísticas de empleados
      if (empleadosResponse.ok) {
        const empleadosData = await empleadosResponse.json();
        const employees = empleadosData.data || [];
        activityData.total_empleados = employees.length;
        activityData.empleados_activos = employees.filter((emp: Employee) => emp.nivel_ingeniero >= 0).length;
      }

      // Calcular estadísticas de solicitudes
      if (solicitudesResponse.ok) {
        const solicitudesData = await solicitudesResponse.json();
        const solicitudes = solicitudesData.solicitudes || [];
        activityData.solicitudes_pendientes = solicitudes.filter((sol: Solicitud) => sol.estado === 'pendiente').length;
        activityData.solicitudes_hoy = solicitudes.filter((sol: Solicitud) =>
          sol.fecha_creacion.startsWith(today)
        ).length;
      }

      // Calcular productos agregados hoy
      if (productosHoyResponse.ok) {
        const productosData = await productosHoyResponse.json();
        const productos = productosData.recent || [];
        activityData.productos_agregados_hoy = productos.filter((prod: RecentProduct) =>
          prod.fecha_creacion.startsWith(today)
        ).length;
      }

      // Calcular ventas del día (basado en productos vendidos)
      if (stats) {
        activityData.ventas_hoy = Math.floor(Math.random() * 10); // Placeholder - necesitaríamos una tabla de ventas real
      }

      setActivityStats(activityData);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  }, [stats, generateAlerts]);

  // Cargar datos iniciales
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Auto-refresh cada 30 segundos
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadDashboardData();
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh, loadDashboardData]);

  // Actualizar tiempo cada segundo
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
    return formattedPrice;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-MX', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('es-MX', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatChange = (current: number, previous: number | undefined): { value: number; isPositive: boolean } => {
    if (!previous) return { value: 0, isPositive: true };
    const change = current - previous;
    return { value: Math.abs(change), isPositive: change >= 0 };
  };

  const markAlertAsRead = (alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId ? { ...alert, read: true } : alert
    ));
  };

  const clearAllAlerts = () => {
    setAlerts([]);
  };

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'success':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        );
      case 'warning':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        );
      case 'danger':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        );
      case 'info':
      default:
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        );
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="modern-dashboard-page">
          {/* Header del Dashboard */}
          <div className="dashboard-header">
            <div className="header-content">
              <div className="header-title-section">
                <div className="title-icon">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <path d="M9 9h6v6H9z"></path>
                  </svg>
                </div>
                <div className="title-text">
                  <h1 className="dashboard-title">Panel de Control ECCSA</h1>
                  <p className="dashboard-subtitle">
                    {user ? `Bienvenido, ${user.name || user.username}` : 'Sistema de gestión ECCSA'}
                  </p>
                  <div className="connection-status">
                    <div className="status-indicator online"></div>
                    <span>Sistema en línea</span>
                  </div>
                </div>
              </div>
              <div className="header-actions">
                <div className="time-widget">
                  <div className="current-time">
                    {currentTime.toLocaleTimeString('es-MX', {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </div>
                  <div className="current-date">
                    {currentTime.toLocaleDateString('es-MX', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                <div className="last-update">
                  <span>Última actualización: {formatTime(lastUpdate)}</span>
                </div>
                <div className="auto-refresh-toggle">
                  <label className="toggle-label">
                    <input
                      type="checkbox"
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                      className="toggle-input"
                    />
                    <span className="toggle-slider"></span>
                    <span className="toggle-text">Auto-actualizar</span>
                  </label>
                </div>
                <button
                  className="refresh-button"
                  onClick={loadDashboardData}
                  disabled={loading}
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="23 4 23 10 17 10"></polyline>
                    <polyline points="1 20 1 14 7 14"></polyline>
                    <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                  </svg>
                  {loading ? 'Actualizando...' : 'Actualizar'}
                </button>
              </div>
            </div>
          </div>

          {/* Alertas */}
          {alerts.length > 0 && (
            <div className="dashboard-alerts">
              <div className="alerts-header">
                <h3>Alertas del Sistema</h3>
                <button onClick={clearAllAlerts} className="clear-alerts-btn">
                  Limpiar todas
                </button>
              </div>
              <div className="alerts-list">
                {alerts.slice(0, 5).map((alert) => (
                  <div
                    key={alert.id}
                    className={`alert-item ${alert.type} ${alert.read ? 'read' : 'unread'}`}
                    onClick={() => markAlertAsRead(alert.id)}
                  >
                    <div className="alert-icon">
                      {getAlertIcon(alert.type)}
                    </div>
                    <div className="alert-content">
                      <div className="alert-title">{alert.title}</div>
                      <div className="alert-message">{alert.message}</div>
                      <div className="alert-time">{formatTime(alert.timestamp)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {loading ? (
            <div className="loading-state">
              <div className="loading-spinner">
                <div className="spinner"></div>
              </div>
              <h3>Cargando datos del dashboard...</h3>
              <p>Obteniendo información en tiempo real</p>
            </div>
          ) : (
            <>
              {/* Estadísticas principales modernas */}
              <div className="dashboard-stats">
                <div className="stats-grid">
                  <div className="stat-card primary">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{stats?.total_productos || 0}</div>
                      <div className="stat-label">Total Productos</div>
                      {previousStats && (
                        <div className="stat-change">
                          {(() => {
                            const change = getStatChange(stats?.total_productos || 0, previousStats.total_productos);
                            return change.value > 0 ? (
                              <span className={change.isPositive ? 'positive' : 'negative'}>
                                {change.isPositive ? '+' : '-'}{change.value}
                              </span>
                            ) : null;
                          })()}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="stat-card success">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{stats?.total_cantidad || 0}</div>
                      <div className="stat-label">Unidades en Stock</div>
                      {previousStats && (
                        <div className="stat-change">
                          {(() => {
                            const change = getStatChange(stats?.total_cantidad || 0, previousStats.total_cantidad);
                            return change.value > 0 ? (
                              <span className={change.isPositive ? 'positive' : 'negative'}>
                                {change.isPositive ? '+' : '-'}{change.value}
                              </span>
                            ) : null;
                          })()}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="stat-card info">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{stats?.total_vendidos || 0}</div>
                      <div className="stat-label">Total Vendidos</div>
                      {previousStats && (
                        <div className="stat-change">
                          {(() => {
                            const change = getStatChange(stats?.total_vendidos || 0, previousStats.total_vendidos);
                            return change.value > 0 ? (
                              <span className={change.isPositive ? 'positive' : 'negative'}>
                                {change.isPositive ? '+' : '-'}{change.value}
                              </span>
                            ) : null;
                          })()}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="stat-card accent">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="12" y1="1" x2="12" y2="23"></line>
                        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{formatPrice(stats?.valor_total_inventario)}</div>
                      <div className="stat-label">Valor Total</div>
                    </div>
                  </div>

                  <div className={`stat-card ${(stats?.productos_stock_bajo || 0) > 0 ? 'warning' : 'success'}`}>
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                        <line x1="12" y1="9" x2="12" y2="13"></line>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{stats?.productos_stock_bajo || 0}</div>
                      <div className="stat-label">Stock Bajo</div>
                    </div>
                  </div>

                  <div className={`stat-card ${(stats?.productos_sin_stock || 0) > 0 ? 'danger' : 'success'}`}>
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <div className="stat-value">{stats?.productos_sin_stock || 0}</div>
                      <div className="stat-label">Sin Stock</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Acciones Rápidas con Datos Reales */}
              <div className="dashboard-actions">
                <h2>Acciones Rápidas</h2>
                <div className="actions-grid">
                  <button
                    className="action-card primary"
                    onClick={() => window.location.href = '/eccsa/productos/almacen/admin/productos/almacen'}
                  >
                    <div className="action-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </div>
                    <div className="action-content">
                      <h3>Agregar Producto</h3>
                      <p>Total productos: {stats?.total_productos || 0}</p>
                      <div className="action-stat">
                        {activityStats?.productos_agregados_hoy || 0} agregados hoy
                      </div>
                    </div>
                  </button>

                  <button
                    className="action-card success"
                    onClick={() => window.location.href = '/eccsa/productos/almacen/admin/productos/almacen'}
                  >
                    <div className="action-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      </svg>
                    </div>
                    <div className="action-content">
                      <h3>Ver Inventario</h3>
                      <p>Stock total: {stats?.total_cantidad || 0} unidades</p>
                      <div className="action-stat">
                        {stats?.productos_sin_stock || 0} sin stock
                      </div>
                    </div>
                  </button>

                  <button
                    className="action-card info"
                    onClick={() => window.location.href = '/eccsa/productos/almacen/admin/empleados'}
                  >
                    <div className="action-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <div className="action-content">
                      <h3>Gestionar Empleados</h3>
                      <p>Total empleados: {activityStats?.total_empleados || 0}</p>
                      <div className="action-stat">
                        {activityStats?.empleados_activos || 0} activos
                      </div>
                    </div>
                  </button>

                  <button
                    className="action-card warning"
                    onClick={() => window.location.href = '/eccsa/productos/almacen/admin/solicitudes'}
                  >
                    <div className="action-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                      </svg>
                    </div>
                    <div className="action-content">
                      <h3>Ver Solicitudes</h3>
                      <p>Pendientes: {activityStats?.solicitudes_pendientes || 0}</p>
                      <div className="action-stat">
                        {activityStats?.solicitudes_hoy || 0} solicitudes hoy
                      </div>
                    </div>
                  </button>

                  <button
                    className="action-card accent"
                    onClick={() => window.location.href = '/eccsa/productos/almacen/admin/productos/precios'}
                  >
                    <div className="action-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <line x1="12" y1="1" x2="12" y2="23"></line>
                        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                      </svg>
                    </div>
                    <div className="action-content">
                      <h3>Lista de Precios</h3>
                      <p>Gestionar cotizaciones</p>
                      <div className="action-stat">
                        Sistema de precios
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Actividades Recientes con Datos Reales */}
              <div className="dashboard-activities">
                <div className="activities-grid">
                  {/* Productos Recientes */}
                  <div className="activity-section">
                    <div className="activity-header">
                      <h3>Productos Recientes</h3>
                      <span className="activity-count">{recentProducts.length}</span>
                    </div>
                    <div className="activity-content">
                      {recentProducts.length > 0 ? (
                        <div className="activity-list">
                          {recentProducts.slice(0, 5).map((product, index) => (
                            <div key={index} className="activity-item">
                              <div className="activity-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                                </svg>
                              </div>
                              <div className="activity-info">
                                <div className="activity-title">{product.numero_almacen}</div>
                                <div className="activity-subtitle">{product.marcas} - {product.modelo_existente.substring(0, 30)}...</div>
                                <div className="activity-time">{formatDate(product.fecha_creacion)}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="activity-empty">
                          <p>No hay productos recientes</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Solicitudes Recientes */}
                  <div className="activity-section">
                    <div className="activity-header">
                      <h3>Solicitudes Recientes</h3>
                      <span className="activity-count">{recentSolicitudes.length}</span>
                    </div>
                    <div className="activity-content">
                      {recentSolicitudes.length > 0 ? (
                        <div className="activity-list">
                          {recentSolicitudes.slice(0, 5).map((solicitud, index) => (
                            <div key={index} className="activity-item">
                              <div className="activity-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                  <polyline points="14,2 14,8 20,8"></polyline>
                                </svg>
                              </div>
                              <div className="activity-info">
                                <div className="activity-title">Solicitud #{solicitud.id}</div>
                                <div className="activity-subtitle">
                                  {solicitud.email_cliente}
                                  {solicitud.modelo_existente && ` - ${solicitud.modelo_existente.substring(0, 25)}...`}
                                </div>
                                <div className="activity-time">{formatDate(solicitud.fecha_creacion)}</div>
                              </div>
                              <div className={`activity-status ${solicitud.estado}`}>
                                {solicitud.estado}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="activity-empty">
                          <p>No hay solicitudes recientes</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Empleados Recientes */}
                  <div className="activity-section">
                    <div className="activity-header">
                      <h3>Empleados Recientes</h3>
                      <span className="activity-count">{recentEmployees.length}</span>
                    </div>
                    <div className="activity-content">
                      {recentEmployees.length > 0 ? (
                        <div className="activity-list">
                          {recentEmployees.slice(0, 5).map((employee, index) => (
                            <div key={index} className="activity-item">
                              <div className="activity-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                  <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                              </div>
                              <div className="activity-info">
                                <div className="activity-title">{employee.nombre}</div>
                                <div className="activity-subtitle">
                                  @{employee.nombre_usuario} - Nivel {employee.nivel_ingeniero}
                                </div>
                                <div className="activity-time">{formatDate(employee.fecha_creacion)}</div>
                              </div>
                              <div className={`activity-status ${employee.nivel_ingeniero === 0 ? 'admin' : employee.nivel_ingeniero === 1 ? 'manager' : 'engineer'}`}>
                                {employee.nivel_ingeniero === 0 ? 'Admin' : employee.nivel_ingeniero === 1 ? 'Jefe' : 'Ingeniero'}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="activity-empty">
                          <p>No hay empleados recientes</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Producto Más Vendido */}
                  {mostSold && (
                    <div className="activity-section highlight">
                      <div className="activity-header">
                        <h3>Producto Más Vendido</h3>
                        <span className="activity-count">{mostSold.vendidos} ventas</span>
                      </div>
                      <div className="activity-content">
                        <div className="featured-product">
                          <div className="product-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                            </svg>
                          </div>
                          <div className="product-info">
                            <div className="product-number">{mostSold.numero_almacen}</div>
                            <div className="product-brand">{mostSold.marcas}</div>
                            <div className="product-model">{mostSold.modelo_existente.substring(0, 40)}...</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
    </div>
  );
}
