'use client';

import { useState } from 'react';
import { FaCheck } from 'react-icons/fa';
import { ScrollAnimation, StaggerAnimation, TextReveal } from './ScrollAnimation';

interface ProcessItem {
  name: string;
}

interface ServiceItem {
  name: string;
}

export default function WhyEccsaSection() {
  const [activeTab, setActiveTab] = useState<'processes' | 'services'>('processes');

  const processes: ProcessItem[] = [
    { name: 'Máquinas embutidoras' },
    { name: 'Máquinas trituradoras' },
    { name: 'Máquinas cortadoras' },
    { name: 'Hornos industriales' },
    { name: 'System Batch' },
    { name: 'Diseño de control de líneas' },
    { name: 'Ensamble con soldadura punteadora' },
    { name: 'Tenso nivelado' },
    { name: '<PERSON>bretead<PERSON>' },
    { name: 'Slitter' },
    { name: 'Cortadoras industriales' },
    { name: 'Dobladoras' },
    { name: '<PERSON><PERSON><PERSON>' },
    { name: '<PERSON>ari<PERSON>' },
    { name: 'Control de prensas' },
    { name: 'Metal-Mecánica' },
    { name: 'Sistemas mezcladores de arena y vidrio' },
    { name: 'Máquinas sopladoras de arena y vidrio' },
    { name: 'Líneas de corte de vidrio' },
    { name: 'Máquina humectadora' },
    { name: 'Máquinas sopladoras' },
    { name: 'Máquinas inyectoras' },
    { name: 'Máquinas extrusoras' },
    { name: 'Plástico-papel' }
  ];

  const services: ServiceItem[] = [
    { name: 'Desarrollo de ingeniería para la automatización de procesos' },
    { name: 'Modernización de control de maquinaria' },
    { name: 'Servicio especializado de ingeniería para puesta en marcha de sistemas automatizados' },
    { name: 'Soporte técnico especializado' },
    { name: 'Consultoría para diagnóstico de fallas en procesos automatizados' },
    { name: 'Asesoría para selección de equipo de control industrial' },
    { name: 'Instalaciones eléctricas industriales certificadas' },
    { name: 'Programas de capacitación sobre nuestras aplicaciones' }
  ];

  return (
    <ScrollAnimation animation="fade-in" delay={100}>
      <section className="why-eccsa-section">
        <div className="why-eccsa-container">
          <div className="why-eccsa-flex-layout">
            <ScrollAnimation animation="slide-right" delay={200}>
              <div className="why-eccsa-question">
                <TextReveal>
                  <h2 className="modern-title">¿Por qué ECCSA?</h2>
                </TextReveal>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="slide-left" delay={400}>
              <div className="why-eccsa-content-wrapper">
                <StaggerAnimation
                  staggerDelay={150}
                  className="why-eccsa-tabs"
                >
                  <button
                    className={`why-eccsa-tab ${activeTab === 'processes' ? 'active' : ''}`}
                    onClick={() => setActiveTab('processes')}
                  >
                    Conocimientos de procesos
                  </button>
                  <button
                    className={`why-eccsa-tab ${activeTab === 'services' ? 'active' : ''}`}
                    onClick={() => setActiveTab('services')}
                  >
                    Servicios para la industria
                  </button>
                </StaggerAnimation>

                <ScrollAnimation animation="slide-up" delay={600}>
                  <div className="why-eccsa-content">
                    {activeTab === 'processes' ? (
                      <StaggerAnimation
                        staggerDelay={100}
                        className="why-eccsa-grid"
                      >
                        {processes.map((process, index) => (
                          <div key={index} className="why-eccsa-item scroll-hover-enhance">
                            <div className="why-eccsa-check"><FaCheck /></div>
                            <div className="why-eccsa-text">{process.name}</div>
                          </div>
                        ))}
                      </StaggerAnimation>
                    ) : (
                      <StaggerAnimation
                        staggerDelay={100}
                        className="why-eccsa-grid"
                      >
                        {services.map((service, index) => (
                          <div key={index} className="why-eccsa-item scroll-hover-enhance">
                            <div className="why-eccsa-check"><FaCheck /></div>
                            <div className="why-eccsa-text">{service.name}</div>
                          </div>
                        ))}
                      </StaggerAnimation>
                    )}
                  </div>
                </ScrollAnimation>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>
    </ScrollAnimation>
  );
}
