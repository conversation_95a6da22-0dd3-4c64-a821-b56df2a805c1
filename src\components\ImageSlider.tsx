'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import ImageWithFallback from './ImageWithFallback';
import { IMAGES, ANIMATION, MAIN_ROUTES } from '@/constants';
import { ScrollAnimation, StaggerAnimation, CounterAnimation, Parallax } from './ScrollAnimation';

interface SlideImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
  ctaLink?: string;
  ctaText?: string;
}

interface ImageSliderProps {
  images: SlideImage[];
  autoPlay?: boolean;
  interval?: number;
}

/**
 * Image slider component with touch support and navigation controls
 * Displays a slideshow of images with optional captions
 */
export default function ImageSlider({
  images,
  autoPlay = true,
  interval = ANIMATION.SLIDER_INTERVAL
}: ImageSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Go to a specific slide
  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  // Go to previous slide
  const goToPrevious = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  }, [images.length]);

  // Go to next slide
  const goToNext = useCallback(() => {
    setCurrentIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  }, [images.length]);

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay || isPaused || images.length <= 1) return;

    const timer = setInterval(goToNext, interval);
    return () => clearInterval(timer);
  }, [autoPlay, isPaused, interval, images.length, goToNext]);

  // Touch event handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    const touchDiff = touchStart - touchEnd;

    if (Math.abs(touchDiff) > 50) {
      if (touchDiff > 0) {
        // Swipe left (next)
        goToNext();
      } else {
        // Swipe right (previous)
        goToPrevious();
      }
    }

    // Reset values
    setTouchStart(0);
    setTouchEnd(0);
  };

  // Pause auto-play on hover
  const handleMouseEnter = () => setIsPaused(true);
  const handleMouseLeave = () => setIsPaused(false);

  if (!images || images.length === 0) return null;

  const currentImage = images[currentIndex];
  const ctaLink = currentImage.ctaLink || MAIN_ROUTES.SERVICES;
  const ctaText = currentImage.ctaText || 'Conozca Nuestros Servicios';

  return (
    <div className="modern-hero-section">
      {/* Fondo con gradiente sutil */}
      <div className="hero-background">
        <div className="hero-gradient-overlay"></div>
        <div className="hero-pattern"></div>
      </div>

      {/* Contenido principal */}
      <div className="hero-container">
        <div className="hero-content-wrapper">
          {/* Lado izquierdo - Contenido principal */}
          <div className="hero-content-left">
            {/* Badge de empresa */}
            <div className="hero-company-badge">
              <div className="badge-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                  <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
              </div>
              <span>Líderes en Automatización Industrial</span>
            </div>

            {/* Título principal */}
            <h1 className="hero-main-title">
              <span className="title-line">Automatización</span>
              <span className="title-line title-highlight">Industrial</span>
              <span className="title-line">del Futuro</span>
            </h1>

            {/* Descripción */}
            <p className="hero-description">
              Transformamos su industria con tecnología de vanguardia, soluciones innovadoras
              y más de 25 años de experiencia liderando el mercado mexicano.
            </p>

            {/* Métricas y botones en layout horizontal compacto */}
            <div className="hero-bottom-section">
              <div className="hero-stats-compact">
                <div className="stat-item">
                  <CounterAnimation
                    endValue={25}
                    className="stat-number"
                    duration={2000}
                  />
                  <span className="stat-label">Años de Experiencia</span>
                </div>
                <div className="stat-item">
                  <CounterAnimation
                    endValue={500}
                    className="stat-number"
                    duration={2500}
                  />
                  <span className="stat-label">Proyectos Completados</span>
                </div>
                <div className="stat-item">
                  <CounterAnimation
                    endValue={150}
                    className="stat-number"
                    duration={2200}
                  />
                  <span className="stat-label">Clientes Satisfechos</span>
                </div>
              </div>

              {/* Botones de acción */}
              <div className="hero-actions">
                <Link href="/products" className="btn btn-primary">
                  <span>Explorar Productos</span>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="m9 18 6-6-6-6"/>
                  </svg>
                </Link>
                <Link href="/contact" className="btn btn-secondary">
                  <span>Contactar Ahora</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Lado derecho - Layout horizontal con logo y marcas */}
          <div className="hero-content-right">
            <div className="hero-right-horizontal">
              {/* Logo principal de ECCSA - Engranaje Industrial Futurista */}
              <div className="hero-industrial-gear-container">
                {/* Anillos giratorios externos */}
                <div className="hero-gear-ring ring-outer">
                  <div className="gear-teeth">⚙️⚙️⚙️⚙️⚙️⚙️⚙️⚙️</div>
                </div>
                <div className="hero-gear-ring ring-middle">
                  <div className="gear-teeth">🔧🔧🔧🔧🔧🔧</div>
                </div>
                <div className="hero-gear-ring ring-inner">
                  <div className="gear-particles">✦✦✦✦✦✦✦✦</div>
                </div>

                {/* Efectos de resplandor */}
                <div className="hero-gear-glow-outer"></div>
                <div className="hero-gear-glow-inner"></div>

                {/* Logo central */}
                <div className="hero-gear-center">
                  <div className="hero-gear-logo-wrapper">
                    <img
                      src="/images/logos/logo_pequeno.png"
                      alt="ECCSA Logo"
                      className="hero-gear-logo"
                    />
                  </div>
                  <div className="hero-gear-pulse"></div>
                </div>

                {/* Engranajes flotantes decorativos */}
                <div className="hero-floating-gear gear-float-1">⚙️</div>
                <div className="hero-floating-gear gear-float-2">🔧</div>
                <div className="hero-floating-gear gear-float-3">⚙️</div>
                <div className="hero-floating-gear gear-float-4">🔧</div>
                <div className="hero-floating-gear gear-float-5">⚙️</div>
              </div>

              {/* Logos de marcas en layout horizontal */}
              <div className="brand-logos-section-horizontal">
                <div className="brand-logos-title">
                  <span>Marcas Líderes</span>
                </div>
                <div className="brand-logos-horizontal">
                  <div className="brand-logo-item">
                    <div className="brand-logo-circle">
                      <img
                        src="/images/Marcas/1.png"
                        alt="Siemens"
                        className="brand-logo-img"
                      />
                    </div>
                    <span className="brand-name">Siemens</span>
                  </div>
                  <div className="brand-logo-item">
                    <div className="brand-logo-circle">
                      <img
                        src="/images/Marcas/2.png"
                        alt="Allen-Bradley"
                        className="brand-logo-img"
                      />
                    </div>
                    <span className="brand-name">Allen-Bradley</span>
                  </div>
                  <div className="brand-logo-item">
                    <div className="brand-logo-circle">
                      <img
                        src="/images/Marcas/3.png"
                        alt="Omron"
                        className="brand-logo-img"
                      />
                    </div>
                    <span className="brand-name">Omron</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
