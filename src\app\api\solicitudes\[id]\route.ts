import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// PUT - Actualizar solicitud
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();
    const { estado, notas, tiempo_entrega_estimado } = body;

    // Validar ID
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'ID de solicitud inválido' },
        { status: 400 }
      );
    }

    // Validar estado
    const estadosValidos = ['pendiente', 'procesando', 'cotizado', 'rechazado'];
    if (estado && !estadosValidos.includes(estado)) {
      return NextResponse.json(
        { success: false, error: 'Estado inválido' },
        { status: 400 }
      );
    }

    // Construir query de actualización dinámicamente
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (estado) {
      updateFields.push('estado = ?');
      updateValues.push(estado);
    }

    if (notas !== undefined) {
      updateFields.push('notas = ?');
      updateValues.push(notas);
    }

    if (tiempo_entrega_estimado !== undefined) {
      updateFields.push('tiempo_entrega_estimado = ?');
      updateValues.push(tiempo_entrega_estimado);
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No hay campos para actualizar' },
        { status: 400 }
      );
    }

    // Agregar timestamp de actualización
    updateFields.push('fecha_actualizacion = CURRENT_TIMESTAMP');
    updateValues.push(id);

    const updateQuery = `
      UPDATE solicitudes
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    const result = await executeQuery({
      query: updateQuery,
      values: updateValues
    });

    // Verificar si se actualizó algún registro
    const affectedRows = (result as any)?.affectedRows || 0;

    if (affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Solicitud actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error al actualizar solicitud:', error);
    return NextResponse.json(
      { success: false, error: 'Error al actualizar solicitud' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar solicitud
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // Validar ID
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'ID de solicitud inválido' },
        { status: 400 }
      );
    }

    // Verificar que la solicitud existe antes de eliminar
    const checkQuery = 'SELECT id FROM solicitudes WHERE id = ?';
    const existingSolicitud = await executeQuery({
      query: checkQuery,
      values: [id]
    });

    if (!Array.isArray(existingSolicitud) || existingSolicitud.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    // Eliminar solicitud
    const deleteQuery = 'DELETE FROM solicitudes WHERE id = ?';
    const result = await executeQuery({
      query: deleteQuery,
      values: [id]
    });

    const affectedRows = (result as any)?.affectedRows || 0;

    if (affectedRows === 0) {
      return NextResponse.json(
        { success: false, error: 'No se pudo eliminar la solicitud' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Solicitud eliminada exitosamente'
    });

  } catch (error) {
    console.error('Error al eliminar solicitud:', error);
    return NextResponse.json(
      { success: false, error: 'Error al eliminar solicitud' },
      { status: 500 }
    );
  }
}

// GET - Obtener solicitud específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // Validar ID
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'ID de solicitud inválido' },
        { status: 400 }
      );
    }

    const query = `
      SELECT
        s.*,
        a.modelo_existente,
        a.marcas,
        a.descripcion as descripcion_producto,
        a.cantidad_nuevo as stock_actual
      FROM solicitudes s
      LEFT JOIN almacen a ON s.numero_almacen = a.numero_almacen
      WHERE s.id = ?
    `;

    const result = await executeQuery({
      query,
      values: [id]
    });

    if (!Array.isArray(result) || result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      solicitud: result[0]
    });

  } catch (error) {
    console.error('Error al obtener solicitud:', error);
    return NextResponse.json(
      { success: false, error: 'Error al obtener solicitud' },
      { status: 500 }
    );
  }
}
