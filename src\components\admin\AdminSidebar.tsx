'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { User } from '@/hooks/useAuth';
import UserProfileModal from './UserProfileModal';
import LogoutModal from './LogoutModal';
import LogoutLoadingModal from './LogoutLoadingModal';
import { useSidebar } from '@/contexts/SidebarContext';
import { FaHome, FaBox, FaDollarSign, FaClipboardList, FaUsers, FaSignOutAlt, FaBars, FaTimes } from 'react-icons/fa';

interface AdminSidebarProps {
  user: User;
  onLogout: () => void;
  onUserUpdate?: (updatedUser: User) => void;
}

export default function AdminSidebar({ user, onLogout, onUserUpdate }: AdminSidebarProps) {
  const { isCollapsed, setIsCollapsed, isMobileOpen, setIsMobileOpen } = useSidebar();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showLogoutLoading, setShowLogoutLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const getRoleDisplayName = (role: string, roleLevel: number) => {
    switch (roleLevel) {
      case 0: return 'Administrador';
      case 1: return 'Jefe';
      case 2: return 'Senior';
      case 3: return 'Semi-Senior';
      case 4: return 'Junior';
      default: return role || 'Usuario';
    }
  };

  const getRoleColor = (roleLevel: number) => {
    switch (roleLevel) {
      case 0: return '#ef4444';
      case 1: return '#f97316';
      case 2: return '#eab308';
      case 3: return '#22c55e';
      case 4: return '#3b82f6';
      default: return '#6b7280';
    }
  };

  const menuItems = [
    {
      title: 'Dashboard',
      icon: FaHome,
      path: '/eccsa/admin',
      description: 'Panel principal'
    },
    {
      title: 'Almacén',
      icon: FaBox,
      path: '/eccsa/admin/almacen',
      description: 'Gestión de productos'
    },
    {
      title: 'Lista de Precios',
      icon: FaDollarSign,
      path: '/eccsa/admin/precios',
      description: 'Gestión de precios'
    },
    {
      title: 'Solicitudes',
      icon: FaClipboardList,
      path: '/eccsa/admin/solicitudes',
      description: 'Cotizaciones'
    },
    {
      title: 'Empleados',
      icon: FaUsers,
      path: '/eccsa/admin/empleados',
      description: 'Gestión de personal'
    }
  ];

  const handleNavigation = (path: string) => {
    router.push(path);
    setIsMobileOpen(false);
  };

  const handleLogout = () => {
    if (isLoggingOut) return;
    setShowLogoutModal(true);
  };

  const confirmLogout = async () => {
    setShowLogoutModal(false);
    setShowLogoutLoading(true);
    setIsLoggingOut(true);

    try {
      await onLogout();
    } catch (error) {
      console.error('Error during logout:', error);
      setIsLoggingOut(false);
      setShowLogoutLoading(false);
    }
  };

  const cancelLogout = () => {
    setShowLogoutModal(false);
  };

  const handleUserUpdate = (updatedUser: any) => {
    if (onUserUpdate) {
      onUserUpdate(updatedUser);
    }
  };

  return (
    <>
      {/* Mobile Toggle Button */}
      <button
        className="glassmorphism-mobile-toggle"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
        style={{
          position: 'fixed',
          top: '1rem',
          left: '1rem',
          zIndex: 1001,
          width: '50px',
          height: '50px',
          border: 'none',
          borderRadius: '12px',
          background: 'rgba(255, 255, 255, 0.15)',
          backdropFilter: 'blur(10px)',
          color: '#475569',
          cursor: 'pointer',
          display: 'none',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '1.25rem',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease'
        }}
      >
        {isMobileOpen ? <FaTimes /> : <FaBars />}
      </button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 999,
            backdropFilter: 'blur(4px)'
          }}
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Glassmorphism Sidebar */}
      <div className={`glassmorphism-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`}>
        {/* Header */}
        <div className="glassmorphism-header">
          <div className="glassmorphism-brand">
            <div className="glassmorphism-logo-container">
              <img
                src="/images/logos/logo_pequeno.png"
                alt="ECCSA Logo"
                className="glassmorphism-logo"
              />
            </div>

            <div className="glassmorphism-brand-text">
              <h3 className="glassmorphism-brand-title">ECCSA</h3>
              <p className="glassmorphism-brand-subtitle">Admin Panel</p>
            </div>
          </div>

          <button
            className="glassmorphism-toggle"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? '→' : '←'}
          </button>
        </div>

        {/* Navigation Menu */}
        <div className="glassmorphism-nav-container">
          <nav className="glassmorphism-nav">
            <ul className="glassmorphism-nav-list">
              {menuItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <li key={item.path} className="glassmorphism-nav-item">
                    <button
                      className={`glassmorphism-nav-link ${pathname === item.path ? 'active' : ''}`}
                      onClick={() => handleNavigation(item.path)}
                      title={isCollapsed ? item.title : ''}
                    >
                      <div className="glassmorphism-nav-icon-container">
                        <IconComponent className="glassmorphism-nav-icon" />
                      </div>

                      <div className="glassmorphism-nav-text">
                        <div>{item.title}</div>
                        <div className="glassmorphism-nav-description">{item.description}</div>
                      </div>
                    </button>
                  </li>
                );
              })}
            </ul>
          </nav>
        </div>

        {/* User Profile Section */}
        <div className="glassmorphism-user-section">
          <div
            className="glassmorphism-user-profile"
            onClick={() => setIsProfileModalOpen(true)}
            title="Configurar perfil"
          >
            <div className="glassmorphism-user-avatar">
              {user.photo ? (
                <img
                  src={user.photo}
                  alt={user.name}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '12px',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                user.name.charAt(0).toUpperCase()
              )}
            </div>

            <div className="glassmorphism-user-info">
              <div className="glassmorphism-user-name">{user.name}</div>
              <div className="glassmorphism-user-role">
                {getRoleDisplayName(user.role, user.role_level)}
              </div>
            </div>
          </div>

          {/* Logout Button */}
          <button
            className="glassmorphism-logout"
            onClick={handleLogout}
            disabled={isLoggingOut}
            title={isCollapsed ? 'Cerrar Sesión' : ''}
          >
            <FaSignOutAlt />
            <span className="glassmorphism-logout-text">
              {isLoggingOut ? 'Cerrando...' : 'Cerrar Sesión'}
            </span>
          </button>
        </div>

      </div>

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
        user={{
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: getRoleDisplayName(user.role, user.role_level),
          role_level: user.role_level,
          description: user.description,
          photo: user.photo
        }}
        onUserUpdate={handleUserUpdate}
      />

      {/* Logout Confirmation Modal */}
      <LogoutModal
        isOpen={showLogoutModal}
        onClose={cancelLogout}
        onConfirm={confirmLogout}
        loading={isLoggingOut}
      />

      {/* Logout Loading Modal */}
      <LogoutLoadingModal
        isVisible={showLogoutLoading}
        message="Cerrando sesión y limpiando datos..."
      />

      {/* CSS for mobile responsiveness */}
      <style jsx>{`
        @media (max-width: 768px) {
          .glassmorphism-mobile-toggle {
            display: flex !important;
          }
        }
      `}</style>
    </>
  );
}
