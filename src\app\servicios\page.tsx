'use client';

import React, { useState } from 'react';
import Image from 'next/image';

export default function ServiciosPage() {
  const [activeService, setActiveService] = useState('Automatización');

  const services = [
    {
      id: 'Automatización',
      name: 'Automatización Industrial',
      icon: '⚙️',
      shortDesc: 'Soluciones completas de automatización para procesos industriales',
      image: '/images/defaults/Automatización-Industrial.jpg'
    },
    {
      id: 'Programación',
      name: 'Programación de PLC',
      icon: '💻',
      shortDesc: 'Desarrollo de software especializado para control industrial',
      image: '/images/defaults/PROGRAMACION-PLC.jpeg'
    },
    {
      id: 'SCADA',
      name: 'Sistemas SCADA',
      icon: '📊',
      shortDesc: 'Monitoreo y control de procesos en tiempo real',
      image: '/images/defaults/sistema-scada-gas-natual-eisenberg-monterrey.jpg'
    },
    {
      id: 'Mantenimiento',
      name: 'Mantenimiento Especializado',
      icon: '🔧',
      shortDesc: 'Servicios de mantenimiento preventivo y correctivo',
      image: '/images/defaults/Mantenimiento-Industrial.jpg'
    },
    {
      id: 'Instrumentación',
      name: 'Instrumentación',
      icon: '📏',
      shortDesc: 'Instalación y calibración de instrumentos de medición',
      image: '/images/defaults/sensor-omron.jpg'
    },
    {
      id: 'Capacitación',
      name: 'Capacitación Técnica',
      icon: '🎓',
      shortDesc: 'Formación especializada en automatización industrial',
      image: '/images/defaults/industrial-experience.jpg'
    }
  ];

  interface ServiceContent {
    title: string;
    subtitle: string;
    description: string;
    features: string[];
    benefits: string[];
    applications: string[];
  }

  // Contenido para cada servicio
  const serviceContent: Record<string, ServiceContent> = {
    Automatización: {
      title: 'Automatización Industrial',
      subtitle: 'Transformamos procesos industriales con tecnología de vanguardia',
      description: 'ECCSA desarrolla soluciones integrales de automatización para procesos industriales. Utilizamos la mejor tecnología y los más exigentes estándares internacionales, proporcionando a nuestros clientes altos niveles de productividad y calidad con la mejor relación costo-beneficio.',
      features: [
        'Diseño e implementación de sistemas PLC',
        'Integración de variadores de frecuencia',
        'Sistemas de arranque y control de motores',
        'Centros de control de motores (CCM)',
        'Redes industriales de comunicación',
        'Sistemas de seguridad industrial'
      ],
      benefits: [
        'Incremento en la productividad',
        'Reducción de costos operativos',
        'Mejora en la calidad del producto',
        'Mayor seguridad industrial',
        'Optimización de recursos'
      ],
      applications: [
        'Industria manufacturera',
        'Procesos químicos y petroquímicos',
        'Industria alimentaria',
        'Minería y metalurgia',
        'Tratamiento de aguas'
      ]
    },
    Programación: {
      title: 'Programación de PLC',
      subtitle: 'Software especializado para control industrial de todas las marcas',
      description: 'Desarrollamos software para control industrial adaptado a las necesidades específicas de cada cliente. Trabajamos con todas las marcas principales de PLC y sistemas de control, garantizando funcionamiento óptimo y soporte completo.',
      features: [
        'Programación en múltiples lenguajes (Ladder, ST, FBD)',
        'Integración con sistemas SCADA/HMI',
        'Comunicación industrial (Ethernet, Profibus, Modbus)',
        'Sistemas de seguridad funcional',
        'Optimización de código existente',
        'Documentación técnica completa'
      ],
      benefits: [
        'Compatibilidad con todas las marcas',
        'Código optimizado y eficiente',
        'Fácil mantenimiento y modificación',
        'Respaldo técnico especializado',
        'Actualizaciones y mejoras continuas'
      ],
      applications: [
        'Líneas de producción automatizadas',
        'Sistemas de control de procesos',
        'Máquinas especiales',
        'Sistemas de transporte y manejo',
        'Control de calidad automatizado'
      ]
    },
    SCADA: {
      title: 'Sistemas SCADA',
      subtitle: 'Monitoreo y control de procesos en tiempo real',
      description: 'Implementamos sistemas SCADA (Supervisory Control and Data Acquisition) que permiten el monitoreo, control y adquisición de datos de procesos industriales en tiempo real, proporcionando información valiosa para la toma de decisiones.',
      features: [
        'Interfaces gráficas intuitivas',
        'Monitoreo en tiempo real',
        'Sistemas de alarmas y eventos',
        'Reportes automáticos y históricos',
        'Acceso remoto seguro',
        'Integración con bases de datos'
      ],
      benefits: [
        'Visibilidad completa del proceso',
        'Respuesta rápida a eventos',
        'Optimización operacional',
        'Reducción de tiempos de parada',
        'Mejora en la toma de decisiones'
      ],
      applications: [
        'Plantas de tratamiento de agua',
        'Sistemas de distribución eléctrica',
        'Procesos de manufactura',
        'Sistemas de transporte',
        'Monitoreo ambiental'
      ]
    },
    Mantenimiento: {
      title: 'Mantenimiento Especializado',
      subtitle: 'Servicios de mantenimiento preventivo y correctivo',
      description: 'Ofrecemos servicios especializados de mantenimiento para equipos de automatización industrial, garantizando el funcionamiento óptimo de sus sistemas y minimizando los tiempos de parada no programados.',
      features: [
        'Mantenimiento preventivo programado',
        'Diagnóstico y reparación de fallas',
        'Actualización de software y firmware',
        'Calibración de instrumentos',
        'Reemplazo de componentes',
        'Soporte técnico 24/7'
      ],
      benefits: [
        'Reducción de paradas no programadas',
        'Extensión de vida útil de equipos',
        'Optimización del rendimiento',
        'Reducción de costos de mantenimiento',
        'Mayor confiabilidad del sistema'
      ],
      applications: [
        'Sistemas de automatización existentes',
        'Equipos de control y medición',
        'Redes de comunicación industrial',
        'Sistemas de seguridad',
        'Equipos de instrumentación'
      ]
    },
    Instrumentación: {
      title: 'Instrumentación',
      subtitle: 'Instalación y calibración de instrumentos de medición y control',
      description: 'Proporcionamos servicios completos de instrumentación industrial, incluyendo la selección, instalación, calibración y mantenimiento de instrumentos de medición y control de procesos.',
      features: [
        'Selección de instrumentos apropiados',
        'Instalación y puesta en marcha',
        'Calibración con estándares certificados',
        'Integración con sistemas de control',
        'Documentación y certificación',
        'Mantenimiento especializado'
      ],
      benefits: [
        'Mediciones precisas y confiables',
        'Cumplimiento de normativas',
        'Optimización de procesos',
        'Reducción de desperdicios',
        'Mejora en la calidad del producto'
      ],
      applications: [
        'Control de temperatura y presión',
        'Medición de flujo y nivel',
        'Análisis de gases y líquidos',
        'Control de pH y conductividad',
        'Sistemas de pesaje industrial'
      ]
    },
    Capacitación: {
      title: 'Capacitación Técnica',
      subtitle: 'Formación especializada en automatización industrial',
      description: 'Ofrecemos programas de capacitación técnica especializada para personal de mantenimiento, operación e ingeniería, cubriendo todos los aspectos de la automatización industrial moderna.',
      features: [
        'Cursos teóricos y prácticos',
        'Capacitación en sitio o en nuestras instalaciones',
        'Programas personalizados',
        'Certificaciones técnicas',
        'Material didáctico especializado',
        'Seguimiento post-capacitación'
      ],
      benefits: [
        'Personal mejor capacitado',
        'Reducción de errores operativos',
        'Mayor eficiencia en mantenimiento',
        'Mejor aprovechamiento de tecnología',
        'Desarrollo profesional continuo'
      ],
      applications: [
        'Programación de PLC',
        'Sistemas SCADA/HMI',
        'Redes industriales',
        'Instrumentación y control',
        'Seguridad industrial'
      ]
    }
  };

  const currentService = serviceContent[activeService];

  return (
    <div className="modern-services-page">
        {/* Hero Section */}
        <div className="services-hero">
          <div className="services-hero-content">
            <div className="hero-badge">
              <span className="badge-icon">🔧</span>
              <span>Servicios Profesionales</span>
            </div>
            <h1 className="services-hero-title">
              Nuestros <span className="title-highlight">Servicios</span>
            </h1>
            <p className="services-hero-description">
              Soluciones integrales de automatización industrial con más de 25 años de experiencia.
              Transformamos procesos industriales con tecnología de vanguardia y soporte especializado.
            </p>
            <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">25+</span>
                <span className="stat-label">Años de Experiencia</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">500+</span>
                <span className="stat-label">Proyectos Completados</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">100+</span>
                <span className="stat-label">Clientes Satisfechos</span>
              </div>
            </div>
          </div>
          <div className="hero-decoration">
            <div className="decoration-circle decoration-circle-1"></div>
            <div className="decoration-circle decoration-circle-2"></div>
            <div className="decoration-circle decoration-circle-3"></div>
          </div>
        </div>

        {/* Services Navigation */}
        <div className="services-navigation-section">
          <div className="services-nav-container">
            <h2 className="nav-title">Explora Nuestros Servicios</h2>
            <div className="services-nav-grid">
              {services.map((service) => (
                <button
                  key={service.id}
                  className={`modern-service-nav-card ${activeService === service.id ? 'active' : ''}`}
                  onClick={() => setActiveService(service.id)}
                >
                  <div className="nav-card-icon">{service.icon}</div>
                  <h3 className="nav-card-title">{service.name}</h3>
                  <p className="nav-card-description">{service.shortDesc}</p>
                  <div className="nav-card-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Service Detail Section */}
        <div className="service-detail-section">
          <div className="service-detail-container">
            <div className="service-detail-grid">
              {/* Service Image */}
              <div className="service-image-container">
                <div className="service-image-wrapper">
                  <Image
                    src={services.find(s => s.id === activeService)?.image || '/images/defaults/default-service.jpg'}
                    alt={currentService.title}
                    width={600}
                    height={400}
                    className="service-detail-image"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                    }}
                  />
                  <div className="service-image-overlay">
                    <div className="service-badge">
                      <span className="service-badge-icon">{services.find(s => s.id === activeService)?.icon}</span>
                      <span className="service-badge-text">Servicio Especializado</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Service Content */}
              <div className="service-content-container">
                <div className="service-content-header">
                  <h2 className="service-detail-title">{currentService.title}</h2>
                  <p className="service-detail-subtitle">{currentService.subtitle}</p>
                </div>

                <div className="service-description">
                  <p>{currentService.description}</p>
                </div>

                {/* Features */}
                <div className="service-features">
                  <h3 className="features-title">Características Principales</h3>
                  <div className="features-grid">
                    {currentService.features.map((feature, index) => (
                      <div key={index} className="feature-item">
                        <div className="feature-icon">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="20,6 9,17 4,12"></polyline>
                          </svg>
                        </div>
                        <span className="feature-text">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Benefits */}
                <div className="service-benefits">
                  <h3 className="benefits-title">Beneficios</h3>
                  <div className="benefits-grid">
                    {currentService.benefits.map((benefit, index) => (
                      <div key={index} className="benefit-item">
                        <div className="benefit-icon">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                          </svg>
                        </div>
                        <span className="benefit-text">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Applications */}
                <div className="service-applications">
                  <h3 className="applications-title">Aplicaciones</h3>
                  <div className="applications-grid">
                    {currentService.applications.map((application, index) => (
                      <div key={index} className="application-item">
                        <div className="application-icon">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                          </svg>
                        </div>
                        <span className="application-text">{application}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <div className="service-cta">
                  <button className="service-cta-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                    Solicitar Cotización
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact CTA Section */}
        <div className="services-cta-section">
          <div className="cta-container">
            <div className="cta-content">
              <div className="cta-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <h3 className="cta-title">¿Necesitas una solución personalizada?</h3>
              <p className="cta-description">
                Nuestro equipo de expertos está listo para desarrollar la solución perfecta para tu proyecto de automatización industrial.
              </p>
              <div className="cta-actions">
                <button className="cta-primary-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                  </svg>
                  Contactar Especialista
                </button>
                <button className="cta-secondary-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  Enviar Consulta
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
}
