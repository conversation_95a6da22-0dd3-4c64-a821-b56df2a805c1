import type { Metadata } from 'next';
import './globals.css';
import './styles.css';
import './admin-styles.css';
import ConditionalNavbar from '@/components/ConditionalNavbar';
import ConditionalMainContent from '@/components/ConditionalMainContent';
import Footer from '@/components/Footer';
import PageTransition from '@/components/PageTransition';
import WhatsAppButton from '@/components/WhatsAppButton';
import Cart from '@/components/Cart';
import ConditionalSidebar from '@/components/ConditionalSidebar';
import { SidebarProvider } from '@/contexts/SidebarContext';
import { IMAGES, CONTACT_INFO, DEFAULT_MESSAGES } from '@/constants';

/**
 * Metadata for the website
 */
export const metadata: Metadata = {
  title: 'Eccsa - Automatización Industrial',
  description: 'Soluciones en automatización y control industrial',
  icons: [
    { rel: 'icon', url: IMAGES.LOGO.SMALL },
    { rel: 'apple-touch-icon', url: IMAGES.LOGO.SMALL },
    { rel: 'shortcut icon', url: IMAGES.LOGO.SMALL }
  ],
};

/**
 * Root layout component for the entire application
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body style={{
        margin: 0,
        padding: 0,
        backgroundColor: '#ffffff',
        minHeight: '100vh',
        color: '#333333',
        width: '100%',
        overflowX: 'hidden'
      }}>
        <SidebarProvider>
          <ConditionalSidebar />
          <ConditionalNavbar />
          <ConditionalMainContent>
            <PageTransition>
              {children}
            </PageTransition>
          </ConditionalMainContent>
          <Footer />
          <WhatsAppButton
            phoneNumber={CONTACT_INFO.WHATSAPP_SALES}
            message={DEFAULT_MESSAGES.WHATSAPP}
          />
          <Cart />
        </SidebarProvider>
      </body>
    </html>
  );
}
