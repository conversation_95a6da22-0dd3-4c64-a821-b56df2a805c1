'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface AuthGuardProps {
  children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    // Verificar si el usuario está autenticado
    const checkAuth = async () => {
      try {
        // Verificar autenticación con el servidor
        const response = await fetch('/api/auth/login', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          if (data.authenticated) {
            setIsAuthenticated(true);
            return;
          }
        }

        // Si no está autenticado por cookies, verificar localStorage como fallback
        const savedAuth = localStorage.getItem('eccsaIsAuthenticated');
        const savedUserData = localStorage.getItem('eccsaUserData');

        if (savedAuth === 'true' && savedUserData) {
          try {
            // Verificar que los datos del usuario son válidos
            JSON.parse(savedUserData);
            setIsAuthenticated(true);
            return;
          } catch (error) {
            console.error('Error parsing saved user data:', error);
            // Limpiar datos corruptos
            localStorage.removeItem('eccsaUserData');
            localStorage.removeItem('eccsaIsAuthenticated');
          }
        }

        // Si no hay autenticación válida, redirigir al login
        setIsAuthenticated(false);
        router.push('/eccsa/productos/almacen/admin');

      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        router.push('/eccsa/productos/almacen/admin');
      }
    };

    checkAuth();
  }, [router]);

  // Mostrar un estado de carga mientras se verifica la autenticación
  if (isAuthenticated === null) {
    return (
      <div className="auth-loading">
        <div className="auth-loading-spinner"></div>
        <p>Verificando autenticación...</p>
      </div>
    );
  }

  // Si el usuario está autenticado, mostrar el contenido
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Si el usuario no está autenticado, no mostrar nada (ya se redirigió)
  return null;
}
