'use client';

import React, { useState, useEffect } from 'react';

interface CartItem {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  marcas?: string;
  Url_imagen?: string;
  stock_disponible: number;
  cantidad: number;
  sin_stock?: boolean;
  mensaje_stock?: string;
  excede_stock?: boolean;
  cantidad_excedente?: number;
}

interface CotizacionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: { nombre: string; email: string; whatsapp?: string }) => void;
  loading: boolean;
  cartItems: CartItem[];
  total: number;
}

export default function CarritoPage() {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCotizacionModal, setShowCotizacionModal] = useState(false);
  const [cotizacionLoading, setCotizacionLoading] = useState(false);

  // Estados para modal de éxito
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successData, setSuccessData] = useState<{
    solicitud_grupo_id: string;
    total_productos: number;
    total_con_iva: number;
    cliente_email: string;
  } | null>(null);

  useEffect(() => {
    // Cargar items del carrito desde localStorage
    const savedCart = localStorage.getItem('eccsa-cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setCartItems(parsedCart);
      } catch (error) {
        console.error('Error parsing cart:', error);
        setCartItems([]);
      }
    }
    setLoading(false);

    // Escuchar cambios en el carrito
    const handleCartUpdate = () => {
      const savedCart = localStorage.getItem('eccsa-cart');
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
        } catch (error) {
          console.error('Error parsing cart:', error);
          setCartItems([]);
        }
      } else {
        setCartItems([]);
      }
    };

    window.addEventListener('cartUpdated', handleCartUpdate);
    return () => window.removeEventListener('cartUpdated', handleCartUpdate);
  }, []);

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }

    const updatedItems = cartItems.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, cantidad: newQuantity };

        // Marcar si excede el stock para mostrar mensaje especial
        if (!item.sin_stock && newQuantity > item.stock_disponible) {
          updatedItem.excede_stock = true;
          updatedItem.cantidad_excedente = newQuantity - item.stock_disponible;
        } else {
          updatedItem.excede_stock = false;
          updatedItem.cantidad_excedente = 0;
        }

        return updatedItem;
      }
      return item;
    });

    setCartItems(updatedItems);
    localStorage.setItem('eccsa-cart', JSON.stringify(updatedItems));
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  const removeItem = (id: number) => {
    const updatedItems = cartItems.filter(item => item.id !== id);
    setCartItems(updatedItems);
    localStorage.setItem('eccsa-cart', JSON.stringify(updatedItems));
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  const clearCart = () => {
    setCartItems([]);
    localStorage.removeItem('eccsa-cart');
    window.dispatchEvent(new CustomEvent('cartUpdated'));
  };

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => {
      const precio = item.precio_ml || 0;
      return total + (precio * item.cantidad);
    }, 0);
  };

  const calculateIVA = () => {
    return calculateSubtotal() * 0.16;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateIVA();
  };

  const handleSolicitarCotizacion = () => {
    setShowCotizacionModal(true);
  };

  const handleCotizacionSubmit = async (formData: {
    nombre: string;
    email: string;
    whatsapp?: string;
  }) => {
    try {
      setCotizacionLoading(true);

      // Preparar datos de productos para la API
      const productosParaCotizacion = cartItems.map(item => ({
        numero_almacen: item.numero_almacen,
        modelo: item.modelo_existente,
        marca: item.marcas || '',
        cantidad: item.cantidad,
        precio: item.precio_ml || 0,
        descripcion: item.descripcion || '',
        stock_disponible: item.stock_disponible,
        sin_stock: item.sin_stock || false,
        excede_stock: item.excede_stock || false
      }));

      const response = await fetch('/api/solicitudes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          cliente_email: formData.email,
          cliente_whatsapp: formData.whatsapp || null,
          nombre: formData.nombre,
          productos: productosParaCotizacion
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Limpiar carrito después de enviar cotización
        clearCart();
        setShowCotizacionModal(false);

        // Mostrar modal de éxito
        setSuccessData({
          solicitud_grupo_id: data.solicitud_grupo_id,
          total_productos: data.resumen.total_productos,
          total_con_iva: data.resumen.total_con_iva,
          cliente_email: formData.email
        });
        setShowSuccessModal(true);
      } else {
        throw new Error(data.error || 'Error al enviar cotización');
      }
    } catch (error) {
      console.error('Error al enviar cotización:', error);
      alert('Error al enviar la cotización. Por favor, intenta de nuevo.');
    } finally {
      setCotizacionLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{
        marginTop: '0px',
        backgroundColor: '#f8fafc',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #0056a6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          marginBottom: '1rem'
        }}></div>
        <h1 style={{ color: '#333', marginBottom: '1rem' }}>Cargando Carrito</h1>
        <p style={{ color: '#666' }}>Obteniendo productos del carrito...</p>

        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{
      marginTop: '0px',
      backgroundColor: '#f8fafc',
      minHeight: '100vh'
    }}>
      {/* Hero Section */}
      <div style={{
        background: 'linear-gradient(135deg, #0056a6 0%, #003366 100%)',
        color: 'white',
        padding: '3rem 2rem 2rem 2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Decorative elements */}
        <div style={{
          position: 'absolute',
          top: '-30px',
          right: '-30px',
          width: '150px',
          height: '150px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          zIndex: 1
        }}></div>

        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}>
          {/* Badge */}
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            backgroundColor: 'rgba(255,255,255,0.2)',
            padding: '0.5rem 1rem',
            borderRadius: '25px',
            marginBottom: '1.5rem',
            fontSize: '0.9rem',
            fontWeight: '600'
          }}>
            <span>🛒</span>
            <span>Tu Carrito</span>
          </div>

          <h1 style={{
            fontSize: '2.5rem',
            marginBottom: '1rem',
            fontWeight: 'bold',
            lineHeight: '1.2'
          }}>
            Carrito de <span style={{ color: '#f7941d' }}>Compras</span>
          </h1>

          <p style={{
            fontSize: '1.2rem',
            opacity: '0.9',
            maxWidth: '600px',
            margin: '0 auto',
            lineHeight: '1.6'
          }}>
            Revisa los productos seleccionados y procede con tu cotización
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem'
      }}>
        {cartItems.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '4rem 2rem',
            backgroundColor: 'white',
            borderRadius: '20px',
            boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
            border: '1px solid #f1f5f9',
            margin: '2rem auto',
            maxWidth: '600px'
          }}>
            <div style={{
              width: '100px',
              height: '100px',
              margin: '0 auto 2rem auto',
              backgroundColor: '#f1f5f9',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#64748b" strokeWidth="2">
                <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.68 8.32a2 2 0 002 2.68h9.36a2 2 0 002-2.68L17 13" />
              </svg>
            </div>
            <h2 style={{
              fontSize: '1.8rem',
              fontWeight: 'bold',
              color: '#374151',
              marginBottom: '1rem'
            }}>
              Tu carrito está vacío
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#64748b',
              marginBottom: '2rem',
              lineHeight: '1.6'
            }}>
              Agrega algunos productos para comenzar tu compra y obtener una cotización personalizada
            </p>
            <a
              href="/productos"
              style={{
                display: 'inline-block',
                padding: '1rem 2rem',
                background: 'linear-gradient(135deg, #0056a6, #003366)',
                color: 'white',
                borderRadius: '12px',
                fontSize: '1rem',
                fontWeight: '600',
                textDecoration: 'none',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(0,86,166,0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,86,166,0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,86,166,0.3)';
              }}
            >
              Ver Productos
            </a>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 400px',
            gap: '2rem',
            marginTop: '2rem'
          }}>
            {/* Lista de productos */}
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
                border: '1px solid #f1f5f9'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '2rem'
                }}>
                  <h2 style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    color: '#1e293b'
                  }}>
                    Productos ({cartItems.length})
                  </h2>
                  <button
                    onClick={clearCart}
                    style={{
                      color: '#ef4444',
                      fontSize: '0.9rem',
                      fontWeight: '500',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      padding: '0.5rem 1rem',
                      borderRadius: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#fef2f2';
                      e.currentTarget.style.color = '#dc2626';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#ef4444';
                    }}
                  >
                    🗑️ Vaciar carrito
                  </button>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  {cartItems.map((item) => (
                    <div key={item.id} style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1.5rem',
                      padding: '1.5rem',
                      border: '1px solid #e2e8f0',
                      borderRadius: '16px',
                      transition: 'all 0.3s ease',
                      backgroundColor: '#fafbfc'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                      e.currentTarget.style.backgroundColor = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow = 'none';
                      e.currentTarget.style.backgroundColor = '#fafbfc';
                    }}>
                      {/* Product Image */}
                      <div style={{
                        width: '80px',
                        height: '80px',
                        backgroundColor: '#f1f5f9',
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        overflow: 'hidden',
                        flexShrink: 0
                      }}>
                        {item.Url_imagen && (item.Url_imagen.startsWith('http') || item.Url_imagen.startsWith('https')) ? (
                          <img
                            src={item.Url_imagen}
                            alt={item.modelo_existente}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              borderRadius: '12px'
                            }}
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                            }}
                          />
                        ) : (
                          <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#64748b" strokeWidth="2">
                            <path d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                          </svg>
                        )}
                      </div>

                      {/* Product Info */}
                      <div style={{ flex: 1 }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.75rem',
                          marginBottom: '0.5rem'
                        }}>
                          <h3 style={{
                            fontSize: '1.1rem',
                            fontWeight: 'bold',
                            color: '#1e293b'
                          }}>
                            {item.modelo_existente}
                          </h3>
                          {item.marcas && (
                            <span style={{
                              fontSize: '0.75rem',
                              backgroundColor: '#0056a6',
                              color: 'white',
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              fontWeight: '600'
                            }}>
                              {item.marcas}
                            </span>
                          )}
                        </div>

                        {item.descripcion && (
                          <p style={{
                            fontSize: '0.9rem',
                            color: '#64748b',
                            marginBottom: '0.75rem',
                            lineHeight: '1.4'
                          }}>
                            {item.descripcion}
                          </p>
                        )}

                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          flexWrap: 'wrap'
                        }}>
                          <p style={{
                            fontSize: '1.2rem',
                            fontWeight: 'bold',
                            color: '#0056a6'
                          }}>
                            ${(item.precio_ml || 0).toLocaleString('es-MX', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            })} MXN
                          </p>

                          {item.sin_stock ? (
                            <span style={{
                              fontSize: '0.75rem',
                              backgroundColor: '#fef3c7',
                              color: '#92400e',
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              fontWeight: '500'
                            }}>
                              Sin stock - Se cotiza tiempo de entrega
                            </span>
                          ) : item.excede_stock ? (
                            <div style={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: '0.5rem',
                              padding: '0.75rem',
                              backgroundColor: '#fef3c7',
                              borderRadius: '8px',
                              border: '1px solid #fbbf24'
                            }}>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem'
                              }}>
                                <span style={{
                                  fontSize: '1rem'
                                }}>⚠️</span>
                                <span style={{
                                  fontSize: '0.85rem',
                                  color: '#92400e',
                                  fontWeight: '600'
                                }}>
                                  Tiempo de entrega diferente
                                </span>
                              </div>
                              <div style={{
                                fontSize: '0.8rem',
                                color: '#92400e',
                                lineHeight: '1.4'
                              }}>
                                <div>• {item.stock_disponible} unidades disponibles inmediatamente</div>
                                <div>• {item.cantidad_excedente} unidades adicionales requieren cotización</div>
                              </div>
                            </div>
                          ) : item.stock_disponible <= 5 ? (
                            <span style={{
                              fontSize: '0.75rem',
                              backgroundColor: '#fed7aa',
                              color: '#c2410c',
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              fontWeight: '500'
                            }}>
                              Solo quedan {item.stock_disponible} en stock
                            </span>
                          ) : (
                            <span style={{
                              fontSize: '0.75rem',
                              backgroundColor: '#dcfce7',
                              color: '#166534',
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              fontWeight: '500'
                            }}>
                              {item.stock_disponible} disponibles
                            </span>
                          )}
                        </div>

                        {/* Información de envío */}
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1.5rem',
                          marginTop: '0.75rem',
                          fontSize: '0.8rem',
                          color: '#64748b'
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem'
                          }}>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <rect x="1" y="3" width="15" height="13"></rect>
                              <polygon points="16,3 21,8 21,16 16,16"></polygon>
                              <circle cx="5.5" cy="18.5" r="2.5"></circle>
                              <circle cx="18.5" cy="18.5" r="2.5"></circle>
                            </svg>
                            <span>Envío no incluido</span>
                          </div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem'
                          }}>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                              <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                            <span>Recoger en tienda</span>
                          </div>
                        </div>
                      </div>

                      {/* Quantity Controls */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem',
                        flexShrink: 0
                      }}>
                        <button
                          onClick={() => updateQuantity(item.id, item.cantidad - 1)}
                          style={{
                            width: '36px',
                            height: '36px',
                            borderRadius: '50%',
                            backgroundColor: '#f1f5f9',
                            border: 'none',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#e2e8f0';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#f1f5f9';
                          }}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                          </svg>
                        </button>

                        <span style={{
                          width: '48px',
                          textAlign: 'center',
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          color: '#1e293b'
                        }}>
                          {item.cantidad}
                        </span>

                        <button
                          onClick={() => updateQuantity(item.id, item.cantidad + 1)}
                          style={{
                            width: '36px',
                            height: '36px',
                            borderRadius: '50%',
                            backgroundColor: '#f1f5f9',
                            border: 'none',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#e2e8f0';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = '#f1f5f9';
                          }}
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                          </svg>
                        </button>
                      </div>

                      {/* Price and Remove */}
                      <div style={{
                        textAlign: 'right',
                        flexShrink: 0,
                        minWidth: '120px'
                      }}>
                        <p style={{
                          fontSize: '1.3rem',
                          fontWeight: 'bold',
                          color: '#1e293b',
                          marginBottom: '0.5rem'
                        }}>
                          ${((item.precio_ml || 0) * item.cantidad).toLocaleString('es-MX', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                          })}
                        </p>
                        <button
                          onClick={() => removeItem(item.id)}
                          style={{
                            color: '#ef4444',
                            fontSize: '0.85rem',
                            fontWeight: '500',
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem',
                            padding: '0.25rem',
                            borderRadius: '6px',
                            transition: 'all 0.2s ease',
                            marginLeft: 'auto'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#fef2f2';
                            e.currentTarget.style.color = '#dc2626';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = '#ef4444';
                          }}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                          </svg>
                          Eliminar
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Resumen del pedido */}
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)',
                border: '1px solid #f1f5f9',
                position: 'sticky',
                top: '2rem'
              }}>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  color: '#1e293b',
                  marginBottom: '2rem'
                }}>
                  Resumen del Pedido
                </h2>

                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '1rem',
                  marginBottom: '2rem'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span style={{ color: '#64748b' }}>Subtotal:</span>
                    <span style={{ fontWeight: '600', color: '#1e293b' }}>
                      ${calculateSubtotal().toLocaleString('es-MX')}
                    </span>
                  </div>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span style={{ color: '#64748b' }}>IVA (16%):</span>
                    <span style={{ fontWeight: '600', color: '#1e293b' }}>
                      ${calculateIVA().toLocaleString('es-MX')}
                    </span>
                  </div>
                  <div style={{
                    borderTop: '1px solid #e2e8f0',
                    paddingTop: '1rem'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{
                        fontSize: '1.2rem',
                        fontWeight: 'bold',
                        color: '#1e293b'
                      }}>
                        Total:
                      </span>
                      <span style={{
                        fontSize: '1.3rem',
                        fontWeight: 'bold',
                        color: '#0056a6'
                      }}>
                        ${calculateTotal().toLocaleString('es-MX')} MXN
                      </span>
                    </div>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '1rem'
                }}>
                  <button
                    onClick={handleSolicitarCotizacion}
                    disabled={cotizacionLoading}
                    style={{
                      width: '100%',
                      padding: '1.25rem',
                      background: cotizacionLoading
                        ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                        : 'linear-gradient(135deg, #f7941d, #e67e22)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      fontSize: '1.1rem',
                      fontWeight: '600',
                      cursor: cotizacionLoading ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.75rem',
                      boxShadow: cotizacionLoading
                        ? '0 4px 12px rgba(148,163,184,0.3)'
                        : '0 4px 12px rgba(247,148,29,0.3)'
                    }}
                    onMouseEnter={(e) => {
                      if (!cotizacionLoading) {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 6px 20px rgba(247,148,29,0.4)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!cotizacionLoading) {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(247,148,29,0.3)';
                      }
                    }}
                  >
                    {cotizacionLoading ? (
                      <>
                        <div style={{
                          width: '20px',
                          height: '20px',
                          border: '2px solid rgba(255,255,255,0.3)',
                          borderTop: '2px solid white',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite'
                        }}></div>
                        Enviando Cotización...
                      </>
                    ) : (
                      <>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                          <path d="M13 8l-3 3 3 3"></path>
                        </svg>
                        Solicitar Cotización Profesional
                      </>
                    )}
                  </button>
                </div>

                <div style={{
                  marginTop: '2rem',
                  fontSize: '0.9rem',
                  color: '#64748b',
                  lineHeight: '1.6'
                }}>
                  <p style={{ marginBottom: '0.5rem' }}>• Envío no incluido</p>
                  <p style={{ marginBottom: '0.5rem' }}>• Opción de recoger en tienda</p>
                  <p style={{ marginBottom: '0.5rem' }}>• Productos sujetos a disponibilidad</p>

                  {/* Mostrar alerta si hay productos que exceden stock */}
                  {cartItems.some(item => item.excede_stock) && (
                    <div style={{
                      marginTop: '1rem',
                      padding: '1rem',
                      backgroundColor: '#fef3c7',
                      borderRadius: '8px',
                      border: '1px solid #fbbf24'
                    }}>
                      <p style={{
                        fontSize: '0.85rem',
                        color: '#92400e',
                        fontWeight: '600',
                        marginBottom: '0.5rem'
                      }}>
                        ⚠️ Atención: Tiempo de entrega diferente
                      </p>
                      <p style={{
                        fontSize: '0.8rem',
                        color: '#92400e',
                        margin: '0'
                      }}>
                        Algunos productos exceden el stock disponible. Se requerirá cotización para tiempo de entrega adicional.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modal de Cotización */}
      {showCotizacionModal && (
        <CotizacionModal
          isOpen={showCotizacionModal}
          onClose={() => setShowCotizacionModal(false)}
          onSubmit={handleCotizacionSubmit}
          loading={cotizacionLoading}
          cartItems={cartItems}
          total={calculateTotal()}
        />
      )}

      {/* Modal de Éxito */}
      {showSuccessModal && successData && (
        <SuccessModal
          isOpen={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          data={successData}
        />
      )}

      {/* CSS for animations and responsive design */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 1024px) {
          .cart-grid {
            grid-template-columns: 1fr !important;
            gap: 1.5rem !important;
          }

          .cart-item {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 1rem !important;
          }

          .quantity-controls {
            align-self: center !important;
          }

          .price-section {
            text-align: center !important;
            align-self: center !important;
          }
        }

        @media (max-width: 768px) {
          .hero-section {
            padding: 2rem 1rem !important;
          }

          .hero-title {
            font-size: 2rem !important;
          }

          .cart-container {
            padding: 1rem !important;
          }

          .cart-item {
            padding: 1rem !important;
          }
        }
      `}</style>
    </div>
  );
}

// Componente Modal de Cotización
function CotizacionModal({ isOpen, onClose, onSubmit, loading, cartItems, total }: CotizacionModalProps) {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    whatsapp: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.nombre.trim()) {
      newErrors.nombre = 'El nombre es obligatorio';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'El email es obligatorio';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.whatsapp && !/^\+?[\d\s\-\(\)]+$/.test(formData.whatsapp)) {
      newErrors.whatsapp = 'Formato de WhatsApp inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #f1f5f9'
        }}>
          <div>
            <h2 style={{
              fontSize: '1.8rem',
              fontWeight: 'bold',
              color: '#1e293b',
              margin: '0 0 0.5rem 0'
            }}>
              Solicitar Cotización Profesional
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#64748b',
              margin: 0
            }}>
              Completa tus datos para recibir una cotización personalizada
            </p>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: '#64748b',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '50%',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#f1f5f9';
                e.currentTarget.style.color = '#dc2626';
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#64748b';
              }
            }}
          >
            ✕
          </button>
        </div>

        {/* Resumen del carrito */}
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '1.5rem',
          borderRadius: '12px',
          marginBottom: '2rem',
          border: '1px solid #e2e8f0'
        }}>
          <h3 style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1rem 0'
          }}>
            Resumen de tu solicitud
          </h3>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '0.75rem'
          }}>
            <span style={{ color: '#64748b' }}>Productos:</span>
            <span style={{ fontWeight: '600', color: '#1e293b' }}>
              {cartItems.length} artículos
            </span>
          </div>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingTop: '0.75rem',
            borderTop: '1px solid #e2e8f0'
          }}>
            <span style={{ fontSize: '1.1rem', fontWeight: '600', color: '#1e293b' }}>
              Total estimado:
            </span>
            <span style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#0056a6'
            }}>
              ${total.toLocaleString('es-MX', { minimumFractionDigits: 2 })} MXN
            </span>
          </div>
        </div>

        {/* Formulario */}
        <form onSubmit={handleSubmit}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Nombre */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '1rem'
              }}>
                Nombre completo *
              </label>
              <input
                type="text"
                value={formData.nombre}
                onChange={(e) => handleInputChange('nombre', e.target.value)}
                disabled={loading}
                placeholder="Tu nombre completo"
                style={{
                  width: '100%',
                  padding: '0.875rem',
                  border: errors.nombre ? '2px solid #dc2626' : '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'text',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  if (!loading && !errors.nombre) {
                    e.target.style.borderColor = '#0056a6';
                    e.target.style.boxShadow = '0 0 0 3px rgba(0,86,166,0.1)';
                  }
                }}
                onBlur={(e) => {
                  if (!errors.nombre) {
                    e.target.style.borderColor = '#e2e8f0';
                    e.target.style.boxShadow = 'none';
                  }
                }}
              />
              {errors.nombre && (
                <p style={{
                  color: '#dc2626',
                  fontSize: '0.875rem',
                  margin: '0.5rem 0 0 0'
                }}>
                  {errors.nombre}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '1rem'
              }}>
                Email corporativo *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={loading}
                placeholder="<EMAIL>"
                style={{
                  width: '100%',
                  padding: '0.875rem',
                  border: errors.email ? '2px solid #dc2626' : '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'text',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  if (!loading && !errors.email) {
                    e.target.style.borderColor = '#0056a6';
                    e.target.style.boxShadow = '0 0 0 3px rgba(0,86,166,0.1)';
                  }
                }}
                onBlur={(e) => {
                  if (!errors.email) {
                    e.target.style.borderColor = '#e2e8f0';
                    e.target.style.boxShadow = 'none';
                  }
                }}
              />
              {errors.email && (
                <p style={{
                  color: '#dc2626',
                  fontSize: '0.875rem',
                  margin: '0.5rem 0 0 0'
                }}>
                  {errors.email}
                </p>
              )}
            </div>

            {/* WhatsApp */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '1rem'
              }}>
                WhatsApp (opcional)
              </label>
              <input
                type="tel"
                value={formData.whatsapp}
                onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                disabled={loading}
                placeholder="+52 81 1234 5678"
                style={{
                  width: '100%',
                  padding: '0.875rem',
                  border: errors.whatsapp ? '2px solid #dc2626' : '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'text',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  if (!loading && !errors.whatsapp) {
                    e.target.style.borderColor = '#0056a6';
                    e.target.style.boxShadow = '0 0 0 3px rgba(0,86,166,0.1)';
                  }
                }}
                onBlur={(e) => {
                  if (!errors.whatsapp) {
                    e.target.style.borderColor = '#e2e8f0';
                    e.target.style.boxShadow = 'none';
                  }
                }}
              />
              {errors.whatsapp && (
                <p style={{
                  color: '#dc2626',
                  fontSize: '0.875rem',
                  margin: '0.5rem 0 0 0'
                }}>
                  {errors.whatsapp}
                </p>
              )}
              <p style={{
                color: '#64748b',
                fontSize: '0.875rem',
                margin: '0.5rem 0 0 0'
              }}>
                Para comunicación directa y seguimiento de tu cotización
              </p>
            </div>
          </div>

          {/* Botones */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            marginTop: '2rem',
            paddingTop: '1.5rem',
            borderTop: '1px solid #e2e8f0'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                flex: 1,
                padding: '0.875rem',
                backgroundColor: loading ? '#f9fafb' : '#f1f5f9',
                color: loading ? '#9ca3af' : '#64748b',
                border: '2px solid #e2e8f0',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                  e.currentTarget.style.borderColor = '#cbd5e1';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                  e.currentTarget.style.borderColor = '#e2e8f0';
                }
              }}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              style={{
                flex: 2,
                padding: '0.875rem',
                background: loading
                  ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                  : 'linear-gradient(135deg, #f7941d, #e67e22)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(247,148,29,0.4)';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {loading ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid rgba(255,255,255,0.3)',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Enviando...
                </>
              ) : (
                <>
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                  </svg>
                  Enviar Cotización
                </>
              )}
            </button>
          </div>
        </form>

        {/* Información adicional */}
        <div style={{
          marginTop: '1.5rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '8px',
          border: '1px solid #bae6fd'
        }}>
          <p style={{
            fontSize: '0.875rem',
            color: '#0369a1',
            margin: 0,
            lineHeight: '1.5'
          }}>
            💡 <strong>¿Qué sigue?</strong> Recibirás una cotización detallada en tu email con precios, disponibilidad y tiempos de entrega. Nuestro equipo se pondrá en contacto contigo en las próximas 24 horas.
          </p>
        </div>
      </div>
    </div>
  );
}

// Componente Modal de Éxito
interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: {
    solicitud_grupo_id: string;
    total_productos: number;
    total_con_iva: number;
    cliente_email: string;
  };
}

function SuccessModal({ isOpen, onClose, data }: SuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '600px',
        width: '100%',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative',
        textAlign: 'center'
      }}>
        {/* Icono de éxito */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #10b981, #059669)',
            padding: '2rem',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 8px 25px rgba(16, 185, 129, 0.3)'
          }}>
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <path d="M20 6L9 17l-5-5" />
            </svg>
          </div>
        </div>

        {/* Título */}
        <h2 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#1e293b',
          margin: '0 0 1rem 0'
        }}>
          ¡Cotización Enviada Exitosamente!
        </h2>

        <p style={{
          fontSize: '1.1rem',
          color: '#64748b',
          lineHeight: '1.6',
          margin: '0 0 2rem 0'
        }}>
          Tu solicitud ha sido procesada correctamente. Recibirás una cotización detallada muy pronto.
        </p>

        {/* Información de la solicitud */}
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '2rem',
          borderRadius: '16px',
          border: '1px solid #e2e8f0',
          marginBottom: '2rem',
          textAlign: 'left'
        }}>
          <h3 style={{
            fontSize: '1.3rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1.5rem 0',
            textAlign: 'center'
          }}>
            Detalles de tu Solicitud
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1.5rem'
          }}>
            <div style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}>
                ID de Solicitud
              </div>
              <div style={{
                fontSize: '1.1rem',
                fontWeight: 'bold',
                color: '#0056a6',
                fontFamily: 'monospace'
              }}>
                {data.solicitud_grupo_id}
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}>
                Productos
              </div>
              <div style={{
                fontSize: '1.1rem',
                fontWeight: 'bold',
                color: '#059669'
              }}>
                {data.total_productos} artículos
              </div>
            </div>

            <div style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '12px',
              border: '1px solid #e2e8f0',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '0.875rem',
                color: '#64748b',
                fontWeight: '600',
                marginBottom: '0.5rem'
              }}>
                Total Estimado
              </div>
              <div style={{
                fontSize: '1.1rem',
                fontWeight: 'bold',
                color: '#dc2626'
              }}>
                ${data.total_con_iva.toLocaleString('es-MX', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} MXN
              </div>
            </div>
          </div>

          <div style={{
            marginTop: '1.5rem',
            padding: '1rem',
            backgroundColor: '#f0f9ff',
            borderRadius: '8px',
            border: '1px solid #bae6fd'
          }}>
            <div style={{
              fontSize: '0.875rem',
              color: '#0369a1',
              fontWeight: '600',
              marginBottom: '0.5rem'
            }}>
              Email de Confirmación
            </div>
            <div style={{
              fontSize: '1rem',
              color: '#1e293b',
              fontWeight: '500'
            }}>
              {data.cliente_email}
            </div>
          </div>
        </div>

        {/* Información adicional */}
        <div style={{
          backgroundColor: '#fef3c7',
          padding: '1.5rem',
          borderRadius: '12px',
          border: '1px solid #fbbf24',
          marginBottom: '2rem',
          textAlign: 'left'
        }}>
          <h4 style={{
            fontSize: '1.1rem',
            fontWeight: '600',
            color: '#92400e',
            margin: '0 0 1rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
            </svg>
            ¿Qué sigue ahora?
          </h4>
          <ul style={{
            margin: 0,
            paddingLeft: '1.5rem',
            color: '#92400e',
            lineHeight: '1.6'
          }}>
            <li style={{ marginBottom: '0.5rem' }}>
              <strong>Revisión:</strong> Nuestro equipo revisará tu solicitud en las próximas 2-4 horas
            </li>
            <li style={{ marginBottom: '0.5rem' }}>
              <strong>Cotización:</strong> Recibirás una cotización detallada en tu email
            </li>
            <li style={{ marginBottom: '0.5rem' }}>
              <strong>Seguimiento:</strong> Te contactaremos para resolver cualquier duda
            </li>
            <li>
              <strong>Entrega:</strong> Coordinaremos los tiempos de entrega según disponibilidad
            </li>
          </ul>
        </div>

        {/* Botón de cerrar */}
        <button
          onClick={onClose}
          style={{
            padding: '1rem 2.5rem',
            background: 'linear-gradient(135deg, #10b981, #059669)',
            color: 'white',
            border: 'none',
            borderRadius: '12px',
            fontSize: '1.1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.75rem',
            margin: '0 auto',
            boxShadow: '0 4px 12px rgba(16,185,129,0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(16,185,129,0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(16,185,129,0.3)';
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M20 6L9 17l-5-5" />
          </svg>
          Perfecto, Entendido
        </button>

        <p style={{
          fontSize: '0.875rem',
          color: '#64748b',
          margin: '1.5rem 0 0 0',
          lineHeight: '1.5'
        }}>
          Si tienes alguna pregunta urgente, puedes contactarnos directamente por WhatsApp o email.
        </p>
      </div>
    </div>
  );
}
