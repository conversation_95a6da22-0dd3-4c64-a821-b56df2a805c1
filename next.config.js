/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Optimización de imágenes
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",

    // Dominios permitidos para imágenes externas
    domains: [
      'localhost',
      'eccsa-web.vercel.app'
    ],

    // Configuración de carga lazy
    loader: 'default',

    // Configuración para imágenes locales
    unoptimized: false,
  },

  // Configuración de rendimiento
  experimental: {
    // optimizeCss: true, // Removido por compatibilidad
    // optimizePackageImports: ['react-icons'], // Removido por compatibilidad
  },

  // Configuración de compresión
  compress: true,

  // Headers de seguridad y rendimiento
  async headers() {
    return [
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
