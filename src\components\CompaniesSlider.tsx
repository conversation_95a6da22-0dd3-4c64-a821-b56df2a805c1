'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ScrollAnimation, StaggerAnimation, TextReveal } from './ScrollAnimation';

interface CompaniesSliderProps {
  companies: {
    src: string;
    alt: string;
  }[];
  speed?: number;
  reverse?: boolean;
  title?: string;
}

export default function CompaniesSlider({
  companies,
  title = "Nuestros Clientes"
}: CompaniesSliderProps) {
  const sliderRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const autoScrollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Touch support for mobile
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Calcular el ancho total y el máximo scroll posible
  useEffect(() => {
    if (sliderRef.current) {
      const scrollWidth = sliderRef.current.scrollWidth;
      const clientWidth = sliderRef.current.clientWidth;
      setMaxScroll(scrollWidth - clientWidth);
    }
  }, [companies]);

  // Efecto para el auto-scroll
  useEffect(() => {
    const startAutoScroll = () => {
      if (autoScrollEnabled && sliderRef.current) {
        autoScrollIntervalRef.current = setInterval(() => {
          if (sliderRef.current) {
            let newPosition = scrollPosition + 1;

            // Si llegamos al final, volvemos al principio
            if (newPosition >= maxScroll) {
              newPosition = 0;
              sliderRef.current.scrollTo({
                left: newPosition,
                behavior: 'auto'
              });
            } else {
              sliderRef.current.scrollTo({
                left: newPosition,
                behavior: 'auto'
              });
            }

            setScrollPosition(newPosition);
          }
        }, 35); // Velocidad del auto-scroll ligeramente más lenta que el otro slider
      }
    };

    startAutoScroll();

    // Limpiar el intervalo cuando el componente se desmonta
    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, [autoScrollEnabled, scrollPosition, maxScroll]);

  // Función para pausar/reanudar el auto-scroll
  const toggleAutoScroll = () => {
    // Pausar el auto-scroll temporalmente
    setAutoScrollEnabled(prev => !prev);

    // Reanudar después de 5 segundos
    setTimeout(() => {
      setAutoScrollEnabled(true);
    }, 5000);
  };

  // Función para mover el slider a la izquierda con bucle
  const scrollLeft = () => {
    // Pausar el auto-scroll temporalmente
    toggleAutoScroll();

    if (sliderRef.current) {
      // Si estamos cerca del inicio, saltamos al final para simular un bucle
      if (scrollPosition < 150) {
        const newPosition = maxScroll;
        sliderRef.current.scrollTo({
          left: newPosition,
          behavior: 'smooth'
        });
        setScrollPosition(newPosition);
      } else {
        // Desplazamiento normal hacia la izquierda
        const newPosition = scrollPosition - 300;
        sliderRef.current.scrollTo({
          left: newPosition,
          behavior: 'smooth'
        });
        setScrollPosition(newPosition);
      }
    }
  };

  // Función para mover el slider a la derecha con bucle
  const scrollRight = () => {
    // Pausar el auto-scroll temporalmente
    toggleAutoScroll();

    if (sliderRef.current) {
      // Si estamos cerca del final, saltamos al inicio para simular un bucle
      if (scrollPosition > maxScroll - 150) {
        const newPosition = 0;
        sliderRef.current.scrollTo({
          left: newPosition,
          behavior: 'smooth'
        });
        setScrollPosition(newPosition);
      } else {
        // Desplazamiento normal hacia la derecha
        const newPosition = scrollPosition + 300;
        sliderRef.current.scrollTo({
          left: newPosition,
          behavior: 'smooth'
        });
        setScrollPosition(newPosition);
      }
    }
  };

  // Actualizar la posición de scroll cuando cambia
  const handleScroll = () => {
    if (sliderRef.current) {
      setScrollPosition(sliderRef.current.scrollLeft);
    }
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setAutoScrollEnabled(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      scrollRight();
    } else if (isRightSwipe) {
      scrollLeft();
    }

    // Resume auto-scroll after touch
    setTimeout(() => setAutoScrollEnabled(true), 5000);
  };

  return (
    <section className="modern-clients-section">
      <div className="modern-clients-container">
        {/* Header Section */}
        <ScrollAnimation animation="fade-in" delay={100}>
          <div className="modern-clients-header">
            <div className="modern-clients-title-wrapper">
              <TextReveal>
                <h2 className="modern-clients-title">{title}</h2>
              </TextReveal>
              <div className="modern-clients-subtitle">
                Empresas líderes que confían en nuestras soluciones de automatización industrial
              </div>
            </div>
            <div className="modern-clients-decorative">
              <div className="clients-decorative-pattern">
                <div className="clients-pattern-dot"></div>
                <div className="clients-pattern-dot"></div>
                <div className="clients-pattern-dot"></div>
              </div>
            </div>
          </div>
        </ScrollAnimation>

        {/* Clients Showcase */}
        <ScrollAnimation animation="slide-up" delay={300}>
          <div className="modern-clients-showcase">
            <div className="modern-clients-grid">
              {companies.map((company, index) => (
                <ScrollAnimation key={`client-${index}`} animation="fade-in" delay={400 + index * 80}>
                  <div className="modern-client-card">
                    <div className="modern-client-inner">
                      <div className="modern-client-logo-wrapper">
                        <img
                          src={company.src}
                          alt={company.alt}
                          className="modern-client-logo"
                          loading="lazy"
                        />
                      </div>
                      <div className="modern-client-overlay">
                        <div className="modern-client-name">{company.alt}</div>
                        <div className="modern-client-badge">Cliente Satisfecho</div>
                      </div>
                    </div>
                    <div className="modern-client-shine"></div>
                  </div>
                </ScrollAnimation>
              ))}
            </div>
          </div>
        </ScrollAnimation>

        {/* Trust Indicators */}
        <ScrollAnimation animation="fade-in" delay={800}>
          <div className="modern-clients-trust">
            <div className="modern-clients-trust-content">
              <div className="modern-clients-trust-item">
                <div className="trust-icon">🏆</div>
                <div className="trust-text">
                  <div className="trust-number">98%</div>
                  <div className="trust-label">Satisfacción</div>
                </div>
              </div>
              <div className="modern-clients-trust-item">
                <div className="trust-icon">🤝</div>
                <div className="trust-text">
                  <div className="trust-number">{companies.length}+</div>
                  <div className="trust-label">Clientes Activos</div>
                </div>
              </div>
              <div className="modern-clients-trust-item">
                <div className="trust-icon">⭐</div>
                <div className="trust-text">
                  <div className="trust-number">25+</div>
                  <div className="trust-label">Años de Confianza</div>
                </div>
              </div>
            </div>
          </div>
        </ScrollAnimation>
      </div>
    </section>
  );
}
