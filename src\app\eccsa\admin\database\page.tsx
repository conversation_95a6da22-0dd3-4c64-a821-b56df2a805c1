'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

interface DatabaseStatus {
  connected: boolean;
  host?: string;
  database?: string;
  tables?: string[];
  lastBackup?: string;
  totalRecords?: number;
  diskUsage?: string;
  uptime?: string;
}

interface TableInfo {
  name: string;
  rows: number;
  size: string;
  lastModified: string;
  status: 'healthy' | 'warning' | 'error';
}

export default function DatabasePage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [backupLoading, setBackupLoading] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Load database status
  const loadDatabaseStatus = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/database/status');
      const data = await response.json();

      if (response.ok) {
        // Simulate database status
        const status: DatabaseStatus = {
          connected: data.connected || true,
          host: 'localhost',
          database: 'EccsaWeb',
          tables: ['almacen', 'users', 'solicitudes', 'lista_de_precio'],
          lastBackup: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          totalRecords: Math.floor(Math.random() * 10000) + 5000,
          diskUsage: '2.3 GB',
          uptime: '15 días, 8 horas'
        };

        setDbStatus(status);

        // Simulate table information
        const tableInfo: TableInfo[] = [
          {
            name: 'almacen',
            rows: Math.floor(Math.random() * 1000) + 500,
            size: '1.2 MB',
            lastModified: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'healthy'
          },
          {
            name: 'users',
            rows: Math.floor(Math.random() * 100) + 10,
            size: '45 KB',
            lastModified: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
            status: 'healthy'
          },
          {
            name: 'solicitudes',
            rows: Math.floor(Math.random() * 500) + 100,
            size: '320 KB',
            lastModified: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'healthy'
          },
          {
            name: 'lista_de_precio',
            rows: Math.floor(Math.random() * 2000) + 800,
            size: '890 KB',
            lastModified: new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'healthy'
          }
        ];

        setTables(tableInfo);
      } else {
        setError('Error al conectar con la base de datos');
      }
    } catch (err) {
      setError('Error al cargar el estado de la base de datos');
      console.error('Database status error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDatabaseStatus();
  }, []);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'healthy': return 'admin-badge-success';
      case 'warning': return 'admin-badge-warning';
      case 'error': return 'admin-badge-danger';
      default: return 'admin-badge-secondary';
    }
  };

  const handleBackup = async () => {
    setBackupLoading(true);
    try {
      // Simulate backup process
      await new Promise(resolve => setTimeout(resolve, 3000));
      alert('Backup completado exitosamente');
      loadDatabaseStatus(); // Refresh status
    } catch (error) {
      alert('Error al crear backup');
    } finally {
      setBackupLoading(false);
    }
  };

  const handleOptimize = async () => {
    try {
      // Simulate optimization
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Optimización completada');
      loadDatabaseStatus(); // Refresh status
    } catch (error) {
      alert('Error al optimizar base de datos');
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="modern-admin-page">
      {/* Header */}
      <div className="modern-admin-header">
        <div>
          <h1 className="modern-admin-title">Gestión de Base de Datos</h1>
          <p style={{ color: '#64748b', margin: '0.5rem 0 0 0', fontSize: '1rem' }}>
            Monitoreo y administración de la base de datos
          </p>
        </div>
        <div className="modern-admin-actions">
          <button
            className="modern-admin-button modern-admin-button-secondary"
            onClick={loadDatabaseStatus}
            disabled={loading}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {loading ? 'Actualizando...' : 'Actualizar'}
          </button>
          <button
            className="modern-admin-button modern-admin-button-warning"
            onClick={handleOptimize}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Optimizar
          </button>
          <button
            className="modern-admin-button modern-admin-button-primary"
            onClick={handleBackup}
            disabled={backupLoading}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
            {backupLoading ? 'Creando Backup...' : 'Crear Backup'}
          </button>
        </div>
      </div>

      {loading ? (
        <div className="modern-admin-loading">
          <div className="modern-admin-spinner"></div>
          <p>Cargando estado de la base de datos...</p>
        </div>
      ) : error ? (
        <div className="modern-admin-error">
          <p>{error}</p>
          <button
            className="modern-admin-button modern-admin-button-secondary"
            onClick={loadDatabaseStatus}
          >
            Reintentar
          </button>
        </div>
      ) : dbStatus ? (
        <>
          {/* Database Status Cards */}
          <div className="admin-stats">
            <div className="admin-stat-card">
              <div className="admin-stat-icon">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                </svg>
              </div>
              <div className="admin-stat-content">
                <div className="admin-stat-title">Estado Conexión</div>
                <div className="admin-stat-value">
                  <span className={`admin-badge ${dbStatus.connected ? 'admin-badge-success' : 'admin-badge-danger'}`}>
                    {dbStatus.connected ? 'Conectado' : 'Desconectado'}
                  </span>
                </div>
              </div>
            </div>

            <div className="admin-stat-card">
              <div className="admin-stat-icon">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="admin-stat-content">
                <div className="admin-stat-title">Total Registros</div>
                <div className="admin-stat-value">{dbStatus.totalRecords?.toLocaleString()}</div>
              </div>
            </div>

            <div className="admin-stat-card">
              <div className="admin-stat-icon">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
              <div className="admin-stat-content">
                <div className="admin-stat-title">Uso de Disco</div>
                <div className="admin-stat-value">{dbStatus.diskUsage}</div>
              </div>
            </div>

            <div className="admin-stat-card">
              <div className="admin-stat-icon">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="admin-stat-content">
                <div className="admin-stat-title">Tiempo Activo</div>
                <div className="admin-stat-value">{dbStatus.uptime}</div>
              </div>
            </div>
          </div>

          {/* Database Info */}
          <div className="modern-admin-card">
            <div className="modern-admin-card-header">
              <h2 className="modern-admin-card-title">Información de la Base de Datos</h2>
            </div>
            <div className="modern-admin-card-content">
              <div className="admin-db-info">
                <div className="admin-db-info-item">
                  <span className="admin-db-info-label">Host:</span>
                  <span className="admin-db-info-value">{dbStatus.host}</span>
                </div>
                <div className="admin-db-info-item">
                  <span className="admin-db-info-label">Base de Datos:</span>
                  <span className="admin-db-info-value">{dbStatus.database}</span>
                </div>
                <div className="admin-db-info-item">
                  <span className="admin-db-info-label">Último Backup:</span>
                  <span className="admin-db-info-value">
                    {dbStatus.lastBackup ? formatDate(dbStatus.lastBackup) : 'No disponible'}
                  </span>
                </div>
                <div className="admin-db-info-item">
                  <span className="admin-db-info-label">Tablas:</span>
                  <span className="admin-db-info-value">{dbStatus.tables?.length || 0}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Tables Status */}
          <div className="modern-admin-card">
            <div className="modern-admin-card-header">
              <h2 className="modern-admin-card-title">Estado de las Tablas</h2>
            </div>
            <div className="modern-admin-card-content">
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>Tabla</th>
                      <th>Registros</th>
                      <th>Tamaño</th>
                      <th>Última Modificación</th>
                      <th>Estado</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tables.map((table) => (
                      <tr key={table.name}>
                        <td className="admin-table-name">{table.name}</td>
                        <td className="admin-table-rows">{table.rows.toLocaleString()}</td>
                        <td className="admin-table-size">{table.size}</td>
                        <td className="admin-table-date">{formatDate(table.lastModified)}</td>
                        <td>
                          <span className={`admin-badge ${getStatusBadgeClass(table.status)}`}>
                            {table.status}
                          </span>
                        </td>
                        <td>
                          <div className="admin-table-actions">
                            <button
                              onClick={() => console.log('View table', table.name)}
                              className="admin-action-btn view"
                              title="Ver datos"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => console.log('Optimize table', table.name)}
                              className="admin-action-btn edit"
                              title="Optimizar"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="modern-admin-empty">
          <div className="modern-admin-empty-icon">
            <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
            </svg>
          </div>
          <h3 className="modern-admin-empty-title">No hay datos disponibles</h3>
          <p className="modern-admin-empty-text">No se pudo cargar la información de la base de datos.</p>
        </div>
      )}
    </div>
  );
}
