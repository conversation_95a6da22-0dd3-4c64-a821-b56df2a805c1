'use client';

import React from 'react';
import { FaSignOutAlt, FaTimes, FaExclamationTriangle } from 'react-icons/fa';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
}

export default function LogoutModal({ isOpen, onClose, onConfirm, loading }: LogoutModalProps) {
  if (!isOpen) return null;

  return (
    <div className="logout-modal-overlay">
      <div className="logout-modal">
        {/* Header */}
        <div className="logout-modal-header">
          <div className="logout-modal-icon">
            <FaSignOutAlt />
          </div>
          <h3 className="logout-modal-title">Cerrar <PERSON></h3>
          <button 
            className="logout-modal-close" 
            onClick={onClose}
            disabled={loading}
          >
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="logout-modal-content">
          <div className="logout-modal-warning">
            <FaExclamationTriangle className="warning-icon" />
            <p>¿Estás seguro de que deseas cerrar sesión?</p>
          </div>
          <p className="logout-modal-description">
            Se cerrará tu sesión actual y serás redirigido a la página de inicio de sesión.
          </p>
        </div>

        {/* Actions */}
        <div className="logout-modal-actions">
          <button 
            className="logout-modal-cancel"
            onClick={onClose}
            disabled={loading}
          >
            Cancelar
          </button>
          <button 
            className="logout-modal-confirm"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="logout-spinner"></div>
                Cerrando sesión...
              </>
            ) : (
              <>
                <FaSignOutAlt />
                Cerrar Sesión
              </>
            )}
          </button>
        </div>
      </div>

      <style jsx>{`
        .logout-modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          backdrop-filter: blur(8px);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          padding: 2rem;
          animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .logout-modal {
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border-radius: 20px;
          width: 100%;
          max-width: 450px;
          overflow: hidden;
          box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1);
          animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .logout-modal-header {
          padding: 2rem 2rem 1rem 2rem;
          text-align: center;
          position: relative;
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .logout-modal-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1rem auto;
          color: white;
          font-size: 1.5rem;
          box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .logout-modal-title {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 700;
          color: #1a202c;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .logout-modal-close {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: none;
          border: none;
          font-size: 1.2rem;
          color: #64748b;
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 8px;
          transition: all 0.2s ease;
        }

        .logout-modal-close:hover:not(:disabled) {
          background: rgba(0, 0, 0, 0.05);
          color: #ef4444;
        }

        .logout-modal-close:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .logout-modal-content {
          padding: 1rem 2rem 2rem 2rem;
          text-align: center;
        }

        .logout-modal-warning {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          margin-bottom: 1rem;
          padding: 1rem;
          background: rgba(251, 191, 36, 0.1);
          border: 1px solid rgba(251, 191, 36, 0.2);
          border-radius: 12px;
        }

        .warning-icon {
          color: #f59e0b;
          font-size: 1.2rem;
        }

        .logout-modal-warning p {
          margin: 0;
          font-weight: 600;
          color: #92400e;
        }

        .logout-modal-description {
          margin: 0;
          color: #64748b;
          line-height: 1.6;
        }

        .logout-modal-actions {
          padding: 1.5rem 2rem 2rem 2rem;
          display: flex;
          gap: 1rem;
          justify-content: center;
        }

        .logout-modal-cancel,
        .logout-modal-confirm {
          padding: 0.75rem 1.5rem;
          border-radius: 12px;
          font-weight: 600;
          font-size: 0.95rem;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          min-width: 140px;
          justify-content: center;
        }

        .logout-modal-cancel {
          background: #f1f5f9;
          border: 1px solid #e2e8f0;
          color: #475569;
        }

        .logout-modal-cancel:hover:not(:disabled) {
          background: #e2e8f0;
          border-color: #cbd5e1;
        }

        .logout-modal-confirm {
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border: none;
          color: white;
          box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .logout-modal-confirm:hover:not(:disabled) {
          background: linear-gradient(135deg, #dc2626, #b91c1c);
          box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
          transform: translateY(-1px);
        }

        .logout-modal-cancel:disabled,
        .logout-modal-confirm:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .logout-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
          .logout-modal {
            margin: 1rem;
            max-width: none;
          }

          .logout-modal-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
          }

          .logout-modal-content {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
          }

          .logout-modal-actions {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
            flex-direction: column;
          }

          .logout-modal-cancel,
          .logout-modal-confirm {
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
}
