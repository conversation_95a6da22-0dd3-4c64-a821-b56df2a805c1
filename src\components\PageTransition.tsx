'use client';

import { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { prefetchRoutes } from '@/utils';
import { PREFETCH_ROUTES, ANIMATION } from '@/constants';

interface PageTransitionProps {
  children: React.ReactNode;
}

/**
 * PageTransition component that handles route changes and prefetching
 * Provides a smooth transition between pages
 */
export default function PageTransition({ children }: PageTransitionProps) {
  const pathname = usePathname();
  const [prevPathname, setPrevPathname] = useState('');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Handle route changes
  useEffect(() => {
    if (prevPathname && prevPathname !== pathname) {
      // Apply immediate transition
      setIsTransitioning(true);

      // End transition after a short delay to allow React to render new content
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, ANIMATION.TRANSITION_DURATION);

      return () => clearTimeout(timer);
    }

    // Update previous pathname
    setPrevPathname(pathname);

    // Don't show transition on first load
    if (!prevPathname) {
      setIsTransitioning(false);
    }
  }, [pathname, prevPathname]);

  // Prefetch routes to improve navigation performance
  useEffect(() => {
    prefetchRoutes(PREFETCH_ROUTES);
  }, []);

  return (
    <div
      ref={contentRef}
      className={`page-content ${isTransitioning ? 'page-transitioning' : ''}`}
    >
      {children}
    </div>
  );
}
