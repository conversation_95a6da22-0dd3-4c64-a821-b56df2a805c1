import type { Metadata } from 'next';
import './admin.css';

export const metadata: Metadata = {
  title: 'ECCSA - Panel de Administración',
  description: 'Sistema de administración para ECCSA Automation - Gestión integral de automatización industrial',
  icons: [
    { rel: 'icon', url: '/images/logos/logo_pequeno.png' },
    { rel: 'apple-touch-icon', url: '/images/logos/logo_pequeno.png' },
    { rel: 'shortcut icon', url: '/images/logos/logo_pequeno.png' }
  ],
};

export default function EccsaAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="admin-layout-container" style={{
      backgroundColor: '#f5f7fa',
      minHeight: '100vh',
      color: '#333333',
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
    }}>
      {children}
    </div>
  );
}
