// Script para recrear la tabla de usuarios
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config({ path: '.env.local' });

// Configuración de la conexión a la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'eccsa',
  password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
  database: process.env.DB_NAME || 'EccsaWeb',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : undefined,
};

async function executeQuery(query, params = []) {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function dropUsersTable() {
  try {
    console.log('Eliminando tabla de usuarios si existe...');
    await executeQuery('DROP TABLE IF EXISTS usuarios');
    console.log('Tabla eliminada correctamente.');
    return true;
  } catch (error) {
    console.error('Error al eliminar la tabla:', error);
    return false;
  }
}

async function createUsersTable() {
  try {
    console.log('Creando tabla de usuarios...');
    
    await executeQuery(`
      CREATE TABLE usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nombre VARCHAR(100) NOT NULL,
        nombre_usuario VARCHAR(50) NOT NULL UNIQUE,
        foto_usuario VARCHAR(255),
        correo VARCHAR(100) NOT NULL UNIQUE,
        contrasena VARCHAR(255) NOT NULL,
        telefono VARCHAR(20),
        nivel_ingeniero INT NOT NULL DEFAULT 4 COMMENT '0=Administrador, 1=Jefe, 2=Senior, 3=Semi-Senior, 4=Junior',
        descripcion TEXT,
        fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        activo BOOLEAN DEFAULT TRUE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('Tabla creada correctamente.');
    return true;
  } catch (error) {
    console.error('Error al crear la tabla:', error);
    return false;
  }
}

async function insertDefaultUsers() {
  try {
    console.log('Insertando usuarios por defecto...');
    
    // Insertar usuario administrador
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        correo, 
        contrasena, 
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Administrador ECCSA',
        'admin',
        '<EMAIL>',
        'eccsa2023',
        0,
        'Usuario administrador del sistema con acceso completo'
      )
    `);
    
    // Insertar a Oscar Castillo
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        foto_usuario,
        correo, 
        contrasena, 
        telefono,
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Oscar Castillo',
        'oscar.castillo',
        '/images/usuarios/oscar.jpg',
        '<EMAIL>',
        'password123',
        '8182803296',
        0,
        'Jefe de automatización industrial con acceso administrativo'
      )
    `);
    
    // Insertar a Esteban Carrera
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        foto_usuario,
        correo, 
        contrasena, 
        telefono,
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Esteban Carrera',
        'esteban.carrera',
        '/images/usuarios/esteban.jpg',
        '<EMAIL>',
        'password123',
        '8187043546',
        0,
        'Especialista en ventas y soporte técnico con acceso administrativo'
      )
    `);
    
    console.log('Usuarios insertados correctamente.');
    return true;
  } catch (error) {
    console.error('Error al insertar usuarios:', error);
    return false;
  }
}

async function verifyUsers() {
  try {
    console.log('Verificando usuarios insertados...');
    
    const users = await executeQuery(`
      SELECT 
        id, 
        nombre, 
        nombre_usuario, 
        correo, 
        nivel_ingeniero,
        telefono,
        descripcion
      FROM usuarios
      ORDER BY id
    `);
    
    console.log('Usuarios en la base de datos:');
    console.table(users);
    
    return users;
  } catch (error) {
    console.error('Error al verificar usuarios:', error);
    return [];
  }
}

// Ejecutar las funciones
async function main() {
  try {
    console.log('=== Iniciando recreación de la tabla de usuarios ===');
    
    // Eliminar la tabla si existe
    const dropped = await dropUsersTable();
    if (!dropped) {
      console.error('No se pudo eliminar la tabla. Abortando...');
      process.exit(1);
    }
    
    // Crear la tabla
    const created = await createUsersTable();
    if (!created) {
      console.error('No se pudo crear la tabla. Abortando...');
      process.exit(1);
    }
    
    // Insertar usuarios por defecto
    const inserted = await insertDefaultUsers();
    if (!inserted) {
      console.error('No se pudieron insertar los usuarios. Abortando...');
      process.exit(1);
    }
    
    // Verificar usuarios insertados
    await verifyUsers();
    
    console.log('=== Recreación de la tabla de usuarios completada con éxito ===');
    process.exit(0);
  } catch (error) {
    console.error('Error en el script principal:', error);
    process.exit(1);
  }
}

main();
