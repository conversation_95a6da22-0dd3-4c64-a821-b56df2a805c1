{"name": "eccsa-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules/.cache", "export": "next export -o out", "verify-images": "node back-eccsa/deploy/verify-images.js", "verify-css": "node back-eccsa/deploy/verify-css.js", "test:db": "node back-eccsa/db/test-db-connection.js", "diagnose:db": "node back-eccsa/db/diagnose-db-connection.js", "fix:admin": "node back-eccsa/utils/fix-admin.js", "start:prod": "node back-eccsa/config/start.js", "build:production": "npm run clean && npm run build && npm run verify-images && npm run verify-css", "build:produccion": "node --no-warnings build-produccion.js"}, "dependencies": {"@tailwindcss/container-queries": "^0.1.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "20.5.7", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "bcryptjs": "^3.0.2", "critters": "^0.0.23", "dotenv": "^16.5.0", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "next": "13.4.19", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.5.0", "serverless-mysql": "^2.1.0", "sharp": "^0.34.2", "typescript": "5.2.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.16", "cheerio": "^1.0.0", "postcss": "^8.4.31", "rimraf": "^6.0.1", "tailwindcss": "^3.3.0"}}