/* Dashboard Limpio y Profesional */
.modern-dashboard-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 1.5rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #1e293b;
}

/* Header del Dashboard */
.dashboard-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: #3b82f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.title-text h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.title-text p {
  color: #64748b;
  font-size: 1rem;
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.time-widget {
  text-align: right;
  padding: 0.75rem 1rem;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.current-time {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  font-family: 'Monaco', 'Menlo', monospace;
}

.current-date {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 400;
  text-transform: capitalize;
  margin-top: 0.25rem;
}

.last-update {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 400;
}

.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  width: 40px;
  height: 20px;
  background: #cbd5e1;
  border-radius: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-input:checked + .toggle-slider {
  background: #3b82f6;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.refresh-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background: #2563eb;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Alertas del Sistema */
.dashboard-alerts {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.alerts-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.clear-alerts-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-alerts-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid;
}

.alert-item.success {
  background: rgba(16, 185, 129, 0.05);
  border-left-color: #10b981;
}

.alert-item.warning {
  background: rgba(245, 158, 11, 0.05);
  border-left-color: #f59e0b;
}

.alert-item.danger {
  background: rgba(239, 68, 68, 0.05);
  border-left-color: #ef4444;
}

.alert-item.info {
  background: rgba(59, 130, 246, 0.05);
  border-left-color: #3b82f6;
}

.alert-item:hover {
  background-opacity: 0.1;
}

.alert-item.read {
  opacity: 0.7;
}

.alert-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.alert-item.success .alert-icon {
  background: #10b981;
}

.alert-item.warning .alert-icon {
  background: #f59e0b;
}

.alert-item.danger .alert-icon {
  background: #ef4444;
}

.alert-item.info .alert-icon {
  background: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.alert-message {
  color: #64748b;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.alert-time {
  color: #94a3b8;
  font-size: 0.625rem;
  font-weight: 500;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.loading-spinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.loading-state p {
  color: #64748b;
  margin: 0;
  font-size: 0.875rem;
}

/* Estadísticas del Dashboard */
.dashboard-stats {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  position: relative;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background: #3b82f6;
}

.stat-card.success .stat-icon {
  background: #10b981;
}

.stat-card.info .stat-icon {
  background: #06b6d4;
}

.stat-card.accent .stat-icon {
  background: #8b5cf6;
}

.stat-card.warning .stat-icon {
  background: #f59e0b;
}

.stat-card.danger .stat-icon {
  background: #ef4444;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-change .positive {
  color: #10b981;
}

.stat-change .negative {
  color: #ef4444;
}

/* Acciones Rápidas */
.dashboard-actions {
  margin-bottom: 2rem;
}

.dashboard-actions h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.action-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.action-card.primary .action-icon {
  background: #3b82f6;
}

.action-card.success .action-icon {
  background: #10b981;
}

.action-card.info .action-icon {
  background: #06b6d4;
}

.action-card.warning .action-icon {
  background: #f59e0b;
}

.action-card.accent .action-icon {
  background: #8b5cf6;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.action-content p {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 0.25rem 0;
}

.action-stat {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-dashboard-page {
    padding: 1rem;
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .title-text h1 {
    font-size: 2rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .stat-card,
  .action-card {
    padding: 1.5rem;
  }

  .stat-value {
    font-size: 2rem;
  }
}

/* Actividades Recientes */
.dashboard-activities {
  margin-bottom: 2rem;
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.activity-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.activity-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.activity-section.highlight {
  background: #f8fafc;
  border: 1px solid #cbd5e1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.activity-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.activity-count {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.activity-content {
  min-height: 180px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.activity-item:hover {
  background: #f1f5f9;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
}

.activity-subtitle {
  color: #64748b;
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-time {
  color: #94a3b8;
  font-size: 0.625rem;
  font-weight: 500;
}

.activity-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.activity-status.pendiente {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.activity-status.completado {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.activity-status.cancelado {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.activity-status.admin {
  background: rgba(139, 92, 246, 0.1);
  color: #7c3aed;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.activity-status.manager {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.activity-status.engineer {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.activity-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #9ca3af;
  font-style: italic;
}

/* Producto Destacado */
.featured-product {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.product-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
}

.product-number {
  font-weight: 700;
  color: #374151;
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.product-brand {
  color: #667eea;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.product-model {
  color: #6b7280;
  font-size: 0.75rem;
}

/* Responsive para actividades */
@media (max-width: 768px) {
  .activities-grid {
    grid-template-columns: 1fr;
  }

  .activity-section {
    padding: 1rem;
  }

  .activity-item {
    padding: 0.75rem;
  }

  .featured-product {
    flex-direction: column;
    text-align: center;
  }
}

/* Scrollbar personalizada */
.activity-content::-webkit-scrollbar {
  width: 4px;
}

.activity-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.activity-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.activity-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
