/**
 * Script para crear la tabla de almacén en la base de datos EccsaWeb
 *
 * Este script lee el archivo SQL y lo ejecuta en la base de datos usando Node.js
 */

import fs from 'fs';
import mysql from 'serverless-mysql';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de la base de datos
const db = mysql({
  config: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'eccsa',
    password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
    database: process.env.DB_NAME || 'EccsaWeb',
    port: parseInt(process.env.DB_PORT || '3306', 10),
  },
});

async function crearTablaAlmacen() {
  try {
    console.log('Iniciando creación de tabla de almacén...');

    // Leer el archivo SQL
    const sqlFilePath = path.join(__dirname, 'crear_tabla_almacen.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Archivo SQL leído correctamente.');

    // Dividir el archivo SQL en consultas individuales
    const consultas = sql.split(';').filter(query => {
      const trimmed = query.trim();
      return trimmed.length > 0 && !trimmed.startsWith('--');
    });

    console.log(`Ejecutando ${consultas.length} consultas...`);

    // Ejecutar cada consulta
    for (let i = 0; i < consultas.length; i++) {
      const consulta = consultas[i].trim();
      if (consulta) {
        try {
          await db.query(consulta);
          console.log(`✓ Consulta ${i + 1} ejecutada: ${consulta.substring(0, 50)}...`);
        } catch (error) {
          console.error(`✗ Error en consulta ${i + 1}:`, error.message);
          console.error(`Consulta: ${consulta.substring(0, 100)}...`);
        }
      }
    }

    // Verificar que la tabla se haya creado correctamente
    console.log('\nVerificando la creación de la tabla...');

    const tablas = await db.query("SHOW TABLES LIKE 'almacen'");
    if (tablas.length > 0) {
      console.log('✓ La tabla "almacen" se creó exitosamente.');

      // Mostrar la estructura de la tabla
      const estructura = await db.query("DESCRIBE almacen");
      console.log('\nEstructura de la tabla "almacen":');
      console.log('------------------------------------');
      console.log('Campo | Tipo | Nulo | Clave | Predeterminado | Extra');
      console.log('------------------------------------');
      estructura.forEach(campo => {
        console.log(`${campo.Field} | ${campo.Type} | ${campo.Null} | ${campo.Key} | ${campo.Default} | ${campo.Extra}`);
      });

      // Mostrar los productos insertados
      const productos = await db.query(`
        SELECT id, numero_almacen, modelo_existente, precio_venta, cantidad_nuevo, proveedor
        FROM almacen
        ORDER BY numero_almacen
      `);

      console.log('\nProductos insertados en el almacén:');
      console.log('------------------------------------');
      console.log('ID | Número | Modelo | Precio Venta | Cantidad | Proveedor');
      console.log('------------------------------------');
      productos.forEach(producto => {
        const modelo = producto.modelo_existente.length > 20
          ? producto.modelo_existente.substring(0, 20) + '...'
          : producto.modelo_existente;
        const proveedor = producto.proveedor.length > 15
          ? producto.proveedor.substring(0, 15) + '...'
          : producto.proveedor;
        console.log(`${producto.id} | ${producto.numero_almacen} | ${modelo} | $${producto.precio_venta} | ${producto.cantidad_nuevo} | ${proveedor}`);
      });

      // Mostrar estadísticas básicas
      const estadisticas = await db.query(`
        SELECT
          COUNT(*) as total_productos,
          SUM(cantidad_nuevo) as total_cantidad,
          AVG(precio_venta) as precio_promedio
        FROM almacen
      `);

      console.log('\nEstadísticas del almacén:');
      console.log('------------------------------------');
      console.log(`Total de productos: ${estadisticas[0].total_productos}`);
      console.log(`Cantidad total en inventario: ${estadisticas[0].total_cantidad}`);
      console.log(`Precio promedio: $${parseFloat(estadisticas[0].precio_promedio).toFixed(2)}`);

    } else {
      console.error('✗ Error: La tabla "almacen" no se creó correctamente.');
    }

  } catch (error) {
    console.error('Error durante la creación de la tabla:', error);
  } finally {
    // Cerrar la conexión
    await db.end();
    console.log('\nProceso completado.');
    console.log('La tabla de almacén está lista para ser utilizada en el sistema ECCSA.');
  }
}

// Ejecutar el script directamente
crearTablaAlmacen().catch(console.error);

export { crearTablaAlmacen };
