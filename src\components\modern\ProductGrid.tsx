'use client';

import { useState } from 'react';
import Link from 'next/link';
import ProductImage from '../ProductImage';

interface Product {
  id: number;
  numero_almacen: string;
  modelo_existente: string;
  descripcion?: string;
  precio_ml?: number;
  cantidad_nuevo?: number;
  marcas?: string;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

interface ModernProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
  className?: string;
}

/**
 * Modern Product Card with Tailwind CSS
 * Implements responsive design and modern interactions
 */
export function ModernProductCard({
  product,
  onAddToCart,
  className = ''
}: ModernProductCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const formatPrice = (price?: number) => {
    if (!price || price <= 0) return 'Consultar precio';
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
    return formattedPrice;
  };

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  return (
    <div
      className={`
        group relative bg-white rounded-xl shadow-soft hover:shadow-strong
        transition-all duration-300 ease-out hover:-translate-y-1
        @container overflow-hidden border border-eccsa-gray-200
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Stock Indicator */}
      <div className="absolute top-3 right-3 z-10">
        <span className={`
          px-2 py-1 text-xs font-semibold rounded-full
          ${(product.cantidad_nuevo || 0) > 0
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
          }
        `}>
          {(product.cantidad_nuevo || 0) > 0 ? 'En stock' : 'Agotado'}
        </span>
      </div>

      {/* Product Image */}
      <div className="relative h-48 @md:h-56 bg-eccsa-gray-50 flex items-center justify-center p-4">
        <ProductImage
          src={product.Url_imagen || "/images/logos/logo_pequeno.png"}
          alt={product.modelo_existente}
          width={200}
          height={200}
          className="max-w-full max-h-full object-contain transition-transform duration-300 group-hover:scale-105"
        />

        {/* Hover Overlay */}
        <div className={`
          absolute inset-0 bg-eccsa-primary/10 backdrop-blur-sm
          flex items-center justify-center transition-opacity duration-300
          ${isHovered ? 'opacity-100' : 'opacity-0'}
        `}>
          <button
            onClick={handleAddToCart}
            disabled={(product.cantidad_nuevo || 0) <= 0}
            className={`
              px-4 py-2 rounded-lg font-semibold transition-all duration-200
              ${(product.cantidad_nuevo || 0) > 0
                ? 'bg-eccsa-primary text-white hover:bg-eccsa-primary-dark shadow-medium'
                : 'bg-eccsa-gray-300 text-eccsa-gray-500 cursor-not-allowed'
              }
            `}
          >
            {(product.cantidad_nuevo || 0) > 0 ? 'Agregar al carrito' : 'Sin stock'}
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4 @md:p-6">
        {/* Brand */}
        {product.marcas && (
          <div className="mb-2">
            <span className="inline-block px-2 py-1 text-xs font-medium bg-eccsa-accent/10 text-eccsa-accent rounded-full">
              {product.marcas}
            </span>
          </div>
        )}

        {/* Product Name */}
        <h3 className="text-lg @md:text-xl font-semibold text-eccsa-gray-900 mb-2 line-clamp-2 group-hover:text-eccsa-primary transition-colors">
          {product.modelo_existente}
        </h3>

        {/* Description */}
        <p className="text-sm text-eccsa-gray-600 mb-3 line-clamp-2">
          {product.Datos_importantes_Descripcion_muestra ||
           product.descripcion ||
           'Producto de automatización industrial'}
        </p>

        {/* Delivery Time */}
        {product.tiempo_de_Entrega && (
          <div className="flex items-center text-sm text-green-600 mb-3">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Entrega: {product.tiempo_de_Entrega}</span>
          </div>
        )}

        {/* Stock Count */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-eccsa-gray-500">
            Stock: {product.cantidad_nuevo || 0} unidades
          </span>
        </div>

        {/* Price */}
        <div className="text-xl font-bold text-eccsa-red mb-4">
          {formatPrice(product.precio_ml)}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <button
            onClick={handleAddToCart}
            disabled={(product.cantidad_nuevo || 0) <= 0}
            className={`
              flex-1 py-2 px-4 rounded-lg font-semibold transition-all duration-200
              ${(product.cantidad_nuevo || 0) > 0
                ? 'bg-eccsa-primary text-white hover:bg-eccsa-primary-dark'
                : 'bg-eccsa-gray-300 text-eccsa-gray-500 cursor-not-allowed'
              }
            `}
          >
            {(product.cantidad_nuevo || 0) > 0 ? 'Agregar' : 'Sin stock'}
          </button>

          <Link
            href={`/products/${product.id}`}
            className="px-4 py-2 border border-eccsa-gray-300 text-eccsa-gray-700 rounded-lg hover:bg-eccsa-gray-50 transition-colors"
          >
            Ver detalles
          </Link>
        </div>
      </div>
    </div>
  );
}

/**
 * Modern Product Grid Container
 * Implements responsive grid with auto-fit columns
 */
interface ModernProductGridProps {
  products: Product[];
  onAddToCart?: (product: Product) => void;
  loading?: boolean;
  className?: string;
}

export default function ModernProductGrid({
  products,
  onAddToCart,
  loading = false,
  className = ''
}: ModernProductGridProps) {
  if (loading) {
    return (
      <div className={`grid grid-cols-auto-fit-sm gap-6 lg:gap-8 ${className}`}>
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-soft animate-pulse">
            <div className="h-48 bg-eccsa-gray-200 rounded-t-xl" />
            <div className="p-6 space-y-3">
              <div className="h-4 bg-eccsa-gray-200 rounded w-3/4" />
              <div className="h-4 bg-eccsa-gray-200 rounded w-1/2" />
              <div className="h-6 bg-eccsa-gray-200 rounded w-1/3" />
              <div className="h-10 bg-eccsa-gray-200 rounded" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-eccsa-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-eccsa-gray-900 mb-2">
          No se encontraron productos
        </h3>
        <p className="text-eccsa-gray-600">
          Intenta ajustar los filtros de búsqueda
        </p>
      </div>
    );
  }

  return (
    <div className={`
      grid grid-cols-auto-fit-sm gap-6 lg:gap-8
      @container-lg:grid-cols-auto-fit-md
      ${className}
    `}>
      {products.map((product) => (
        <ModernProductCard
          key={product.id}
          product={product}
          onAddToCart={onAddToCart}
        />
      ))}
    </div>
  );
}
