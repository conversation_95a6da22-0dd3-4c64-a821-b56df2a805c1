import { NextRequest, NextResponse } from 'next/server';
import { UserModel } from '@/eccsa_back/models/User';

/**
 * PUT /api/users/[id]/photo
 * Actualiza solo la foto de un usuario
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const { photo } = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'ID de usuario inválido' },
        { status: 400 }
      );
    }

    // Verificar si el usuario existe
    const existingUser = await UserModel.getById(id);

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Actualizar solo la foto
    const userData = {
      foto_usuario: photo || null
    };

    const updated = await UserModel.update(id, userData);

    if (!updated) {
      return NextResponse.json(
        { error: 'No se pudo actualizar la foto' },
        { status: 500 }
      );
    }

    // Obtener el usuario actualizado
    const updatedUser = await UserModel.getById(id);

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'Error al obtener usuario actualizado' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      user: {
        id: updatedUser.id,
        name: updatedUser.nombre,
        username: updatedUser.nombre_usuario,
        email: updatedUser.correo,
        photo: updatedUser.foto_usuario
      },
      message: 'Foto actualizada exitosamente'
    });

  } catch (error) {
    console.error(`Error al actualizar foto del usuario con ID ${params.id}:`, error);

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
