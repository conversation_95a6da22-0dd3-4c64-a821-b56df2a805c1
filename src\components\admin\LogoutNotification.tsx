'use client';

import { useEffect, useState } from 'react';

interface LogoutNotificationProps {
  isVisible: boolean;
  message?: string;
}

export default function LogoutNotification({ 
  isVisible, 
  message = 'Cerrando sesión...' 
}: LogoutNotificationProps) {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShow(true);
    } else {
      const timer = setTimeout(() => setShow(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  if (!show) return null;

  return (
    <div className={`logout-notification ${isVisible ? 'visible' : 'hidden'}`}>
      <div className="logout-notification-content">
        <div className="logout-spinner">
          <div className="spinner-ring"></div>
        </div>
        <div className="logout-message">
          <h3>Cerrando Sesión</h3>
          <p>{message}</p>
        </div>
      </div>

      <style jsx>{`
        .logout-notification {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(10px);
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .logout-notification.visible {
          opacity: 1;
        }

        .logout-notification.hidden {
          opacity: 0;
        }

        .logout-notification-content {
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
          border-radius: 20px;
          padding: 3rem 2rem;
          text-align: center;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(255, 255, 255, 0.2);
          max-width: 400px;
          width: 90%;
        }

        .logout-spinner {
          margin-bottom: 1.5rem;
          display: flex;
          justify-content: center;
        }

        .spinner-ring {
          width: 60px;
          height: 60px;
          border: 4px solid rgba(239, 68, 68, 0.2);
          border-top: 4px solid #ef4444;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .logout-message h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1.5rem;
          font-weight: 700;
          color: #1a202c;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .logout-message p {
          margin: 0;
          color: #64748b;
          font-size: 1rem;
        }

        @media (max-width: 480px) {
          .logout-notification-content {
            padding: 2rem 1.5rem;
          }

          .spinner-ring {
            width: 50px;
            height: 50px;
          }

          .logout-message h3 {
            font-size: 1.25rem;
          }

          .logout-message p {
            font-size: 0.9rem;
          }
        }
      `}</style>
    </div>
  );
}
