/* Estilos personalizados para Eccsa Dashboard */

:root {
  /* Colores extraídos del logo de Eccsa */
  --primary-color: #0056a6; /* Azul principal de Eccsa */
  --secondary-color: #00a0e3; /* Azul secundario */
  --accent-color: #f7941d; /* Naranja acento */
  --eccsa-red: #e31e24; /* Rojo de Eccsa */
  --eccsa-dark-blue: #003366; /* Azul oscuro de Eccsa */

  /* Otros colores del sistema */
  --text-color: #333333;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #666666;
  --white: #ffffff;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;

  /* Estilos generales */
  --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;

  /* Fuentes */
  --heading-font: 'Montserrat', sans-serif;
  --body-font: 'Open Sans', sans-serif;
}

/* Reset y estilos base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #ffffff;
  color: var(--text-color);
  line-height: 1.6;
  font-size: 0.95rem; /* Reducción del tamaño base de la fuente */
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
}

/* Contenedores */
.container {
  width: 100%;
  margin: 0;
  padding: 0 40px;
}

.section {
  margin-bottom: 30px;
}

/* Contenido principal */
.main-content {
  min-height: calc(100vh - 92px);
  padding-top: 92px;
  width: 100%;
  overflow-x: hidden;
}

/* ===== NUEVO NAVBAR MODERNO ECCSA ===== */
.navbar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 30px rgba(0, 86, 166, 0.1);
  width: 100%;
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
}

.navbar {
  background: transparent;
  padding: 20px 0;
  width: 100%;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 60px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
}

.navbar-logo {
  display: flex;
  align-items: center;
  z-index: 9999;
  transition: transform 0.3s ease;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

/* Botón de menú móvil moderno */
.navbar-toggle {
  display: none;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  cursor: pointer;
  color: white;
  z-index: 9999;
  font-size: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.navbar-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 86, 166, 0.4);
}

/* Enlaces de navegación modernos */
.navbar-links {
  display: flex;
  gap: 40px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  border-radius: 50px;
  box-shadow: 0 4px 20px rgba(0, 86, 166, 0.1);
}

.navbar-link {
  color: #1a202c;
  font-weight: 600;
  text-decoration: none;
  padding: 12px 20px;
  font-size: 16px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 25px;
}

.navbar-link:hover {
  color: white;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.navbar-link.active {
  color: white;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.navbar-link.active::after {
  display: none;
}

/* Iconos de acción modernos */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.navbar-icon-button {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 86, 166, 0.1);
  color: #1a202c;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.1);
}

.navbar-icon-button:hover {
  color: white;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 86, 166, 0.3);
}

.navbar-cart {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #f7941d, #0056a6);
  color: white;
  font-size: 12px;
  font-weight: 700;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(247, 148, 29, 0.4);
  animation: pulse 2s infinite;
}

/* Estilos responsivos para el navbar moderno */
@media (max-width: 992px) {
  .navbar-container {
    padding: 0 30px;
  }

  .navbar-toggle {
    display: block;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
  }

  .navbar-links {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    z-index: 9998;
    gap: 30px;
    border-radius: 0;
    box-shadow: none;
  }

  .navbar-links.active {
    display: flex;
  }

  .navbar-link {
    padding: 20px 40px;
    width: auto;
    text-align: center;
    font-size: 20px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 86, 166, 0.1);
    min-width: 200px;
  }

  .navbar-actions {
    position: absolute;
    right: 90px;
    top: 50%;
    transform: translateY(-50%);
  }
}

@media (max-width: 576px) {
  .navbar-container {
    padding: 0 20px;
  }

  .navbar-logo img {
    max-width: 140px;
  }

  .navbar-toggle {
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .navbar-actions {
    right: 75px;
  }

  .navbar-icon-button {
    width: 45px;
    height: 45px;
    padding: 10px;
  }

  .navbar-links {
    padding: 30px 20px;
    gap: 25px;
  }

  .navbar-link {
    padding: 18px 30px;
    font-size: 18px;
    min-width: 180px;
  }
}

/* Encabezados */
.page-title {
  font-size: 24px; /* Reducido de 28px */
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--white);
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: 10px;
}

.section-title {
  font-size: 20px; /* Reducido de 22px */
  font-weight: bold;
  margin-bottom: 15px;
  color: var(--accent-color);
}

/* Tarjetas */
.card {
  background-color: #1a1a1a;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 5px rgba(255, 255, 255, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  transition: var(--transition);
  border: 1px solid #333;
}

.card:hover {
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
  border-color: var(--accent-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.card-icon {
  font-size: 24px;
  color: var(--primary-color);
}

.card-content {
  margin-bottom: 15px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-change {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.card-change.positive {
  color: var(--success-color);
}

.card-change.negative {
  color: var(--danger-color);
}

/* Grid */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

/* Botones - Estilo Rodisa */
.btn {
  display: inline-block;
  padding: 12px 25px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition);
  font-family: var(--heading-font);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 6px rgba(227, 30, 36, 0.2);
}

.btn:hover {
  background-color: #c01a1f;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(227, 30, 36, 0.3);
}

.btn-secondary {
  background-color: var(--secondary-color);
  box-shadow: 0 4px 6px rgba(0, 86, 166, 0.2);
}

.btn-secondary:hover {
  background-color: #004a8c;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 86, 166, 0.3);
}

.btn-accent {
  background-color: var(--accent-color);
  box-shadow: 0 4px 6px rgba(247, 148, 29, 0.2);
}

.btn-accent:hover {
  background-color: #e58000;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(247, 148, 29, 0.3);
}

/* Contenedor de imágenes */
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--border-radius);
  margin: 0;
  padding: 0;
  border: none;
  background-color: #000000; /* Fondo negro para coincidir con el fondo de la página */
}

/* Excepción para el slider principal */
.modern-slider-image-wrapper.image-container {
  border-radius: 0;
}

/* Utilidades */
.text-center {
  text-align: center;
}

.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

/* ===== HERO DINÁMICO SIN SLIDER ===== */
.hero-dynamic-section {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 700px;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #2a2f3e 100%);
  display: flex;
  align-items: center;
}

/* Fondo animado con formas geométricas */
.hero-animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.geometric-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(0, 86, 166, 0.1), rgba(247, 148, 29, 0.1));
  animation: floatGeometric 8s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 12s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
  animation-duration: 10s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
  animation-duration: 14s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
  animation-duration: 11s;
}

.shape-5 {
  width: 60px;
  height: 60px;
  top: 50%;
  left: 5%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.shape-6 {
  width: 90px;
  height: 90px;
  top: 70%;
  right: 25%;
  animation-delay: 5s;
  animation-duration: 13s;
}

@keyframes floatGeometric {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-30px) rotate(120deg) scale(1.1);
    opacity: 0.6;
  }
  66% {
    transform: translateY(20px) rotate(240deg) scale(0.9);
    opacity: 0.4;
  }
}

/* Grid de puntos animados */
.hero-dots-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 20px;
  padding: 40px;
  pointer-events: none;
  z-index: 1;
}

.hero-dot {
  width: 3px;
  height: 3px;
  background: rgba(247, 148, 29, 0.4);
  border-radius: 50%;
  animation: dotPulse 3s ease-in-out infinite;
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);
  }
}

/* Contenido principal */
.hero-main-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  z-index: 2;
}

.hero-content-grid {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Sección de texto */
.hero-text-section {
  animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Badge con efecto neón */
.hero-neon-badge {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.neon-text {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  z-index: 1;
  display: block;
  box-shadow: 0 0 20px rgba(0, 86, 166, 0.5);
}

.neon-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 50px;
  filter: blur(15px);
  opacity: 0.7;
  animation: neonPulse 2s ease-in-out infinite alternate;
}

@keyframes neonPulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.05);
    opacity: 1;
  }
}

/* Título dinámico con efecto typewriter */
.hero-dynamic-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.title-line-1,
.title-line-2,
.title-line-3 {
  display: block;
  opacity: 0;
  animation: typewriter 1s ease-out forwards;
}

.title-line-1 {
  color: white;
  animation-delay: 0.5s;
}

.title-line-2 {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation-delay: 1s;
}

.title-line-3 {
  color: #f7941d;
  animation-delay: 1.5s;
}

@keyframes typewriter {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Descripción */
.hero-dynamic-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 500px;
  animation: fadeInUp 1s ease-out 2s both;
}

/* Métricas animadas */
.hero-metrics {
  margin-bottom: 3rem;
  animation: fadeInUp 1s ease-out 2.5s both;
}

.metric-item {
  margin-bottom: 1.5rem;
}

.metric-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #f7941d;
  line-height: 1;
  margin-bottom: 0.5rem;
  animation: countUp 2s ease-out 3s both;
}

.metric-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.metric-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 2px;
  width: 0;
  animation: fillBar 2s ease-out 3.5s forwards;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fillBar {
  to {
    width: 100%;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Botones CTA con efectos */
.hero-cta-buttons {
  display: flex;
  gap: 1.5rem;
  animation: fadeInUp 1s ease-out 4s both;
}

.cta-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.cta-primary {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

.btn-ripple {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.cta-btn:hover .btn-ripple {
  left: 100%;
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.cta-btn:hover .btn-arrow {
  transform: translateX(3px);
}

/* Sección visual interactiva */
.hero-visual-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slideInRight 1s ease-out 0.5s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Círculos concéntricos animados */
.hero-circles {
  position: relative;
  width: 400px;
  height: 400px;
}

.circle {
  position: absolute;
  border: 2px solid rgba(247, 148, 29, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: rotateCircle 20s linear infinite;
}

.circle-1 {
  width: 350px;
  height: 350px;
  top: 25px;
  left: 25px;
  animation-duration: 25s;
}

.circle-2 {
  width: 280px;
  height: 280px;
  top: 60px;
  left: 60px;
  animation-duration: 20s;
  animation-direction: reverse;
}

.circle-3 {
  width: 210px;
  height: 210px;
  top: 95px;
  left: 95px;
  animation-duration: 15s;
}

.circle-4 {
  width: 140px;
  height: 140px;
  top: 130px;
  left: 130px;
  animation-duration: 10s;
  animation-direction: reverse;
}

@keyframes rotateCircle {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.circle-content {
  color: rgba(247, 148, 29, 0.8);
  animation: counterRotate 20s linear infinite;
}

.circle-1 .circle-content {
  animation-duration: 25s;
}

.circle-2 .circle-content {
  animation-duration: 20s;
  animation-direction: reverse;
}

.circle-3 .circle-content {
  animation-duration: 15s;
}

.circle-4 .circle-content {
  animation-duration: 10s;
  animation-direction: reverse;
}

@keyframes counterRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

/* Logo central con efectos */
.hero-logo-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.logo-glow-effect {
  position: absolute;
  top: -20px;
  left: -20px;
  width: calc(100% + 40px);
  height: calc(100% + 40px);
  background: radial-gradient(circle, rgba(0, 86, 166, 0.3), transparent 70%);
  border-radius: 50%;
  animation: logoGlow 3s ease-in-out infinite alternate;
}

.hero-main-logo {
  position: relative;
  z-index: 2;
  border-radius: 50%;
  animation: logoFloat 4s ease-in-out infinite;
}

.logo-pulse-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  border: 2px solid rgba(247, 148, 29, 0.5);
  border-radius: 50%;
  animation: pulseRing 2s ease-out infinite;
}

@keyframes logoGlow {
  0% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* Líneas conectoras animadas */
.hero-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  stroke-dasharray: 5, 5;
  animation: dashMove 3s linear infinite;
  opacity: 0.6;
}

.line-1 {
  animation-delay: 0s;
}

.line-2 {
  animation-delay: 0.5s;
}

.line-3 {
  animation-delay: 1s;
}

.line-4 {
  animation-delay: 1.5s;
}

@keyframes dashMove {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 20;
  }
}

/* Indicador de scroll */
.hero-scroll-prompt {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  animation: fadeInUp 1s ease-out 5s both;
}

.scroll-mouse {
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 15px;
  position: relative;
  display: flex;
  justify-content: center;
  padding-top: 8px;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: scrollWheel 2s ease-in-out infinite;
}

@keyframes scrollWheel {
  0%, 100% {
    transform: translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateY(15px);
    opacity: 0.3;
  }
}

.scroll-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

/* Responsive Design para Hero Dinámico */
@media (max-width: 1024px) {
  .hero-content-grid {
    gap: 3rem;
    padding: 0 30px;
  }

  .hero-dynamic-title {
    font-size: 3.5rem;
  }

  .hero-circles {
    width: 350px;
    height: 350px;
  }

  .circle-1 {
    width: 300px;
    height: 300px;
    top: 25px;
    left: 25px;
  }

  .circle-2 {
    width: 240px;
    height: 240px;
    top: 55px;
    left: 55px;
  }

  .circle-3 {
    width: 180px;
    height: 180px;
    top: 85px;
    left: 85px;
  }

  .circle-4 {
    width: 120px;
    height: 120px;
    top: 115px;
    left: 115px;
  }
}

@media (max-width: 768px) {
  .hero-dynamic-section {
    min-height: 600px;
  }

  .hero-content-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 20px;
    text-align: center;
  }

  .hero-dynamic-title {
    font-size: 2.8rem;
  }

  .hero-dynamic-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: none;
  }

  .hero-metrics {
    margin-bottom: 2rem;
  }

  .metric-number {
    font-size: 2rem;
  }

  .hero-cta-buttons {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .hero-circles {
    width: 300px;
    height: 300px;
    margin: 0 auto;
  }

  .circle-1 {
    width: 250px;
    height: 250px;
    top: 25px;
    left: 25px;
  }

  .circle-2 {
    width: 200px;
    height: 200px;
    top: 50px;
    left: 50px;
  }

  .circle-3 {
    width: 150px;
    height: 150px;
    top: 75px;
    left: 75px;
  }

  .circle-4 {
    width: 100px;
    height: 100px;
    top: 100px;
    left: 100px;
  }

  .hero-main-logo {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .hero-dynamic-section {
    min-height: 500px;
  }

  .hero-content-grid {
    padding: 0 15px;
  }

  .hero-dynamic-title {
    font-size: 2.2rem;
  }

  .hero-dynamic-description {
    font-size: 1rem;
  }

  .metric-number {
    font-size: 1.8rem;
  }

  .metric-label {
    font-size: 0.8rem;
  }

  .neon-text {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .hero-circles {
    width: 250px;
    height: 250px;
  }

  .circle-1 {
    width: 200px;
    height: 200px;
    top: 25px;
    left: 25px;
  }

  .circle-2 {
    width: 160px;
    height: 160px;
    top: 45px;
    left: 45px;
  }

  .circle-3 {
    width: 120px;
    height: 120px;
    top: 65px;
    left: 65px;
  }

  .circle-4 {
    width: 80px;
    height: 80px;
    top: 85px;
    left: 85px;
  }

  .hero-main-logo {
    width: 120px;
    height: 120px;
  }

  .hero-scroll-prompt {
    bottom: 20px;
  }

  .geometric-shape {
    display: none;
  }

  .hero-dots-grid {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(4, 1fr);
    padding: 20px;
  }
}

/* Contenido principal del hero */
.hero-content-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  z-index: 3;
}

.hero-content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Badge animado */
.hero-badge {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-badge-text {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
  display: block;
}

.hero-badge-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 50px;
  filter: blur(10px);
  opacity: 0.5;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Título principal */
.hero-title {
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-title-line {
  display: block;
  color: white;
  margin-bottom: 0.5rem;
}

.hero-title-highlight {
  display: block;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Descripción */
.hero-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 600px;
  animation: fadeInUp 1s ease-out 0.6s both;
}

/* Estadísticas */
.hero-stats {
  display: flex;
  gap: 3rem;
  margin-bottom: 3rem;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-stat {
  text-align: center;
}

.hero-stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #f7941d;
  line-height: 1;
  margin-bottom: 0.5rem;
  animation: countUp 2s ease-out 1s both;
}

.hero-stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Botones de acción */
.hero-actions {
  display: flex;
  gap: 1.5rem;
  animation: fadeInUp 1s ease-out 1s both;
}

.hero-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.hero-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-btn-primary {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.hero-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

.hero-btn-icon {
  transition: transform 0.3s ease;
}

.hero-btn:hover .hero-btn-icon {
  transform: translateX(3px);
}

/* Navegación moderna */
.hero-navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 40px;
  z-index: 4;
  pointer-events: none;
}

.hero-nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  pointer-events: auto;
}

.hero-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.hero-nav-btn svg {
  transition: transform 0.3s ease;
}

.hero-nav-btn:hover svg {
  transform: scale(1.2);
}

/* Indicadores de progreso */
.hero-indicators {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 4;
}

.hero-indicator {
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hero-indicator:hover {
  background: rgba(255, 255, 255, 0.5);
}

.hero-indicator-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 2px;
  width: 0;
  transition: width 0.3s ease;
}

.hero-indicator.active .hero-indicator-progress {
  width: 100%;
}

/* Scroll indicator */
.hero-scroll-indicator {
  position: absolute;
  bottom: 40px;
  right: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  z-index: 4;
  animation: fadeInUp 1s ease-out 1.2s both;
}

.hero-scroll-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.hero-scroll-line {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, #f7941d, transparent);
  animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.5;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

/* Animación fadeInUp */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design para Hero Slider */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 3.5rem;
  }

  .hero-description {
    font-size: 1.2rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .hero-stat-number {
    font-size: 2rem;
  }

  .hero-navigation {
    padding: 0 20px;
  }

  .hero-nav-btn {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 768px) {
  .hero-slider-modern {
    min-height: 600px;
  }

  .hero-content-container {
    padding: 0 20px;
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .hero-stats {
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .hero-stat-number {
    font-size: 1.8rem;
  }

  .hero-stat-label {
    font-size: 0.8rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-btn {
    justify-content: center;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .hero-indicators {
    bottom: 20px;
    gap: 0.5rem;
  }

  .hero-indicator {
    width: 40px;
  }

  .hero-scroll-indicator {
    display: none;
  }

  .hero-navigation {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-slider-modern {
    min-height: 500px;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .hero-stat {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .hero-stat-number {
    font-size: 1.5rem;
    margin-bottom: 0;
  }

  .hero-stat-label {
    font-size: 0.75rem;
  }

  .hero-badge-text {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .hero-indicators {
    bottom: 15px;
  }

  .hero-indicator {
    width: 30px;
    height: 3px;
  }
}

.modern-slider-caption {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 10%;
  background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
  color: var(--white);
}

.caption-content {
  max-width: 600px;
}

.modern-slider-title {
  font-size: 2.5rem; /* Reducido de 3rem */
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.modern-slider-description {
  font-size: 1.3rem; /* Reducido de 1.5rem */
  margin-bottom: 2rem;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.slider-cta-button {
  background-color: var(--accent-color);
  color: var(--white);
  border: none;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.slider-cta-button:hover {
  background-color: #ff8c00;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.modern-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.modern-slider-arrow:hover {
  background: linear-gradient(135deg, #f7941d, #0056a6);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.modern-slider-arrow-left {
  left: 30px;
}

.modern-slider-arrow-right {
  right: 30px;
}

.modern-slider-dots {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 10;
  gap: 10px;
}

.modern-slider-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  border: 2px solid rgba(0, 86, 166, 0.3);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.modern-slider-dot.active {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-color: #f7941d;
  transform: scale(1.3);
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.4);
}

/* Modern Layout - Pantalla completa */
.modern-layout {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* Evita scroll horizontal */
}

/* Ajuste para el navbar fijo moderno */
.hero-section {
  margin-top: 90px; /* Margen para el nuevo navbar más alto */
  width: 100%;
  max-width: 100%;
  padding: 0;
  overflow: hidden;
  position: relative;
  background-color: #ffffff; /* Fondo blanco para coincidir con el nuevo diseño */
  border: none;
}

/* ===== MODERN HERO SECTION STYLES ===== */
.modern-hero-section {
  position: relative;
  min-height: 90vh;
  background: #ffffff;
  overflow: visible;
  display: flex;
  align-items: center;
  padding: 2rem 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 86, 166, 0.02) 0%,
    rgba(247, 148, 29, 0.02) 50%,
    rgba(0, 86, 166, 0.02) 100%);
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 86, 166, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(247, 148, 29, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: patternFloat 20s ease-in-out infinite;
}

@keyframes patternFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(1deg); }
  66% { transform: translate(-20px, 20px) rotate(-1deg); }
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-content-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
  min-height: 80vh;
}

/* Lado izquierdo - Contenido */
.hero-content-left {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-company-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 86, 166, 0.1);
  color: #0056a6;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  width: fit-content;
  border: 1px solid rgba(0, 86, 166, 0.2);
  transition: all 0.3s ease;
}

.hero-company-badge:hover {
  background: rgba(0, 86, 166, 0.15);
  transform: translateY(-2px);
}

.badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-main-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  color: #1a1a1a;
  margin: 0;
}

.title-line {
  display: block;
  margin-bottom: 0.2rem;
}

.title-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-description {
  font-size: 1.2rem;
  color: #666;
  line-height: 1.6;
  max-width: 500px;
  margin: 0;
}

/* Layout horizontal compacto para métricas y botones */
.hero-bottom-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hero-stats-compact {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Botones de acción */
.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #0056a6, #003d7a);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #0056a6;
  border-color: #0056a6;
}

.btn-secondary:hover {
  background: #0056a6;
  color: white;
  transform: translateY(-2px);
}

/* Lado derecho - Layout horizontal */
.hero-content-right {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slideInRight 0.8s ease-out 0.2s both;
}

.hero-right-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== ENGRANAJE INDUSTRIAL FUTURISTA HERO ===== */
.hero-industrial-gear-container {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto 2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Anillos giratorios concéntricos */
.hero-gear-ring {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(0, 86, 166, 0.2);
}

.ring-outer {
  width: 380px;
  height: 380px;
  border: 3px dashed rgba(0, 86, 166, 0.3);
  animation: heroGearRotate 40s linear infinite;
}

.ring-middle {
  width: 300px;
  height: 300px;
  border: 2px solid rgba(247, 148, 29, 0.4);
  animation: heroGearRotateReverse 30s linear infinite;
}

.ring-inner {
  width: 220px;
  height: 220px;
  border: 2px dotted rgba(0, 86, 166, 0.5);
  animation: heroGearRotate 20s linear infinite;
}

/* Elementos decorativos en los anillos */
.gear-teeth {
  font-size: 1.5rem;
  color: rgba(0, 86, 166, 0.6);
  animation: heroGearCounterRotate 40s linear infinite;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
}

.ring-middle .gear-teeth {
  font-size: 1.3rem;
  color: rgba(247, 148, 29, 0.7);
  animation: heroGearCounterRotateReverse 30s linear infinite;
}

.gear-particles {
  font-size: 1rem;
  color: rgba(0, 86, 166, 0.8);
  animation: heroGearCounterRotate 20s linear infinite;
  filter: drop-shadow(0 0 10px rgba(0, 86, 166, 0.5));
}

/* Efectos de resplandor */
.hero-gear-glow-outer {
  position: absolute;
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(0, 86, 166, 0.15) 0%, transparent 70%);
  border-radius: 50%;
  animation: heroGearPulse 6s ease-in-out infinite;
}

.hero-gear-glow-inner {
  position: absolute;
  width: 280px;
  height: 280px;
  background: radial-gradient(circle, rgba(247, 148, 29, 0.2) 0%, transparent 60%);
  border-radius: 50%;
  animation: heroGearPulseReverse 4s ease-in-out infinite;
}

/* Centro del engranaje con logo */
.hero-gear-center {
  position: relative;
  z-index: 10;
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-gear-logo-wrapper {
  position: relative;
  z-index: 12;
  width: 160px;
  height: 160px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
  border: 4px solid rgba(0, 86, 166, 0.2);
  animation: heroGearCenterRotate 35s linear infinite;
  transition: all 0.4s ease;
}

.hero-gear-logo-wrapper:hover {
  transform: scale(1.1);
  box-shadow: 0 20px 60px rgba(0, 86, 166, 0.3);
  border-color: rgba(0, 86, 166, 0.5);
}

.hero-gear-logo {
  width: 130px;
  height: auto;
  filter: drop-shadow(0 5px 20px rgba(0, 0, 0, 0.3));
  transition: all 0.4s ease;
}

.hero-gear-logo-wrapper:hover .hero-gear-logo {
  filter: drop-shadow(0 8px 30px rgba(0, 86, 166, 0.4));
  transform: scale(1.05);
}

.hero-gear-pulse {
  position: absolute;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 86, 166, 0.3) 0%, transparent 50%);
  border-radius: 50%;
  animation: heroGearPulseCenter 3s ease-in-out infinite;
  z-index: 11;
}

/* Engranajes flotantes decorativos */
.hero-floating-gear {
  position: absolute;
  font-size: 2.5rem;
  color: rgba(0, 86, 166, 0.4);
  z-index: 5;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

.hero-floating-gear:hover {
  color: rgba(0, 86, 166, 0.8);
  transform: scale(1.2);
}

.gear-float-1 {
  top: 5%;
  right: 10%;
  animation: heroFloatGear1 12s ease-in-out infinite;
}

.gear-float-2 {
  top: 15%;
  left: 5%;
  animation: heroFloatGear2 15s ease-in-out infinite;
}

.gear-float-3 {
  bottom: 10%;
  right: 5%;
  animation: heroFloatGear3 18s ease-in-out infinite;
}

.gear-float-4 {
  bottom: 20%;
  left: 10%;
  animation: heroFloatGear4 14s ease-in-out infinite;
}

.gear-float-5 {
  top: 50%;
  right: -5%;
  animation: heroFloatGear5 16s ease-in-out infinite;
}

/* ===== ANIMACIONES ENGRANAJE FUTURISTA ===== */
@keyframes heroGearRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes heroGearRotateReverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes heroGearCenterRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes heroGearCounterRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes heroGearCounterRotateReverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes heroGearPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes heroGearPulseReverse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.9);
  }
}

@keyframes heroGearPulseCenter {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes heroFloatGear1 {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
  }
}

@keyframes heroFloatGear2 {
  0%, 100% {
    transform: translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateX(10px) rotate(-90deg);
  }
  50% {
    transform: translateX(-5px) rotate(-180deg);
  }
  75% {
    transform: translateX(15px) rotate(-270deg);
  }
}

@keyframes heroFloatGear3 {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(12px) rotate(120deg);
  }
  66% {
    transform: translateY(-8px) rotate(240deg);
  }
}

@keyframes heroFloatGear4 {
  0%, 100% {
    transform: translateX(0px) rotate(0deg);
  }
  50% {
    transform: translateX(-12px) rotate(180deg);
  }
}

@keyframes heroFloatGear5 {
  0%, 100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
  25% {
    transform: translate(-8px, -10px) rotate(90deg);
  }
  50% {
    transform: translate(5px, -15px) rotate(180deg);
  }
  75% {
    transform: translate(-3px, -5px) rotate(270deg);
  }
}

/* Sección de logos de marcas - Layout horizontal */
.brand-logos-section-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.brand-logos-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-align: center;
}

.brand-logos-horizontal {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: center;
}

.brand-logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: transform 0.3s ease;
}

.brand-logo-item:hover {
  transform: translateY(-3px);
}

.brand-logo-circle {
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 2px solid rgba(0, 86, 166, 0.1);
  transition: all 0.3s ease;
}

.brand-logo-circle:hover {
  border-color: rgba(0, 86, 166, 0.3);
  box-shadow: 0 6px 20px rgba(0, 86, 166, 0.15);
}

.brand-logo-img {
  width: 70%;
  height: 70%;
  object-fit: contain;
  filter: grayscale(30%);
  transition: filter 0.3s ease;
}

.brand-logo-item:hover .brand-logo-img {
  filter: grayscale(0%);
}

.brand-name {
  font-size: 0.7rem;
  font-weight: 600;
  color: #666;
  text-align: center;
}

/* Elementos decorativos flotantes */
.hero-decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  color: rgba(0, 86, 166, 0.3);
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  left: 15%;
  animation-delay: 2s;
}

.element-3 {
  bottom: 20%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-hero-section {
    height: auto;
    min-height: 100vh;
    padding: 1rem 0;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .hero-content-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
    height: auto;
  }

  .hero-content-left {
    order: 1;
    gap: 1rem;
  }

  .hero-content-right {
    order: 0;
  }

  .hero-right-horizontal {
    gap: 1rem;
  }

  .hero-main-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero-stats-compact {
    justify-content: center;
    gap: 1rem;
  }

  .hero-bottom-section {
    gap: 0.75rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .brand-logos-horizontal {
    gap: 1rem;
  }

  .brand-logo-circle {
    width: 45px;
    height: 45px;
  }

  .hero-industrial-gear-container {
    width: 320px;
    height: 320px;
  }

  .ring-outer {
    width: 300px;
    height: 300px;
  }

  .ring-middle {
    width: 240px;
    height: 240px;
  }

  .ring-inner {
    width: 180px;
    height: 180px;
  }

  .hero-gear-glow-outer {
    width: 280px;
    height: 280px;
  }

  .hero-gear-glow-inner {
    width: 220px;
    height: 220px;
  }

  .hero-gear-logo-wrapper {
    width: 130px;
    height: 130px;
  }

  .hero-gear-logo {
    width: 100px;
  }

  .hero-floating-gear {
    font-size: 2rem;
  }

  .floating-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-stats-compact {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .brand-logos-horizontal {
    gap: 0.75rem;
  }

  .brand-logo-circle {
    width: 40px;
    height: 40px;
  }

  .hero-industrial-gear-container {
    width: 280px;
    height: 280px;
  }

  .ring-outer {
    width: 260px;
    height: 260px;
  }

  .ring-middle {
    width: 200px;
    height: 200px;
  }

  .ring-inner {
    width: 150px;
    height: 150px;
  }

  .hero-gear-glow-outer {
    width: 240px;
    height: 240px;
  }

  .hero-gear-glow-inner {
    width: 180px;
    height: 180px;
  }

  .hero-gear-logo-wrapper {
    width: 110px;
    height: 110px;
  }

  .hero-gear-logo {
    width: 85px;
  }

  .hero-floating-gear {
    font-size: 1.5rem;
  }

  .gear-teeth {
    font-size: 1.2rem;
  }

  .ring-middle .gear-teeth {
    font-size: 1rem;
  }

  .gear-particles {
    font-size: 0.8rem;
  }

  .stat-number {
    font-size: 1.75rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }
}

/* ===== MODERN CATALOG PAGE STYLES ===== */
.modern-catalog-page {
  margin-top: 90px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* ===== HERO SECTION ===== */
.catalog-hero {
  position: relative;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  padding: 4rem 2rem;
  overflow: hidden;
}

.catalog-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.6s ease-out;
}

.badge-icon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.catalog-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.title-highlight {
  background: linear-gradient(135deg, #f7941d, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.catalog-hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 3rem;
  opacity: 0.9;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #f7941d;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: float 6s ease-in-out infinite;
}

.decoration-circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.decoration-circle-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 5%;
  animation-delay: 2s;
}

.decoration-circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  right: 5%;
  animation-delay: 4s;
}

/* ===== ADVANCED FILTERS SECTION ===== */
.advanced-filters-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 2rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.advanced-filters-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
}

.filters-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Filter Header */
.filter-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.filter-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-icon-badge {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #0056a6, #003366);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.3);
}

.filter-title-content h3.filter-main-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  background: linear-gradient(135deg, #1f2937, #0056a6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filter-subtitle {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

.filter-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.results-count-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Main Filters Container */
.filters-main-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Primary Search */
.primary-search-container {
  width: 100%;
}

.search-wrapper {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-icon-container {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 2;
}

.advanced-search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  background: white;
  color: #1f2937;
  transition: all 0.3s ease;
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.advanced-search-input:focus {
  border-color: #0056a6;
  box-shadow: 0 0 0 4px rgba(0, 86, 166, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.advanced-search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.clear-search-btn {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  background: #e5e7eb;
  color: #374151;
  transform: translateY(-50%) scale(1.1);
}

/* Filter Pills */
.filter-pills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

.filter-pill-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 0.5rem;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.filter-pill-wrapper:hover {
  border-color: #0056a6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.15);
}

.filter-pill-wrapper:hover .pill-arrow {
  color: #0056a6;
  transform: scale(1.1);
}

.pill-icon {
  color: #0056a6;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  pointer-events: none;
}

.pill-select {
  border: none;
  background: transparent;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  padding: 0.25rem 0.5rem;
  min-width: 120px;
  flex: 1;
  position: relative;
  z-index: 2;
}

.pill-arrow {
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  transition: all 0.2s ease;
  pointer-events: none;
  margin-left: auto;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.clear-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* ===== RESULTS SECTION ===== */
.catalog-results-section {
  padding: 3rem 2rem;
  background: #f8fafc;
}

.results-container {
  max-width: 1400px;
  margin: 0 auto;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.results-info {
  flex: 1;
}

.results-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.results-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: #6b7280;
}

.count-number {
  font-weight: 700;
  color: #0056a6;
  font-size: 1.2rem;
}

.count-text {
  color: #374151;
}

.count-category {
  color: #f7941d;
  font-weight: 600;
}

/* ===== ESTILOS MODERNOS PARA MODALES DE ALMACÉN ===== */

/* Modal overlay mejorado */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

/* Container del modal moderno */
.modal-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal grande para formularios */
.modal-container.modal-large {
  max-width: 1000px;
  width: 95%;
}

/* Modal pequeño para confirmaciones */
.modal-container.modal-small {
  max-width: 450px;
}

/* Header del modal moderno */
.modal-header {
  background: linear-gradient(135deg, #1a1d29 0%, #2d3748 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.modal-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-header h2::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 2px;
}

/* Botón de cerrar moderno */
.modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
  font-weight: 300;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Contenido del modal */
.modal-content {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

/* Formulario del modal moderno */
.modal-form {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

/* Grupos de formulario modernos */
.modal-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-form-group label::after {
  content: '';
  width: 2px;
  height: 12px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  border-radius: 1px;
  opacity: 0.6;
}

/* Inputs modernos */
.modal-form-group input,
.modal-form-group textarea,
.modal-form-group select {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #374151;
}

.modal-form-group input:focus,
.modal-form-group textarea:focus,
.modal-form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.modal-form-group input:hover,
.modal-form-group textarea:hover,
.modal-form-group select:hover {
  border-color: #d1d5db;
}

/* Textarea específico */
.modal-form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Inputs de solo lectura */
.modal-form-group input[readonly] {
  background: #f9fafb;
  border-color: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
}

/* Small text bajo inputs */
.modal-form-group small {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Botones de acción del modal */
.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem 2rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  margin-top: auto;
}

.modal-submit,
.modal-cancel,
.modal-delete {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.modal-submit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modal-submit:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.modal-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.modal-cancel {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.modal-cancel:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
}

.modal-delete {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.modal-delete:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* Efectos de ripple para botones */
.modal-submit::before,
.modal-delete::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modal-submit:hover::before,
.modal-delete:hover::before {
  left: 100%;
}

/* Iconos de advertencia y confirmación */
.modal-warning-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.modal-warning-content {
  text-align: center;
  margin-bottom: 1.5rem;
}

.modal-warning-content p {
  color: #374151;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.modal-warning-content strong {
  color: #1f2937;
  font-weight: 700;
}

/* Elementos específicos para modales de almacén */
.modal-product-info {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.modal-product-info h4 {
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modal-product-info p {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

/* Indicadores de estado */
.modal-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Campos de formulario con iconos */
.modal-form-group-with-icon {
  position: relative;
}

.modal-form-group-with-icon input {
  padding-left: 2.5rem;
}

.modal-form-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1rem;
  pointer-events: none;
}

.modal-form-group-with-icon input:focus + .modal-form-icon {
  color: #3b82f6;
}

/* Responsive Design para Modales */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-container {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 12px;
    max-height: 95vh;
  }

  .modal-container.modal-large {
    width: 100%;
    max-width: none;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-header h2 {
    font-size: 1.125rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .modal-form {
    padding: 1.5rem;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modal-actions {
    padding: 1rem 1.5rem;
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .modal-submit,
  .modal-cancel,
  .modal-delete {
    width: 100%;
    justify-content: center;
    padding: 1rem 1.5rem;
  }

  .modal-form-group label {
    font-size: 0.8rem;
  }

  .modal-form-group input,
  .modal-form-group textarea,
  .modal-form-group select {
    font-size: 0.875rem;
    padding: 0.875rem 1rem;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.25rem;
  }

  .modal-container {
    border-radius: 8px;
    max-height: 98vh;
  }

  .modal-header {
    padding: 0.875rem 1rem;
  }

  .modal-header h2 {
    font-size: 1rem;
  }

  .modal-content {
    padding: 1rem;
  }

  .modal-form {
    padding: 1rem;
    gap: 0.75rem;
  }

  .modal-actions {
    padding: 0.875rem 1rem;
  }

  .modal-close {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}

/* Animaciones adicionales para elementos específicos */
.modal-form-group {
  animation: slideInUp 0.3s ease-out;
}

.modal-form-group:nth-child(1) { animation-delay: 0.1s; }
.modal-form-group:nth-child(2) { animation-delay: 0.15s; }
.modal-form-group:nth-child(3) { animation-delay: 0.2s; }
.modal-form-group:nth-child(4) { animation-delay: 0.25s; }
.modal-form-group:nth-child(5) { animation-delay: 0.3s; }
.modal-form-group:nth-child(6) { animation-delay: 0.35s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estados de carga para botones */
.modal-submit.loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.modal-submit.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Mejoras para accesibilidad */
.modal-overlay:focus-within .modal-container {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.modal-form-group input:invalid {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.modal-form-group input:invalid:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Tooltips para campos de formulario */
.modal-form-tooltip {
  position: relative;
  display: inline-block;
}

.modal-form-tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #1f2937;
  color: white;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1001;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
}

.modal-form-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* ===== ESTILOS MODERNOS PARA PÁGINA DE ALMACÉN ===== */

/* Contenedor principal */
.modern-warehouse-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* Header moderno */
.warehouse-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.title-text h1.warehouse-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.warehouse-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refresh-button,
.add-product-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.refresh-button {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.refresh-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.add-product-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.add-product-button:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Estadísticas modernas */
.warehouse-stats {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--card-color);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  --card-color: #3b82f6;
}

.stat-card.success {
  --card-color: #10b981;
}

.stat-card.info {
  --card-color: #06b6d4;
}

.stat-card.accent {
  --card-color: #f59e0b;
}

.stat-card.warning {
  --card-color: #f59e0b;
}

.stat-card.danger {
  --card-color: #ef4444;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--card-color);
  background: color-mix(in srgb, var(--card-color) 10%, transparent);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

/* Sección de productos */
.warehouse-products {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

.products-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.products-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.products-title p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.products-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-container svg {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #374151;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.filter-container {
  min-width: 200px;
}

.filter-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
  color: #374151;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Contenido de productos */
.products-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Estados de carga, error y vacío modernos */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 400px;
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state h3,
.error-state h3,
.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.loading-state p,
.error-state p,
.empty-state p {
  color: #64748b;
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  max-width: 400px;
  line-height: 1.6;
}

.error-icon,
.empty-icon {
  margin-bottom: 1.5rem;
  color: #64748b;
}

.error-icon {
  color: #ef4444;
}

.retry-button,
.add-first-product-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.retry-button {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.retry-button:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.add-first-product-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.add-first-product-button:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Responsive Design para Almacén */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .header-title-section {
    justify-content: center;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  .products-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    min-width: auto;
  }

  .filter-container {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .warehouse-header {
    margin-bottom: 1rem;
  }

  .header-content {
    padding: 1.5rem;
  }

  .title-icon {
    width: 48px;
    height: 48px;
  }

  .title-text h1.warehouse-title {
    font-size: 1.5rem;
  }

  .warehouse-subtitle {
    font-size: 0.875rem;
  }

  .warehouse-stats {
    padding: 0 1rem;
    margin-bottom: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .warehouse-products {
    padding: 0 1rem 1rem 1rem;
  }

  .products-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .products-title h2 {
    font-size: 1.25rem;
  }

  .products-title p {
    font-size: 0.875rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .refresh-button,
  .add-product-button {
    width: 100%;
    justify-content: center;
  }

  .loading-state,
  .error-state,
  .empty-state {
    padding: 2rem 1rem;
    min-height: 300px;
  }

  .loading-state h3,
  .error-state h3,
  .empty-state h3 {
    font-size: 1.125rem;
  }

  .loading-state p,
  .error-state p,
  .empty-state p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 1rem;
  }

  .title-text h1.warehouse-title {
    font-size: 1.25rem;
  }

  .warehouse-subtitle {
    font-size: 0.8rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .products-header {
    padding: 1rem;
  }

  .products-title h2 {
    font-size: 1.125rem;
  }

  .search-input {
    padding: 0.875rem 1rem 0.875rem 3rem;
  }

  .filter-select {
    padding: 0.875rem 1rem;
  }
}

/* ===== ESTILOS MODERNOS PARA PÁGINA DE EMPLEADOS ===== */

/* Contenedor principal */
.modern-employees-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* Header de empleados */
.employees-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.employees-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.employees-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.add-employee-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.add-employee-button:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Estadísticas de empleados */
.employees-stats {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

/* Mensajes de estado modernos */
.success-message,
.error-message {
  max-width: 1400px;
  margin: 0 auto 1rem auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
  animation: slideInDown 0.3s ease-out;
}

.success-message {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
  border: 1px solid #86efac;
}

.error-message {
  background: linear-gradient(135deg, #fef2f2, #fecaca);
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.message-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Contenido de empleados */
.employees-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

.employees-header-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.employees-title-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.employees-title-section p {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
}

.employees-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.add-first-employee-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.add-first-employee-button:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Responsive Design para Empleados */
@media (max-width: 1024px) {
  .employees-stats {
    padding: 0 1rem;
  }

  .employees-content {
    padding: 0 1rem 1rem 1rem;
  }

  .success-message,
  .error-message {
    padding: 1rem;
    margin: 0 1rem 1rem 1rem;
  }
}

@media (max-width: 768px) {
  .employees-header-section {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .employees-title-section h2 {
    font-size: 1.25rem;
  }

  .employees-title-section p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .employees-header-section {
    padding: 1rem;
  }

  .employees-title-section h2 {
    font-size: 1.125rem;
  }
}

/* ===== ESTILOS MODERNOS PARA PÁGINA DE SOLICITUDES ===== */

/* Contenedor principal */
.modern-requests-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* Header de solicitudes */
.requests-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.requests-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.requests-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Toggle de auto-actualizar */
.auto-refresh-toggle {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-slider {
  background: #3b82f6;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-text {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Estadísticas de solicitudes */
.requests-stats {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

/* Contenido de solicitudes */
.requests-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

.requests-header-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.requests-title-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.requests-title-section p {
  color: #64748b;
  margin: 0;
  font-size: 1rem;
}

.requests-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.requests-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

/* Cards de solicitudes mejoradas */
.solicitudes-adaptive-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.solicitud-adaptive-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.solicitud-adaptive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.solicitud-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
}

.solicitud-id {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.125rem;
}

.solicitud-date {
  font-size: 0.875rem;
  color: #64748b;
}

.estado-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.solicitud-content {
  margin-bottom: 1rem;
}

.product-model {
  font-size: 1.125rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
  display: block;
}

.product-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.product-brand,
.product-alm,
.product-shelf {
  font-size: 0.875rem;
  color: #64748b;
  background: #f8fafc;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.solicitud-client {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.client-email {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.client-phone {
  font-size: 0.875rem;
  color: #64748b;
}

.solicitud-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric-item {
  text-align: center;
}

.metric-item label {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-weight: 600;
  color: #1e293b;
}

.stock-warning {
  display: block;
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

.stock-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
}

.stock-badge.in-stock {
  background: #dcfce7;
  color: #166534;
}

.stock-badge.out-stock {
  background: #fef2f2;
  color: #991b1b;
}

.metric-value.price {
  font-size: 0.875rem;
  color: #059669;
}

.solicitud-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn {
  background: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.view-btn:hover {
  background: #f1f5f9;
  border-color: #9ca3af;
}

.edit-btn {
  background: #dbeafe;
  color: #1d4ed8;
  border-color: #93c5fd;
}

.edit-btn:hover {
  background: #bfdbfe;
  border-color: #60a5fa;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fca5a5;
}

.delete-btn:hover {
  background: #fecaca;
  border-color: #f87171;
}

.btn-icon {
  font-size: 0.875rem;
}

.btn-text {
  font-size: 0.75rem;
}

/* Responsive Design para Solicitudes */
@media (max-width: 1024px) {
  .requests-stats {
    padding: 0 1rem;
  }

  .requests-content {
    padding: 0 1rem 1rem 1rem;
  }

  .solicitudes-adaptive-container {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .requests-header-section {
    flex-direction: column;
    align-items: stretch;
    padding: 1.5rem;
  }

  .requests-title-section h2 {
    font-size: 1.25rem;
  }

  .requests-filters {
    justify-content: stretch;
  }

  .filter-container {
    flex: 1;
  }

  .solicitudes-adaptive-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .solicitud-metrics {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .solicitud-actions {
    justify-content: center;
  }

  .action-btn {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .requests-header-section {
    padding: 1rem;
  }

  .requests-title-section h2 {
    font-size: 1.125rem;
  }

  .solicitud-adaptive-card {
    padding: 1rem;
  }

  .solicitud-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .product-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    padding: 0.75rem;
    font-size: 0.75rem;
  }
}

/* ===== ESTILOS MODERNOS PARA DASHBOARD ===== */

/* Contenedor principal del dashboard */
.modern-dashboard-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* Header del dashboard */
.dashboard-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.dashboard-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.last-update {
  font-size: 0.875rem;
  color: #64748b;
  background: #f8fafc;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* Alertas del dashboard */
.dashboard-alerts {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.alerts-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.clear-alerts-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-alerts-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid var(--alert-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.alert-item.unread {
  background: #fefefe;
  border-left-width: 6px;
}

.alert-item.read {
  opacity: 0.7;
}

.alert-item.success {
  --alert-color: #10b981;
}

.alert-item.warning {
  --alert-color: #f59e0b;
}

.alert-item.danger {
  --alert-color: #ef4444;
}

.alert-item.info {
  --alert-color: #3b82f6;
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: color-mix(in srgb, var(--alert-color) 10%, transparent);
  color: var(--alert-color);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.alert-message {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.alert-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

/* Estadísticas del dashboard */
.dashboard-stats {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.stat-change {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-change .positive {
  color: #10b981;
}

.stat-change .negative {
  color: #ef4444;
}

/* Acciones rápidas */
.dashboard-actions {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.dashboard-actions h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid transparent;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--action-color);
}

.action-card.primary {
  --action-color: #3b82f6;
}

.action-card.success {
  --action-color: #10b981;
}

.action-card.info {
  --action-color: #06b6d4;
}

.action-card.warning {
  --action-color: #f59e0b;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: color-mix(in srgb, var(--action-color) 10%, transparent);
  color: var(--action-color);
}

.action-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.action-content p {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
}

.action-stat {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--action-color);
  background: color-mix(in srgb, var(--action-color) 10%, transparent);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

/* Actividades recientes */
.dashboard-activities {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.activity-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.activity-section.highlight {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.activity-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.activity-count {
  background: #f1f5f9;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.activity-content {
  padding: 1.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: #f1f5f9;
  transform: translateX(2px);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: #64748b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.activity-info {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.activity-subtitle {
  color: #64748b;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.activity-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

.activity-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.activity-status.pendiente {
  background: #fef3c7;
  color: #92400e;
}

.activity-status.procesando {
  background: #dbeafe;
  color: #1e40af;
}

.activity-status.cotizado {
  background: #dcfce7;
  color: #166534;
}

.activity-status.rechazado {
  background: #fecaca;
  color: #991b1b;
}

.activity-status.admin {
  background: #fecaca;
  color: #991b1b;
}

.activity-status.manager {
  background: #fef3c7;
  color: #92400e;
}

.activity-status.engineer {
  background: #dbeafe;
  color: #1e40af;
}

.activity-empty {
  text-align: center;
  padding: 2rem;
  color: #9ca3af;
}

/* Producto destacado */
.featured-product {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
}

.product-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f59e0b;
  color: white;
}

.product-info {
  flex: 1;
}

.product-number {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.product-brand {
  color: #f59e0b;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.product-model {
  color: #64748b;
  font-size: 0.75rem;
}

/* Responsive Design para Dashboard */
@media (max-width: 1024px) {
  .dashboard-stats,
  .dashboard-actions,
  .dashboard-activities,
  .dashboard-alerts {
    padding: 0 1rem;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .activities-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    margin-bottom: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .header-title-section {
    justify-content: center;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-subtitle {
    font-size: 0.875rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: 1rem;
  }

  .activities-grid {
    grid-template-columns: 1fr;
  }

  .activity-header {
    padding: 1rem;
  }

  .activity-content {
    padding: 1rem;
  }

  .alerts-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .alert-icon {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .dashboard-stats,
  .dashboard-actions,
  .dashboard-activities,
  .dashboard-alerts {
    padding: 0 0.5rem;
  }

  .header-content {
    padding: 1rem;
  }

  .dashboard-title {
    font-size: 1.25rem;
  }

  .dashboard-subtitle {
    font-size: 0.8rem;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .featured-product {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .alert-item {
    padding: 0.75rem;
  }

  .alert-icon {
    width: 32px;
    height: 32px;
  }
}

/* ===== ESTILOS PARA LISTADO DE PRECIOS ===== */

/* Contenedor principal */
.modern-price-list-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
}

/* Header de la página */
.price-list-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

/* Filtros */
.price-list-filters {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 1.5rem;
  align-items: end;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-container svg {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sort-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-toggle:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.sort-toggle.asc {
  color: #059669;
  border-color: #059669;
}

.sort-toggle.desc {
  color: #dc2626;
  border-color: #dc2626;
}

.results-info {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Contenido de la tabla */
.price-list-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.price-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.price-table {
  width: 100%;
  border-collapse: collapse;
}

.price-table thead {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.price-table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
}

.price-table tbody tr {
  transition: all 0.2s ease;
}

.price-table tbody tr:hover {
  background: #f9fafb;
}

.price-table tbody tr:not(:last-child) {
  border-bottom: 1px solid #f3f4f6;
}

.price-table td {
  padding: 1rem;
  font-size: 0.875rem;
  color: #374151;
  vertical-align: middle;
}

.product-number {
  font-weight: 600;
  color: #1f2937;
  font-family: 'Courier New', monospace;
}

.product-brand {
  color: #3b82f6;
  font-weight: 500;
}

.product-model {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-stock {
  font-weight: 600;
  text-align: center;
}

.product-stock.no-stock {
  color: #dc2626;
  background: #fef2f2;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.product-stock.low-stock {
  color: #d97706;
  background: #fffbeb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.product-price {
  font-weight: 600;
  text-align: right;
  font-family: 'Courier New', monospace;
}

.product-price.sale {
  color: #059669;
  font-size: 0.9rem;
}

.product-margin {
  font-weight: 600;
  text-align: center;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .price-list-filters,
  .price-list-content {
    padding: 0 1rem;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .price-table-container {
    overflow-x: auto;
  }

  .price-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .price-list-header {
    margin-bottom: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .header-title-section {
    justify-content: center;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filters-row {
    gap: 0.75rem;
  }

  .price-table th,
  .price-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .product-model {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .price-list-filters,
  .price-list-content {
    padding: 0 0.5rem;
  }

  .header-content {
    padding: 1rem;
  }

  .price-table th,
  .price-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  .product-model {
    max-width: 100px;
  }

  .action-btn {
    padding: 0.75rem;
    font-size: 0.75rem;
  }
}

/* Estilos para impresión */
@media print {
  .modern-price-list-page {
    background: white;
  }

  .price-list-header,
  .price-list-filters {
    display: none;
  }

  .price-list-content {
    max-width: none;
    padding: 0;
  }

  .price-table-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .price-table th,
  .price-table td {
    border: 1px solid #000;
    padding: 0.5rem;
  }

  .price-table thead {
    background: #f0f0f0 !important;
  }
}

/* ===== ESTILOS PARA INFORMACIÓN DE ENVÍO Y RECOGIDA ===== */

/* Información de envío en productos */
.product-shipping-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin: 0.75rem 0;
  padding: 0.75rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.shipping-option,
.pickup-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.shipping-option svg {
  color: #dc2626;
  flex-shrink: 0;
}

.pickup-option svg {
  color: #3b82f6;
  flex-shrink: 0;
}

.shipping-option span,
.pickup-option span {
  font-weight: 500;
}

/* Responsive para información de envío */
@media (max-width: 768px) {
  .product-shipping-info {
    padding: 0.5rem;
    gap: 0.375rem;
  }

  .shipping-option,
  .pickup-option {
    font-size: 0.8rem;
    gap: 0.375rem;
  }

  .shipping-option svg,
  .pickup-option svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .product-shipping-info {
    padding: 0.375rem;
    gap: 0.25rem;
  }

  .shipping-option,
  .pickup-option {
    font-size: 0.75rem;
    gap: 0.25rem;
  }

  .shipping-option svg,
  .pickup-option svg {
    width: 12px;
    height: 12px;
  }
}

/* Estilos para botones sin stock */
.primary-action-btn.no-stock,
.list-primary-btn.no-stock {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-color: #f59e0b;
  color: white;
}

.primary-action-btn.no-stock:hover,
.list-primary-btn.no-stock:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  border-color: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.primary-action-btn.no-stock svg,
.list-primary-btn.no-stock svg {
  color: white;
}

/* ===== ESTILOS MODERNOS PARA SOLICITUDES ===== */

/* Grid de solicitudes modernas */
.modern-solicitudes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

/* Card moderna de solicitud */
.modern-solicitud-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.modern-solicitud-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

/* Header de la card */
.solicitud-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.solicitud-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.solicitud-id {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  color: #1f2937;
  font-size: 1.1rem;
}

.solicitud-id svg {
  color: #3b82f6;
}

.grupo-id {
  font-size: 0.75rem;
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.solicitud-date {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Estados modernos */
.estado-badge.modern {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
  border: 2px solid transparent;
}

.estado-badge.modern.pendiente {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  border-color: #f59e0b;
}

.estado-badge.modern.procesando {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  border-color: #3b82f6;
}

.estado-badge.modern.cotizado {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
  border-color: #10b981;
}

.estado-badge.modern.rechazado {
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  color: #991b1b;
  border-color: #ef4444;
}

/* Tipo de solicitud */
.solicitud-type {
  padding: 0 1.5rem 1rem 1.5rem;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
}

.type-badge.carrito {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  color: #0c4a6e;
  border: 1px solid #0ea5e9;
}

.type-badge.individual {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  color: #6b21a8;
  border: 1px solid #a855f7;
}

/* Información del cliente */
.solicitud-client-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.client-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.client-header svg {
  color: #6b7280;
}

.client-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.client-email {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.client-phone {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #6b7280;
}

.client-phone svg {
  color: #10b981;
}

/* Información del producto */
.solicitud-product-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.carrito-header {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.carrito-products-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.product-name {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-qty {
  font-size: 0.8rem;
  color: #6b7280;
  font-weight: 600;
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.more-products {
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 0.5rem;
  background: #f3f4f6;
  border-radius: 6px;
}

.individual-product .product-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.individual-product .product-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.product-brand,
.product-alm,
.product-shelf {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.product-brand {
  background: #dbeafe;
  color: #1e40af;
}

.product-alm {
  background: #f3e8ff;
  color: #6b21a8;
}

.product-shelf {
  background: #ecfdf5;
  color: #166534;
}

.individual-product .product-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.stock-info.available {
  color: #166534;
  font-weight: 600;
}

.stock-info.unavailable {
  color: #dc2626;
  font-weight: 600;
}

/* Resumen financiero */
.solicitud-financial-summary {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid #f3f4f6;
}

.financial-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  color: #1f2937;
  font-size: 1rem;
}

.financial-header svg {
  color: #059669;
}

.financial-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.financial-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  padding: 0.25rem 0;
}

.financial-row.total {
  border-top: 2px solid #e5e7eb;
  padding-top: 0.75rem;
  margin-top: 0.5rem;
  font-weight: 700;
  font-size: 1rem;
}

.total-amount {
  color: #059669;
  font-weight: 800;
  font-size: 1.125rem;
}

.stock-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  font-size: 0.8rem;
  color: #92400e;
  font-weight: 500;
}

.stock-alert svg {
  color: #f59e0b;
  flex-shrink: 0;
}

/* Acciones de la card */
.solicitud-card-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #fafafa;
}

.modern-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.modern-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-action-btn.view {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  border-color: #3b82f6;
}

.modern-action-btn.view:hover {
  background: linear-gradient(135deg, #bfdbfe, #93c5fd);
  border-color: #2563eb;
}

.modern-action-btn.edit {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  border-color: #f59e0b;
}

.modern-action-btn.edit:hover {
  background: linear-gradient(135deg, #fde68a, #fcd34d);
  border-color: #d97706;
}

.modern-action-btn.delete {
  background: linear-gradient(135deg, #fecaca, #fca5a5);
  color: #991b1b;
  border-color: #ef4444;
}

.modern-action-btn.delete:hover {
  background: linear-gradient(135deg, #fca5a5, #f87171);
  border-color: #dc2626;
}

/* Responsive para solicitudes */
@media (max-width: 1024px) {
  .modern-solicitudes-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
  }

  .solicitud-card-header {
    padding: 1rem;
  }

  .solicitud-client-info,
  .solicitud-product-info,
  .solicitud-financial-summary {
    padding: 0.75rem 1rem;
  }

  .solicitud-card-actions {
    padding: 1rem;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .modern-solicitudes-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .solicitud-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .estado-badge.modern {
    align-self: flex-start;
  }

  .solicitud-card-actions {
    flex-direction: column;
  }

  .modern-action-btn {
    justify-content: center;
  }

  .individual-product .product-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .modern-solicitudes-grid {
    padding: 0.5rem 0;
  }

  .modern-solicitud-card {
    border-radius: 12px;
  }

  .solicitud-card-header,
  .solicitud-client-info,
  .solicitud-product-info,
  .solicitud-financial-summary,
  .solicitud-card-actions {
    padding: 0.75rem;
  }

  .financial-row {
    font-size: 0.8rem;
  }

  .financial-row.total {
    font-size: 0.9rem;
  }

  .total-amount {
    font-size: 1rem;
  }

  .modern-action-btn {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
  background: white;
  padding: 0.25rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.view-btn {
  padding: 0.75rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.view-btn.active {
  background: #0056a6;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 86, 166, 0.2);
}

/* ===== PRODUCTS GRID ===== */
.modern-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.modern-product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f1f5f9;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modern-product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: #e2e8f0;
}

.modern-product-image {
  position: relative;
  height: 240px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  overflow: hidden;
}

.product-image-element {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.4s ease;
}

.modern-product-card:hover .product-image-element {
  transform: scale(1.05);
}

.modern-stock-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.stock-status {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stock-status.available {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stock-status.unavailable {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.quick-actions {
  position: absolute;
  top: 1rem;
  left: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.modern-product-card:hover .quick-actions {
  opacity: 1;
  transform: translateY(0);
}

.quick-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.quick-action-btn:hover {
  background: white;
  transform: scale(1.1);
  color: #0056a6;
}

/* ===== PRODUCT INFO ===== */
.modern-product-info {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-category-badge {
  display: inline-block;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.modern-product-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-brand-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.brand-label {
  color: #6b7280;
  font-weight: 500;
}

.brand-name {
  color: #374151;
  font-weight: 600;
}

.modern-product-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.delivery-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #059669;
  background: #ecfdf5;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border-left: 3px solid #10b981;
}

.delivery-icon {
  color: #059669;
}

.product-price-section {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.modern-product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0056a6;
  display: block;
}

.modern-product-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.primary-action-btn {
  flex: 1;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  border: none;
  padding: 0.875rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.primary-action-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.primary-action-btn:hover:before {
  left: 100%;
}

.primary-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 86, 166, 0.3);
}

.primary-action-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-action-btn {
  background: white;
  color: #0056a6;
  border: 2px solid #0056a6;
  padding: 0.875rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 120px;
}

.secondary-action-btn:hover {
  background: #0056a6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.2);
}

/* ===== NO RESULTS ===== */
.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.no-results-icon {
  margin: 0 auto 1.5rem;
  color: #9ca3af;
}

.no-results-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 1rem;
}

.no-results-description {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto 2rem;
}

.reset-filters-btn {
  background: linear-gradient(135deg, #f7941d, #ff8c00);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(247, 148, 29, 0.3);
}

/* ===== CTA SECTION ===== */
.catalog-cta-section {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
  padding: 4rem 2rem;
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.cta-icon {
  margin: 0 auto 1.5rem;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.cta-description {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-primary-btn {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.cta-primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 86, 166, 0.4);
}

.cta-secondary-btn {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.cta-secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .modern-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .filter-pills-container {
    justify-content: flex-start;
    gap: 0.75rem;
  }

  .filter-pill {
    flex: 1;
    min-width: 200px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .modern-catalog-page {
    margin-top: 70px;
  }

  .catalog-hero {
    padding: 3rem 1rem;
  }

  .catalog-hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .advanced-filters-section {
    padding: 1.5rem 1rem;
  }

  .filter-header-modern {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    text-align: left;
  }

  .filter-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .filter-icon-badge {
    width: 40px;
    height: 40px;
  }

  .filter-title-content h3.filter-main-title {
    font-size: 1.5rem;
  }

  .filter-stats {
    align-self: stretch;
    justify-content: center;
  }

  .search-wrapper {
    max-width: 100%;
  }

  .filter-pills-container {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .filter-pill-wrapper {
    justify-content: space-between;
    padding: 0.75rem;
    min-width: auto;
  }

  .pill-select {
    min-width: auto;
    flex: 1;
    text-align: left;
  }

  .pill-arrow {
    margin-left: 0.5rem;
  }

  .clear-filters-btn {
    justify-content: center;
    width: 100%;
  }

  .catalog-results-section {
    padding: 2rem 1rem;
  }

  .modern-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .modern-product-actions {
    flex-direction: column;
  }

  .secondary-action-btn {
    min-width: auto;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary-btn,
  .cta-secondary-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .catalog-hero {
    padding: 2rem 1rem;
  }

  .catalog-hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .modern-products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-product-card {
    margin: 0 auto;
    max-width: 350px;
  }

  .view-toggle {
    display: none;
  }
}

/* ===== LIST VIEW STYLES ===== */
.modern-products-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.modern-product-list-item {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  display: grid;
  grid-template-columns: 140px 1fr auto;
  gap: 1.5rem;
  align-items: start;
}

.modern-product-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #e2e8f0;
}

/* List Product Image */
.list-product-image {
  position: relative;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  overflow: hidden;
}

.list-image-element {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.modern-product-list-item:hover .list-image-element {
  transform: scale(1.05);
}

.list-stock-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;
}

/* List Product Main Info */
.list-product-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.list-product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.list-product-number {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: monospace;
}

.list-product-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
  margin: 0;
}

.list-product-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
}

.list-product-brand svg {
  color: #f7941d;
}

.list-product-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.list-product-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.8rem;
  color: #6b7280;
  background: #f9fafb;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  border-left: 2px solid #e5e7eb;
}

.detail-item svg {
  color: #0056a6;
  flex-shrink: 0;
}

/* List Product Actions Section */
.list-product-actions-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  min-width: 200px;
}

.list-price-section {
  text-align: right;
}

.list-product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0056a6;
  display: block;
  line-height: 1.2;
}

.list-price-usd {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
  display: block;
  margin-top: 0.25rem;
}

.list-product-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.list-primary-btn {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  white-space: nowrap;
}

.list-primary-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.3);
}

.list-primary-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.list-secondary-btn {
  background: white;
  color: #0056a6;
  border: 2px solid #0056a6;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  white-space: nowrap;
}

.list-secondary-btn:hover {
  background: #0056a6;
  color: white;
  transform: translateY(-1px);
}

.list-details-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-details-btn:hover {
  background: #e5e7eb;
  color: #374151;
  transform: translateY(-1px);
}

/* List View Responsive */
@media (max-width: 1024px) {
  .modern-product-list-item {
    grid-template-columns: 100px 1fr;
    gap: 1rem;
  }

  .list-product-actions-section {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
    min-width: auto;
  }

  .list-price-section {
    text-align: left;
  }

  .list-product-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .modern-product-list-item {
    grid-template-columns: 80px 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }

  .list-product-image {
    width: 80px;
    height: 80px;
  }

  .list-product-title {
    font-size: 1.1rem;
  }

  .list-product-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .list-product-actions {
    flex-direction: column;
    width: 100%;
  }

  .list-primary-btn,
  .list-secondary-btn {
    width: 100%;
    justify-content: center;
  }

  .list-details-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modern-product-list-item {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .list-product-image {
    width: 100px;
    height: 100px;
    margin: 0 auto;
  }

  .list-product-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }

  .list-product-actions-section {
    align-items: center;
  }

  .list-price-section {
    text-align: center;
  }
}

.products-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #222222;
  font-family: var(--heading-font);
  letter-spacing: -0.5px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.products-description {
  font-size: 1.1rem;
  color: #555555;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.products-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 3rem;
  background-color: #ffffff;
  border-bottom: 1px solid #eeeeee;
  flex-wrap: wrap;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

.category-filter {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-filter label {
  color: #222222;
  font-weight: 600;
  font-size: 1rem;
}

.category-select {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  border: 1px solid #dddddd;
  background-color: #ffffff;
  color: #333333;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
}

.category-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 86, 166, 0.1);
}

.search-filter {
  flex: 1;
  max-width: 400px;
  position: relative;
  display: flex;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border-radius: 6px 0 0 6px;
  border: 1px solid #dddddd;
  border-right: none;
  background-color: #ffffff;
  color: #333333;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.search-button {
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 0 1.5rem;
  border-radius: 0 6px 6px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.search-button:hover {
  background-color: var(--secondary-color);
}

.products-results {
  padding: 1rem 3rem;
  background-color: #ffffff;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #eeeeee;
}

.results-count {
  color: #555555;
  font-size: 1rem;
  font-weight: 500;
}

.results-sort {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.results-sort label {
  color: #222222;
  font-size: 1rem;
  font-weight: 600;
}

.sort-select {
  padding: 0.5rem 1rem;
  border: 1px solid #dddddd;
  border-radius: 6px;
  font-size: 1rem;
  color: #333333;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 25px;
  padding: 2rem 3rem;
  background-color: #ffffff;
  max-height: calc(3 * (300px + 25px)); /* Altura para 3 filas máximo */
  overflow-y: auto;
}

.product-card {
  background-color: #ffffff;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  border: none;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.product-image {
  padding: 2rem;
  background-color: #f9f9f9;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 220px;
  border-bottom: 1px solid #eeeeee;
  overflow: hidden;
  position: relative;
}

.product-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.02), rgba(0,0,0,0));
  pointer-events: none;
}

/* Indicador de stock */
.stock-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.stock-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stock-badge.in-stock {
  background-color: #28a745;
}

.stock-badge.out-of-stock {
  background-color: #dc3545;
}

.product-info {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.product-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #222222;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-brand-container {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.product-brand {
  font-size: 1rem;
  color: #ffffff;
  font-weight: 700;
  background: linear-gradient(135deg, #0056A6, #00a0e3);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.3);
  border: 2px solid #F26522;
  position: relative;
  overflow: hidden;
}

.product-brand::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.product-brand:hover::before {
  left: 100%;
}

/* Marca simple para página de productos */
.product-brand-simple {
  font-size: 0.9rem;
  color: #F26522; /* Color naranja del logo de ECCSA */
  margin-bottom: 0.5rem;
  font-weight: 600;
}

/* Tiempo de entrega para productos */
.product-delivery-time {
  font-size: 0.85rem;
  color: #28a745;
  margin-bottom: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.delivery-icon {
  font-size: 0.9rem;
}

/* Tiempo de entrega para productos destacados */
.featured-delivery-time {
  font-size: 0.75rem;
  color: #28a745;
  margin-bottom: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  justify-content: center;
  min-height: 1.2rem; /* Altura fija para uniformidad */
}

.product-category {
  font-size: 0.9rem;
  color: #666666;
  margin-bottom: 0.5rem;
  background-color: #f5f5f5;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: inline-block;
}

.product-description {
  font-size: 1rem;
  color: #555555;
  margin: 1rem 0;
  flex: 1;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #C41C1C; /* Color rojo del logo de ECCSA */
  margin: 0.5rem 0 1.25rem;
}

.product-actions {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.product-button {
  background-color: #0056A6; /* Color azul del logo de ECCSA */
  color: #ffffff;
  border: none;
  padding: 0.75rem 0;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.product-button:hover {
  background-color: #003D75; /* Versión más oscura del azul */
  transform: translateY(-2px);
}

.product-details-button {
  background-color: transparent;
  color: #222222;
  border: 1px solid #dddddd;
  padding: 0.75rem 0;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1rem;
}

.product-details-button:hover {
  background-color: #f5f5f5;
  border-color: #bbbbbb;
}

.products-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0 3rem;
  gap: 0.5rem;
}

.pagination-button {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dddddd;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not([disabled]) {
  background-color: #f5f5f5;
  border-color: #bbbbbb;
}

.pagination-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dddddd;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-number:hover:not(.active) {
  background-color: #f5f5f5;
  border-color: #bbbbbb;
}

.pagination-number.active {
  background-color: var(--primary-color);
  color: #ffffff;
  border-color: var(--primary-color);
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background-color: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 10px;
  margin: 2rem 0;
}

.no-products p {
  color: #555555;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.products-contact {
  text-align: center;
  padding: 4rem 3rem;
  background-color: #000000;
  margin: 3rem 0 0;
  border-top: none;
}

.products-contact h2 {
  font-size: 2.2rem;
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.products-contact p {
  color: #cccccc;
  margin-bottom: 2.5rem;
  font-size: 1.2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.7;
}

.contact-button {
  background-color: #F26522; /* Color naranja del logo de ECCSA */
  color: #ffffff;
  border: none;
  padding: 1.2rem 2.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-button:hover {
  background-color: #E55511;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(242, 101, 34, 0.3);
}

/* Estilos para la página de administración */
.admin-page {
  margin-top: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* Nuevo diseño de login */
.login-container {
  display: flex;
  width: 900px;
  height: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.login-left-panel {
  width: 40%;
  background-color: #f5f7fa;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-illustration {
  width: 100%;
  height: 250px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}

.login-logo {
  width: 100px;
  height: 100px;
  position: relative;
  z-index: 2;
  animation: pulse 3s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.login-illustration-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.login-illustration-gear {
  position: absolute;
  border-radius: 50%;
  border: 3px dashed;
  animation: rotate 20s infinite linear;
}

.gear-blue {
  width: 180px;
  height: 180px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-color: var(--primary-color);
  opacity: 0.2;
}

.gear-orange {
  width: 120px;
  height: 120px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-color: var(--accent-color);
  opacity: 0.2;
  animation-direction: reverse;
}

@keyframes rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.login-illustration-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(#ddd 1px, transparent 1px);
  background-size: 15px 15px;
  opacity: 0.3;
}

.login-left-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.5rem;
  text-align: center;
}

.login-left-subtitle {
  font-size: 1rem;
  color: #666666;
  text-align: center;
}

.login-right-panel {
  width: 60%;
  background-color: #ffffff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 1rem;
}

.login-description {
  font-size: 1rem;
  color: #666666;
  margin-bottom: 1rem;
}

.login-credentials-info {
  background-color: #f0f7ff;
  border: 1px dashed var(--primary-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.login-credentials-info p {
  margin: 0.25rem 0;
  color: #333333;
}

.login-credentials-info p:first-child {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.login-error {
  background-color: #fff8f8;
  color: #e53935;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 500;
  border: 1px solid #ffcdd2;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.login-input-icon {
  position: absolute;
  left: 15px;
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-input {
  width: 100%;
  padding: 1rem 1rem 1rem 50px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f5f7fa;
  color: #333333;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.login-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(0, 86, 166, 0.1);
}

.login-remember {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.login-remember input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.login-remember label {
  color: #666666;
  font-size: 0.9rem;
}

.login-button {
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.login-button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 86, 166, 0.2);
}

.login-footer {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.login-forgot {
  color: var(--primary-color);
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.login-forgot:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Estilos para el sidebar de administración */
.admin-sidebar {
  width: 250px;
  height: 100vh;
  background-color: #ffffff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.admin-sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #e0e0e0;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar-logo span {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333333;
  white-space: nowrap;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: #666666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-item {
  margin-bottom: 5px;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #666666;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.sidebar-link:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.sidebar-item.active .sidebar-link {
  background-color: #f0f7ff;
  color: var(--primary-color);
  border-left: 3px solid var(--primary-color);
}

.sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.sidebar-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-arrow {
  margin-left: 10px;
}

.sidebar-submenu {
  list-style: none;
  padding: 0;
  margin: 5px 0 5px 35px;
}

.sidebar-subitem {
  margin-bottom: 2px;
}

.sidebar-sublink {
  display: block;
  padding: 8px 15px;
  color: #666666;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 4px;
  font-size: 0.9rem;
}

.sidebar-sublink:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
}

.sidebar-footer-link {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #666666;
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 10px;
  border-radius: 4px;
}

.sidebar-footer-link:hover {
  background-color: #f5f5f5;
  color: #e53935;
}

/* Estilos para el contenido principal con sidebar */
.admin-content {
  margin-left: 250px;
  padding: clamp(1rem, 3vw, 2rem);
  transition: all 0.3s ease;
  min-height: 100vh;
  background-color: #f5f7fa;
  container-type: inline-size;
}

.admin-content.expanded {
  margin-left: 70px;
}

/* Estilos responsivos para el login */
@media (max-width: 992px) {
  .login-container {
    width: 90%;
    max-width: 700px;
    height: auto;
    flex-direction: column;
  }

  .login-left-panel {
    width: 100%;
    padding: 2rem;
  }

  .login-right-panel {
    width: 100%;
    padding: 2rem;
  }

  .login-illustration {
    height: 180px;
    margin-bottom: 1rem;
  }

  .login-logo {
    width: 80px;
    height: 80px;
  }

  .gear-blue {
    width: 150px;
    height: 150px;
  }

  .gear-orange {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 576px) {
  .login-container {
    width: 95%;
    border-radius: 8px;
  }

  .login-left-panel {
    padding: 1.5rem 1rem;
  }

  .login-right-panel {
    padding: 1.5rem 1rem;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .login-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .login-input {
    padding: 0.8rem 0.8rem 0.8rem 45px;
    font-size: 0.9rem;
  }

  .login-button {
    padding: 0.8rem;
    font-size: 0.9rem;
  }
}

.admin-layout {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

.admin-dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.admin-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.5rem;
}

.admin-welcome {
  font-size: 1.1rem;
  color: #666666;
  margin-bottom: 1.5rem;
}

.admin-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.admin-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.admin-stat-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.admin-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.admin-stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  background-color: rgba(0, 86, 166, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.admin-stat-content {
  flex: 1;
}

.admin-stat-title {
  font-size: 1rem;
  color: #666666;
  margin-bottom: 0.5rem;
}

.admin-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333333;
}

.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.admin-action-button {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #333333;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-action-button:hover {
  background-color: #f5f7fa;
  border-color: #d0d0d0;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.admin-action-button svg {
  color: var(--primary-color);
}

.admin-activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.admin-activity-item:hover {
  background-color: #f9f9f9;
  border-color: #e0e0e0;
}

.admin-activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.admin-activity-content {
  flex: 1;
}

.admin-activity-text {
  font-size: 1rem;
  color: #333333;
  margin-bottom: 0.25rem;
}

.admin-activity-time {
  font-size: 0.85rem;
  color: #999999;
}

.admin-welcome {
  font-size: 1.1rem;
  color: #666666;
  text-align: center;
  margin-bottom: 1rem;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.admin-stat-card {
  background-color: var(--white);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.admin-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #d0d0d0;
}

.admin-stat-card h3 {
  font-size: 1rem;
  color: #666666;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.admin-actions {
  background-color: var(--white);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.admin-actions h2 {
  font-size: 1.3rem;
  color: #333333;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.admin-action-button {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.admin-action-button:hover {
  background-color: var(--secondary-color);
}

.admin-recent {
  background-color: var(--white);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.admin-recent h2 {
  font-size: 1.3rem;
  color: #333333;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.admin-activity-list {
  list-style-type: none;
  padding: 0;
}

.admin-activity-list li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
  color: #666666;
  font-size: 0.9rem;
}

.admin-activity-list li:last-child {
  border-bottom: none;
}

.admin-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.admin-logout {
  background-color: #f44336;
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.admin-logout:hover {
  background-color: #d32f2f;
}

.admin-back-link {
  display: inline-block;
  background-color: transparent;
  color: #666666;
  border: 1px solid #e0e0e0;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.admin-back-link:hover {
  background-color: #f5f5f5;
  color: #333333;
  border-color: #d0d0d0;
}

/* ===== NUEVO BRANDS SLIDER MODERNO ===== */
.brands-slider-container {
  padding: 80px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  margin: 0;
  overflow: hidden;
  position: relative;
  border-top: 1px solid rgba(0, 86, 166, 0.1);
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
}

.brands-slider-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 30% 70%, rgba(0, 86, 166, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(247, 148, 29, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

/* ===== NUEVA SECCIÓN PRODUCTOS DESTACADOS PREMIUM ===== */
.featured-products-modern-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.featured-products-modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 75%, rgba(0, 86, 166, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(247, 148, 29, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.featured-products-modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

/* Header de la sección */
.featured-products-modern-header {
  text-align: center;
  margin-bottom: 4rem;
}

.featured-products-modern-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.featured-products-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.featured-products-modern-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== PREMIUM PRODUCTS SHOWCASE ===== */
.featured-products-premium-showcase {
  margin-top: 5rem;
  position: relative;
  z-index: 1;
}

.featured-products-premium-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* ===== PREMIUM PRODUCT CARDS ===== */
.featured-product-premium-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 86, 166, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.06);
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 480px;
}

.featured-product-premium-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.12);
}

/* Card Background Effects */
.premium-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.premium-card-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(0, 86, 166, 0.02) 0%,
    rgba(247, 148, 29, 0.01) 50%,
    rgba(0, 86, 166, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.featured-product-premium-card:hover .premium-card-gradient {
  opacity: 1;
}

.premium-card-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 86, 166, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(247, 148, 29, 0.03) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.featured-product-premium-card:hover .premium-card-pattern {
  opacity: 1;
}

/* ===== PREMIUM PRODUCT IMAGE SECTION ===== */
.premium-product-image-container {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  overflow: hidden;
  z-index: 1;
}

.premium-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.4s ease;
}

.premium-product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4px 12px rgba(0, 86, 166, 0.1));
}

.featured-product-premium-card:hover .premium-product-image {
  transform: scale(1.08) rotate(2deg);
  filter: drop-shadow(0 8px 24px rgba(0, 86, 166, 0.2));
}

/* ===== PREMIUM FLOATING ELEMENTS ===== */
.premium-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.premium-rank-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 8px 12px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(0, 86, 166, 0.3);
  transform: translateY(0);
  transition: all 0.4s ease;
}

.featured-product-premium-card:hover .premium-rank-badge {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 86, 166, 0.4);
}

.rank-number {
  font-size: 1.2rem;
  line-height: 1;
  margin-bottom: 2px;
}

.rank-label {
  font-size: 0.6rem;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.premium-stock-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 86, 166, 0.1);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: all 0.4s ease;
}

.featured-product-premium-card:hover .premium-stock-indicator {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stock-pulse {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.stock-count {
  font-size: 0.75rem;
  font-weight: 600;
  color: #0056a6;
  white-space: nowrap;
}

/* ===== PREMIUM IMAGE OVERLAY ===== */
.premium-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 86, 166, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 4;
}

.featured-product-premium-card:hover .premium-image-overlay {
  opacity: 1;
  visibility: visible;
}

.premium-overlay-content {
  transform: translateY(20px);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.featured-product-premium-card:hover .premium-overlay-content {
  transform: translateY(0);
}

.premium-quick-actions {
  display: flex;
  gap: 16px;
}

.premium-quick-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  color: #0056a6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.premium-quick-btn:hover {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.premium-view-btn:hover {
  color: #10b981;
}

.premium-cart-btn:hover {
  color: #f7941d;
}

/* ===== PREMIUM PRODUCT CONTENT ===== */
.premium-product-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* Product Meta (Brand & Category) */
.premium-product-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 1rem;
}

.premium-brand-tag {
  background: linear-gradient(135deg, #0056a6, #004494);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.3);
}

.premium-category-tag {
  background: rgba(247, 148, 29, 0.1);
  color: #f7941d;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(247, 148, 29, 0.2);
}

/* Product Title */
.premium-product-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 3.1rem;
}

/* Product Description */
.premium-product-description {
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

/* Product Features */
.premium-product-features {
  margin-bottom: 1rem;
}

.premium-feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.85rem;
  color: #475569;
}

.feature-icon {
  color: #10b981;
  font-weight: 700;
  font-size: 0.9rem;
}

/* ===== PREMIUM PRODUCT FOOTER ===== */
.premium-product-footer {
  margin-top: auto;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 86, 166, 0.08);
}

/* Price Section */
.premium-price-section {
  margin-bottom: 1.5rem;
}

.premium-price-label {
  display: block;
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
}

.premium-price-value {
  font-size: 1.3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

/* Action Buttons */
.premium-action-buttons {
  display: flex;
  gap: 12px;
}

.premium-primary-action {
  flex: 1;
  background: linear-gradient(135deg, #0056a6, #004494);
  color: white;
  padding: 12px 20px;
  border-radius: 16px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 86, 166, 0.3);
  position: relative;
  overflow: hidden;
}

.premium-primary-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.premium-primary-action:hover::before {
  left: 100%;
}

.premium-primary-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 86, 166, 0.4);
}

.premium-secondary-action {
  background: rgba(247, 148, 29, 0.1);
  color: #f7941d;
  border: 1px solid rgba(247, 148, 29, 0.3);
  padding: 12px 20px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.premium-secondary-action:hover {
  background: rgba(247, 148, 29, 0.2);
  border-color: rgba(247, 148, 29, 0.5);
  transform: translateY(-2px);
}

/* ===== PREMIUM SKELETON LOADING ===== */
.featured-product-premium-card.loading {
  pointer-events: none;
  animation: none;
}

.premium-image-skeleton {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
}

.premium-floating-elements .skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
}

.premium-rank-badge.skeleton {
  width: 60px;
  height: 50px;
}

.premium-stock-indicator.skeleton {
  width: 100px;
  height: 36px;
}

/* Indicador de stock minimalista */
.featured-stock-modern-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.featured-stock-modern-badge {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 0.3rem 0.6rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stock-icon {
  font-size: 0.8rem;
}

.stock-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #0056a6;
}

/* Contenido del producto - Minimalista */
.featured-product-modern-content {
  padding: 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.featured-product-modern-brand {
  color: #0056a6;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.featured-product-modern-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.featured-product-modern-description {
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Acciones del producto - Minimalistas */
.featured-product-modern-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.featured-product-modern-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #0056a6;
}

.featured-product-modern-button {
  background: #0056a6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  display: inline-block;
  text-align: center;
}

.featured-product-modern-button:hover {
  background: #004494;
  transform: translateY(-1px);
}

/* CTA Section */
.featured-products-modern-cta {
  text-align: center;
  margin-top: 3rem;
}

.featured-cta-content {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.featured-cta-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
}

.featured-cta-content p {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.featured-cta-button {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 1rem 2rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.featured-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.cta-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.featured-cta-button:hover .cta-arrow {
  transform: translateX(3px);
}

/* Skeleton loading para productos destacados modernos */
.featured-product-modern-card.loading {
  pointer-events: none;
}

.skeleton-number {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 50%;
}

/* ===== RESPONSIVE DESIGN PARA PREMIUM PRODUCTS ===== */
@media (max-width: 1200px) {
  .featured-products-premium-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 0 1.5rem;
  }

  .featured-product-premium-card {
    min-height: 460px;
  }

  .premium-product-image-container {
    height: 180px;
    padding: 1.25rem;
  }

  .premium-product-content {
    padding: 1.25rem;
  }
}

@media (max-width: 1024px) {
  .featured-products-premium-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
  }

  .featured-products-modern-title {
    font-size: 2.25rem;
  }

  .featured-product-premium-card {
    min-height: 440px;
  }

  .premium-product-image-container {
    height: 160px;
    padding: 1rem;
  }

  .premium-rank-badge {
    top: 12px;
    left: 12px;
    padding: 5px 8px;
  }

  .premium-stock-indicator {
    top: 12px;
    right: 12px;
    padding: 5px 10px;
  }

  .rank-number {
    font-size: 1rem;
  }

  .premium-product-title {
    font-size: 1.1rem;
  }

  .premium-price-value {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .featured-products-modern-section {
    padding: 80px 0;
  }

  .featured-products-modern-container {
    padding: 0 20px;
  }

  .featured-products-modern-title {
    font-size: 2rem;
  }

  .featured-products-modern-subtitle {
    font-size: 1.1rem;
  }

  .featured-products-premium-showcase {
    margin-top: 3rem;
  }

  .featured-products-premium-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  .featured-product-premium-card {
    min-height: 520px;
    border-radius: 20px;
  }

  .premium-product-image-container {
    height: 220px;
    padding: 1rem;
  }

  .premium-product-content {
    padding: 1.5rem;
  }

  .premium-rank-badge {
    top: 12px;
    left: 12px;
    padding: 6px 8px;
  }

  .premium-stock-indicator {
    top: 12px;
    right: 12px;
    padding: 6px 10px;
  }

  .rank-number {
    font-size: 1rem;
  }

  .rank-label {
    font-size: 0.55rem;
  }

  .stock-count {
    font-size: 0.7rem;
  }

  .premium-product-title {
    font-size: 1.2rem;
    min-height: 3.2rem;
  }

  .premium-product-description {
    font-size: 0.9rem;
  }

  .premium-feature-item {
    font-size: 0.8rem;
  }

  .premium-price-value {
    font-size: 1.3rem;
  }

  .premium-action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .premium-primary-action,
  .premium-secondary-action {
    padding: 10px 16px;
    font-size: 0.85rem;
  }

  .featured-cta-content {
    padding: 2rem;
  }

  .featured-cta-content h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .featured-products-modern-section {
    padding: 60px 0;
  }

  .featured-products-modern-title {
    font-size: 1.75rem;
  }

  .featured-products-modern-header {
    margin-bottom: 2.5rem;
  }

  .featured-products-premium-showcase {
    margin-top: 2rem;
  }

  .featured-products-premium-grid {
    padding: 0 0.5rem;
    gap: 1.5rem;
  }

  .featured-product-premium-card {
    min-height: 480px;
    border-radius: 16px;
  }

  .premium-product-image-container {
    height: 180px;
    padding: 0.75rem;
  }

  .premium-product-content {
    padding: 1.25rem;
  }

  .premium-rank-badge {
    top: 8px;
    left: 8px;
    padding: 4px 6px;
  }

  .premium-stock-indicator {
    top: 8px;
    right: 8px;
    padding: 4px 8px;
  }

  .rank-number {
    font-size: 0.9rem;
  }

  .rank-label {
    font-size: 0.5rem;
  }

  .stock-count {
    font-size: 0.65rem;
  }

  .premium-product-meta {
    gap: 8px;
    margin-bottom: 0.75rem;
  }

  .premium-brand-tag,
  .premium-category-tag {
    padding: 4px 8px;
    font-size: 0.7rem;
  }

  .premium-product-title {
    font-size: 1.1rem;
    min-height: 2.8rem;
    margin-bottom: 0.75rem;
  }

  .premium-product-description {
    font-size: 0.85rem;
    margin-bottom: 1rem;
  }

  .premium-feature-item {
    font-size: 0.75rem;
    margin-bottom: 6px;
  }

  .premium-product-footer {
    padding-top: 1rem;
  }

  .premium-price-section {
    margin-bottom: 1rem;
  }

  .premium-price-value {
    font-size: 1.2rem;
  }

  .premium-action-buttons {
    gap: 8px;
  }

  .premium-primary-action,
  .premium-secondary-action {
    padding: 8px 12px;
    font-size: 0.8rem;
    border-radius: 12px;
  }

  .premium-quick-btn {
    width: 48px;
    height: 48px;
  }

  .featured-cta-content {
    padding: 1.5rem;
  }

  .featured-cta-content h3 {
    font-size: 1.3rem;
  }

  .featured-cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

.featured-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.featured-product-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 420px; /* Altura fija para uniformidad */
}

.featured-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.featured-product-image {
  padding: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  height: 150px;
  position: relative;
}

.featured-product-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  max-width: 120px;
  max-height: 120px;
}

.featured-product-card:hover .featured-product-img {
  transform: scale(1.05);
}

.featured-product-info {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Indicador de stock para productos destacados */
.featured-stock-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.featured-stock-badge {
  background-color: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Marca del producto destacado */
.featured-product-brand {
  font-size: 0.8rem;
  color: #ffffff;
  font-weight: 700;
  background: linear-gradient(135deg, #0056A6, #00a0e3);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-bottom: 0.8rem;
  border: 1px solid #F26522;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 86, 166, 0.3);
}

/* Precio del producto destacado */
.featured-product-price {
  font-size: 1rem;
  font-weight: 700;
  color: #C41C1C;
  margin: 0.8rem 0;
  text-align: center;
}

.featured-product-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--primary-color);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.6rem; /* Altura fija para 2 líneas */
}

.featured-product-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 1rem;
  flex-grow: 1;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 3.6rem; /* Altura fija para 3 líneas */
}

.featured-product-link {
  display: inline-block;
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  position: relative;
  padding-right: 20px;
  align-self: flex-start;
}

.featured-product-link::after {
  content: '→';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.2s ease;
}

.featured-product-link:hover::after {
  transform: translate(3px, -50%);
}

.featured-products-cta {
  text-align: center;
  margin-top: 2rem;
}

/* Skeleton loading para productos destacados */
.featured-product-card.loading {
  pointer-events: none;
}

.skeleton-line {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-line.short {
  width: 60%;
}

.skeleton-line.medium {
  width: 80%;
}

.skeleton-line.long {
  width: 100%;
}

.featured-delivery-time {
  font-size: 0.8rem;
  color: #666;
  margin: 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delivery-icon {
  font-size: 1rem;
}

@media (max-width: 768px) {
  .featured-products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

/* Companies Section - Estilo Rodisa */
.companies-section {
  padding: 20px 0;
  background-color: #e0e0e0; /* Fondo gris más oscuro */
  margin: 0;
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  text-align: center;
}

/* ===== NUEVO COMPANIES SECTION MODERNO ===== */
.companies-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  border-top: 1px solid rgba(0, 86, 166, 0.1);
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
}

.companies-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 75%, rgba(0, 86, 166, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(247, 148, 29, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.companies-title {
  text-align: center;
  color: #1a202c;
  font-size: 3rem;
  margin-bottom: 4rem;
  font-weight: 800;
  font-family: var(--heading-font);
  position: relative;
  display: inline-block;
  width: 100%;
}

.companies-title::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 5px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.3);
}

/* Companies Slider Container */
.companies-slider-container {
  padding: 0;
  overflow: hidden;
  background: transparent;
  border: none;
  position: relative;
  z-index: 1;
}

/* ===== NAVEGACIÓN MODERNA DE SLIDERS ===== */
.brands-slider-navigation,
.companies-slider-navigation {
  display: flex;
  align-items: center;
  position: relative;
  margin: 0 60px;
  gap: 20px;
}

.brands-slider-content,
.companies-slider-content {
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
  border-radius: 20px;
  padding: 10px;
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  cursor: grab;
}

.brands-slider-content:active,
.companies-slider-content:active {
  cursor: grabbing;
}

/* Ocultar scrollbar para Chrome, Safari y Opera */
.brands-slider-content::-webkit-scrollbar,
.companies-slider-content::-webkit-scrollbar {
  display: none;
}

.brands-slider-track,
.companies-slider-track {
  display: flex;
  padding: 20px 0;
  gap: 20px;
}

.slider-nav-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
  position: relative;
  overflow: hidden;
}

.slider-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f7941d, #0056a6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.slider-nav-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.slider-nav-button:hover::before {
  opacity: 1;
}

.slider-nav-button:active {
  transform: translateY(-1px) scale(0.98);
}

.slider-nav-button span {
  position: relative;
  z-index: 1;
}

/* ===== TÍTULOS MODERNOS DE SLIDERS ===== */
.slider-title-container {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.slider-title {
  font-size: 3rem;
  font-weight: 800;
  position: relative;
  display: inline-block;
  padding-bottom: 20px;
  margin-bottom: 0;
}

/* Título para Brands Slider */
.brands-slider-container .slider-title {
  color: #1a202c;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brands-slider-container .slider-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 5px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.3);
}

.slider-subtitle {
  font-size: 1.1rem;
  margin-top: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Subtítulo para Brands Slider */
.brands-slider-container .slider-subtitle {
  color: #64748b;
}

/* Subtítulo para Companies Slider */
.companies-slider-container .slider-subtitle {
  color: #64748b;
}

.slider-subtitle::before {
  content: "⚡";
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* ===== ANIMACIONES PARA SKELETON LOADING ===== */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.image-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* ===== ELEMENTOS DE MARCAS Y EMPRESAS MODERNOS ===== */
.brand-item,
.company-item {
  flex-shrink: 0;
  width: 200px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px;
  border-radius: 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* Estilos para Brand Items */
.brand-item {
  background: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 86, 166, 0.08);
}

.brand-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.03), rgba(247, 148, 29, 0.03));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.brand-item:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 50px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.2);
}

.brand-item:hover::before {
  opacity: 1;
}

/* Estilos para Company Items */
.company-item {
  background: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 86, 166, 0.08);
}

.company-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.03), rgba(247, 148, 29, 0.03));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.company-item:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 50px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.2);
}

.company-item:hover::before {
  opacity: 1;
}

/* Imágenes dentro de los items */
.brand-item img,
.company-item img {
  max-width: 160px;
  max-height: 80px;
  object-fit: contain;
  transition: all 0.4s ease;
  position: relative;
  z-index: 1;
  filter: grayscale(0.2) brightness(0.9);
  padding: 10px;
}

.brand-item:hover img {
  filter: grayscale(0) brightness(1);
  transform: scale(1.05);
}

.company-item img {
  filter: grayscale(0.2) brightness(0.9);
}

.company-item:hover img {
  filter: grayscale(0) brightness(1);
  transform: scale(1.05);
}

/* ===== MODERN BRANDS SLIDER STYLES ===== */
.modern-brands-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.modern-brands-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.modern-brands-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.modern-brands-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
}

.modern-brands-title-wrapper {
  margin-bottom: 2rem;
}

.modern-brands-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #1e293b 0%, #0056a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-brands-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.modern-brands-decorative {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.brands-decorative-line {
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0056a6, transparent);
}

.brands-decorative-dot {
  width: 8px;
  height: 8px;
  background: #0056a6;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.modern-brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.modern-brand-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.modern-brand-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.3);
}

.modern-brand-inner {
  position: relative;
  z-index: 2;
  text-align: center;
}

.modern-brand-logo-container {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.modern-brand-card:hover .modern-brand-logo-container {
  background: rgba(0, 86, 166, 0.05);
}

.modern-brand-logo {
  max-width: 100%;
  max-height: 80px;
  object-fit: contain;
  filter: grayscale(20%);
  transition: all 0.3s ease;
}

.modern-brand-card:hover .modern-brand-logo {
  filter: grayscale(0%);
  transform: scale(1.05);
}

.modern-brand-overlay {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.modern-brand-card:hover .modern-brand-overlay {
  opacity: 1;
  transform: translateY(0);
}

.modern-brand-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
}

.modern-brand-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 86, 166, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.modern-brand-card:hover .modern-brand-glow {
  opacity: 1;
}

.modern-brands-bottom {
  text-align: center;
}

.modern-brands-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.modern-brands-stat {
  text-align: center;
}

.modern-brands-stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.modern-brands-stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== MODERN CLIENTS SLIDER STYLES ===== */
.modern-clients-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  position: relative;
  overflow: hidden;
}

.modern-clients-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(0, 86, 166, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(0, 86, 166, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.modern-clients-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.modern-clients-header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
}

.modern-clients-title-wrapper {
  margin-bottom: 2rem;
}

.modern-clients-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #1e293b 0%, #0056a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-clients-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 650px;
  margin: 0 auto;
  line-height: 1.6;
}

.modern-clients-decorative {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.clients-decorative-pattern {
  display: flex;
  gap: 0.5rem;
}

.clients-pattern-dot {
  width: 6px;
  height: 6px;
  background: #0056a6;
  border-radius: 50%;
  animation: wave 2s ease-in-out infinite;
}

.clients-pattern-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.clients-pattern-dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes wave {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.5); opacity: 1; }
}

.modern-clients-showcase {
  margin-bottom: 4rem;
}

.modern-clients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.modern-client-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: 1px solid rgba(0, 86, 166, 0.08);
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-client-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 86, 166, 0.12);
  border-color: rgba(0, 86, 166, 0.2);
}

.modern-client-inner {
  position: relative;
  z-index: 2;
  text-align: center;
  width: 100%;
}

.modern-client-logo-wrapper {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.modern-client-card:hover .modern-client-logo-wrapper {
  background: rgba(0, 86, 166, 0.05);
}

.modern-client-logo {
  max-width: 100%;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(30%);
  transition: all 0.3s ease;
}

.modern-client-card:hover .modern-client-logo {
  filter: grayscale(0%);
  transform: scale(1.1);
}

.modern-client-overlay {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.modern-client-card:hover .modern-client-overlay {
  opacity: 1;
  transform: translateY(0);
}

.modern-client-name {
  font-size: 0.9rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.modern-client-badge {
  font-size: 0.75rem;
  color: #0056a6;
  font-weight: 600;
  background: rgba(0, 86, 166, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  display: inline-block;
}

.modern-client-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  transform: rotate(45deg);
}

.modern-client-card:hover .modern-client-shine {
  opacity: 1;
  animation: shine 0.6s ease-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.modern-clients-trust {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.modern-clients-trust-content {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.modern-clients-trust-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: left;
}

.trust-icon {
  font-size: 2rem;
  filter: grayscale(20%);
}

.trust-text {
  display: flex;
  flex-direction: column;
}

.trust-number {
  font-size: 1.8rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1;
}

.trust-label {
  font-size: 0.85rem;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== RESPONSIVE DESIGN PARA SLIDERS ===== */
@media (max-width: 1024px) {
  .brands-slider-navigation,
  .companies-slider-navigation {
    margin: 0 40px;
  }

  .slider-nav-button {
    width: 50px;
    height: 50px;
    font-size: 18px;
  }

  .brand-item,
  .company-item {
    width: 180px;
    height: 100px;
    margin: 0 15px;
  }

  .slider-title {
    font-size: 2.5rem;
  }

  .companies-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .brands-slider-container,
  .companies-section {
    padding: 50px 0;
  }

  .brands-slider-navigation,
  .companies-slider-navigation {
    margin: 0 20px;
    gap: 15px;
  }

  .slider-nav-button {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .brand-item,
  .company-item {
    width: 160px;
    height: 90px;
    margin: 0 12px;
  }

  .brand-item img,
  .company-item img {
    max-width: 140px;
    max-height: 70px;
  }

  .slider-title {
    font-size: 2.2rem;
  }

  .companies-title {
    font-size: 2.2rem;
    margin-bottom: 3rem;
  }

  .slider-subtitle {
    font-size: 1rem;
  }
}

/* Modern Sliders Responsive */
@media (max-width: 1024px) {
  .modern-brands-section,
  .modern-clients-section {
    padding: 4rem 0;
  }

  .modern-brands-container,
  .modern-clients-container {
    padding: 0 1.5rem;
  }

  .modern-brands-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .modern-clients-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.25rem;
  }

  .modern-brands-stats,
  .modern-clients-trust-content {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .modern-brands-section,
  .modern-clients-section {
    padding: 3rem 0;
  }

  .modern-brands-container,
  .modern-clients-container {
    padding: 0 1rem;
  }

  .modern-brands-header,
  .modern-clients-header {
    margin-bottom: 3rem;
  }

  .modern-brands-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
    margin-bottom: 3rem;
  }

  .modern-clients-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  .modern-brand-card {
    padding: 1.5rem;
  }

  .modern-client-card {
    padding: 1.25rem;
    height: 160px;
  }

  .modern-brands-stats,
  .modern-clients-trust-content {
    gap: 1.5rem;
  }

  .modern-brands-stat-number,
  .trust-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .modern-brands-section,
  .modern-clients-section {
    padding: 2rem 0;
  }

  .modern-brands-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .modern-clients-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }

  .modern-brand-card {
    padding: 1.25rem;
  }

  .modern-client-card {
    padding: 1rem;
    height: 140px;
  }

  .modern-brand-logo-container {
    height: 100px;
    margin-bottom: 1rem;
  }

  .modern-client-logo-wrapper {
    height: 60px;
    margin-bottom: 0.75rem;
  }

  .modern-brands-stats,
  .modern-clients-trust-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .modern-clients-trust-item {
    justify-content: center;
  }

  .modern-brands-stat-number,
  .trust-number {
    font-size: 1.75rem;
  }

  .modern-brands-stat-label,
  .trust-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .brands-slider-navigation,
  .companies-slider-navigation {
    margin: 0 5px;
    gap: 8px;
  }

  .slider-nav-button {
    width: 44px;
    height: 44px;
    font-size: 16px;
    min-width: 44px;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .brand-item,
  .company-item {
    width: 140px;
    height: 80px;
    margin: 0 8px;
    flex-shrink: 0;
  }

  .brand-item img,
  .company-item img {
    max-width: 120px;
    max-height: 60px;
  }

  .slider-title {
    font-size: 1.8rem;
  }

  .companies-title {
    font-size: 1.8rem;
  }

  .slider-subtitle {
    font-size: 0.9rem;
  }

  .brands-slider-content,
  .companies-slider-content {
    padding: 5px;
    margin: 0 5px;
  }

  .brands-slider-track,
  .companies-slider-track {
    gap: 15px;
    padding: 15px 0;
  }
}

/* Modal de Solicitud de Cotización */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.solicitud-modal {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: #f3f4f6;
}

.modal-body {
  padding: 24px;
}

.product-info-section {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.product-info-section h3 {
  margin: 0 0 12px 0;
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.product-details p {
  margin: 8px 0;
  color: #374151;
  font-size: 0.9rem;
}

.product-details strong {
  color: #1f2937;
}

.solicitud-form .form-group {
  margin-bottom: 20px;
}

.solicitud-form label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.required {
  color: #ef4444;
}

.solicitud-form input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.solicitud-form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.stock-alert {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  color: #92400e;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.success-message {
  background-color: #d1fae5;
  border: 1px solid #10b981;
  color: #065f46;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.btn-secondary {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  padding: 10px 20px;
  border: none;
  background-color: var(--primary-color);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Estilos para la página de Solicitudes */
.solicitudes-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.solicitudes-table th,
.solicitudes-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.solicitudes-table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.solicitudes-table tbody tr:hover {
  background-color: #f9fafb;
}

.product-cell {
  min-width: 200px;
}

.product-cell strong {
  color: #1f2937;
  font-size: 0.9rem;
}

.product-cell small {
  color: #6b7280;
  font-size: 0.8rem;
}

.client-cell {
  min-width: 180px;
}

.client-cell div {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
}

.client-cell small {
  color: #6b7280;
  font-size: 0.8rem;
}

.estado-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.stock-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.stock-badge.in-stock {
  background-color: #d1fae5;
  color: #065f46;
}

.stock-badge.out-stock {
  background-color: #fee2e2;
  color: #dc2626;
}

.stock-warning {
  color: #f59e0b;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view,
.btn-edit,
.btn-delete {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background-color: #dbeafe;
}

.btn-edit:hover {
  background-color: #fef3c7;
}

.btn-delete:hover {
  background-color: #fee2e2;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 180px), 1fr));
  gap: clamp(1rem, 2vw, 1.5rem);
  margin-bottom: clamp(1.5rem, 3vw, 2rem);
}

.stat-card {
  background: white;
  padding: clamp(1rem, 3vw, 1.5rem);
  border-radius: clamp(6px, 1vw, 12px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  margin: 0 0 clamp(0.5rem, 1vw, 0.75rem) 0;
  color: #6b7280;
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  font-weight: 500;
}

.stat-number {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  margin: 0;
  color: #1f2937;
}

.filters-section {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(0.75rem, 2vw, 1.5rem);
  align-items: center;
  margin-bottom: clamp(1rem, 2vw, 1.5rem);
  padding: clamp(1rem, 3vw, 1.5rem);
  background: white;
  border-radius: clamp(6px, 1vw, 12px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: clamp(0.5rem, 1vw, 0.75rem);
  flex: 1;
  min-width: fit-content;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

.refresh-button {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.refresh-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auto-refresh-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #374151;
  cursor: pointer;
  user-select: none;
}

.auto-refresh-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.retry-button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* Modales de Solicitudes */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.solicitud-detail-modal,
.solicitud-edit-modal {
  max-width: 900px;
  width: 95%;
}

.solicitud-delete-modal {
  max-width: 500px;
  width: 95%;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.detail-section {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #374151;
  min-width: 140px;
  margin-right: 16px;
}

.detail-item span {
  color: #1f2937;
  text-align: right;
  flex: 1;
}

.stock-available {
  color: #10b981;
  font-weight: 600;
}

.stock-unavailable {
  color: #ef4444;
  font-weight: 600;
}

.estado-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.estado-pendiente {
  background-color: #f59e0b;
}

.estado-procesando {
  background-color: #3b82f6;
}

.estado-cotizado {
  background-color: #10b981;
}

.estado-rechazado {
  background-color: #ef4444;
}

.stock-alert-text {
  color: #f59e0b;
  font-weight: 600;
}

.notes-content {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 12px;
  color: #374151;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Modal de Edición */
.product-info-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  text-align: center;
}

.product-info-summary h3 {
  margin: 0 0 8px 0;
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
}

.product-info-summary p {
  margin: 4px 0;
  color: #64748b;
  font-size: 0.9rem;
}

.product-info-summary strong {
  color: #374151;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.form-select,
.form-input,
.form-textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus,
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Modal de Eliminación */
.delete-confirmation {
  text-align: center;
  padding: 20px;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.delete-confirmation h3 {
  color: #dc2626;
  margin-bottom: 16px;
  font-size: 1.2rem;
}

.delete-confirmation p {
  color: #374151;
  margin-bottom: 12px;
  line-height: 1.5;
}

.warning-text {
  color: #dc2626;
  font-weight: 600;
  font-size: 0.9rem;
}

.btn-danger {
  padding: 10px 20px;
  border: none;
  background-color: #dc2626;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mensajes de éxito y error */
.success-message {
  background-color: #d1fae5;
  border: 1px solid #10b981;
  color: #065f46;
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 16px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #991b1b;
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 16px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Estilos adicionales para modales */
.modal-header {
  padding: 24px 24px 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 20px 24px 24px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
}

/* Contenedor adaptativo inteligente para solicitudes */
.solicitudes-adaptive-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 400px), 1fr));
  gap: clamp(1rem, 2vw, 2rem);
  padding: clamp(0.5rem, 2vw, 1.5rem);
  container-type: inline-size;
}

/* Card adaptativa que se reorganiza según el espacio disponible */
.solicitud-adaptive-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: clamp(8px, 1vw, 16px);
  padding: clamp(1rem, 3vw, 1.5rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: clamp(0.75rem, 2vw, 1.25rem);
  min-width: 0; /* Permite que el contenido se contraiga */
}

.solicitud-adaptive-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Header adaptativo */
.solicitud-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: clamp(0.5rem, 2vw, 1rem);
  padding-bottom: clamp(0.5rem, 1vw, 1rem);
  border-bottom: 1px solid #f3f4f6;
}

.solicitud-id {
  font-weight: 600;
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  color: #1f2937;
  flex-shrink: 0;
}

.solicitud-date {
  font-size: clamp(0.8rem, 2vw, 0.9rem);
  color: #6b7280;
  margin-left: auto;
}

/* Contenido que se adapta al espacio */
.solicitud-content {
  display: grid;
  gap: clamp(0.75rem, 2vw, 1rem);
  grid-template-areas:
    "product"
    "client"
    "metrics";
}

/* En contenedores más anchos, reorganizar horizontalmente */
@container (min-width: 500px) {
  .solicitud-content {
    grid-template-areas:
      "product client"
      "metrics metrics";
    grid-template-columns: 1fr 1fr;
  }
}

/* En contenedores muy anchos, todo en una fila */
@container (min-width: 700px) {
  .solicitud-content {
    grid-template-areas: "product client metrics";
    grid-template-columns: 2fr 2fr 1fr;
  }
}

/* Secciones del contenido */
.solicitud-product {
  grid-area: product;
  min-width: 0;
}

.solicitud-client {
  grid-area: client;
  min-width: 0;
}

.solicitud-metrics {
  grid-area: metrics;
  display: flex;
  flex-wrap: wrap;
  gap: clamp(0.5rem, 1vw, 1rem);
  align-items: flex-start;
}

/* Información del producto */
.product-info {
  display: flex;
  flex-direction: column;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
}

.product-model {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  color: #1f2937;
  line-height: 1.3;
  word-break: break-word;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: clamp(0.125rem, 0.25vw, 0.25rem);
  font-size: clamp(0.75rem, 1.5vw, 0.85rem);
  color: #6b7280;
}

.product-brand,
.product-alm,
.product-shelf {
  line-height: 1.2;
}

/* Información del cliente */
.client-email {
  font-size: clamp(0.8rem, 1.8vw, 0.95rem);
  color: #1f2937;
  font-weight: 500;
  word-break: break-word;
  line-height: 1.3;
}

.client-phone {
  font-size: clamp(0.75rem, 1.5vw, 0.85rem);
  color: #6b7280;
  margin-top: clamp(0.25rem, 0.5vw, 0.5rem);
}

/* Métricas adaptativas */
.metric-item {
  display: flex;
  flex-direction: column;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
  min-width: fit-content;
  flex: 1;
  min-width: 0;
}

.metric-item label {
  font-size: clamp(0.7rem, 1.4vw, 0.8rem);
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: clamp(0.85rem, 1.8vw, 1rem);
  color: #1f2937;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
  flex-wrap: wrap;
}

.metric-value.price {
  color: #059669;
  font-weight: 700;
}

/* Botones adaptativos */
.solicitud-actions {
  display: flex;
  gap: clamp(0.5rem, 1vw, 1rem);
  padding-top: clamp(0.75rem, 1.5vw, 1rem);
  border-top: 1px solid #f3f4f6;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
  padding: clamp(0.5rem, 1vw, 0.75rem) clamp(0.75rem, 1.5vw, 1rem);
  border: none;
  border-radius: clamp(4px, 0.5vw, 8px);
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: fit-content;
  justify-content: center;
  text-align: center;
}

.btn-icon {
  font-size: clamp(0.9rem, 1.8vw, 1.1rem);
  flex-shrink: 0;
}

.btn-text {
  white-space: nowrap;
}

/* Colores de botones */
.view-btn {
  background: #3b82f6;
  color: white;
}

.view-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.edit-btn {
  background: #f59e0b;
  color: white;
}

.edit-btn:hover {
  background: #d97706;
  transform: translateY(-1px);
}

.delete-btn {
  background: #ef4444;
  color: white;
}

.delete-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Adaptación de botones en contenedores pequeños */
@container (max-width: 400px) {
  .solicitud-actions {
    flex-direction: column;
  }

  .action-btn {
    flex: none;
    width: 100%;
  }
}

/* Adaptación de métricas en contenedores pequeños */
@container (max-width: 350px) {
  .solicitud-metrics {
    flex-direction: column;
  }

  .metric-item {
    flex: none;
    width: 100%;
  }
}

/* Estilos adaptativos para admin-header */
.admin-header {
  margin-bottom: clamp(1rem, 2vw, 2rem);
}

.admin-header h1 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  color: #1f2937;
  margin-bottom: clamp(0.25rem, 0.5vw, 0.5rem);
}

.admin-header p {
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  color: #6b7280;
}

/* Estados y badges adaptativos */
.estado-badge {
  display: inline-block;
  padding: clamp(0.25rem, 0.5vw, 0.5rem) clamp(0.5rem, 1vw, 0.75rem);
  border-radius: clamp(6px, 1vw, 12px);
  color: white;
  font-size: clamp(0.75rem, 1.5vw, 0.85rem);
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.stock-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: clamp(0.25rem, 0.5vw, 0.5rem) clamp(0.5rem, 1vw, 0.75rem);
  border-radius: clamp(4px, 0.5vw, 8px);
  font-size: clamp(0.75rem, 1.5vw, 0.85rem);
  font-weight: 600;
  min-width: clamp(2rem, 4vw, 3rem);
}

.stock-badge.in-stock {
  background-color: #d1fae5;
  color: #065f46;
}

.stock-badge.out-stock {
  background-color: #fee2e2;
  color: #991b1b;
}

.stock-warning {
  color: #f59e0b;
  font-size: clamp(0.7rem, 1.4vw, 0.8rem);
  font-weight: 600;
  margin-top: clamp(0.125rem, 0.25vw, 0.25rem);
}

/* Estados de carga adaptativos */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: clamp(2rem, 5vw, 4rem);
  text-align: center;
  background: white;
  border-radius: clamp(8px, 1vw, 16px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: clamp(1rem, 2vw, 2rem) 0;
}

.loading-state p,
.error-state p,
.empty-state p {
  font-size: clamp(1rem, 2vw, 1.2rem);
  color: #6b7280;
  margin-bottom: clamp(0.5rem, 1vw, 1rem);
}

.retry-button {
  padding: clamp(0.5rem, 1vw, 0.75rem) clamp(1rem, 2vw, 1.5rem);
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: clamp(4px, 0.5vw, 8px);
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Estilos para botones de filtros adaptativos */
.refresh-button {
  display: flex;
  align-items: center;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
  padding: clamp(0.5rem, 1vw, 0.75rem) clamp(0.75rem, 1.5vw, 1rem);
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: clamp(4px, 0.5vw, 8px);
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.refresh-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auto-refresh-label {
  display: flex;
  align-items: center;
  gap: clamp(0.25rem, 0.5vw, 0.5rem);
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  color: #374151;
  cursor: pointer;
  user-select: none;
}

.auto-refresh-checkbox {
  width: clamp(1rem, 2vw, 1.2rem);
  height: clamp(1rem, 2vw, 1.2rem);
  cursor: pointer;
}

/* Estilos para select adaptativos */
.filter-select {
  padding: clamp(0.5rem, 1vw, 0.75rem) clamp(0.75rem, 1.5vw, 1rem);
  border: 1px solid #d1d5db;
  border-radius: clamp(4px, 0.5vw, 8px);
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  color: #374151;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: clamp(120px, 20vw, 200px);
  flex: 1;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  white-space: nowrap;
}

/* Fin de estilos adaptativos para solicitudes */

/* ===== NUEVA SECCIÓN EMPRESAS QUE CONFÍAN EN NOSOTROS ===== */
.trust-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.trust-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 86, 166, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(247, 148, 29, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.trust-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

/* Header de la sección */
.trust-header {
  text-align: center;
  margin-bottom: 4rem;
}

.trust-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 86, 166, 0.2);
  animation: float 3s ease-in-out infinite;
}

.trust-badge-text {
  font-size: 1rem;
}

.trust-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.trust-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.trust-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de estadísticas */
.trust-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.trust-stat-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.trust-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.03), rgba(247, 148, 29, 0.03));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.trust-stat-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.3);
}

.trust-stat-card:hover::before {
  opacity: 1;
}

.trust-stat-icon {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem auto;
  position: relative;
  z-index: 1;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.trust-stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.trust-stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* Sección de industrias */
.trust-industries {
  margin-bottom: 4rem;
}

.trust-industries-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  text-align: center;
  margin-bottom: 3rem;
}

.trust-industries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.trust-industry-card {
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.trust-industry-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.15);
}

.trust-industry-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.trust-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.trust-industry-card:hover .trust-card-image {
  transform: scale(1.05);
}

.trust-industry-content {
  padding: 2rem;
  position: relative;
}

.trust-industry-icon {
  position: absolute;
  top: -35px;
  left: 2rem;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.trust-industry-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 1rem 0 1rem 0;
}

.trust-industry-content p {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.trust-industry-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trust-industry-features span {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

/* Sección de testimonio */
.trust-testimonial {
  margin-bottom: 2rem;
}

.trust-testimonial-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.trust-testimonial-quote {
  padding: 3rem;
  position: relative;
}

.trust-quote-icon {
  font-size: 3rem;
  color: rgba(0, 86, 166, 0.2);
  margin-bottom: 1.5rem;
}

.trust-testimonial-quote p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: #374151;
  font-style: italic;
  margin-bottom: 2rem;
  position: relative;
}

.trust-testimonial-author {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.trust-testimonial-author strong {
  font-size: 1.1rem;
  color: #1a202c;
  font-weight: 700;
}

.trust-testimonial-author span {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

.trust-testimonial-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.trust-testimonial-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive Design para Trust Section */
@media (max-width: 1024px) {
  .trust-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .trust-industries-grid {
    grid-template-columns: 1fr;
  }

  .trust-testimonial-content {
    grid-template-columns: 1fr;
  }

  .trust-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .trust-section {
    padding: 80px 0;
  }

  .trust-container {
    padding: 0 20px;
  }

  .trust-title {
    font-size: 2.5rem;
  }

  .trust-subtitle {
    font-size: 1.1rem;
  }

  .trust-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }

  .trust-stat-card {
    padding: 1.5rem;
  }

  .trust-stat-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .trust-stat-number {
    font-size: 2.5rem;
  }

  .trust-industries-title {
    font-size: 2rem;
  }

  .trust-industry-card {
    margin-bottom: 1rem;
  }

  .trust-testimonial-quote {
    padding: 2rem;
  }

  .trust-testimonial-quote p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .trust-title {
    font-size: 2rem;
  }

  .trust-badge {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .trust-stats-grid {
    grid-template-columns: 1fr;
  }

  .trust-stat-card {
    padding: 1.25rem;
  }

  .trust-industries-title {
    font-size: 1.8rem;
  }

  .trust-industry-content {
    padding: 1.5rem;
  }

  .trust-industry-content h4 {
    font-size: 1.3rem;
  }

  .trust-testimonial-quote {
    padding: 1.5rem;
  }

  .trust-testimonial-quote p {
    font-size: 1rem;
  }
}

/* ===== NUEVA SECCIÓN ¿POR QUÉ ECCSA? MODERNA ===== */
.why-eccsa-modern-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
}

.why-eccsa-modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 30% 70%, rgba(0, 86, 166, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(247, 148, 29, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.why-eccsa-modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

/* Header de la sección */
.why-eccsa-modern-header {
  text-align: center;
  margin-bottom: 4rem;
}

.why-eccsa-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 86, 166, 0.2);
  animation: float 3s ease-in-out infinite;
}

.why-eccsa-badge-text {
  font-size: 1rem;
}

.why-eccsa-modern-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.why-eccsa-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.why-eccsa-modern-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de tarjetas */
.why-eccsa-modern-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.why-eccsa-modern-card {
  background: white;
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.why-eccsa-modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.02), rgba(247, 148, 29, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.why-eccsa-modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.3);
}

.why-eccsa-modern-card:hover::before {
  opacity: 1;
}

/* Header de la tarjeta */
.why-eccsa-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.why-eccsa-card-icon {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
  flex-shrink: 0;
}

.why-eccsa-card-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

/* Contenido de la tarjeta */
.why-eccsa-card-content {
  position: relative;
  z-index: 1;
}

.why-eccsa-card-content p {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.why-eccsa-features-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.why-eccsa-feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #374151;
}

.why-eccsa-check {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 700;
  flex-shrink: 0;
}

/* Sección inferior */
.why-eccsa-bottom-section {
  margin-top: 3rem;
}

.why-eccsa-experience-card {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.why-eccsa-experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.02), rgba(247, 148, 29, 0.02));
}

.why-eccsa-experience-content {
  display: flex;
  align-items: center;
  gap: 2rem;
  position: relative;
  z-index: 1;
}

.why-eccsa-experience-number {
  font-size: 4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.why-eccsa-experience-text h4 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.why-eccsa-experience-text p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.why-eccsa-experience-image {
  position: relative;
  z-index: 1;
  border-radius: 16px;
  overflow: hidden;
  width: 300px;
  height: 200px;
}

.why-eccsa-exp-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive Design para Why ECCSA Section */
@media (max-width: 1024px) {
  .why-eccsa-modern-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .why-eccsa-experience-card {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .why-eccsa-modern-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .why-eccsa-modern-section {
    padding: 80px 0;
  }

  .why-eccsa-modern-container {
    padding: 0 20px;
  }

  .why-eccsa-modern-title {
    font-size: 2.5rem;
  }

  .why-eccsa-modern-subtitle {
    font-size: 1.1rem;
  }

  .why-eccsa-modern-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .why-eccsa-modern-card {
    padding: 2rem;
  }

  .why-eccsa-card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .why-eccsa-card-header h3 {
    font-size: 1.3rem;
  }

  .why-eccsa-experience-card {
    padding: 2rem;
  }

  .why-eccsa-experience-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .why-eccsa-experience-number {
    font-size: 3rem;
  }

  .why-eccsa-experience-text h4 {
    font-size: 1.5rem;
  }

  .why-eccsa-experience-image {
    width: 250px;
    height: 150px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .why-eccsa-modern-title {
    font-size: 2rem;
  }

  .why-eccsa-badge {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .why-eccsa-modern-card {
    padding: 1.5rem;
  }

  .why-eccsa-card-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .why-eccsa-card-header h3 {
    font-size: 1.2rem;
  }

  .why-eccsa-experience-card {
    padding: 1.5rem;
  }

  .why-eccsa-experience-number {
    font-size: 2.5rem;
  }

  .why-eccsa-experience-text h4 {
    font-size: 1.3rem;
  }

  .why-eccsa-experience-text p {
    font-size: 1rem;
  }

  .why-eccsa-experience-image {
    width: 200px;
    height: 120px;
  }
}

/* Estilos del Carrito de Compras */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  backdrop-filter: blur(2px);
}

.cart-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: clamp(320px, 40vw, 480px);
  height: 100vh;
  background: white;
  z-index: 9999;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: clamp(1rem, 3vw, 1.5rem);
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cart-header h2 {
  margin: 0;
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  font-weight: 600;
  color: #1f2937;
}

.cart-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.cart-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: clamp(2rem, 5vw, 4rem);
  text-align: center;
  flex: 1;
}

.cart-empty-icon {
  color: #d1d5db;
  margin-bottom: 1rem;
}

.cart-empty h3 {
  margin: 0 0 0.5rem 0;
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  color: #374151;
}

.cart-empty p {
  margin: 0;
  color: #6b7280;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: clamp(0.5rem, 1vw, 1rem);
}

.cart-item {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: clamp(0.5rem, 1vw, 1rem);
  align-items: center;
  padding: clamp(0.75rem, 1.5vw, 1rem);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: clamp(0.5rem, 1vw, 1rem);
  background: white;
  transition: all 0.2s ease;
}

.cart-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cart-item-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-item-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.cart-item-details {
  min-width: 0;
}

.cart-item-name {
  margin: 0 0 0.25rem 0;
  font-size: clamp(0.85rem, 1.7vw, 0.95rem);
  font-weight: 600;
  color: #1f2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cart-item-brand,
.cart-item-code {
  margin: 0;
  font-size: clamp(0.75rem, 1.5vw, 0.8rem);
  color: #6b7280;
  line-height: 1.2;
}

.cart-item-price {
  margin: 0.25rem 0 0 0;
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  font-weight: 600;
  color: #059669;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
}

.quantity-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  min-width: 32px;
  text-align: center;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
}

.remove-item-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #ef4444;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.remove-item-btn:hover {
  background: #fee2e2;
}

.cart-item-total {
  font-size: clamp(0.85rem, 1.7vw, 0.95rem);
  font-weight: 700;
  color: #059669;
  text-align: right;
}

.cart-summary {
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  padding: clamp(1rem, 2vw, 1.5rem);
}

.cart-totals {
  margin-bottom: clamp(1rem, 2vw, 1.5rem);
}

.cart-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: clamp(0.85rem, 1.7vw, 0.95rem);
}

.cart-total-row:last-child {
  margin-bottom: 0;
}

.cart-subtotal {
  font-weight: 600;
  color: #374151;
}

.cart-tax {
  font-weight: 500;
  color: #6b7280;
}

.cart-final-total {
  padding-top: 0.5rem;
  border-top: 1px solid #d1d5db;
  font-size: clamp(1rem, 2vw, 1.1rem);
  font-weight: 700;
}

.cart-total-price {
  color: #059669;
  font-weight: 700;
}

.cart-actions {
  display: flex;
  gap: clamp(0.5rem, 1vw, 1rem);
  flex-direction: column;
}

.cart-clear-btn,
.cart-checkout-btn {
  padding: clamp(0.75rem, 1.5vw, 1rem);
  border: none;
  border-radius: 8px;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.cart-clear-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.cart-clear-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.cart-checkout-btn {
  background: #3b82f6;
  color: white;
}

.cart-checkout-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Contador del carrito en navbar */
.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
}

/* Estilos para botones de productos */
.add-to-cart-btn {
  background: #059669 !important;
  color: white !important;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: #047857 !important;
  transform: translateY(-2px);
}

.add-to-cart-btn:disabled {
  background: #d1d5db !important;
  color: #6b7280 !important;
  cursor: not-allowed;
  transform: none;
}

.product-button-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-size: 0.9rem;
}

.product-button-secondary:hover {
  background: #e5e7eb;
  color: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive para carrito en móviles */
@media (max-width: 768px) {
  .cart-panel {
    width: 100vw;
    right: 0;
  }

  .cart-item {
    grid-template-columns: auto 1fr;
    grid-template-rows: auto auto;
    gap: 0.75rem;
  }

  .cart-item-controls {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .cart-item-total {
    grid-column: 1 / -1;
    text-align: center;
    padding-top: 0.5rem;
    border-top: 1px solid #f3f4f6;
  }

  .cart-actions {
    flex-direction: column;
  }
}

/* ===== MODERN ADMIN SIDEBAR ===== */
.modern-admin-sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1a1d29 0%, #232940 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-admin-sidebar.collapsed {
  width: 80px;
}

/* Header Section */
.modern-sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-logo-container {
  position: relative;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.brand-logo {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.brand-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0056a6, #00a0e3);
  border-radius: 8px;
  opacity: 0.2;
  filter: blur(6px);
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.4; transform: scale(1.05); }
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.brand-title {
  font-size: 1rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(135deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-sidebar-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.modern-sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.toggle-icon {
  color: #ffffff;
  transition: all 0.3s ease;
}

.modern-sidebar-toggle:hover .toggle-icon {
  transform: scale(1.1);
}

/* User Profile Section */
.modern-user-profile-link {
  text-decoration: none;
  display: block;
  margin: 1rem 1.5rem;
}

.modern-user-profile {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.modern-user-profile:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.user-avatar-container {
  position: relative;
  width: 44px;
  height: 44px;
  flex-shrink: 0;
}

.user-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0056a6, #00a0e3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-initial {
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  z-index: 2;
}

.avatar-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: 14px;
  background: linear-gradient(135deg, #0056a6, #00a0e3);
  background-clip: padding-box;
  animation: rotate-ring 3s linear infinite;
}

@keyframes rotate-ring {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.user-status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid #1a1d29;
  border-radius: 50%;
  animation: pulse-status 2s ease-in-out infinite;
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  display: flex;
  align-items: center;
}

.role-badge {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-arrow {
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.modern-user-profile:hover .profile-arrow {
  color: #ffffff;
  transform: translateX(2px);
}

/* Navigation Section */
.modern-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.modern-sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.modern-sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.modern-sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.modern-sidebar-nav {
  padding: 0 1rem;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(4px);
}

.nav-link-content {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
}

.nav-icon-container {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.nav-icon {
  color: inherit;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.nav-link:hover .icon-bg {
  opacity: 1;
  transform: scale(1);
}

.nav-text {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-arrow {
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.nav-arrow svg {
  transition: transform 0.3s ease;
}

.nav-arrow svg.rotated {
  transform: rotate(180deg);
}

.nav-link:hover .nav-arrow {
  color: rgba(255, 255, 255, 0.8);
}

/* Active State */
.nav-item.active .nav-link {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.2), rgba(0, 160, 227, 0.2));
  color: #ffffff;
  border: 1px solid rgba(0, 86, 166, 0.3);
}

.nav-item.active .nav-link .icon-bg {
  background: linear-gradient(135deg, #0056a6, #00a0e3);
  opacity: 1;
  transform: scale(1);
}

.active-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(180deg, #0056a6, #00a0e3);
  border-radius: 0 4px 4px 0;
  animation: slide-in 0.3s ease;
}

@keyframes slide-in {
  from { width: 0; opacity: 0; }
  to { width: 4px; opacity: 1; }
}

/* Submenu Styles */
.nav-submenu {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  animation: submenu-expand 0.3s ease;
}

@keyframes submenu-expand {
  from { opacity: 0; max-height: 0; }
  to { opacity: 1; max-height: 300px; }
}

.nav-subitem {
  position: relative;
}

.nav-sublink {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem 0.75rem 3rem;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  gap: 0.75rem;
}

.nav-sublink:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  padding-left: 3.25rem;
}

.sublink-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-sublink:hover .sublink-indicator {
  background: #00a0e3;
  transform: scale(1.2);
}

.sublink-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-subitem.active .nav-sublink {
  color: #ffffff;
  background: rgba(0, 86, 166, 0.2);
}

.nav-subitem.active .sublink-indicator {
  background: #0056a6;
  box-shadow: 0 0 8px rgba(0, 86, 166, 0.5);
}

.sublink-active {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 70%;
  background: linear-gradient(180deg, #0056a6, #00a0e3);
  border-radius: 0 2px 2px 0;
}

/* Footer Section */
.modern-sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.modern-logout-button:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
}

.logout-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.modern-logout-button:hover .logout-icon-container {
  background: rgba(239, 68, 68, 0.3);
  transform: scale(1.1);
}

.logout-text {
  flex: 1;
  text-align: left;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .modern-admin-sidebar {
    width: 100%;
    max-width: 320px;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-admin-sidebar.mobile-open {
    transform: translateX(0);
  }

  .modern-admin-sidebar.collapsed {
    transform: translateX(-100%);
    width: 100%;
    max-width: 320px;
  }

  .modern-sidebar-header {
    padding: 1rem 1.5rem;
    min-height: 70px;
  }

  .brand-logo-container {
    width: 44px;
    height: 44px;
  }

  .brand-logo {
    width: 36px;
    height: 36px;
  }

  .brand-title {
    font-size: 1.125rem;
  }

  .modern-sidebar-toggle {
    display: none;
  }

  .modern-user-profile-link {
    margin: 1rem 1.5rem;
  }

  .modern-user-profile {
    padding: 0.875rem;
  }

  .user-avatar-container {
    width: 40px;
    height: 40px;
  }

  .user-name {
    font-size: 0.8rem;
  }

  .role-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .modern-sidebar-content {
    padding: 0.75rem 0;
  }

  .modern-sidebar-nav {
    padding: 0 1rem;
  }

  .nav-link-content {
    padding: 0.75rem 0.875rem;
  }

  .nav-icon-container {
    width: 36px;
    height: 36px;
  }

  .nav-text {
    font-size: 0.8rem;
  }

  .nav-sublink {
    padding: 0.625rem 0.875rem 0.625rem 2.75rem;
    font-size: 0.75rem;
  }

  .nav-sublink:hover {
    padding-left: 3rem;
  }

  .modern-sidebar-footer {
    padding: 1rem 1.5rem;
  }

  .modern-logout-button {
    padding: 0.75rem 0.875rem;
    font-size: 0.8rem;
  }

  .logout-icon-container {
    width: 28px;
    height: 28px;
  }
}

/* Collapsed State Styles */
.modern-admin-sidebar.collapsed .brand-text,
.modern-admin-sidebar.collapsed .nav-text,
.modern-admin-sidebar.collapsed .nav-arrow,
.modern-admin-sidebar.collapsed .logout-text,
.modern-admin-sidebar.collapsed .modern-user-profile-link {
  opacity: 0;
  visibility: hidden;
}

/* Asegurar que el toggle button siempre sea visible */
.modern-admin-sidebar.collapsed .modern-sidebar-toggle {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
}

/* Ajustar el header cuando está colapsado */
.modern-admin-sidebar.collapsed .modern-sidebar-header {
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 1rem;
}

.modern-admin-sidebar.collapsed .sidebar-brand {
  justify-content: center;
}

.modern-admin-sidebar.collapsed .brand-logo-container {
  margin: 0;
}

/* Posicionar el toggle button cuando está colapsado */
.modern-admin-sidebar.collapsed .modern-sidebar-toggle {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-admin-sidebar.collapsed .modern-sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
}

.modern-admin-sidebar.collapsed .nav-link-content {
  justify-content: center;
  padding: 0.875rem 0.5rem;
}

.modern-admin-sidebar.collapsed .nav-icon-container {
  margin: 0;
}

.modern-admin-sidebar.collapsed .modern-logout-button {
  justify-content: center;
  padding: 0.875rem 0.5rem;
}

.modern-admin-sidebar.collapsed .logout-icon-container {
  margin: 0;
}

/* Tooltip for collapsed state */
.modern-admin-sidebar.collapsed .nav-link {
  position: relative;
}

.modern-admin-sidebar.collapsed .nav-link:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 0.5rem;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease forwards;
}

@keyframes tooltip-fade-in {
  from { opacity: 0; transform: translateY(-50%) translateX(-10px); }
  to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

/* Content adjustment for new sidebar */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

.admin-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
  transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* Cuando el sidebar está colapsado */
.admin-content.sidebar-collapsed {
  margin-left: 80px;
}

/* Responsive para móvil */
@media (max-width: 768px) {
  .admin-content {
    margin-left: 0;
    padding: 1rem;
  }

  .admin-content.sidebar-collapsed {
    margin-left: 0;
  }
}

/* Estilos del Modal de Cotización del Carrito */
.cart-quote-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.quote-cart-summary {
  background: #f9fafb;
  border-radius: 8px;
  padding: clamp(1rem, 2vw, 1.5rem);
  margin-bottom: clamp(1rem, 2vw, 1.5rem);
}

.quote-cart-summary h3 {
  margin: 0 0 1rem 0;
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  color: #1f2937;
}

.quote-items-list {
  margin-bottom: 1rem;
}

.quote-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.quote-item:last-child {
  border-bottom: none;
}

.quote-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quote-item-name {
  font-weight: 600;
  color: #1f2937;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
}

.quote-item-details {
  font-size: clamp(0.8rem, 1.6vw, 0.85rem);
  color: #6b7280;
}

.quote-item-price {
  font-weight: 600;
  color: #059669;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  text-align: right;
}

.quote-totals {
  border-top: 1px solid #d1d5db;
  padding-top: 1rem;
}

.quote-total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
}

.quote-total-row:last-child {
  margin-bottom: 0;
}

.quote-final {
  font-size: clamp(1.1rem, 2.2vw, 1.2rem);
  font-weight: 700;
  color: #1f2937;
  border-top: 1px solid #d1d5db;
  padding-top: 0.5rem;
}

.quote-final span:last-child {
  color: #059669;
}

/* Estilos para controles de cantidad en cotización */
.quote-quantity-control {
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quote-quantity-control label {
  font-size: clamp(0.8rem, 1.6vw, 0.9rem);
  font-weight: 600;
  color: #374151;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: none;
  background: white;
  padding: 0.5rem 0.25rem;
  border-radius: 4px;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
}

.quantity-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.quantity-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Alerta de stock en cotización */
.stock-alert-quote {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
  font-size: clamp(0.8rem, 1.6vw, 0.85rem);
  font-weight: 500;
  line-height: 1.4;
}

.quote-form h3 {
  margin: 0 0 1rem 0;
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  color: #1f2937;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: clamp(0.75rem, 1.5vw, 1rem);
  margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
}

.required {
  color: #dc2626;
  font-weight: 700;
}

.form-group input,
.form-group textarea {
  padding: clamp(0.75rem, 1.5vw, 1rem);
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: clamp(0.75rem, 1.5vw, 1rem);
  justify-content: flex-end;
  margin-top: clamp(1.5rem, 3vw, 2rem);
}

.btn-secondary,
.btn-primary {
  padding: clamp(0.75rem, 1.5vw, 1rem) clamp(1.5rem, 3vw, 2rem);
  border-radius: 6px;
  font-weight: 600;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* Responsive para modal de cotización */
@media (max-width: 768px) {
  .cart-quote-modal {
    width: 95vw;
    max-height: 95vh;
    margin: 2.5vh auto;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
    text-align: center;
  }

  .quote-quantity-control {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .quantity-input-group {
    align-self: stretch;
    justify-content: center;
  }
}

/* Estilos para la página del carrito */
.cart-page {
  min-height: 100vh;
  background: #f9fafb;
  padding: clamp(1rem, 2vw, 2rem) 0;
}

.cart-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 2vw, 2rem);
}

.cart-page-header {
  text-align: center;
  margin-bottom: clamp(2rem, 4vw, 3rem);
}

.cart-page-header h1 {
  font-size: clamp(2rem, 4vw, 2.5rem);
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.cart-page-header p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  color: #6b7280;
}

.cart-page-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: clamp(2rem, 4vw, 3rem);
  align-items: start;
}

/* Sección de productos */
.cart-products-section h2 {
  font-size: clamp(1.5rem, 3vw, 1.8rem);
  color: #1f2937;
  margin-bottom: clamp(1rem, 2vw, 1.5rem);
}

.cart-products-list {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 1.5rem);
}

.cart-product-card {
  background: white;
  border-radius: 12px;
  padding: clamp(1rem, 2vw, 1.5rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: clamp(1rem, 2vw, 1.5rem);
  align-items: start;
}

.cart-product-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-product-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.cart-product-card:hover .cart-product-img {
  transform: scale(1.05);
}

.cart-product-info {
  flex: 1;
}

.cart-product-info h3 {
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.cart-product-brand {
  color: #6b7280;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  margin-bottom: 0.25rem;
}

.cart-product-alm {
  color: #374151;
  font-size: clamp(0.85rem, 1.7vw, 0.9rem);
  font-weight: 500;
  margin-bottom: 1rem;
}

.cart-product-description {
  margin: 1rem 0;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 6px;
}

.cart-product-description h4 {
  font-size: clamp(0.9rem, 1.8vw, 1rem);
  color: #374151;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.cart-product-description p {
  font-size: clamp(0.85rem, 1.7vw, 0.9rem);
  color: #6b7280;
  line-height: 1.5;
}

.cart-product-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: clamp(0.8rem, 1.6vw, 0.85rem);
  color: #6b7280;
}

.cart-product-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
  min-width: 200px;
}

.cart-product-price {
  text-align: right;
}

.price-unit {
  display: block;
  font-size: clamp(0.85rem, 1.7vw, 0.9rem);
  color: #6b7280;
}

.price-total {
  display: block;
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  font-weight: 700;
  color: #059669;
}

.cart-quantity-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.cart-quantity-controls label {
  font-size: clamp(0.85rem, 1.7vw, 0.9rem);
  font-weight: 600;
  color: #374151;
}

.stock-alert-cart {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 0.75rem;
  color: #92400e;
  font-size: clamp(0.8rem, 1.6vw, 0.85rem);
  font-weight: 500;
  text-align: center;
  max-width: 200px;
}

.remove-item-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: clamp(0.8rem, 1.6vw, 0.85rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-item-btn:hover {
  background: #fecaca;
  border-color: #f87171;
}

/* Sección de resumen */
.cart-summary-section {
  position: sticky;
  top: 2rem;
}

.cart-summary {
  background: white;
  border-radius: 12px;
  padding: clamp(1rem, 2vw, 1.5rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: clamp(1rem, 2vw, 1.5rem);
}

.cart-summary h3 {
  font-size: clamp(1.2rem, 2.4vw, 1.4rem);
  color: #1f2937;
  margin-bottom: 1rem;
}

.summary-totals {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: clamp(0.9rem, 1.8vw, 1rem);
}

.summary-total {
  font-size: clamp(1.1rem, 2.2vw, 1.2rem);
  font-weight: 700;
  color: #1f2937;
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
}

.summary-total span:last-child {
  color: #059669;
}

/* Formulario de cotización */
.cart-quote-form {
  background: white;
  border-radius: 12px;
  padding: clamp(1rem, 2vw, 1.5rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cart-quote-form h3 {
  font-size: clamp(1.2rem, 2.4vw, 1.4rem);
  color: #1f2937;
  margin-bottom: 1rem;
}

/* Estados vacío y carga */
.cart-page-loading,
.cart-page-empty {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-cart-content h1 {
  font-size: clamp(1.8rem, 3.6vw, 2.2rem);
  color: #1f2937;
  margin-bottom: 1rem;
}

.empty-cart-content p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  color: #6b7280;
  margin-bottom: 2rem;
}

/* Responsive para página del carrito */
@media (max-width: 768px) {
  .cart-page-content {
    grid-template-columns: 1fr;
    gap: clamp(1.5rem, 3vw, 2rem);
  }

  .cart-product-card {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
  }

  .cart-product-image {
    justify-self: center;
  }

  .cart-product-controls {
    align-items: center;
    min-width: auto;
  }

  .cart-quantity-controls {
    align-items: center;
  }

  .stock-alert-cart {
    max-width: 100%;
  }

  .cart-summary-section {
    position: static;
  }
}

/* Marquee Animation */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Brands Marquee */
.brands-marquee-wrapper {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.brands-marquee {
  display: flex;
  animation: marquee 300s linear infinite; /* Mucho más lento: 300s */
  width: max-content;
  cursor: pointer;
  position: relative;
  transition: transform 0.3s ease;
}

.brands-marquee:hover {
  transform: scale(0.99);
}

/* Indicador de desplazamiento */
.brands-marquee-wrapper::before,
.brands-marquee-wrapper::after {
  content: '⟺';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 10;
  animation: pulse 2s infinite;
  pointer-events: none;
}

.brands-marquee-wrapper::before {
  left: 20px;
}

.brands-marquee-wrapper::after {
  right: 20px;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}

.brand-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  min-width: 180px;
}

.brand-item img {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
  max-width: 100%;
}

.brand-item:hover img {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

/* Companies Marquee */
.companies-marquee-wrapper {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.companies-marquee {
  display: flex;
  animation: marquee 350s linear infinite; /* Extremadamente lento: 350s */
  width: max-content;
  cursor: pointer;
  position: relative;
  transition: transform 0.3s ease;
}

.companies-marquee:hover {
  transform: scale(0.99);
}

/* Indicador de desplazamiento */
.companies-marquee-wrapper::before,
.companies-marquee-wrapper::after {
  content: '⟺';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 10;
  animation: pulse 2s infinite;
  pointer-events: none;
}

.companies-marquee-wrapper::before {
  left: 20px;
}

.companies-marquee-wrapper::after {
  right: 20px;
}

.company-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  min-width: 180px;
}

.company-item img {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
  max-width: 100%;
}

.company-item:hover img {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

/* Gradient overlays for marquee edges */
.brands-marquee-overlay-left,
.brands-marquee-overlay-right,
.companies-marquee-overlay-left,
.companies-marquee-overlay-right {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 80px;
  z-index: 1;
  pointer-events: none;
}

.brands-marquee-overlay-left {
  left: 0;
  background: linear-gradient(to right, #f5f5f5, rgba(245, 245, 245, 0));
}

.brands-marquee-overlay-right {
  right: 0;
  background: linear-gradient(to left, #f5f5f5, rgba(245, 245, 245, 0));
}

.companies-marquee-overlay-left {
  left: 0;
  background: linear-gradient(to right, #f9f9f9, rgba(249, 249, 249, 0));
}

.companies-marquee-overlay-right {
  right: 0;
  background: linear-gradient(to left, #f9f9f9, rgba(249, 249, 249, 0));
}

/* Resto del contenido necesita margen para no quedar debajo del navbar */
.about-section {
  margin-top: 40px; /* Espacio después del slider */
}

/* Modern Titles - Estilo Rodisa */
.modern-title {
  font-size: 2.1rem; /* Reducido de 2.5rem */
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
  font-family: var(--heading-font);
}

.modern-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.text-center .modern-title::after {
  left: 50%;
  transform: translateX(-50%);
}

/* Section Description */
.section-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666666;
  margin-bottom: 2rem;
  max-width: 800px;
}

.text-center .section-description {
  margin-left: auto;
  margin-right: auto;
}

/* Hero Section */
.hero-section {
  margin-bottom: 0;
}

/* About Section - Estilo Rodisa */
.about-section {
  padding: 60px 0;
  background-color: #fff;
  margin: 0;
  position: relative;
  overflow: hidden;
}

/* Nueva Sección Quiénes Somos - Moderna y Bonita */
.about-us-modern-section {
  padding: 60px 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.about-us-modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 86, 166, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(247, 148, 29, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.about-us-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

.about-us-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Header de la sección */
.about-us-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.about-us-title {
  font-size: 2.8rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.about-us-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-us-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  font-weight: 500;
  max-width: 600px;
  margin: 0 auto;
}

/* Grid principal */
.about-us-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Sección de texto */
.about-us-text-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about-us-intro {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.about-us-company-name {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: #1a202c;
}

.about-us-eccsa {
  color: #0056a6;
  font-weight: 800;
}

.about-us-main-description {
  font-size: 1rem;
  line-height: 1.7;
  color: #64748b;
  margin: 0;
}

/* Características */
.about-us-features {
  display: grid;
  gap: 1.2rem;
}

.about-us-feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 3px solid #f7941d;
}

.about-us-feature:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.15);
}

.about-us-feature-icon {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 3px 12px rgba(0, 86, 166, 0.3);
}

.about-us-feature-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.4rem 0;
}

.about-us-feature-content p {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* Sección visual */
.about-us-visual-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

/* Logo como engranaje giratorio */
.about-us-gear-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  width: 300px;
  height: 300px;
  margin: 0 auto 2rem auto;
}

.about-us-gear-background {
  position: absolute;
  width: 280px;
  height: 280px;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.1), rgba(247, 148, 29, 0.1));
  border-radius: 50%;
  border: 3px dashed rgba(0, 86, 166, 0.3);
  animation: gearRotate 30s linear infinite;
}

.about-us-gear-glow {
  position: absolute;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(0, 86, 166, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: gearPulse 4s ease-in-out infinite;
}

.about-us-gear-wrapper {
  position: relative;
  z-index: 3;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 4px solid rgba(0, 86, 166, 0.1);
  animation: gearRotateReverse 25s linear infinite;
  transition: all 0.3s ease;
}

.about-us-gear-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(0, 86, 166, 0.25);
  border-color: rgba(0, 86, 166, 0.3);
}

.about-us-gear-logo {
  width: 160px;
  height: auto;
  filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

.about-us-gear-wrapper:hover .about-us-gear-logo {
  filter: drop-shadow(0 8px 25px rgba(0, 86, 166, 0.3));
}

/* Engranajes decorativos pequeños */
.about-us-gear-small {
  position: absolute;
  font-size: 2rem;
  color: rgba(0, 86, 166, 0.6);
  z-index: 2;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.gear-1 {
  top: 10%;
  right: 15%;
  animation: gearRotateSmall1 15s linear infinite;
}

.gear-2 {
  bottom: 20%;
  left: 10%;
  animation: gearRotateSmall2 20s linear infinite reverse;
}

.gear-3 {
  top: 30%;
  left: 5%;
  animation: gearRotateSmall3 18s linear infinite;
}

/* Animaciones de engranajes */
@keyframes gearRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gearRotateReverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes gearRotateSmall1 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gearRotateSmall2 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gearRotateSmall3 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gearPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* Estadísticas */
.about-us-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 350px;
}

.about-us-stat {
  background: white;
  padding: 1.2rem 0.8rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 3px solid #0056a6;
}

.about-us-stat:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.15);
}

.about-us-stat-number {
  font-size: 1.6rem;
  font-weight: 800;
  color: #f7941d;
  margin-bottom: 0.4rem;
  line-height: 1;
}

.about-us-stat-label {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

/* Animaciones */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-us-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .about-us-visual-section {
    order: -1;
  }

  .about-us-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .about-us-modern-section {
    padding: 50px 0;
  }

  .about-us-container {
    padding: 0 20px;
  }

  .about-us-title {
    font-size: 2.2rem;
  }

  .about-us-subtitle {
    font-size: 1rem;
  }

  .about-us-intro {
    padding: 1.5rem;
  }

  .about-us-company-name {
    font-size: 1.6rem;
  }

  .about-us-stats {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .about-us-gear-container {
    width: 250px;
    height: 250px;
  }

  .about-us-gear-background {
    width: 230px;
    height: 230px;
  }

  .about-us-gear-glow {
    width: 200px;
    height: 200px;
  }

  .about-us-gear-wrapper {
    width: 160px;
    height: 160px;
  }

  .about-us-gear-logo {
    width: 130px;
  }

  .about-us-gear-small {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .about-us-title {
    font-size: 1.8rem;
  }

  .about-us-feature {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
    padding: 1rem;
  }

  .about-us-feature-icon {
    align-self: center;
  }

  .about-us-gear-container {
    width: 200px;
    height: 200px;
  }

  .about-us-gear-background {
    width: 180px;
    height: 180px;
  }

  .about-us-gear-glow {
    width: 160px;
    height: 160px;
  }

  .about-us-gear-wrapper {
    width: 130px;
    height: 130px;
  }

  .about-us-gear-logo {
    width: 100px;
  }

  .about-us-gear-small {
    font-size: 1.2rem;
  }
}

/* ===== NUEVA SECCIÓN SERVICIOS MODERNA ===== */
.services-modern-section {
  padding: 100px 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.services-modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 86, 166, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(247, 148, 29, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.services-modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

/* Header de servicios */
.services-modern-header {
  text-align: center;
  margin-bottom: 4rem;
}

.services-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border: 1px solid rgba(0, 86, 166, 0.2);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 86, 166, 0.2);
  animation: float 3s ease-in-out infinite;
}

.services-badge-text {
  font-size: 1rem;
}

.services-modern-title {
  font-size: 4rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.services-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.services-modern-subtitle {
  font-size: 1.3rem;
  color: #64748b;
  font-weight: 400;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de servicios */
.services-modern-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

/* Tarjetas de servicio */
.service-modern-card {
  background: white;
  border: 1px solid rgba(0, 86, 166, 0.1);
  border-radius: 24px;
  padding: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.service-modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.05), rgba(247, 148, 29, 0.05));
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.service-modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 86, 166, 0.15);
  border-color: rgba(0, 86, 166, 0.3);
}

.service-modern-card:hover::before {
  opacity: 1;
}

/* Imagen de servicio */
.service-modern-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 24px 24px 0 0;
  position: relative;
  z-index: 1;
}

/* Header de la tarjeta */
.service-modern-header {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 2;
}

.service-modern-number {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

/* Contenido de la tarjeta */
.service-modern-content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: 2rem;
}

.service-modern-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.service-modern-description {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Características del servicio */
.service-modern-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.service-feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #64748b;
}

.service-feature-check {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  flex-shrink: 0;
}

/* Footer de la tarjeta */
.service-modern-footer {
  position: relative;
  z-index: 1;
  margin-top: auto;
  padding: 0 2rem 2rem 2rem;
}

.service-modern-btn {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.service-modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.4);
}

.service-btn-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.service-modern-btn:hover .service-btn-arrow {
  transform: translateX(4px);
}

/* Overlay de la tarjeta */
.service-modern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.95), rgba(247, 148, 29, 0.95));
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.service-modern-card:hover .service-modern-overlay {
  opacity: 1;
  transform: scale(1);
}

.service-overlay-content {
  text-align: center;
  color: white;
}

.service-overlay-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.service-overlay-content h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.service-overlay-content p {
  font-size: 1rem;
  opacity: 0.9;
}

/* Sección CTA */
.services-modern-cta {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid rgba(0, 86, 166, 0.1);
  border-radius: 24px;
  padding: 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.services-modern-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(247, 148, 29, 0.05), rgba(0, 86, 166, 0.05));
  z-index: 0;
}

.services-cta-content {
  position: relative;
  z-index: 1;
}

.services-cta-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
}

.services-cta-content p {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.services-cta-btn {
  background: linear-gradient(135deg, #f7941d, #0056a6);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 16px;
  font-weight: 700;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(247, 148, 29, 0.3);
}

.services-cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(247, 148, 29, 0.4);
}

.services-cta-icon {
  font-size: 1.2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

/* Responsive Design para Servicios */
@media (max-width: 1024px) {
  .services-modern-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
  }

  .services-modern-title {
    font-size: 3rem;
  }

  .service-modern-card {
    min-height: 380px;
  }
}

@media (max-width: 768px) {
  .services-modern-section {
    padding: 80px 0;
  }

  .services-modern-container {
    padding: 0 20px;
  }

  .services-modern-title {
    font-size: 2.5rem;
  }

  .services-modern-subtitle {
    font-size: 1.1rem;
  }

  .services-modern-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-modern-card {
    min-height: 350px;
    padding: 1.5rem;
  }

  .service-modern-icon-container {
    width: 60px;
    height: 60px;
  }

  .service-modern-icon {
    font-size: 1.5rem;
  }

  .service-modern-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .services-modern-cta {
    padding: 2rem;
  }

  .services-cta-content h3 {
    font-size: 1.5rem;
  }

  .services-cta-content p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .services-modern-title {
    font-size: 2rem;
  }

  .services-badge {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .service-modern-card {
    min-height: 320px;
    padding: 1.25rem;
  }

  .service-modern-header {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
  }

  .service-modern-title {
    font-size: 1.3rem;
  }

  .service-modern-description {
    font-size: 0.9rem;
  }

  .services-cta-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8)), url('/images/defaults/Automatización-Industrial.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.15;
  z-index: 0;
}

.about-container {
  display: flex;
  gap: 40px;
  align-items: center;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0 40px;
  text-align: left;
  position: relative;
  z-index: 1;
}

.about-content {
  flex: 1;
}

.about-paragraphs {
  position: relative;
  padding-left: 0;
}

.about-paragraphs::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.about-image {
  flex: 1;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.about-img-container {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Gear Icon Container */
.gear-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  perspective: 1000px;
}

.gear-icon {
  max-width: 300px; /* Increased from 200px to 300px */
  transform-style: preserve-3d;
  animation: rotate 15s linear infinite;
  filter: drop-shadow(5px 15px 15px rgba(0, 0, 0, 0.6));
  transition: all 0.5s ease;
}

@keyframes rotate {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}

/* Features Section - Estilo Rodisa */
.features-section {
  padding: 40px 40px;
  background-color: var(--primary-color);
  color: white;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-width: 100%;
  margin: 0 auto;
}

.feature-box {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.feature-box:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.feature-icon-container {
  width: 80px;
  height: 80px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.feature-box:hover .feature-icon-container {
  transform: scale(1.1);
}

.feature-icon-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.feature-box-title {
  font-size: 1.1rem; /* Reducido de 1.2rem */
  font-weight: 700;
  margin-bottom: 15px;
  color: white;
}

.feature-box-text {
  font-size: 0.9rem; /* Reducido de 0.95rem */
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

/* ===== FEATURES MODERNAS REDISEÑADAS ===== */
.features-modern-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.features-modern-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 75%, rgba(0, 86, 166, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(247, 148, 29, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.features-modern-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

/* Header de la sección */
.features-modern-header {
  text-align: center;
  margin-bottom: 4rem;
}

.features-modern-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.features-highlight {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features-modern-subtitle {
  font-size: 1.2rem;
  color: #64748b;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de características */
.features-modern-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

/* Tarjetas de características modernas - Más compactas */
.feature-modern-card {
  background: white;
  border-radius: 16px;
  padding: 1.75rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 86, 166, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: auto;
  max-height: 400px;
}

.feature-modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--accent-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.feature-modern-card:hover::before {
  transform: scaleX(1);
}

.feature-modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.12);
  border-color: rgba(0, 86, 166, 0.15);
}

/* Número de la característica - Más pequeño */
.feature-number {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--accent-color);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Contenedor del icono - Más compacto */
.feature-icon-container {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;
}

.feature-icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-color), rgba(0, 0, 0, 0.1));
  border-radius: 50%;
  opacity: 0.1;
}

.feature-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  color: var(--accent-color);
  z-index: 2;
  transition: all 0.3s ease;
}

.feature-icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--accent-color);
  border-radius: 50%;
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-modern-card:hover .feature-icon {
  transform: translate(-50%, -50%) scale(1.1);
}

.feature-modern-card:hover .feature-icon-glow {
  opacity: 0.3;
}

/* Contenido de la característica */
.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.feature-description {
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex: 1;
}

/* Barra de progreso decorativa - Más pequeña */
.feature-progress-bar {
  width: 100%;
  height: 3px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.feature-progress-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 2px;
  width: 0;
  animation: fillProgress 2s ease-out 0.5s forwards;
}

@keyframes fillProgress {
  to {
    width: 100%;
  }
}

/* Badge de calidad - Más compacto */
.feature-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  width: fit-content;
}

.badge-icon {
  background: #059669;
  color: white;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  font-weight: 700;
}

/* Efectos decorativos */
.feature-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: var(--accent-color);
  opacity: 0.05;
  animation: floatDecoration 6s ease-in-out infinite;
}

.decoration-1 {
  width: 12px;
  height: 12px;
  top: 15%;
  right: 15%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 8px;
  height: 8px;
  bottom: 20%;
  left: 10%;
  animation-delay: 2s;
}

.decoration-3 {
  width: 15px;
  height: 15px;
  top: 60%;
  right: 10%;
  animation-delay: 4s;
}

@keyframes floatDecoration {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

/* Hover overlay */
.feature-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-color), rgba(0, 0, 0, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 24px;
  z-index: 10;
}

.feature-modern-card:hover .feature-hover-overlay {
  opacity: 1;
  transform: scale(1);
}

.hover-content {
  text-align: center;
  color: white;
}

.hover-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  animation: bounceIn 0.6s ease-out 0.2s both;
}

.hover-text {
  font-size: 1rem;
  font-weight: 600;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* CTA Section */
.features-cta-section {
  text-align: center;
  margin-top: 2rem;
}

.features-cta-content {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 86, 166, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.features-cta-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
}

.features-cta-content p {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.features-cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
}

.cta-primary-btn {
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.cta-primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 86, 166, 0.4);
}

.cta-primary-btn svg {
  transition: transform 0.3s ease;
}

.cta-primary-btn:hover svg {
  transform: translateX(3px);
}

.cta-secondary-btn {
  background: rgba(0, 86, 166, 0.1);
  color: #0056a6;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(0, 86, 166, 0.2);
}

.cta-secondary-btn:hover {
  background: rgba(0, 86, 166, 0.2);
  border-color: rgba(0, 86, 166, 0.4);
  transform: translateY(-3px);
}

/* Responsive Design para Features Modernas */
@media (max-width: 1024px) {
  .features-modern-container {
    padding: 0 30px;
  }

  .features-modern-title {
    font-size: 3rem;
  }

  .features-modern-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .feature-modern-card {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .features-modern-section {
    padding: 80px 0;
  }

  .features-modern-container {
    padding: 0 20px;
  }

  .features-modern-title {
    font-size: 2.5rem;
  }

  .features-modern-subtitle {
    font-size: 1.1rem;
  }

  .features-modern-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-modern-card {
    padding: 1.5rem;
    max-width: 400px;
    margin: 0 auto;
  }

  .feature-title {
    font-size: 1.2rem;
  }

  .feature-description {
    font-size: 0.9rem;
  }

  .features-cta-content {
    padding: 2rem;
  }

  .features-cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .cta-primary-btn,
  .cta-secondary-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .features-modern-title {
    font-size: 1.75rem;
  }

  .features-modern-header {
    margin-bottom: 3rem;
  }

  .feature-modern-card {
    padding: 1.25rem;
    max-width: 100%;
  }

  .feature-number {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
    top: 15px;
    right: 15px;
  }

  .feature-icon-container {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;
  }

  .feature-icon {
    font-size: 1.8rem;
  }

  .feature-title {
    font-size: 1.1rem;
  }

  .feature-description {
    font-size: 0.85rem;
  }

  .features-cta-content {
    padding: 1.5rem;
  }

  .features-cta-content h3 {
    font-size: 1.5rem;
  }

  .features-cta-content p {
    font-size: 1rem;
  }

  .cta-primary-btn,
  .cta-secondary-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Estilos para el logo 3D */
.logo-3d-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  perspective: 1000px;
}

.logo-3d {
  max-width: 80%;
  transform-style: preserve-3d;
  animation: float 8s ease-in-out infinite;
  filter: drop-shadow(5px 15px 15px rgba(0, 0, 0, 0.6));
  transition: all 0.5s ease;
}

.logo-3d:hover {
  animation-play-state: paused;
  transform: scale(1.1) rotateY(10deg);
}

@keyframes float {
  0% {
    transform: translateY(0px) rotateY(0deg) rotateX(0deg);
    filter: drop-shadow(5px 15px 15px rgba(0, 0, 0, 0.6));
  }
  25% {
    transform: translateY(-10px) rotateY(5deg) rotateX(5deg);
    filter: drop-shadow(10px 20px 20px rgba(0, 0, 0, 0.7));
  }
  50% {
    transform: translateY(0px) rotateY(10deg) rotateX(0deg);
    filter: drop-shadow(5px 15px 15px rgba(0, 0, 0, 0.6));
  }
  75% {
    transform: translateY(10px) rotateY(5deg) rotateX(-5deg);
    filter: drop-shadow(10px 20px 20px rgba(0, 0, 0, 0.7));
  }
  100% {
    transform: translateY(0px) rotateY(0deg) rotateX(0deg);
    filter: drop-shadow(5px 15px 15px rgba(0, 0, 0, 0.6));
  }
}

.about-text {
  font-size: 0.95rem; /* Reducido de 1.1rem */
  line-height: 1.7;
  margin-bottom: 1.2rem;
  color: #333;
  font-family: var(--body-font);
  text-align: left;
  position: relative;
  padding-left: 1rem;
}

.about-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 3px;
  height: calc(100% - 1rem);
  background-color: var(--primary-color);
  border-radius: 3px;
}

.about-text strong {
  color: var(--primary-color);
  font-weight: 700;
}

.about-cta {
  margin-top: 2rem;
  text-align: left;
}

/* Services Section */
.services-section {
  padding: 2.5rem 40px;
  margin-bottom: 0;
  width: 100%;
  background-color: #000;
}

.services-intro {
  font-size: 1rem; /* Reducido de 1.2rem */
  max-width: 800px;
  margin: 0 0 1.5rem;
  color: #e0e0e0;
  text-align: left;
}

.services-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}

/* Modern Service Cards */
.modern-service-card {
  background-color: #111;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 400px;
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;
  width: 300px;
  margin: 0 10px 20px;
}

.modern-service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

.modern-service-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.modern-service-img-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.service-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-service-card:hover .service-overlay {
  opacity: 1;
}

.modern-service-icon {
  font-size: 2.5rem;
  color: var(--white);
  background-color: rgba(0, 0, 0, 0.7);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.modern-service-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.modern-service-title {
  font-size: 1.1rem; /* Reducido de 1.3rem */
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: var(--accent-color);
  text-align: left;
  padding: 0 10px;
}

.modern-service-description {
  font-size: 0.9rem; /* Reducido de 1rem */
  line-height: 1.5;
  color: #e0e0e0;
  margin-bottom: 1rem;
  flex-grow: 1;
  text-align: left;
  padding: 0 10px;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.modern-service-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--accent-color);
  border: none;
  padding: 0.5rem 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
  width: 100%;
}

.modern-service-button svg {
  transition: transform 0.3s ease;
}

.modern-service-button:hover {
  color: var(--white);
}

.modern-service-button:hover svg {
  transform: translateX(5px);
}

/* Why Us Section */
.why-us-section {
  padding: 2.5rem 40px;
  background-color: #f5f5f5;
  margin-bottom: 0;
  width: 100%;
  margin-top: 20px;
}

.why-us-intro {
  font-size: 1rem; /* Reducido de 1.2rem */
  max-width: 800px;
  margin: 0 auto 1.5rem;
  color: #333;
}

.features-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.feature-card {
  background-color: var(--primary-color);
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid var(--secondary-color);
  flex: 0 0 auto;
  width: 300px;
  margin: 0 10px 20px;
  box-shadow: 0 5px 15px rgba(0, 86, 166, 0.2);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--accent-color);
  background-color: var(--eccsa-dark-blue);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.feature-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: var(--accent-color);
}

.feature-text {
  font-size: 0.95rem;
  line-height: 1.5;
  color: white;
  padding: 0 5px;
}

/* Why ECCSA Section */
.why-eccsa-section {
  padding: 4rem 40px;
  background-color: #f9f9f9;
  margin: 0;
  width: 100%;
}

.why-eccsa-container {
  max-width: 1400px;
  margin: 0 auto;
}

.why-eccsa-flex-layout {
  display: flex;
  align-items: flex-start;
  gap: 3rem;
}

.why-eccsa-question {
  flex: 0 0 25%;
  position: sticky;
  top: 100px;
}

.why-eccsa-question .modern-title {
  margin-bottom: 0;
  text-align: left;
}

.why-eccsa-content-wrapper {
  flex: 1;
}

.why-eccsa-tabs {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.why-eccsa-tab {
  background-color: #fff;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.8rem 1.5rem;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.why-eccsa-tab:hover {
  background-color: rgba(0, 86, 166, 0.1);
}

.why-eccsa-tab.active {
  background-color: var(--primary-color);
  color: white;
}

.why-eccsa-content {
  background-color: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  max-height: 500px;
  overflow-y: auto;
}

.why-eccsa-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.why-eccsa-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.why-eccsa-item:hover {
  background-color: rgba(0, 86, 166, 0.05);
  transform: translateX(5px);
}

.why-eccsa-check {
  color: var(--accent-color);
  font-size: 1.2rem;
  font-weight: bold;
}

.why-eccsa-text {
  font-size: 1rem;
  color: #444;
}

@media (max-width: 992px) {
  .why-eccsa-flex-layout {
    flex-direction: column;
    gap: 1.5rem;
  }

  .why-eccsa-question {
    flex: 0 0 100%;
    position: relative;
    top: 0;
    text-align: center;
  }

  .why-eccsa-question .modern-title {
    text-align: center;
  }

  .why-eccsa-tabs {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .why-eccsa-grid {
    grid-template-columns: 1fr;
  }

  .why-eccsa-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Contact Section */
.contact-section {
  padding: 4rem 40px;
  margin-bottom: 0;
  width: 100%;
  background-color: #000000;
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--eccsa-red), var(--primary-color), var(--accent-color));
}

.contact-container {
  display: flex;
  gap: 3rem;
  align-items: center;
  width: 100%;
  text-align: center;
}

.contact-info {
  flex: 1;
}

.contact-info .modern-title {
  color: #ffffff;
  margin-bottom: 2rem;
}

.contact-info .modern-title::after {
  background-color: var(--accent-color);
}

.contact-map {
  flex: 1;
  height: 450px;
}

.contact-map-iframe {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  border: 3px solid rgba(255, 255, 255, 0.1);
}

.contact-text {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  color: #ffffff;
  text-align: left;
  max-width: 600px;
}

.contact-details {
  margin-bottom: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid var(--accent-color);
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.2rem;
  color: #ffffff;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  font-size: 1.8rem;
  margin-right: 1.5rem;
  color: var(--accent-color);
  background-color: rgba(255, 255, 255, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-button {
  background-color: #F26522; /* Color naranja del logo de ECCSA */
  color: #ffffff;
  border: none;
  padding: 1.2rem 2.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: inline-block;
  margin-top: 1rem;
}

.contact-button:hover {
  background-color: #E55511;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(242, 101, 34, 0.3);
}

/* Página de Servicios */
.services-page {
  width: 100%;
  margin-top: 60px; /* Espacio para el navbar fijo */
}

.services-header {
  background-image: url('/images/defaults/Automatización-Industrial.jpg');
  background-size: cover;
  background-position: center;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.services-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6));
}

.services-header-content {
  position: relative;
  text-align: center;
  color: white;
  z-index: 1;
}

.services-header-content h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}

.services-header-content p {
  font-size: 1.2rem;
  color: #00a0e3;
}

.services-content-container {
  display: flex;
  width: 100%;
  min-height: 600px;
}

.services-sidebar {
  width: 250px;
  background-color: #f5f5f5;
  padding: 2rem 0;
  border-right: 1px solid #e0e0e0;
}

.service-nav-button {
  display: block;
  width: 100%;
  padding: 1rem 1.5rem;
  text-align: left;
  background: none;
  border: none;
  border-left: 4px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-nav-button:hover {
  background-color: #e0e0e0;
  color: var(--primary-color);
}

.service-nav-button.active {
  background-color: #e0e0e0;
  border-left: 4px solid var(--primary-color);
  color: var(--primary-color);
  font-weight: 600;
}

.service-content {
  flex: 1;
  padding: 2rem 3rem;
}

.service-content h2 {
  font-size: 1.8rem; /* Reducido de 2rem */
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.service-subtitle {
  font-size: 1.2rem; /* Reducido de 1.4rem */
  color: #00a0e3;
  margin: 1.5rem 0 1rem;
}

.service-description {
  font-size: 0.95rem; /* Reducido de 1.1rem */
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: #555;
}

.service-features {
  list-style: none;
  margin: 0 0 2rem;
  padding: 0;
}

.service-feature-item {
  padding: 0.5rem 0;
  display: flex;
  align-items: flex-start;
  font-size: 0.95rem; /* Reducido de 1.1rem */
}

.feature-check {
  color: var(--primary-color);
  font-weight: bold;
  margin-right: 0.75rem;
  font-size: 1.2rem;
}

.service-brands {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 2rem;
  justify-content: center;
  background-color: #f8f8f8;
  padding: 2rem;
  border-radius: 10px;
}

.brand-logo {
  width: 150px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #eaeaea;
}

.brand-logo:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
  border-color: var(--primary-color);
}

/* Página de Productos */
.products-page {
  width: 100%;
  max-width: 1400px;
  margin: 80px auto 0;
  padding: 0 10px;
  box-sizing: border-box;
}

.mobile-view {
  max-width: 100%;
  padding: 0 5px;
  width: 100vw;
}

.products-header {
  margin-bottom: 2rem;
  text-align: center;
  border-bottom: 2px solid var(--medium-gray);
  padding-bottom: 1.5rem;
}

.products-title {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-weight: 700;
}

.products-description {
  font-size: 1.1rem;
  color: var(--dark-gray);
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.products-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
}

.category-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  max-width: 400px;
}

.category-filter label {
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
}

.category-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: 4px;
  background-color: white;
  width: 100%;
  min-width: 150px;
  flex: 1;
}

.search-filter {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 400px;
  width: 100%;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
}

.search-button {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
}

.results-count {
  font-size: 1rem;
  color: var(--dark-gray);
  margin-right: auto;
}

.results-sort {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-sort label {
  white-space: nowrap;
}

.sort-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--medium-gray);
  border-radius: 4px;
  background-color: white;
  min-width: 150px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
}

.product-card {
  border: 1px solid var(--medium-gray);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 100%;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  padding: 1rem;
  border-bottom: 1px solid var(--medium-gray);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.product-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-info {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.product-category {
  display: inline-block;
  background-color: var(--light-gray);
  color: var(--dark-gray);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  margin-bottom: 0.8rem;
}

.product-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.product-brand {
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1rem;
}

.product-description {
  font-size: 0.95rem;
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
  line-height: 1.5;
  flex-grow: 1;
}

.product-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

.product-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.product-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.7rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex: 1;
}

.product-button:hover {
  background-color: var(--secondary-color);
}

.product-details-button {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 0.7rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.product-details-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background-color: var(--light-gray);
  border-radius: 8px;
}

.products-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.pagination-button {
  background-color: white;
  border: 1px solid var(--medium-gray);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--light-gray);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--medium-gray);
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-number:hover {
  background-color: var(--light-gray);
}

.pagination-number.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.products-contact {
  background-color: var(--light-gray);
  padding: 3rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 3rem;
}

.products-contact h2 {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.products-contact p {
  font-size: 1.1rem;
  color: var(--dark-gray);
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive para productos */
@media (max-width: 992px) {
  .products-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .category-filter,
  .search-filter {
    max-width: 100%;
    width: 100%;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .product-info {
    padding: 1.2rem;
  }

  .product-name {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .products-page {
    margin-top: 70px;
  }

  .products-title {
    font-size: 2rem;
  }

  .products-description {
    font-size: 1rem;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .results-count {
    margin-bottom: 0.5rem;
  }

  .results-sort {
    width: 100%;
  }

  .sort-select {
    flex: 1;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.5rem;
  }

  .product-actions {
    flex-direction: column;
  }

  .product-button,
  .product-details-button {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .products-page {
    padding: 0;
    margin-top: 60px;
    width: 100%;
    max-width: 100%;
  }

  .products-header {
    margin-bottom: 1.5rem;
  }

  .products-title {
    font-size: 1.8rem;
  }

  .products-description {
    font-size: 0.9rem;
  }

  .category-filter label,
  .results-sort label {
    font-size: 0.9rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    width: 100%;
    padding: 0 2px;
  }

  .product-card {
    width: 100%;
    margin: 0 auto;
    max-width: 100%;
    box-sizing: border-box;
  }

  .mobile-view .product-card {
    width: 100%;
    max-width: 100%;
    margin: 0 auto 15px;
    border-left: none;
    border-right: none;
    border-radius: 0;
  }

  .mobile-view .products-grid {
    padding: 0;
    gap: 0;
  }

  .mobile-view .product-image {
    border-radius: 0;
  }

  .product-image {
    height: 180px;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-size: 1.1rem;
  }

  .product-description {
    font-size: 0.9rem;
  }

  .pagination-numbers {
    display: none;
  }

  .products-contact {
    padding: 2rem 1rem;
  }

  .products-contact h2 {
    font-size: 1.5rem;
  }

  .products-contact p {
    font-size: 0.9rem;
  }
}

/* ===== FOOTER MODERNO REDISEÑADO ===== */
.footer-modern {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #1a202c 100%);
  color: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: 0;
}

/* Decorative wave at top */
.footer-wave {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: rotate(180deg);
}

.footer-wave svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 60px;
  color: #f8fafc;
}

/* Main container */
.footer-container-modern {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 1;
}

.footer-main-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 3rem;
  padding: 4rem 0 3rem;
  position: relative;
}

.footer-main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 86, 166, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(247, 148, 29, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Company section */
.footer-company-section {
  grid-column: span 1;
}

.footer-logo-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.footer-logo-modern {
  filter: drop-shadow(0 0 10px rgba(0, 160, 227, 0.3));
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.footer-logo-modern:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 15px rgba(0, 160, 227, 0.5));
}

.footer-company-info {
  width: 100%;
}

.footer-company-name {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.footer-company-tagline {
  color: #cbd5e0;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.footer-company-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f7941d;
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: #a0aec0;
  margin-top: 0.25rem;
}

/* Social media modern */
.footer-social-modern {
  margin-top: 1rem;
}

.social-title {
  font-size: 1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 1rem;
}

.social-icons-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.social-icon-modern {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  text-decoration: none;
}

.social-icon-modern:hover {
  transform: translateY(-3px);
  background: var(--social-color);
  border-color: var(--social-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.social-icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--social-color);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-icon-modern:hover .social-icon-bg {
  opacity: 1;
}

.social-icon-svg {
  font-size: 1.25rem;
  color: #cbd5e0;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.social-icon-modern:hover .social-icon-svg {
  color: white;
  transform: scale(1.1);
}

.social-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  z-index: 10;
}

.social-icon-modern:hover .social-tooltip {
  opacity: 1;
}

/* Navigation section */
.footer-nav-section {
  grid-column: span 1;
}

.footer-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.footer-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  border-radius: 1px;
}

.title-icon {
  font-size: 1rem;
}

.footer-nav-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.footer-nav-list li {
  margin-bottom: 0.75rem;
}

.footer-nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #cbd5e0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
}

.footer-nav-link:hover {
  color: #f7941d;
  padding-left: 0.5rem;
}

.nav-arrow {
  font-size: 0.8rem;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.footer-nav-link:hover .nav-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* Services highlight */
.footer-services-highlight {
  margin-top: 1rem;
}

.services-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #a0aec0;
  margin-bottom: 1rem;
}

.services-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.service-tag {
  background: rgba(0, 86, 166, 0.2);
  color: #90cdf4;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(0, 86, 166, 0.3);
  transition: all 0.3s ease;
}

.service-tag:hover {
  background: rgba(0, 86, 166, 0.3);
  color: #bee3f8;
  transform: translateY(-1px);
}

/* Contact section */
.footer-contact-section {
  grid-column: span 1;
}

.contact-items-modern {
  margin-bottom: 2rem;
}

.contact-item-modern {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.contact-item-modern:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 86, 166, 0.3);
  transform: translateY(-2px);
}

.contact-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 10px;
  flex-shrink: 0;
}

.contact-icon-modern {
  font-size: 1.1rem;
  color: white;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.contact-label {
  font-size: 0.8rem;
  color: #a0aec0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 0.9rem;
  color: #e2e8f0;
  line-height: 1.4;
}

/* CTA Button */
.footer-cta {
  margin-top: 1rem;
}

.footer-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  padding: 0.875rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.footer-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.4);
  color: white;
}

.footer-cta-button svg {
  transition: transform 0.3s ease;
}

.footer-cta-button:hover svg {
  transform: translateX(3px);
}

/* Map section */
.footer-map-section {
  grid-column: span 1;
}

.map-container-modern {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.map-container-modern:hover {
  border-color: rgba(0, 86, 166, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.8), rgba(247, 148, 29, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.map-container-modern:hover .map-overlay {
  opacity: 1;
}

.map-info {
  text-align: center;
  color: white;
}

.map-title {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.map-subtitle {
  display: block;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Footer bottom modern */
.footer-bottom-modern {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
  background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.footer-copyright-modern {
  flex: 1;
}

.footer-copyright-modern p {
  margin: 0;
  color: #cbd5e0;
  font-size: 0.9rem;
}

.footer-subtitle {
  color: #a0aec0;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Company Badges */
.footer-company-badges {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.company-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.6rem 0.8rem;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.1), rgba(247, 148, 29, 0.1));
  border-radius: 10px;
  border: 1px solid rgba(247, 148, 29, 0.2);
  transition: all 0.3s ease;
  min-width: 60px;
}

.company-badge:hover {
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.2), rgba(247, 148, 29, 0.2));
  border-color: rgba(247, 148, 29, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(247, 148, 29, 0.2);
}

.badge-text {
  font-size: 0.75rem;
  font-weight: 700;
  color: #f7941d;
  line-height: 1;
}

.badge-label {
  font-size: 0.65rem;
  color: #cbd5e0;
  margin-top: 0.2rem;
  text-align: center;
}

/* Policy links */
.footer-policy-links-modern {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.policy-link-modern {
  color: #cbd5e0;
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  position: relative;
}

.policy-link-modern::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: #f7941d;
  transition: width 0.3s ease;
}

.policy-link-modern:hover {
  color: #f7941d;
}

.policy-link-modern:hover::after {
  width: 100%;
}

/* ===== RESPONSIVE FOOTER MODERNO ===== */
@media (max-width: 1200px) {
  .footer-main-content {
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
  }

  .footer-company-section {
    grid-column: span 2;
  }

  .footer-nav-section,
  .footer-contact-section,
  .footer-map-section {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .footer-container-modern {
    padding: 0 20px;
  }

  .footer-main-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 0 2rem;
  }

  .footer-company-section,
  .footer-nav-section,
  .footer-contact-section,
  .footer-map-section {
    grid-column: span 1;
  }

  .footer-logo-container {
    align-items: center;
    text-align: center;
  }

  .footer-company-name {
    font-size: 1.75rem;
  }

  .footer-company-stats {
    justify-content: center;
    gap: 2rem;
  }

  .social-icons-grid {
    grid-template-columns: repeat(4, 1fr);
    justify-items: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .footer-company-badges {
    order: -1;
    justify-content: center;
    gap: 0.5rem;
  }

  .company-badge {
    padding: 0.5rem 0.6rem;
    min-width: 50px;
  }

  .badge-text {
    font-size: 0.7rem;
  }

  .badge-label {
    font-size: 0.6rem;
  }

  .footer-policy-links-modern {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .footer-wave svg {
    height: 40px;
  }

  .footer-main-content {
    padding: 2rem 0 1.5rem;
    gap: 1.5rem;
  }

  .footer-company-name {
    font-size: 1.5rem;
  }

  .footer-company-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }

  .social-icons-grid {
    gap: 0.5rem;
  }

  .social-icon-modern {
    width: 45px;
    height: 45px;
  }

  .contact-item-modern {
    padding: 0.75rem;
    margin-bottom: 1rem;
  }

  .contact-icon-wrapper {
    width: 35px;
    height: 35px;
  }

  .contact-icon-modern {
    font-size: 1rem;
  }

  .footer-cta-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
  }

  .map-container-modern iframe {
    height: 150px;
  }

  .footer-bottom-modern {
    padding: 1.5rem 0;
  }

  .certification-badge {
    padding: 0.5rem 0.75rem;
  }

  .cert-text {
    font-size: 0.75rem;
  }

  .cert-label {
    font-size: 0.65rem;
  }

  .footer-policy-links-modern {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* ===== ANIMACIONES DE SCROLL ===== */

/* Configuración base para animaciones */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Animaciones específicas */
.fade-in {
  opacity: 0;
  transition: opacity 0.8s ease-out;
}

.fade-in.visible {
  opacity: 1;
}

.slide-up {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-down {
  opacity: 0;
  transform: translateY(-60px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-left {
  opacity: 0;
  transform: translateX(60px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-right {
  opacity: 0;
  transform: translateX(-60px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.scale-up {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-up.visible {
  opacity: 1;
  transform: scale(1);
}

.rotate-in {
  opacity: 0;
  transform: rotate(-10deg) scale(0.8);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.rotate-in.visible {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* Animaciones con bounce */
.bounce-in {
  opacity: 0;
  transform: scale(0.3);
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.bounce-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* Animaciones de flip */
.flip-in-x {
  opacity: 0;
  transform: perspective(400px) rotateX(90deg);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.flip-in-x.visible {
  opacity: 1;
  transform: perspective(400px) rotateX(0deg);
}

.flip-in-y {
  opacity: 0;
  transform: perspective(400px) rotateY(90deg);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.flip-in-y.visible {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg);
}

/* Animaciones de zoom */
.zoom-in {
  opacity: 0;
  transform: scale(0);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.zoom-in.visible {
  opacity: 1;
  transform: scale(1);
}

.zoom-out {
  opacity: 0;
  transform: scale(1.5);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.zoom-out.visible {
  opacity: 1;
  transform: scale(1);
}

/* Animaciones de blur */
.blur-in {
  opacity: 0;
  filter: blur(10px);
  transform: scale(1.1);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.blur-in.visible {
  opacity: 1;
  filter: blur(0px);
  transform: scale(1);
}

/* Animaciones stagger (en secuencia) */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stagger-item.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Delays para stagger */
.stagger-delay-1 { transition-delay: 0.1s; }
.stagger-delay-2 { transition-delay: 0.2s; }
.stagger-delay-3 { transition-delay: 0.3s; }
.stagger-delay-4 { transition-delay: 0.4s; }
.stagger-delay-5 { transition-delay: 0.5s; }
.stagger-delay-6 { transition-delay: 0.6s; }

/* Animaciones de texto */
.text-reveal {
  overflow: hidden;
}

.text-reveal-inner {
  transform: translateY(100%);
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-reveal.visible .text-reveal-inner {
  transform: translateY(0);
}

/* Animaciones de línea */
.line-draw {
  position: relative;
  overflow: hidden;
}

.line-draw::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.line-draw.visible::after {
  width: 100%;
}

/* Animaciones de contador */
.counter-animate {
  font-variant-numeric: tabular-nums;
  transition: all 0.3s ease;
}

/* Parallax */
.parallax-element {
  will-change: transform;
}

/* Animaciones de hover mejoradas con scroll */
.scroll-hover-enhance {
  transition: all 0.3s ease;
}

.scroll-hover-enhance.visible:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.2);
}

/* Animaciones adicionales para elementos específicos */
.navbar-link {
  position: relative;
  overflow: hidden;
}

.navbar-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  transition: left 0.3s ease;
}

.navbar-link:hover::before {
  left: 0;
}

/* Animaciones para sliders */
.brand-item,
.company-item {
  transition: all 0.3s ease;
}

.brand-item:hover,
.company-item:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* Animaciones para botones */
.why-eccsa-tab {
  position: relative;
  overflow: hidden;
}

.why-eccsa-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.why-eccsa-tab:hover::before {
  left: 100%;
}

/* Animaciones de entrada para elementos del hero */
.hero-dynamic-section .geometric-shape {
  animation: floatShape 6s ease-in-out infinite;
}

.hero-dynamic-section .geometric-shape:nth-child(even) {
  animation-direction: reverse;
}

@keyframes floatShape {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Animaciones para métricas del hero */
.metric-item {
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-5px);
}

.metric-item:hover .metric-number {
  color: #f7941d;
  transform: scale(1.1);
}

/* Animaciones para elementos de contacto en footer */
.contact-item-modern {
  position: relative;
  overflow: hidden;
}

.contact-item-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 166, 0.1), transparent);
  transition: left 0.8s ease;
}

.contact-item-modern:hover::before {
  left: 100%;
}

/* Animaciones para productos destacados */
.featured-product-modern-card {
  position: relative;
  overflow: hidden;
}

.featured-product-modern-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(0, 86, 166, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.featured-product-modern-card:hover::after {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Animaciones para características modernas */
.feature-modern-card {
  position: relative;
}

.feature-modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, var(--accent-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: inherit;
}

.feature-modern-card:hover::before {
  opacity: 0.05;
}

/* Animaciones de pulsación para elementos importantes */
.pulse-on-scroll {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 86, 166, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 86, 166, 0.6);
  }
}

/* Animaciones para elementos de navegación móvil */
.mobile-nav-link {
  position: relative;
  overflow: hidden;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  transition: width 0.3s ease;
}

.mobile-nav-link:hover::before {
  width: 100%;
}

.policy-link:hover {
  color: var(--primary-color);
}

/* ===== NAVBAR PROFESIONAL MODERNO ===== */

/* Top Contact Bar */
.navbar-top-bar {
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  font-size: 0.875rem;
  padding: 8px 0;
  position: relative;
  z-index: 1001;
}

.navbar-top-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-top-left {
  display: flex;
  gap: 2rem;
}

.navbar-contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.navbar-contact-item:hover {
  opacity: 1;
}

.navbar-contact-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.navbar-top-right {
  font-weight: 500;
}

.navbar-highlight {
  color: #f7941d;
  font-weight: 700;
}

/* Main Header */
.navbar-header {
  position: fixed;
  top: 44px;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 20px rgba(0, 86, 166, 0.08);
}

.navbar-scrolled {
  top: 0;
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 32px rgba(0, 86, 166, 0.15);
  border-bottom: 1px solid rgba(0, 86, 166, 0.15);
}

.navbar-scrolled .navbar-top-bar {
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.navbar-main {
  position: relative;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

/* Logo Section */
.navbar-brand {
  flex-shrink: 0;
}

.navbar-logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.navbar-logo-link:hover {
  transform: scale(1.02);
}

.navbar-logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-logo-img {
  height: auto;
  width: auto;
  max-height: 45px;
}

.navbar-logo-text {
  display: flex;
  flex-direction: column;
}

.navbar-logo-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1;
  letter-spacing: -0.02em;
}

.navbar-logo-subtitle {
  font-size: 0.75rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Desktop Navigation */
.navbar-nav-desktop {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-nav-item {
  position: relative;
}

.navbar-nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 1.5rem;
  text-decoration: none;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.navbar-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.05), rgba(247, 148, 29, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navbar-nav-link:hover::before {
  opacity: 1;
}

.navbar-nav-label {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

.navbar-nav-description {
  font-size: 0.75rem;
  color: #666;
  margin-top: 2px;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

.navbar-nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 80%;
  height: 3px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  border-radius: 2px;
  transition: transform 0.3s ease;
}

.navbar-nav-link:hover .navbar-nav-indicator,
.navbar-nav-active .navbar-nav-indicator {
  transform: translateX(-50%) scaleX(1);
}

.navbar-nav-active .navbar-nav-label {
  color: #0056a6;
}

.navbar-nav-active .navbar-nav-description {
  color: #f7941d;
}

/* Action Buttons */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Cart Button */
.navbar-cart-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: 2px solid rgba(0, 86, 166, 0.2);
  border-radius: 12px;
  color: #0056a6;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navbar-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 166, 0.1), transparent);
  transition: left 0.6s ease;
}

.navbar-cart-btn:hover::before {
  left: 100%;
}

.navbar-cart-btn:hover {
  border-color: #0056a6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.2);
}

.navbar-cart-icon-wrapper {
  position: relative;
}

.navbar-cart-icon {
  width: 20px;
  height: 20px;
}

.navbar-cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #f7941d;
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.navbar-cart-text {
  position: relative;
  z-index: 1;
}

/* CTA Button */
.navbar-cta-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.navbar-cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f7941d 0%, #e8850f 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navbar-cta-btn:hover::before {
  opacity: 1;
}

.navbar-cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 86, 166, 0.4);
}

.navbar-cta-btn span,
.navbar-cta-arrow {
  position: relative;
  z-index: 1;
}

.navbar-cta-arrow {
  transition: transform 0.3s ease;
}

.navbar-cta-btn:hover .navbar-cta-arrow {
  transform: translateX(4px);
}

/* Mobile Toggle */
.navbar-mobile-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: transparent;
  border: 2px solid rgba(0, 86, 166, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navbar-mobile-toggle:hover {
  border-color: #0056a6;
  background: rgba(0, 86, 166, 0.05);
}

.navbar-hamburger-icon {
  width: 24px;
  height: 24px;
  color: #0056a6;
  transition: transform 0.3s ease;
}

.navbar-mobile-toggle:hover .navbar-hamburger-icon {
  transform: scale(1.1);
}

/* Mobile Menu */
.navbar-mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.navbar-mobile-open {
  visibility: visible;
  opacity: 1;
}

.navbar-mobile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.navbar-mobile-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 400px;
  height: 100vh;
  background: white;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
  box-shadow: -10px 0 50px rgba(0, 86, 166, 0.2);
}

.navbar-mobile-open .navbar-mobile-panel {
  transform: translateX(0);
}

.navbar-mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0, 86, 166, 0.1);
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.05), rgba(247, 148, 29, 0.05));
}

.navbar-mobile-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 700;
  color: #0056a6;
  font-size: 1.25rem;
}

.navbar-mobile-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid rgba(0, 86, 166, 0.2);
  border-radius: 10px;
  color: #0056a6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navbar-mobile-close:hover {
  border-color: #0056a6;
  background: rgba(0, 86, 166, 0.05);
  transform: scale(1.05);
}

.navbar-mobile-nav {
  padding: 2rem 0;
}

.navbar-mobile-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 2rem;
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 86, 166, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navbar-mobile-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 166, 0.05), transparent);
  transition: left 0.6s ease;
}

.navbar-mobile-link:hover::before {
  left: 100%;
}

.navbar-mobile-link:hover {
  background: rgba(0, 86, 166, 0.02);
  padding-left: 2.5rem;
}

.navbar-mobile-link-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.navbar-mobile-link-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

.navbar-mobile-link-desc {
  font-size: 0.875rem;
  color: #666;
  transition: color 0.3s ease;
}

.navbar-mobile-link-arrow {
  width: 20px;
  height: 20px;
  color: #999;
  transform: rotate(-90deg);
  transition: all 0.3s ease;
}

.navbar-mobile-link:hover .navbar-mobile-link-arrow {
  color: #0056a6;
  transform: rotate(-90deg) translateX(4px);
}

.navbar-mobile-active {
  background: rgba(0, 86, 166, 0.05);
  border-left: 4px solid #0056a6;
}

.navbar-mobile-active .navbar-mobile-link-label {
  color: #0056a6;
}

.navbar-mobile-active .navbar-mobile-link-desc {
  color: #f7941d;
}

.navbar-mobile-footer {
  padding: 2rem;
  border-top: 1px solid rgba(0, 86, 166, 0.1);
  background: rgba(0, 86, 166, 0.02);
}

.navbar-mobile-contact h4 {
  font-size: 1rem;
  font-weight: 700;
  color: #0056a6;
  margin-bottom: 1rem;
}

.navbar-mobile-contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: #666;
  font-size: 0.875rem;
}

.navbar-mobile-contact-item svg {
  width: 18px;
  height: 18px;
  color: #0056a6;
  flex-shrink: 0;
}

.navbar-mobile-cta {
  display: block;
  width: 100%;
  padding: 1rem;
  margin-top: 1.5rem;
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  text-decoration: none;
  text-align: center;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navbar-mobile-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f7941d 0%, #e8850f 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navbar-mobile-cta:hover::before {
  opacity: 1;
}

.navbar-mobile-cta span {
  position: relative;
  z-index: 1;
}

.navbar-mobile-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .navbar-nav-desktop {
    gap: 0.25rem;
  }

  .navbar-nav-link {
    padding: 0.875rem 1.25rem;
  }

  .navbar-nav-label {
    font-size: 0.9rem;
  }

  .navbar-nav-description {
    font-size: 0.7rem;
  }
}

@media (max-width: 1024px) {
  .navbar-top-left {
    gap: 1.5rem;
  }

  .navbar-contact-item {
    font-size: 0.8rem;
  }

  .navbar-nav-desktop {
    display: none;
  }

  .navbar-mobile-toggle {
    display: flex;
  }

  .navbar-cta-btn {
    display: none;
  }

  .navbar-container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .navbar-top-bar {
    padding: 6px 0;
  }

  .navbar-top-container {
    padding: 0 1rem;
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }

  .navbar-top-left {
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
    order: 2;
  }

  .navbar-top-right {
    font-size: 0.75rem;
    order: 1;
    margin-bottom: 0.25rem;
  }

  .navbar-contact-item {
    font-size: 0.7rem;
  }

  .navbar-contact-item span {
    display: none;
  }

  .navbar-header {
    top: 32px;
  }

  .navbar-scrolled {
    top: 0;
  }

  .navbar-container {
    padding: 0 1rem;
    height: 60px;
  }

  .navbar-logo-container {
    gap: 0.5rem;
  }

  .navbar-logo-img {
    max-height: 35px;
  }

  .navbar-logo-title {
    font-size: 1.1rem;
  }

  .navbar-logo-subtitle {
    font-size: 0.65rem;
  }

  .navbar-cart-btn {
    padding: 0.5rem 0.75rem;
    gap: 0.25rem;
    font-size: 0.85rem;
  }

  .navbar-cart-text {
    display: none;
  }

  .navbar-cart-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .navbar-top-bar {
    padding: 6px 0;
  }

  .navbar-top-left {
    gap: 0.75rem;
  }

  .navbar-contact-item span {
    display: none;
  }

  .navbar-top-right {
    font-size: 0.75rem;
  }

  .navbar-container {
    height: 60px;
  }

  .navbar-logo-text {
    display: none;
  }

  .navbar-mobile-panel {
    max-width: 100%;
  }

  .main-content {
    padding-top: 92px !important;
  }
}

/* ===== PRODUCTOS DESTACADOS PROFESIONAL ===== */

/* ===== PREMIUM PROFESSIONAL CTA SECTION ===== */
.featured-products-premium-section {
  margin-top: 5rem;
  padding: 80px 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
  border-radius: 32px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 32px 80px rgba(0, 86, 166, 0.12),
    0 8px 32px rgba(0, 86, 166, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 86, 166, 0.08);
}

/* Premium Background Elements */
.premium-bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.premium-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.03), rgba(247, 148, 29, 0.03));
  animation: float 6s ease-in-out infinite;
}

.premium-circle-1 {
  width: 200px;
  height: 200px;
  top: -50px;
  right: -50px;
  animation-delay: 0s;
}

.premium-circle-2 {
  width: 150px;
  height: 150px;
  bottom: -30px;
  left: -30px;
  animation-delay: 2s;
}

.premium-circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.featured-cta-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

/* CTA Info Section */
.featured-cta-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.featured-cta-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #0056a6, #003d75);
  color: white;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  width: fit-content;
  box-shadow: 0 4px 15px rgba(0, 86, 166, 0.3);
}

.cta-badge-icon {
  font-size: 1rem;
}

.featured-cta-title {
  font-size: 1.75rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1.2;
  margin: 0;
}

.featured-cta-description {
  font-size: 1.125rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.cta-counter {
  color: #f7941d;
  font-weight: 700;
}

.featured-cta-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  color: #333;
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #0056a6, #003d75);
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 700;
  flex-shrink: 0;
}

/* CTA Actions Section */
.featured-cta-actions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.cta-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  width: 100%;
}

.cta-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 86, 166, 0.1);
  border: 1px solid rgba(0, 86, 166, 0.1);
  transition: all 0.3s ease;
}

.cta-stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 86, 166, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: #0056a6;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cta-buttons-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.featured-cta-primary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #0056a6 0%, #003d75 100%);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 86, 166, 0.3);
}

.featured-cta-primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f7941d 0%, #e8850f 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.featured-cta-primary-btn:hover::before {
  opacity: 1;
}

.featured-cta-primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 86, 166, 0.4);
}

.featured-cta-primary-btn span,
.btn-arrow {
  position: relative;
  z-index: 1;
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.featured-cta-primary-btn:hover .btn-arrow {
  transform: translateX(4px);
}

.featured-cta-secondary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  background: transparent;
  color: #0056a6;
  text-decoration: none;
  border: 2px solid #0056a6;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.featured-cta-secondary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 166, 0.1), transparent);
  transition: left 0.6s ease;
}

.featured-cta-secondary-btn:hover::before {
  left: 100%;
}

.featured-cta-secondary-btn:hover {
  background: #0056a6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.3);
}

.featured-cta-secondary-btn span {
  position: relative;
  z-index: 1;
}

/* Responsive Design for Professional CTA */
@media (max-width: 1024px) {
  .featured-cta-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .featured-cta-title {
    font-size: 1.5rem;
  }

  .cta-stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .featured-products-professional-cta {
    padding: 2rem 1.5rem;
    margin-top: 3rem;
  }

  .featured-cta-title {
    font-size: 1.375rem;
  }

  .featured-cta-description {
    font-size: 1rem;
  }

  .cta-stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .cta-stat-item {
    padding: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .cta-buttons-group {
    gap: 0.75rem;
  }

  .featured-cta-primary-btn,
  .featured-cta-secondary-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .featured-products-professional-cta {
    padding: 1.5rem 1rem;
    margin-top: 2rem;
  }

  .featured-cta-title {
    font-size: 1.25rem;
  }

  .featured-cta-features {
    gap: 0.5rem;
  }

  .cta-feature {
    font-size: 0.9rem;
  }
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 2rem;
  }

  .footer-columns {
    flex-direction: column;
    width: 100%;
    gap: 1.5rem;
  }

  .footer-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .footer-column-title {
    text-align: center;
  }

  .footer-column-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-contact-item {
    justify-content: flex-start;
    text-align: left;
    width: 100%;
    max-width: 300px;
  }

  .footer-links-list {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 992px) {
  .about-container,
  .contact-container {
    flex-direction: column;
  }

  .about-image,
  .contact-map {
    width: 100%;
    margin-top: 2rem;
  }

  .modern-title {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 992px) {
  .services-content-container {
    flex-direction: column;
  }

  .services-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem 0;
  }

  .service-nav-button {
    padding: 0.75rem 1.5rem;
    border-left: none;
    border-bottom: 3px solid transparent;
    text-align: center;
    display: inline-block;
    width: auto;
  }

  .service-nav-button.active {
    border-left: none;
    border-bottom: 3px solid var(--primary-color);
  }

  .services-sidebar {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .service-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .navbar-links {
    display: none;
  }

  .modern-slider-content,
  .modern-slider-container {
    height: 400px; /* Altura reducida para tablets */
  }

  .modern-slider-arrow {
    width: 50px;
    height: 50px;
  }

  .modern-slider-title {
    font-size: 2rem;
  }

  .modern-slider-description {
    font-size: 1.2rem;
  }

  .slider-cta-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .modern-title {
    font-size: 1.8rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .features-container {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr 1fr;
  }

  .about-section,
  .services-section,
  .why-us-section,
  .contact-section {
    padding: 2rem 1rem;
  }

  .about-container {
    flex-direction: column;
    gap: 2rem;
  }

  .services-header {
    height: 200px;
  }

  .services-header-content h1 {
    font-size: 2rem;
  }

  .services-header-content p {
    font-size: 1rem;
  }

  .service-content h2 {
    font-size: 1.5rem;
  }

  .service-subtitle {
    font-size: 1.2rem;
  }

  .service-description {
    font-size: 1rem;
  }

  .service-feature-item {
    font-size: 1rem;
  }

  .service-brands {
    justify-content: center;
  }

  .about-image {
    width: 100%;
  }
}

/* ===== MODERN SERVICES PAGE STYLES ===== */
.modern-services-page {
  margin-top: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* ===== SERVICES HERO SECTION ===== */
.services-hero {
  position: relative;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  padding: 2rem 1rem;
  overflow: hidden;
}

.services-hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.services-hero-title {
  font-size: clamp(1.5rem, 3vw, 2.2rem);
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.1;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.services-hero-description {
  font-size: 0.9rem;
  line-height: 1.5;
  max-width: 600px;
  margin: 0 auto 1.5rem;
  opacity: 0.9;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* Hero Badge */
.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 1rem;
}

.title-highlight {
  background: linear-gradient(135deg, #f7941d, #ff8c00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hero Stats */
.hero-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f7941d;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Hero Decoration */
.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(247, 148, 29, 0.1);
  animation: float 6s ease-in-out infinite;
}

.decoration-circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-circle-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-circle-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* ===== SERVICES NAVIGATION SECTION ===== */
.services-navigation-section {
  background: white;
  padding: 1.5rem 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.services-nav-container {
  max-width: 900px;
  margin: 0 auto;
}

.nav-title {
  text-align: center;
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.services-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.modern-service-nav-card {
  background: white;
  border: 2px solid #f1f5f9;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.modern-service-nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 86, 166, 0.05), rgba(247, 148, 29, 0.05));
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.modern-service-nav-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.15);
  border-color: #0056a6;
}

.modern-service-nav-card:hover::before {
  opacity: 1;
}

.modern-service-nav-card.active {
  border-color: #0056a6;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.25);
}

.nav-card-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.nav-card-title {
  font-size: 0.95rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  position: relative;
  z-index: 1;
}

.nav-card-description {
  font-size: 0.75rem;
  opacity: 0.8;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.nav-card-arrow {
  position: relative;
  z-index: 1;
  opacity: 0.7;
  transition: transform 0.3s ease;
}

.modern-service-nav-card:hover .nav-card-arrow {
  transform: translateX(5px);
}

/* ===== SERVICE DETAIL SECTION ===== */
.service-detail-section {
  background: #f8fafc;
  padding: 1.5rem 1rem;
}

.service-detail-container {
  max-width: 1000px;
  margin: 0 auto;
}

.service-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 1.5rem;
  align-items: start;
}

/* Service Image */
.service-image-container {
  position: sticky;
  top: 1rem;
}

.service-image-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.service-detail-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.service-image-wrapper:hover .service-detail-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem;
  transform: translateY(100%);
  transition: transform 0.4s ease;
}

.service-image-wrapper:hover .image-overlay {
  transform: translateY(0);
}

.overlay-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.overlay-icon {
  font-size: 2rem;
}

.overlay-text {
  font-size: 1.2rem;
  font-weight: 600;
}

/* Service Content */
.service-content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.service-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.service-category-badge {
  display: inline-block;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.service-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.service-subtitle {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

.service-description {
  margin-bottom: 1.5rem;
}

.service-description p {
  font-size: 0.8rem;
  line-height: 1.5;
  color: #374151;
}

/* Features Section */
.service-features-section {
  margin-bottom: 1.25rem;
}

.features-title {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.features-title svg {
  color: #10b981;
  width: 16px;
  height: 16px;
}

.features-grid {
  display: grid;
  gap: 0.4rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.4rem;
  padding: 0.4rem;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 2px solid #10b981;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: #f0fdf4;
  transform: translateX(2px);
}

.feature-icon {
  color: #10b981;
  flex-shrink: 0;
  margin-top: 1px;
}

.feature-icon svg {
  width: 12px;
  height: 12px;
}

.feature-text {
  font-size: 0.75rem;
  color: #374151;
  line-height: 1.3;
}

/* Benefits Section */
.service-benefits-section {
  margin-bottom: 1.25rem;
}

.benefits-title {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.benefits-title svg {
  color: #f7941d;
  width: 16px;
  height: 16px;
}

.benefits-list {
  display: grid;
  gap: 0.4rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 0.4rem;
  padding: 0.4rem;
  background: #fef7ed;
  border-radius: 6px;
  border-left: 2px solid #f7941d;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  background: #fed7aa;
  transform: translateX(2px);
}

.benefit-icon {
  color: #f7941d;
  flex-shrink: 0;
  margin-top: 1px;
}

.benefit-icon svg {
  width: 12px;
  height: 12px;
}

.benefit-text {
  font-size: 0.75rem;
  color: #374151;
  line-height: 1.3;
}

/* Technologies Section */
.service-technologies-section {
  margin-bottom: 1.25rem;
}

.technologies-title {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.technologies-title svg {
  color: #0056a6;
  width: 16px;
  height: 16px;
}

.technologies-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
}

.technology-tag {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.technology-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.3);
}

/* Brands Section */
.service-brands-section {
  margin-bottom: 1.25rem;
}

.brands-title {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.brands-title svg {
  color: #f7941d;
  width: 16px;
  height: 16px;
}

.brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.75rem;
}

.brand-logo-container {
  background: white;
  border: 1px solid #f1f5f9;
  border-radius: 8px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-height: 50px;
}

.brand-logo-container:hover {
  border-color: #0056a6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 86, 166, 0.1);
}

.brand-logo-image {
  max-width: 100%;
  max-height: 40px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.brand-logo-container:hover .brand-logo-image {
  filter: grayscale(0%);
}

/* CTA Section */
.service-cta {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.service-cta-button {
  background: linear-gradient(135deg, #f7941d, #ff8c00);
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.service-cta-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.service-cta-button:hover:before {
  left: 100%;
}

.service-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(247, 148, 29, 0.4);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .service-detail-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .service-image-container {
    position: static;
  }

  .services-nav-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .modern-services-page {
    margin-top: 0;
  }

  .services-hero {
    padding: 3rem 1rem;
  }

  .services-hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .services-navigation-section {
    padding: 3rem 1rem;
  }

  .nav-title {
    font-size: 2rem;
  }

  .services-nav-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-detail-section {
    padding: 3rem 1rem;
  }

  .service-content-wrapper {
    padding: 2rem;
  }

  .service-title {
    font-size: 2rem;
  }

  .features-title,
  .benefits-title,
  .technologies-title,
  .brands-title {
    font-size: 1.25rem;
  }

  .brands-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .services-hero {
    padding: 2rem 1rem;
  }

  .services-hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .services-navigation-section {
    padding: 2rem 1rem;
  }

  .nav-title {
    font-size: 1.75rem;
  }

  .modern-service-nav-card {
    padding: 1.5rem;
    min-height: 150px;
  }

  .nav-card-icon {
    font-size: 2.5rem;
  }

  .nav-card-title {
    font-size: 1.1rem;
  }

  .service-detail-section {
    padding: 2rem 1rem;
  }

  .service-content-wrapper {
    padding: 1.5rem;
  }

  .service-title {
    font-size: 1.75rem;
  }

  .technologies-tags {
    justify-content: center;
  }

  .brands-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .modern-slider-content,
  .modern-slider-container {
    height: 300px; /* Altura reducida para móviles */
  }

  .modern-slider-arrow {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .modern-slider-arrow-left {
    left: 15px;
  }

  .modern-slider-arrow-right {
    right: 15px;
  }

  .modern-slider-title {
    font-size: 1.5rem;
  }

  .modern-slider-description {
    font-size: 1rem;
  }

  .slider-cta-button {
    padding: 8px 16px;
    font-size: 0.8rem;
  }

  .modern-title {
    font-size: 1.5rem;
  }

  .about-text,
  .contact-text,
  .services-intro {
    font-size: 1rem;
  }

  .modern-service-image {
    height: 200px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-box {
    padding: 20px;
  }

  .feature-box-title {
    font-size: 1.1rem;
  }

  .feature-box-text {
    font-size: 0.9rem;
  }
}

/* Estilos para el spinner de carga */
.loading-spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.loading-spinner-overlay.visible {
  opacity: 1;
}

.loading-spinner-overlay.hidden {
  opacity: 0;
}

.loading-spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(0.8);
  transition: transform 0.3s ease-in-out;
}

.loading-spinner-overlay.visible .loading-spinner-container {
  transform: scale(1);
}

.loading-spinner-logo {
  position: relative;
  z-index: 10;
  animation: pulse 1.5s infinite ease-in-out;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.2));
}

.loading-spinner-gear {
  position: absolute;
  border-radius: 50%;
  border: 2px dashed;
  animation: rotate 3s infinite linear;
}

.loading-spinner-gear.gear-blue {
  width: 80px;
  height: 80px;
  border-color: var(--primary-color);
  opacity: 0.6;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Estilos para la transición de página */
.page-content {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
  min-height: 100vh;
}

.page-transitioning {
  opacity: 0;
  transform: translateY(10px);
  pointer-events: none;
}

.page-transition-complete {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* ===== MODERN PROJECTS PAGE STYLES ===== */
.modern-projects-page {
  margin-top: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* ===== PROJECTS HERO SECTION ===== */
.projects-hero {
  position: relative;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  padding: 2rem 1rem;
  overflow: hidden;
}

.projects-hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.projects-hero-title {
  font-size: clamp(1.5rem, 3vw, 2.2rem);
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.1;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.projects-hero-description {
  font-size: 0.9rem;
  line-height: 1.5;
  max-width: 600px;
  margin: 0 auto 1.5rem;
  opacity: 0.9;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* Sección de introducción */
.projects-intro {
  padding: 3rem 0;
  background-color: #f8f9fa;
}

.projects-intro-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.projects-intro-content {
  flex: 1;
  min-width: 300px;
}

.projects-intro-content h2 {
  font-size: 1.8rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 15px;
}

.projects-intro-content h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background-color: var(--accent-color);
}

.projects-intro-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1rem;
}

.projects-intro-stats {
  flex: 1;
  min-width: 300px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
}

/* Filtro de categorías */
.projects-filter {
  padding: 2rem 0;
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.filter-container h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.filter-button {
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-button:hover {
  background-color: #e0e0e0;
}

.filter-button.active {
  background-color: var(--primary-color);
  color: white;
}

/* Lista de proyectos */
.projects-list {
  padding: 2rem 0 4rem;
}

.projects-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.project-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-category {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--accent-color);
  color: white;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.project-content {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.project-industry {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 1rem;
}

.project-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
  margin-bottom: 1.5rem;
}

.project-details {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.project-detail-item {
  margin-bottom: 1rem;
}

.project-detail-item h4 {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.3rem;
}

.project-detail-item p {
  font-size: 0.85rem;
  line-height: 1.5;
  color: #666;
}

/* Sección CTA */
.projects-cta {
  background-color: #f8f9fa;
  padding: 4rem 0;
}

.cta-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.cta-content {
  background-color: white;
  padding: 3rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.cta-content h2 {
  font-size: 1.8rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1rem;
  color: #555;
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.cta-benefits {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  text-align: left;
}

.cta-benefit {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.benefit-icon {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.cta-benefit span {
  font-size: 0.9rem;
  color: #555;
  line-height: 1.5;
}

.cta-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.cta-button {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-button.primary {
  background-color: var(--accent-color);
  color: white;
}

.cta-button.primary:hover {
  background-color: #0056a6;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.cta-button.secondary:hover {
  background-color: rgba(0, 86, 166, 0.05);
  transform: translateY(-2px);
}

.button-icon {
  font-size: 0.9rem;
}

/* Responsive para proyectos */
@media (max-width: 768px) {
  .projects-header {
    height: 200px;
  }

  .projects-header h1 {
    font-size: 1.8rem;
  }

  .projects-intro-container {
    flex-direction: column;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .cta-content {
    padding: 2rem;
  }

  .cta-benefits {
    grid-template-columns: 1fr;
  }
}

/* ===== MODERN CONTACT PAGE STYLES ===== */
.modern-contact-page {
  margin-top: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* ===== CONTACT HERO SECTION ===== */
.contact-hero {
  position: relative;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.contact-hero-content {
  max-width: 800px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.badge-icon {
  font-size: 1.1rem;
}

.contact-hero-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.title-highlight {
  color: #f7941d;
  position: relative;
}

.contact-hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #f7941d;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(247, 148, 29, 0.1);
  backdrop-filter: blur(20px);
}

.decoration-circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
}

.decoration-circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  background: rgba(255, 255, 255, 0.05);
}

.decoration-circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  background: rgba(247, 148, 29, 0.05);
}

/* ===== CONTACT OPTIONS SECTION ===== */
.contact-options-section {
  padding: 4rem 2rem;
  background: white;
}

.contact-options-container {
  max-width: 1200px;
  margin: 0 auto;
}

.options-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.contact-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.modern-contact-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-contact-option:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.1);
  border-color: var(--option-color);
}

.modern-contact-option.active {
  border-color: var(--option-color);
  background: linear-gradient(135deg, var(--option-color), rgba(var(--option-color), 0.8));
  color: white;
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.2);
}

.option-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.modern-contact-option.active .option-icon {
  filter: brightness(0) invert(1);
}

.option-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: inherit;
}

.option-description {
  font-size: 0.95rem;
  opacity: 0.8;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.option-arrow {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.modern-contact-option:hover .option-arrow,
.modern-contact-option.active .option-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* ===== CONTACT DETAILS SECTION ===== */
.contact-details-section {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.contact-details-container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.contact-info-modern {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 86, 166, 0.1);
  border: 1px solid rgba(0, 86, 166, 0.1);
}

.contact-card-header-modern {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.contact-icon-modern {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.contact-header-text {
  flex: 1;
}

.contact-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.contact-card-subtitle {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.contact-method:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-2px);
}

.method-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.method-icon.whatsapp {
  background: linear-gradient(135deg, #25d366, #128c7e);
}

.method-content {
  flex: 1;
}

.method-content h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.method-content p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.contact-link-modern {
  color: #0056a6;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.contact-link-modern:hover {
  color: #003366;
  text-decoration: underline;
}

.contact-person {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  border: 1px solid #cbd5e1;
  margin-bottom: 1rem;
}

.person-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #0056a6, #003366);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.person-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.person-info p {
  font-size: 0.85rem;
  color: #6b7280;
  margin: 0;
}

.whatsapp-btn-modern {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.whatsapp-btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 211, 102, 0.3);
}

.phone-number {
  font-size: 0.8rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

/* ===== CONTACT MAP SECTION ===== */
.contact-map-modern {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 86, 166, 0.1);
  border: 1px solid rgba(0, 86, 166, 0.1);
  height: fit-content;
}

.map-header {
  margin-bottom: 1.5rem;
}

.map-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.map-header p {
  font-size: 0.95rem;
  color: #6b7280;
  margin: 0;
}

.map-container-modern {
  border-radius: 16px;
  overflow: hidden;
  height: 400px;
  box-shadow: 0 8px 25px rgba(0, 86, 166, 0.1);
  border: 1px solid #e5e7eb;
}

/* ===== RESPONSIVE DESIGN FOR CONTACT ===== */
@media (max-width: 768px) {
  .contact-hero {
    padding: 3rem 1rem;
    min-height: 50vh;
  }

  .contact-hero-title {
    font-size: 2.5rem;
  }

  .contact-hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .options-title {
    font-size: 2rem;
  }

  .contact-options-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modern-contact-option {
    padding: 1.5rem;
  }

  .option-icon {
    font-size: 2.5rem;
  }

  .contact-details-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-info-modern,
  .contact-map-modern {
    padding: 2rem;
  }

  .map-container-modern {
    height: 300px;
  }

  .contact-options-section,
  .contact-details-section {
    padding: 3rem 1rem;
  }

  .decoration-circle-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -100px;
  }

  .decoration-circle-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -75px;
  }

  .decoration-circle-3 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .contact-hero {
    padding: 2rem 1rem;
  }

  .contact-hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .contact-options-section,
  .contact-details-section {
    padding: 2rem 1rem;
  }

  .contact-info-modern,
  .contact-map-modern {
    padding: 1.5rem;
  }

  .contact-card-header-modern {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .contact-icon-modern {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .contact-card-title {
    font-size: 1.25rem;
  }

  .contact-method {
    padding: 0.75rem;
  }

  .method-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .person-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .contact-person {
    padding: 1rem;
  }
}

/* ===== PROFESSIONAL NAVBAR STYLES ===== */
.professional-navbar-top {
  background: #0056a6;
  color: white;
  padding: 6px 0;
  font-size: 0.8rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.professional-navbar-top-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.professional-contact-info {
  display: flex;
  gap: 1.5rem;
}

.professional-contact-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.professional-contact-icon {
  font-size: 0.8rem;
  opacity: 0.9;
}

.professional-tagline {
  font-weight: 500;
  font-size: 0.8rem;
}

.professional-navbar-main {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 86, 166, 0.1);
  position: fixed;
  top: 32px;
  left: 0;
  right: 0;
  z-index: 1000;
}

.professional-navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.professional-navbar-brand {
  flex-shrink: 0;
}

.professional-logo-link {
  display: block;
  transition: opacity 0.3s ease;
}

.professional-logo-link:hover {
  opacity: 0.8;
}

.professional-logo-img {
  height: 35px;
  width: auto;
}

.professional-navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.professional-nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.professional-nav-link:hover {
  color: #0056a6;
  background: rgba(0, 86, 166, 0.05);
}

.professional-nav-active {
  color: #0056a6;
  background: rgba(0, 86, 166, 0.1);
}

.professional-navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.professional-cart-btn {
  position: relative;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
}

.professional-cart-btn:hover {
  border-color: #0056a6;
  color: #0056a6;
  background: rgba(0, 86, 166, 0.05);
}

.professional-cart-icon {
  font-size: 1.2rem;
}

.professional-cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #f7941d;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.professional-cta-btn {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.professional-cta-btn:hover {
  background: linear-gradient(135deg, #003366, #001a33);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.3);
}

.professional-mobile-toggle {
  display: none;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 0.5rem;
  cursor: pointer;
  color: #333;
  transition: all 0.3s ease;
}

.professional-mobile-toggle:hover {
  border-color: #0056a6;
  color: #0056a6;
}

.professional-mobile-icon {
  font-size: 1.25rem;
}

/* ===== PROFESSIONAL MOBILE MENU ===== */
.professional-mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.professional-mobile-open {
  visibility: visible;
  opacity: 1;
}

.professional-mobile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.professional-mobile-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 350px;
  height: 100vh;
  background: white;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  box-shadow: -10px 0 30px rgba(0, 86, 166, 0.2);
}

.professional-mobile-open .professional-mobile-panel {
  transform: translateX(0);
}

.professional-mobile-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  font-weight: 600;
  color: #0056a6;
}

.professional-mobile-nav {
  padding: 1rem 0;
}

.professional-mobile-link {
  display: block;
  padding: 1rem 1.5rem;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.professional-mobile-link:hover {
  background: #f8fafc;
  color: #0056a6;
}

.professional-mobile-active {
  background: rgba(0, 86, 166, 0.1);
  color: #0056a6;
  border-left: 4px solid #0056a6;
}

.professional-mobile-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.professional-mobile-contact {
  margin-bottom: 1.5rem;
}

.professional-mobile-contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  color: #666;
}

.professional-mobile-cta {
  display: block;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  text-decoration: none;
  padding: 0.875rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.professional-mobile-cta:hover {
  background: linear-gradient(135deg, #003366, #001a33);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE DESIGN FOR PROFESSIONAL NAVBAR ===== */
@media (max-width: 1024px) {
  .professional-navbar-nav {
    display: none;
  }

  .professional-mobile-toggle {
    display: block;
  }

  .professional-cta-btn {
    display: none;
  }
}

@media (max-width: 768px) {
  .professional-navbar-top {
    padding: 4px 0;
  }

  .professional-navbar-top-container {
    padding: 0 1rem;
  }

  .professional-contact-info {
    gap: 0.75rem;
  }

  .professional-contact-item span {
    display: none;
  }

  .professional-tagline {
    font-size: 0.7rem;
  }

  .professional-navbar-main {
    top: 28px;
  }

  .professional-navbar-container {
    padding: 0 1rem;
    height: 50px;
  }

  .professional-logo-img {
    height: 30px;
    width: auto;
  }
}

@media (max-width: 480px) {
  .professional-navbar-top {
    padding: 3px 0;
  }

  .professional-contact-info {
    gap: 0.5rem;
  }

  .professional-tagline {
    display: none;
  }

  .professional-navbar-main {
    top: 24px;
  }

  .professional-navbar-container {
    height: 45px;
  }

  .professional-logo-img {
    height: 28px;
  }

  .professional-mobile-panel {
    max-width: 100%;
  }
}

/* Encabezado de contacto */
.contact-header {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/images/defaults/Automatización-Industrial.jpg');
  background-size: cover;
  background-position: center;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  margin-bottom: 3rem;
}

.contact-header-content {
  max-width: 800px;
  padding: 0 20px;
}

.contact-header h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Contenido principal */
.contact-main {
  padding-bottom: 4rem;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Pestañas de navegación */
.contact-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.contact-tab {
  padding: 1rem 1.5rem;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-tab:hover {
  background-color: #e0e0e0;
}

.contact-tab.active {
  background-color: var(--primary-color);
  color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

/* Contenido de contacto */
.contact-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 992px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-info-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  height: fit-content;
}

.contact-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.contact-icon-large {
  font-size: 2.5rem;
  color: var(--primary-color);
}

.contact-card-header h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin: 0;
}

.contact-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
}

.contact-details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .contact-details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.contact-detail-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.contact-detail-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-top: 0.2rem;
}

.contact-detail-icon.whatsapp {
  color: #25D366;
}

.contact-detail-item h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: #333;
}

.contact-detail-item p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

.whatsapp-contact-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #25D366;
  color: white;
  border: none;
  padding: 0.6rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.whatsapp-contact-button:hover {
  background-color: #128C7E;
  transform: translateY(-2px);
}

.contact-phone-number {
  font-size: 0.85rem;
  color: #777;
}

/* Mapa */
.contact-map-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  height: fit-content;
}

.contact-map-container h2 {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.contact-map {
  border-radius: 8px;
  overflow: hidden;
  height: 450px;
}

/* Responsive */
@media (max-width: 768px) {
  .contact-header {
    height: 200px;
  }

  .contact-header h1 {
    font-size: 1.8rem;
  }

  .contact-tab {
    padding: 0.8rem 1rem;
    font-size: 0.85rem;
  }

  .contact-info-card,
  .contact-map-container {
    padding: 1.5rem;
  }

  .contact-map {
    height: 300px;
  }
}

/* Botón flotante de WhatsApp */
.whatsapp-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: auto;
  height: auto;
  background-color: #25D366;
  color: white;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  transition: all 0.3s ease;
  text-decoration: none;
}

.whatsapp-button:hover {
  background-color: #128C7E;
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.whatsapp-text {
  margin-left: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .whatsapp-button {
    padding: 10px;
    border-radius: 50%;
  }

  .whatsapp-text {
    display: none;
  }
}



/* ===== PROJECTS FILTER SECTION ===== */
.projects-filter-section {
  background: white;
  padding: 1.5rem 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.filter-container {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
}

.filter-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.filter-buttons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 0.75rem;
}

.modern-filter-button {
  background: white;
  border: 2px solid #f1f5f9;
  border-radius: 12px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-height: 70px;
  justify-content: center;
}

.modern-filter-button:hover {
  border-color: #0056a6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.1);
}

.modern-filter-button.active {
  border-color: #0056a6;
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 86, 166, 0.2);
}

.filter-icon {
  font-size: 1.2rem;
}

.filter-text {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

/* ===== PROJECTS SHOWCASE ===== */
.projects-showcase {
  background: #f8fafc;
  padding: 2rem 1rem;
}

.projects-container {
  max-width: 1000px;
  margin: 0 auto;
}

.projects-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.modern-project-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
}

.modern-project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.project-image-container {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.project-image-modern {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.modern-project-card:hover .project-image-modern {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 86, 166, 0.8), rgba(247, 148, 29, 0.8));
  opacity: 0;
  transition: opacity 0.4s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
}

.modern-project-card:hover .project-overlay {
  opacity: 1;
}

.project-category-badge {
  background: rgba(255, 255, 255, 0.9);
  color: #0056a6;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
}

.project-year-badge {
  background: rgba(247, 148, 29, 0.9);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
}

.project-content-modern {
  padding: 1.25rem;
}

.project-header {
  margin-bottom: 1rem;
}

.project-title-modern {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.project-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.meta-icon {
  color: #0056a6;
}

.project-description-modern {
  font-size: 0.8rem;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 1rem;
}

.project-technologies {
  margin-bottom: 1rem;
}

.tech-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.tech-tag {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.65rem;
  font-weight: 500;
}

.project-benefits {
  margin-bottom: 1rem;
}

.benefits-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.benefits-list {
  display: grid;
  gap: 0.3rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
}

.benefit-icon {
  color: #10b981;
  font-size: 0.7rem;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 0.7rem;
  color: #374151;
  line-height: 1.3;
}

.project-actions {
  text-align: center;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.project-details-btn {
  background: linear-gradient(135deg, #f7941d, #ff8c00);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}

.project-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(247, 148, 29, 0.3);
}

.btn-icon {
  font-size: 0.7rem;
}

/* ===== PROJECTS CTA MODERN ===== */
.projects-cta-modern {
  background: linear-gradient(135deg, #0056a6 0%, #003366 100%);
  color: white;
  padding: 2.5rem 1rem;
}

.cta-container-modern {
  max-width: 900px;
  margin: 0 auto;
}

.cta-content-modern {
  text-align: center;
}

.cta-header {
  margin-bottom: 2rem;
}

.cta-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.cta-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.cta-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: left;
}

.feature-icon {
  font-size: 2rem;
  background: rgba(247, 148, 29, 0.2);
  padding: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.feature-content h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
}

.feature-content p {
  font-size: 0.85rem;
  opacity: 0.8;
  line-height: 1.4;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button-primary {
  background: linear-gradient(135deg, #f7941d, #ff8c00);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.cta-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(247, 148, 29, 0.4);
}

.cta-button-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.cta-button-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* ===== RESPONSIVE DESIGN FOR PROJECTS ===== */
@media (max-width: 768px) {
  .modern-projects-page {
    margin-top: 0;
  }

  .projects-hero {
    padding: 2rem 1rem;
  }

  .projects-hero-title {
    font-size: 1.8rem;
  }

  .filter-buttons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .modern-filter-button {
    min-height: 60px;
    padding: 0.5rem;
  }

  .filter-icon {
    font-size: 1rem;
  }

  .filter-text {
    font-size: 0.7rem;
  }

  .projects-grid-modern {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project-image-container {
    height: 150px;
  }

  .project-content-modern {
    padding: 1rem;
  }

  .cta-features {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .cta-feature {
    flex-direction: column;
    text-align: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-button-primary,
  .cta-button-secondary {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .projects-hero-title {
    font-size: 1.5rem;
  }

  .filter-buttons-grid {
    grid-template-columns: 1fr;
  }

  .modern-filter-button {
    min-height: 50px;
  }

  .cta-title {
    font-size: 1.5rem;
  }

  .feature-icon {
    font-size: 1.5rem;
    padding: 0.5rem;
  }
}

/* Estilos responsivos generales para dispositivos móviles */
@media (max-width: 768px) {
  body {
    width: 100%;
    overflow-x: hidden;
  }

  html, body {
    max-width: 100%;
    overflow-x: hidden;
  }

  .container,
  .section,
  .navbar-wrapper,
  .navbar,
  .navbar-container,
  .footer-section,
  .footer-container,
  .modern-slider-container,
  .modern-slider,
  .modern-slider-content,
  .products-page,
  .products-grid,
  .product-card {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
  }

  main {
    width: 100% !important;
    overflow-x: hidden !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  img {
    max-width: 100%;
    height: auto;
  }
}

/* ===== ESTILOS PARA PÁGINA DE SERVICIOS MODERNA ===== */

.modern-services-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Hero Section */
.services-hero {
  position: relative;
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #0056a6 0%, #003366 50%, #f7941d 100%);
  overflow: hidden;
  color: white;
}

.services-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 12px 24px;
  border-radius: 50px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
  font-size: 16px;
}

.badge-icon {
  font-size: 20px;
}

.services-hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 30px;
  line-height: 1.1;
}

.title-highlight {
  background: linear-gradient(45deg, #f7941d, #ffb84d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.services-hero-description {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 50px;
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 50px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 800;
  color: #f7941d;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Decoraciones del hero */
.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.decoration-circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation: float 6s ease-in-out infinite;
}

.decoration-circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation: float 8s ease-in-out infinite reverse;
}

.decoration-circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 10%;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Services Navigation Section */
.services-navigation-section {
  padding: 80px 0;
  background: white;
}

.services-nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.nav-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 60px;
}

.services-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.modern-service-nav-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 30px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-align: left;
}
.modern-service-nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #0056a6, #f7941d);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.modern-service-nav-card:hover::before,
.modern-service-nav-card.active::before {
  transform: scaleX(1);
}

.modern-service-nav-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.15);
  border-color: #0056a6;
}

.modern-service-nav-card.active {
  background: linear-gradient(135deg, #0056a6, #003366);
  color: white;
  border-color: #0056a6;
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 86, 166, 0.25);
}

.nav-card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.modern-service-nav-card.active .nav-card-icon {
  filter: brightness(1.2);
}

.nav-card-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: inherit;
}

.nav-card-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.8;
  margin-bottom: 20px;
}

.nav-card-arrow {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(0, 86, 166, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modern-service-nav-card:hover .nav-card-arrow,
.modern-service-nav-card.active .nav-card-arrow {
  background: rgba(247, 148, 29, 0.2);
  transform: scale(1.1);
}

/* Service Detail Section */
.service-detail-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.service-detail-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.service-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.service-image-container {
  position: relative;
}

.service-image-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-detail-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-image-wrapper:hover .service-detail-image {
  transform: scale(1.05);
}

.service-image-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

.service-badge {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  border-radius: 50px;
  font-weight: 600;
  color: #1a202c;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.service-badge-icon {
  font-size: 1.2rem;
}

.service-content-container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.service-content-header {
  margin-bottom: 30px;
}

.service-detail-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 15px;
}

.service-detail-subtitle {
  font-size: 1.2rem;
  color: #0056a6;
  font-weight: 600;
}

.service-description {
  margin-bottom: 40px;
}

.service-description p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
}

/* Features, Benefits, Applications */
.service-features,
.service-benefits,
.service-applications {
  margin-bottom: 40px;
}

.features-title,
.benefits-title,
.applications-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.features-title::before {
  content: '⚙️';
  font-size: 1.2rem;
}

.benefits-title::before {
  content: '⭐';
  font-size: 1.2rem;
}

.applications-title::before {
  content: '🏭';
  font-size: 1.2rem;
}

.features-grid,
.benefits-grid,
.applications-grid {
  display: grid;
  gap: 15px;
}

.feature-item,
.benefit-item,
.application-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-item:hover,
.benefit-item:hover,
.application-item:hover {
  background: #e2e8f0;
  transform: translateX(5px);
}

.feature-icon,
.benefit-icon,
.application-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.feature-text,
.benefit-text,
.application-text {
  font-size: 1rem;
  color: #4a5568;
  font-weight: 500;
}

/* CTA Button */
.service-cta {
  margin-top: 40px;
}

.service-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #0056a6, #f7941d);
  color: white;
  border: none;
  padding: 18px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 86, 166, 0.3);
}

.service-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 86, 166, 0.4);
}

/* Services CTA Section */
.services-cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: white;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 40px;
  text-align: center;
}

.cta-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-icon {
  margin-bottom: 30px;
  color: #f7941d;
}

.cta-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 40px;
}

.cta-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-primary-btn,
.cta-secondary-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 18px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
}

.cta-primary-btn {
  background: linear-gradient(135deg, #f7941d, #0056a6);
  color: white;
  box-shadow: 0 4px 20px rgba(247, 148, 29, 0.3);
}

.cta-primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(247, 148, 29, 0.4);
}

.cta-secondary-btn {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .service-detail-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .services-nav-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .services-hero {
    padding: 100px 0 60px;
  }

  .services-hero-content {
    padding: 0 20px;
  }

  .services-hero-title {
    font-size: 2.5rem;
  }

  .services-hero-description {
    font-size: 1.1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 30px;
  }

  .services-nav-container,
  .service-detail-container,
  .cta-container {
    padding: 0 20px;
  }

  .services-nav-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .modern-service-nav-card {
    padding: 25px;
  }

  .service-content-container {
    padding: 30px 20px;
  }

  .service-detail-title {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary-btn,
  .cta-secondary-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .services-hero-title {
    font-size: 2rem;
  }

  .nav-title {
    font-size: 2rem;
  }

  .service-detail-title {
    font-size: 1.8rem;
  }

  .cta-title {
    font-size: 1.8rem;
  }

  .cta-content {
    padding: 40px 20px;
  }
}

/* ===== FIN DE ESTILOS PARA PÁGINA DE SERVICIOS MODERNA ===== */