/**
 * <PERSON>ript para inlinear recursos JavaScript y CSS directamente en el HTML
 * Esta es una solución radical para problemas de MIME type en servidores restrictivos
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  fg: {
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m',
  }
};

// Función para imprimir mensajes con formato
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  
  switch (type) {
    case 'info':
      console.log(`${colors.fg.blue}[${timestamp}] INFO:${colors.reset} ${message}`);
      break;
    case 'success':
      console.log(`${colors.fg.green}[${timestamp}] ÉXITO:${colors.reset} ${message}`);
      break;
    case 'warning':
      console.log(`${colors.fg.yellow}[${timestamp}] ADVERTENCIA:${colors.reset} ${message}`);
      break;
    case 'error':
      console.log(`${colors.fg.red}[${timestamp}] ERROR:${colors.reset} ${message}`);
      break;
  }
}

// Función para encontrar todos los archivos HTML en un directorio
function findHtmlFiles(dir) {
  const htmlFiles = [];
  
  function searchDir(currentDir) {
    const files = fs.readdirSync(currentDir);
    
    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        searchDir(filePath);
      } else if (file.endsWith('.html')) {
        htmlFiles.push(filePath);
      }
    }
  }
  
  searchDir(dir);
  return htmlFiles;
}

// Función para leer un archivo
async function readFile(filePath) {
  try {
    return await readFileAsync(filePath, 'utf8');
  } catch (error) {
    log(`Error al leer el archivo ${filePath}: ${error.message}`, 'error');
    return null;
  }
}

// Función para inlinear scripts
async function inlineScripts(htmlContent, basePath) {
  // Buscar todos los scripts externos
  const scriptRegex = /<script[^>]+src=["']([^"']+)["'][^>]*><\/script>/g;
  let match;
  let modifiedContent = htmlContent;
  let scriptCount = 0;
  
  while ((match = scriptRegex.exec(htmlContent)) !== null) {
    const fullTag = match[0];
    const scriptSrc = match[1];
    
    // Ignorar scripts externos (CDN, etc.)
    if (scriptSrc.startsWith('http') || scriptSrc.startsWith('//')) {
      continue;
    }
    
    // Construir la ruta completa al archivo JavaScript
    const scriptPath = path.join(basePath, scriptSrc.startsWith('/') ? scriptSrc.slice(1) : scriptSrc);
    
    try {
      // Leer el contenido del script
      const scriptContent = await readFile(scriptPath);
      
      if (scriptContent) {
        // Reemplazar la etiqueta script con el contenido inlineado
        const inlineScript = `<script>${scriptContent}</script>`;
        modifiedContent = modifiedContent.replace(fullTag, inlineScript);
        scriptCount++;
      }
    } catch (error) {
      log(`No se pudo inlinear el script ${scriptSrc}: ${error.message}`, 'warning');
    }
  }
  
  log(`Se han inlineado ${scriptCount} scripts`, 'success');
  return modifiedContent;
}

// Función para inlinear estilos CSS
async function inlineStyles(htmlContent, basePath) {
  // Buscar todos los enlaces a hojas de estilo
  const linkRegex = /<link[^>]+rel=["']stylesheet["'][^>]+href=["']([^"']+)["'][^>]*>/g;
  let match;
  let modifiedContent = htmlContent;
  let styleCount = 0;
  
  while ((match = linkRegex.exec(htmlContent)) !== null) {
    const fullTag = match[0];
    const styleSrc = match[1];
    
    // Ignorar estilos externos (CDN, etc.)
    if (styleSrc.startsWith('http') || styleSrc.startsWith('//')) {
      continue;
    }
    
    // Construir la ruta completa al archivo CSS
    const stylePath = path.join(basePath, styleSrc.startsWith('/') ? styleSrc.slice(1) : styleSrc);
    
    try {
      // Leer el contenido del CSS
      const styleContent = await readFile(stylePath);
      
      if (styleContent) {
        // Reemplazar el enlace con el contenido inlineado
        const inlineStyle = `<style>${styleContent}</style>`;
        modifiedContent = modifiedContent.replace(fullTag, inlineStyle);
        styleCount++;
      }
    } catch (error) {
      log(`No se pudo inlinear el estilo ${styleSrc}: ${error.message}`, 'warning');
    }
  }
  
  log(`Se han inlineado ${styleCount} hojas de estilo`, 'success');
  return modifiedContent;
}

// Función principal
async function inlineResources(outputDir) {
  log(`Iniciando proceso de inlineado de recursos en ${outputDir}...`, 'info');
  
  // Encontrar todos los archivos HTML
  const htmlFiles = findHtmlFiles(outputDir);
  log(`Se encontraron ${htmlFiles.length} archivos HTML`, 'info');
  
  // Procesar cada archivo HTML
  for (const htmlFile of htmlFiles) {
    log(`Procesando ${htmlFile}...`, 'info');
    
    // Leer el contenido del archivo HTML
    const htmlContent = await readFile(htmlFile);
    
    if (htmlContent) {
      // Inlinear scripts y estilos
      let modifiedContent = await inlineScripts(htmlContent, outputDir);
      modifiedContent = await inlineStyles(modifiedContent, outputDir);
      
      // Guardar el archivo modificado
      try {
        await writeFileAsync(htmlFile, modifiedContent, 'utf8');
        log(`Archivo ${htmlFile} modificado correctamente`, 'success');
      } catch (error) {
        log(`Error al guardar el archivo ${htmlFile}: ${error.message}`, 'error');
      }
    }
  }
  
  log('Proceso de inlineado de recursos completado', 'success');
}

// Exportar la función principal
module.exports = inlineResources;
