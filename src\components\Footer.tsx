'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { MAIN_ROUTES, CONTACT_INFO, COMPANY_INFO, IMAGES } from '@/constants';

/**
 * Modern Footer component with clean design and updated contact information
 */
export default function Footer() {
  const pathname = usePathname();

  // Hide footer on admin pages
  if (pathname?.startsWith('/eccsa/admin')) {
    return null;
  }
  // Navigation links
  const navigationLinks = [
    { href: MAIN_ROUTES.HOME, label: 'Inicio' },
    { href: MAIN_ROUTES.SERVICES, label: 'Servicios' },
    { href: MAIN_ROUTES.PRODUCTS, label: 'Productos' },
    { href: MAIN_ROUTES.PROJECTS, label: 'Proyectos' },
    { href: '/contacto', label: 'Contacto' },
  ];

  // Services offered
  const services = [
    'Automatización Industrial',
    'Programación PLC',
    'Sistemas SCADA',
    'Instalaciones Eléctricas',
    'Mantenimiento Industrial',
    'Consultoría Técnica'
  ];

  // Contact specialists
  const contactSpecialists = [
    {
      area: 'Servicios de Automatización',
      name: 'Oscar Castillo',
      position: 'Gerente de Servicios',
      phone: '+5218182803296',
      email: '<EMAIL>'
    },
    {
      area: 'Compra de Productos',
      name: 'Esteban Carrera',
      position: 'Gerente de Ventas',
      phone: '+5218187043546',
      email: '<EMAIL>'
    }
  ];

  return (
    <footer style={{
      backgroundColor: '#1a1a1a',
      color: '#ffffff',
      marginTop: '4rem'
    }}>
      {/* Main Footer Content */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '4rem 2rem 2rem 2rem'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '3rem',
          marginBottom: '3rem'
        }}>

          {/* Company Section */}
          <div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '2rem'
            }}>
              <Image
                src={IMAGES.LOGO.SMALL}
                alt="ECCSA Logo"
                width={60}
                height={60}
                style={{ objectFit: 'contain' }}
              />
              <div>
                <h3 style={{
                  fontSize: '1.8rem',
                  fontWeight: 'bold',
                  margin: 0,
                  color: '#ffffff'
                }}>
                  ECCSA
                </h3>
                <p style={{
                  fontSize: '0.9rem',
                  color: '#f7941d',
                  margin: 0,
                  fontWeight: '500'
                }}>
                  Automatización Industrial del Futuro
                </p>
              </div>
            </div>

            <p style={{
              fontSize: '1rem',
              lineHeight: '1.6',
              color: '#cccccc',
              marginBottom: '2rem'
            }}>
              Líderes en automatización industrial con más de 25 años de experiencia.
              Transformamos la industria mexicana con tecnología de vanguardia.
            </p>

            {/* Company Stats */}
            <div style={{
              display: 'flex',
              gap: '2rem',
              flexWrap: 'wrap'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: '#f7941d'
                }}>25+</div>
                <div style={{
                  fontSize: '0.8rem',
                  color: '#cccccc'
                }}>Años</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: '#f7941d'
                }}>500+</div>
                <div style={{
                  fontSize: '0.8rem',
                  color: '#cccccc'
                }}>Proyectos</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: '#f7941d'
                }}>100%</div>
                <div style={{
                  fontSize: '0.8rem',
                  color: '#cccccc'
                }}>Calidad</div>
              </div>
            </div>
          </div>
          {/* Navigation & Services Section */}
          <div>
            <h4 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#ffffff',
              marginBottom: '1.5rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <span style={{ color: '#f7941d' }}>🔗</span>
              Enlaces Rápidos
            </h4>

            <ul style={{
              listStyle: 'none',
              padding: 0,
              margin: 0,
              marginBottom: '2rem'
            }}>
              {navigationLinks.map((link) => (
                <li key={link.href} style={{ marginBottom: '0.75rem' }}>
                  <Link
                    href={link.href}
                    style={{
                      color: '#cccccc',
                      textDecoration: 'none',
                      fontSize: '1rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.color = '#f7941d';
                      e.currentTarget.style.paddingLeft = '0.5rem';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.color = '#cccccc';
                      e.currentTarget.style.paddingLeft = '0';
                    }}
                  >
                    <span style={{ color: '#f7941d' }}>→</span>
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>

            <h5 style={{
              fontSize: '1.1rem',
              fontWeight: '600',
              color: '#ffffff',
              marginBottom: '1rem'
            }}>
              Nuestros Servicios
            </h5>

            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '0.5rem'
            }}>
              {services.map((service, index) => (
                <span
                  key={index}
                  style={{
                    backgroundColor: '#2a2a2a',
                    color: '#cccccc',
                    padding: '0.4rem 0.8rem',
                    borderRadius: '20px',
                    fontSize: '0.8rem',
                    border: '1px solid #3a3a3a',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f7941d';
                    e.currentTarget.style.color = '#ffffff';
                    e.currentTarget.style.borderColor = '#f7941d';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#2a2a2a';
                    e.currentTarget.style.color = '#cccccc';
                    e.currentTarget.style.borderColor = '#3a3a3a';
                  }}
                >
                  {service}
                </span>
              ))}
            </div>
          </div>

          {/* Contact Specialists Section */}
          <div>
            <h4 style={{
              fontSize: '1.3rem',
              fontWeight: 'bold',
              color: '#ffffff',
              marginBottom: '1.5rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <span style={{ color: '#f7941d' }}>📞</span>
              Especialistas de Contacto
            </h4>

            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem'
            }}>
              {contactSpecialists.map((specialist, index) => (
                <div
                  key={index}
                  style={{
                    backgroundColor: '#2a2a2a',
                    borderRadius: '12px',
                    padding: '1.5rem',
                    border: '1px solid #3a3a3a',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#333333';
                    e.currentTarget.style.borderColor = '#f7941d';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#2a2a2a';
                    e.currentTarget.style.borderColor = '#3a3a3a';
                  }}
                >
                  <div style={{
                    fontSize: '0.9rem',
                    color: '#f7941d',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    {specialist.area}
                  </div>

                  <div style={{
                    fontSize: '1.1rem',
                    color: '#ffffff',
                    fontWeight: 'bold',
                    marginBottom: '0.25rem'
                  }}>
                    {specialist.name}
                  </div>

                  <div style={{
                    fontSize: '0.8rem',
                    color: '#cccccc',
                    marginBottom: '1rem'
                  }}>
                    {specialist.position}
                  </div>

                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '0.5rem'
                  }}>
                    <a
                      href={`https://wa.me/${specialist.phone.replace(/[^0-9]/g, '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        color: '#25d366',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.color = '#ffffff';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.color = '#25d366';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
                      </svg>
                      {specialist.phone}
                    </a>

                    <a
                      href={`mailto:${specialist.email}`}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        color: '#cccccc',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.color = '#f7941d';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.color = '#cccccc';
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                      {specialist.email}
                    </a>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Button */}
            <div style={{ marginTop: '2rem' }}>
              <Link
                href="/contacto"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: '#f7941d',
                  color: '#ffffff',
                  padding: '1rem 2rem',
                  borderRadius: '12px',
                  textDecoration: 'none',
                  fontWeight: '600',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(247,148,29,0.3)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e67e22';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(247,148,29,0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#f7941d';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(247,148,29,0.3)';
                }}
              >
                <span>Solicitar Cotización</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="m9 18 6-6-6-6"/>
                </svg>
              </Link>
            </div>
          </div>

        </div>

        {/* Footer Bottom */}
        <div style={{
          borderTop: '1px solid #3a3a3a',
          paddingTop: '2rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '1.5rem'
        }}>
          {/* Location Section */}
          <div style={{
            textAlign: 'center',
            marginBottom: '1rem'
          }}>
            <h4 style={{
              fontSize: '1.2rem',
              fontWeight: 'bold',
              color: '#ffffff',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem'
            }}>
              <span style={{ color: '#f7941d' }}>📍</span>
              Nuestra Ubicación
            </h4>
            <p style={{
              color: '#cccccc',
              fontSize: '1rem',
              margin: 0
            }}>
              Monterrey, Nuevo León, México
            </p>
            <p style={{
              color: '#999999',
              fontSize: '0.9rem',
              margin: '0.5rem 0 0 0'
            }}>
              Corazón industrial del norte de México
            </p>
          </div>

          {/* Copyright and Links */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: '1rem',
            paddingTop: '1.5rem',
            borderTop: '1px solid #2a2a2a'
          }}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '0.25rem'
            }}>
              <p style={{
                color: '#cccccc',
                fontSize: '0.9rem',
                margin: 0
              }}>
                &copy; {new Date().getFullYear()} {COMPANY_INFO.NAME}. Todos los derechos reservados.
              </p>
              <p style={{
                color: '#999999',
                fontSize: '0.8rem',
                margin: 0
              }}>
                Desarrollado con ❤️ para la industria mexicana
              </p>
            </div>

            <div style={{
              display: 'flex',
              gap: '2rem',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              {/* Company Highlights */}
              <div style={{
                display: 'flex',
                gap: '1.5rem',
                alignItems: 'center'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <span style={{
                    color: '#f7941d',
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>ISO 9001</span>
                  <span style={{
                    color: '#999999',
                    fontSize: '0.7rem'
                  }}>Certificado</span>
                </div>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <span style={{
                    color: '#f7941d',
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>24/7</span>
                  <span style={{
                    color: '#999999',
                    fontSize: '0.7rem'
                  }}>Soporte</span>
                </div>
              </div>

              {/* Policy Links */}
              <div style={{
                display: 'flex',
                gap: '1rem',
                alignItems: 'center'
              }}>
                <Link
                  href="/privacy"
                  style={{
                    color: '#999999',
                    textDecoration: 'none',
                    fontSize: '0.8rem',
                    transition: 'color 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = '#f7941d';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = '#999999';
                  }}
                >
                  Privacidad
                </Link>
                <span style={{ color: '#3a3a3a' }}>|</span>
                <Link
                  href="/terms"
                  style={{
                    color: '#999999',
                    textDecoration: 'none',
                    fontSize: '0.8rem',
                    transition: 'color 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = '#f7941d';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = '#999999';
                  }}
                >
                  Términos
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive CSS */}
      <style jsx>{`
        @media (max-width: 768px) {
          .footer-grid {
            grid-template-columns: 1fr !important;
            gap: 2rem !important;
          }

          .footer-bottom-flex {
            flex-direction: column !important;
            text-align: center !important;
            gap: 1rem !important;
          }

          .company-stats {
            justify-content: center !important;
          }

          .footer-highlights {
            justify-content: center !important;
          }
        }

        @media (max-width: 480px) {
          .footer-container {
            padding: 2rem 1rem !important;
          }

          .specialist-card {
            padding: 1rem !important;
          }

          .company-stats {
            gap: 1rem !important;
          }
        }
      `}</style>
    </footer>
  );
}
