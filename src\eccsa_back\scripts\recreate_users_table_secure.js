// Script para recrear la tabla de usuarios con contraseñas seguras
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import crypto from 'crypto';

// Configurar dotenv
dotenv.config({ path: '.env.local' });

// Configuración de la conexión a la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'eccsa',
  password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
  database: process.env.DB_NAME || 'EccsaWeb',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : undefined,
};

// Función para generar un hash seguro de contraseña
function hashPassword(password) {
  // Generar un salt aleatorio
  const salt = crypto.randomBytes(16).toString('hex');
  
  // Crear un hash con el salt
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  
  // Devolver el salt y el hash juntos
  return `${salt}:${hash}`;
}

// Función para verificar una contraseña
function verifyPassword(storedPassword, suppliedPassword) {
  // Separar el salt y el hash almacenados
  const [salt, storedHash] = storedPassword.split(':');
  
  // Hash de la contraseña proporcionada con el mismo salt
  const suppliedHash = crypto.pbkdf2Sync(suppliedPassword, salt, 1000, 64, 'sha512').toString('hex');
  
  // Comparar los hashes
  return storedHash === suppliedHash;
}

async function executeQuery(query, params = []) {
  let connection;
  try {
    connection = await mysql.createConnection(dbConfig);
    const [results] = await connection.execute(query, params);
    return results;
  } catch (error) {
    console.error('Error executing query:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function dropUsersTable() {
  try {
    console.log('Eliminando tabla de usuarios si existe...');
    await executeQuery('DROP TABLE IF EXISTS usuarios');
    console.log('Tabla eliminada correctamente.');
    return true;
  } catch (error) {
    console.error('Error al eliminar la tabla:', error);
    return false;
  }
}

async function createUsersTable() {
  try {
    console.log('Creando tabla de usuarios...');
    
    await executeQuery(`
      CREATE TABLE usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nombre VARCHAR(100) NOT NULL,
        nombre_usuario VARCHAR(50) NOT NULL UNIQUE,
        foto_usuario VARCHAR(255),
        correo VARCHAR(100) NOT NULL UNIQUE,
        contrasena VARCHAR(255) NOT NULL,
        telefono VARCHAR(20),
        nivel_ingeniero INT NOT NULL DEFAULT 4 COMMENT '0=Administrador, 1=Jefe, 2=Senior, 3=Semi-Senior, 4=Junior',
        descripcion TEXT,
        fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        activo BOOLEAN DEFAULT TRUE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('Tabla creada correctamente.');
    return true;
  } catch (error) {
    console.error('Error al crear la tabla:', error);
    return false;
  }
}

async function insertDefaultUsers() {
  try {
    console.log('Insertando usuarios por defecto...');
    
    // Contraseñas originales (para mostrar al usuario)
    const adminPassword = 'Eccsa@Admin2023';
    const oscarPassword = 'Oscar@Eccsa2023';
    const estebanPassword = 'Esteban@Eccsa2023';
    
    // Contraseñas hasheadas para almacenar en la base de datos
    const hashedAdminPassword = hashPassword(adminPassword);
    const hashedOscarPassword = hashPassword(oscarPassword);
    const hashedEstebanPassword = hashPassword(estebanPassword);
    
    // Insertar usuario administrador
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        correo, 
        contrasena, 
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Administrador ECCSA',
        'admin',
        '<EMAIL>',
        ?,
        0,
        'Usuario administrador del sistema con acceso completo'
      )
    `, [hashedAdminPassword]);
    
    // Insertar a Oscar Castillo
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        foto_usuario,
        correo, 
        contrasena, 
        telefono,
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Oscar Castillo',
        'oscar.castillo',
        '/images/usuarios/oscar.jpg',
        '<EMAIL>',
        ?,
        '8182803296',
        0,
        'Jefe de automatización industrial con acceso administrativo'
      )
    `, [hashedOscarPassword]);
    
    // Insertar a Esteban Carrera
    await executeQuery(`
      INSERT INTO usuarios (
        nombre, 
        nombre_usuario, 
        foto_usuario,
        correo, 
        contrasena, 
        telefono,
        nivel_ingeniero, 
        descripcion
      ) VALUES (
        'Esteban Carrera',
        'esteban.carrera',
        '/images/usuarios/esteban.jpg',
        '<EMAIL>',
        ?,
        '8187043546',
        0,
        'Especialista en ventas y soporte técnico con acceso administrativo'
      )
    `, [hashedEstebanPassword]);
    
    console.log('Usuarios insertados correctamente.');
    console.log('\n=== CREDENCIALES DE ACCESO ===');
    console.log('Usuario: admin | Contraseña:', adminPassword);
    console.log('Usuario: oscar.castillo | Contraseña:', oscarPassword);
    console.log('Usuario: esteban.carrera | Contraseña:', estebanPassword);
    console.log('===============================\n');
    
    return {
      admin: adminPassword,
      oscar: oscarPassword,
      esteban: estebanPassword
    };
  } catch (error) {
    console.error('Error al insertar usuarios:', error);
    return false;
  }
}

async function verifyUsers() {
  try {
    console.log('Verificando usuarios insertados...');
    
    const users = await executeQuery(`
      SELECT 
        id, 
        nombre, 
        nombre_usuario, 
        correo, 
        nivel_ingeniero,
        telefono,
        descripcion
      FROM usuarios
      ORDER BY id
    `);
    
    console.log('Usuarios en la base de datos:');
    console.table(users);
    
    return users;
  } catch (error) {
    console.error('Error al verificar usuarios:', error);
    return [];
  }
}

// Ejecutar las funciones
async function main() {
  try {
    console.log('=== Iniciando recreación de la tabla de usuarios ===');
    
    // Eliminar la tabla si existe
    const dropped = await dropUsersTable();
    if (!dropped) {
      console.error('No se pudo eliminar la tabla. Abortando...');
      process.exit(1);
    }
    
    // Crear la tabla
    const created = await createUsersTable();
    if (!created) {
      console.error('No se pudo crear la tabla. Abortando...');
      process.exit(1);
    }
    
    // Insertar usuarios por defecto
    const credentials = await insertDefaultUsers();
    if (!credentials) {
      console.error('No se pudieron insertar los usuarios. Abortando...');
      process.exit(1);
    }
    
    // Verificar usuarios insertados
    await verifyUsers();
    
    console.log('=== Recreación de la tabla de usuarios completada con éxito ===');
    
    // Mostrar las credenciales nuevamente al final
    console.log('\n=== CREDENCIALES DE ACCESO ===');
    console.log('Usuario: admin | Contraseña:', credentials.admin);
    console.log('Usuario: oscar.castillo | Contraseña:', credentials.oscar);
    console.log('Usuario: esteban.carrera | Contraseña:', credentials.esteban);
    console.log('===============================\n');
    
    process.exit(0);
  } catch (error) {
    console.error('Error en el script principal:', error);
    process.exit(1);
  }
}

main();
