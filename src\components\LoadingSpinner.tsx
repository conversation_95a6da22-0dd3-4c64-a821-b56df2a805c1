'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';

interface LoadingSpinnerProps {
  show: boolean;
}

export default function LoadingSpinner({ show }: LoadingSpinnerProps) {
  // Ya no necesitamos estados adicionales porque queremos que sea inmediato

  // Si no se debe mostrar, no renderizar nada
  if (!show) return null;

  return (
    <div className="loading-spinner-overlay visible">
      <div className="loading-spinner-container">
        <div className="loading-spinner-logo">
          <Image
            src="/images/logos/logo_pequeno.png"
            alt="ECCSA Logo"
            width={60}
            height={60}
            priority
          />
        </div>
        <div className="loading-spinner-gear gear-blue"></div>
      </div>
    </div>
  );
}
