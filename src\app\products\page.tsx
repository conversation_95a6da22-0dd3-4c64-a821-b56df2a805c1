'use client';

import { useState, useEffect } from 'react';
import ProductImage from '@/components/ProductImage';
import SolicitudCotizacionModal from '@/components/SolicitudCotizacionModal';

// Definición de tipos para los productos del almacén
interface AlmacenProduct {
  id: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  minimo?: number;
  maximo?: number;
  pedir_cantidad?: number;
  precio_us?: number;
  precio_mx?: number;
  impuesto?: number;
  codigo_sat?: string;
  nota?: string;
  proveedor?: string;
  marcas?: string;
  tiempo_entrega_proveedor?: string;
  fecha_pedido?: string;
  fecha_recibido?: string;
  fecha_creacion?: string;
  fecha_actualizacion?: string;
  activo?: boolean;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

// Interfaz para productos mostrados en el catálogo
interface Product {
  id: number;
  name: string;
  category: string;
  description: string;
  price: string;
  image: string;
  brand: string;
  stock: number;
  almacenNumber: string;
  deliveryTime?: string;
}

// Función para convertir productos del almacén a formato de catálogo
const convertAlmacenToProduct = (almacenProduct: AlmacenProduct): Product => {
  // Formatear precio usando precio_ml (Mercado Libre) restando el 16% de IVA
  let priceText = "Consultar precio";
  if (almacenProduct.precio_ml && almacenProduct.precio_ml > 0) {
    // Restar el 16% de IVA del precio
    const precioSinIVA = almacenProduct.precio_ml / 1.16;
    const formattedPrice = new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(precioSinIVA);
    priceText = formattedPrice;
  }

  // Usar marca como categoría, o "General" si no hay marca
  const category = almacenProduct.marcas || "General";

  // Usar la descripción para mostrar si está disponible, sino usar la descripción normal
  const displayDescription = almacenProduct.Datos_importantes_Descripcion_muestra ||
                            almacenProduct.descripcion ||
                            "Producto de automatización industrial";

  // Usar la imagen del producto si está disponible, sino usar imagen por defecto
  const productImage = almacenProduct.Url_imagen || "/images/logos/logo_pequeno.png";

  return {
    id: almacenProduct.id,
    name: almacenProduct.modelo_existente,
    category: category,
    description: displayDescription,
    price: priceText,
    image: productImage,
    brand: almacenProduct.marcas || "Sin marca",
    stock: almacenProduct.cantidad_nuevo || 0,
    almacenNumber: almacenProduct.numero_almacen,
    deliveryTime: almacenProduct.tiempo_de_Entrega
  };
};

export default function ProductsPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('Todos');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [almacenProducts, setAlmacenProducts] = useState<AlmacenProduct[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Función para agregar al carrito usando localStorage
  const addToCart = (item: any) => {
    const savedCart = localStorage.getItem('eccsa-cart');
    let cartItems = [];

    if (savedCart) {
      try {
        cartItems = JSON.parse(savedCart);
      } catch (error) {
        console.error('Error parsing cart:', error);
        cartItems = [];
      }
    }

    // Buscar si el item ya existe
    const existingItemIndex = cartItems.findIndex((cartItem: any) => cartItem.id === item.id);

    if (existingItemIndex >= 0) {
      // Si existe, incrementar cantidad
      const newQuantity = Math.min(
        cartItems[existingItemIndex].cantidad + 1,
        item.stock_disponible
      );

      if (newQuantity > cartItems[existingItemIndex].cantidad) {
        cartItems[existingItemIndex].cantidad = newQuantity;
      } else {
        alert('No se puede agregar más cantidad. Stock limitado.');
        return;
      }
    } else {
      // Si no existe, agregarlo
      cartItems.push({ ...item, cantidad: 1 });
    }

    // Guardar en localStorage
    localStorage.setItem('eccsa-cart', JSON.stringify(cartItems));

    // Disparar eventos de manera inmediata
    window.dispatchEvent(new CustomEvent('cartUpdated'));

    // Pequeño delay para asegurar que el carrito se actualice antes de abrirse
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('openCart'));
    }, 50);

    console.log('Producto agregado al carrito:', item.modelo_existente);
  };

  // Cargar productos del almacén
  const loadProducts = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Cargando productos del almacén...');
      const response = await fetch('/api/almacen');
      const data = await response.json();

      console.log('📦 Respuesta de la API:', data);
      console.log('✅ Response OK:', response.ok);

      if (response.ok) {
        // Guardar productos originales del almacén
        const almacenProductsData = data.products || [];
        console.log('🏪 Productos del almacén:', almacenProductsData);
        console.log('📊 Cantidad de productos:', almacenProductsData.length);

        setAlmacenProducts(almacenProductsData);

        // Convertir productos del almacén al formato del catálogo
        const convertedProducts = almacenProductsData.map(convertAlmacenToProduct);
        console.log('🔄 Productos convertidos:', convertedProducts);

        setProducts(convertedProducts);

        // Obtener categorías únicas
        const uniqueCategories = Array.from(new Set(convertedProducts.map((product: Product) => product.category))) as string[];
        console.log('📂 Categorías encontradas:', uniqueCategories);
        setCategories(uniqueCategories);
      } else {
        console.error('❌ Error en la respuesta:', data);
        setError(data.error || 'Error al cargar productos');
      }
    } catch (err) {
      console.error('💥 Error de conexión:', err);
      setError('Error de conexión al cargar productos');
    } finally {
      setLoading(false);
    }
  };

  // Cargar productos al montar el componente
  useEffect(() => {
    loadProducts();
  }, []);

  // Detectar si es dispositivo móvil
  useEffect(() => {
    const checkIfMobile = () => {
      // Consideramos móvil cualquier dispositivo con ancho menor a 992px para tarjetas más anchas
      setIsMobile(window.innerWidth <= 992);
    };

    // Verificar al cargar
    checkIfMobile();

    // Verificar al cambiar el tamaño de la ventana
    window.addEventListener('resize', checkIfMobile);

    // Limpiar el event listener
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Filtrar productos por categoría y término de búsqueda
  const filteredProducts = products.filter(product =>
    (selectedCategory === 'Todos' || product.category === selectedCategory) &&
    (product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
     product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
     product.brand.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Función para abrir modal de solicitud
  const handleSolicitarCotizacion = (product: Product) => {
    setSelectedProduct(product);
    setModalOpen(true);
  };

  // Función para cerrar modal
  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedProduct(null);
  };

  // Función para agregar al carrito
  const handleAddToCart = (product: Product) => {
    console.log('handleAddToCart called with:', product);

    // Buscar el producto original del almacén
    const almacenProduct = almacenProducts.find(p => p.id === product.id);

    if (!almacenProduct) {
      alert('Error: No se pudo encontrar la información del producto');
      return;
    }

    // Crear item para el carrito
    // Calcular precio sin IVA para el carrito
    const precioSinIVA = almacenProduct.precio_ml ? almacenProduct.precio_ml / 1.16 : almacenProduct.precio_ml;

    const cartItem = {
      id: almacenProduct.id,
      numero_almacen: almacenProduct.numero_almacen,
      modelo_existente: almacenProduct.modelo_existente,
      descripcion: almacenProduct.descripcion,
      precio_ml: precioSinIVA,
      marcas: almacenProduct.marcas,
      Url_imagen: almacenProduct.Url_imagen,
      stock_disponible: almacenProduct.cantidad_nuevo || 0,
      // Agregar información de stock para el carrito
      sin_stock: (almacenProduct.cantidad_nuevo || 0) === 0,
      mensaje_stock: (almacenProduct.cantidad_nuevo || 0) === 0 ? 'No hay stock - Se cotiza tiempo de entrega' : undefined
    };

    console.log('Adding item to cart:', cartItem);
    addToCart(cartItem);

    // Mostrar mensaje informativo si no hay stock
    if ((almacenProduct.cantidad_nuevo || 0) === 0) {
      alert('Producto agregado al carrito. No hay stock disponible - Se cotizará tiempo de entrega.');
    }
  };

  // Mostrar estado de carga
  if (loading) {
    return (
      <div className="products-page">
        <div className="products-header">
          <h1 className="products-title">Catálogo de Productos</h1>
          <p className="products-description">Cargando productos...</p>
        </div>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>Cargando productos del almacén...</p>
        </div>
      </div>
    );
  }

  // Mostrar error si hay alguno
  if (error) {
    return (
      <div className="products-page">
        <div className="products-header">
          <h1 className="products-title">Catálogo de Productos</h1>
          <p className="products-description">Error al cargar productos</p>
        </div>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p style={{ color: 'red' }}>{error}</p>
          <button onClick={loadProducts} style={{ marginTop: '20px', padding: '10px 20px' }}>
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="modern-catalog-page">
      {/* Hero Section */}
      <div className="catalog-hero">
        <div className="catalog-hero-content">
          <div className="hero-badge">
            <span className="badge-icon">⚡</span>
            <span>Catálogo Profesional</span>
          </div>
          <h1 className="catalog-hero-title">
            Catálogo de <span className="title-highlight">Productos</span>
          </h1>
          <p className="catalog-hero-description">
            Descubre nuestra amplia gama de productos de automatización industrial de las mejores marcas del mercado.
            Soluciones confiables y eficientes para impulsar tu industria.
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">{products.length}+</span>
              <span className="stat-label">Productos</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{categories.length}+</span>
              <span className="stat-label">Categorías</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">25+</span>
              <span className="stat-label">Marcas</span>
            </div>
          </div>
        </div>
        <div className="hero-decoration">
          <div className="decoration-circle decoration-circle-1"></div>
          <div className="decoration-circle decoration-circle-2"></div>
          <div className="decoration-circle decoration-circle-3"></div>
        </div>
      </div>

      {/* Advanced Filters Section */}
      <div className="advanced-filters-section">
        <div className="filters-wrapper">
          {/* Filter Header */}
          <div className="filter-header-modern">
            <div className="filter-title-section">
              <div className="filter-icon-badge">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
                </svg>
              </div>
              <div className="filter-title-content">
                <h3 className="filter-main-title">Filtros Inteligentes</h3>
                <p className="filter-subtitle">Encuentra productos específicos con precisión</p>
              </div>
            </div>
            <div className="filter-stats">
              <span className="results-count-badge">
                {filteredProducts.length} resultados
              </span>
            </div>
          </div>

          {/* Main Filters Container */}
          <div className="filters-main-container">
            {/* Primary Search */}
            <div className="primary-search-container">
              <div className="search-wrapper">
                <div className="search-icon-container">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Buscar productos, marcas, modelos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="advanced-search-input"
                />
                {searchTerm && (
                  <button
                    className="clear-search-btn"
                    onClick={() => setSearchTerm('')}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Filter Pills */}
            <div className="filter-pills-container">
              {/* Category Filter */}
              <div
                className="filter-pill-wrapper"
                onClick={(e) => {
                  const select = e.currentTarget.querySelector('select');
                  if (select && e.target !== select) {
                    select.focus();
                    select.click();
                  }
                }}
              >
                <div className="pill-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="pill-select"
                >
                  <option value="Todos">Todas las categorías</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <div className="pill-arrow">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>

              {/* Sort Filter */}
              <div
                className="filter-pill-wrapper"
                onClick={(e) => {
                  const select = e.currentTarget.querySelector('select');
                  if (select && e.target !== select) {
                    select.focus();
                    select.click();
                  }
                }}
              >
                <div className="pill-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 6h18M7 12h10m-7 6h4"></path>
                  </svg>
                </div>
                <select className="pill-select">
                  <option value="relevance">Relevancia</option>
                  <option value="name-asc">Nombre (A-Z)</option>
                  <option value="name-desc">Nombre (Z-A)</option>
                  <option value="brand">Marca</option>
                  <option value="price-asc">Precio menor</option>
                  <option value="price-desc">Precio mayor</option>
                </select>
                <div className="pill-arrow">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>

              {/* Stock Filter */}
              <div
                className="filter-pill-wrapper"
                onClick={(e) => {
                  const select = e.currentTarget.querySelector('select');
                  if (select && e.target !== select) {
                    select.focus();
                    select.click();
                  }
                }}
              >
                <div className="pill-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                    <line x1="12" y1="22.08" x2="12" y2="12"></line>
                  </svg>
                </div>
                <select className="pill-select">
                  <option value="all">Todo el stock</option>
                  <option value="available">Solo disponibles</option>
                  <option value="low">Stock bajo</option>
                </select>
                <div className="pill-arrow">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </div>
              </div>

              {/* Clear Filters */}
              {(searchTerm || selectedCategory !== 'Todos') && (
                <button
                  className="clear-filters-btn"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('Todos');
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 6h18l-2 13H5L3 6z"></path>
                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  </svg>
                  Limpiar filtros
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="catalog-results-section">
        <div className="results-container">
          <div className="results-header">
            <div className="results-info">
              <h3 className="results-title">Resultados de Búsqueda</h3>
              <p className="results-count">
                <span className="count-number">{filteredProducts.length}</span>
                <span className="count-text">
                  {filteredProducts.length === 1 ? 'producto encontrado' : 'productos encontrados'}
                </span>
                {selectedCategory !== 'Todos' && (
                  <span className="count-category">en "{selectedCategory}"</span>
                )}
              </p>
            </div>
            <div className="view-toggle">
              <button
                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                title="Vista de cuadrícula"
                onClick={() => setViewMode('grid')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="3" width="7" height="7"></rect>
                  <rect x="14" y="3" width="7" height="7"></rect>
                  <rect x="14" y="14" width="7" height="7"></rect>
                  <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
              </button>
              <button
                className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
                title="Vista de lista"
                onClick={() => setViewMode('list')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="8" y1="6" x2="21" y2="6"></line>
                  <line x1="8" y1="12" x2="21" y2="12"></line>
                  <line x1="8" y1="18" x2="21" y2="18"></line>
                  <line x1="3" y1="6" x2="3.01" y2="6"></line>
                  <line x1="3" y1="12" x2="3.01" y2="12"></line>
                  <line x1="3" y1="18" x2="3.01" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>

          {/* Products Grid/List */}
          {viewMode === 'grid' ? (
            <div className="modern-products-grid">
              {filteredProducts.length > 0 ? (
                filteredProducts.map(product => (
                  <div key={product.id} className="modern-product-card">
                    {/* Product Image */}
                    <div className="modern-product-image">
                      {product.image && (product.image.startsWith('http') || product.image.startsWith('https')) ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="product-image-element"
                          loading="lazy"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                          }}
                        />
                      ) : (
                        <ProductImage
                          src={product.image}
                          alt={product.name}
                          width={280}
                          height={200}
                          priority
                          className="product-image-element"
                        />
                      )}

                      {/* Stock Badge */}
                      <div className="modern-stock-badge">
                        <span className={`stock-status ${product.stock > 0 ? 'available' : 'unavailable'}`}>
                          {product.stock > 0 ? `${product.stock} disponibles` : 'Sin stock'}
                        </span>
                      </div>

                      {/* Quick Actions */}
                      <div className="quick-actions">
                        <button className="quick-action-btn" title="Vista rápida">
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                          </svg>
                        </button>
                        <button className="quick-action-btn" title="Agregar a favoritos">
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="modern-product-info">
                      <div className="product-category-badge">
                        {product.category}
                      </div>

                      <h3 className="modern-product-title">{product.name}</h3>

                      <div className="product-brand-info">
                        <span className="brand-label">Marca:</span>
                        <span className="brand-name">{product.brand}</span>
                      </div>

                      {/* Información de envío y recogida */}
                      <div className="product-shipping-info">
                        <div className="shipping-option">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="1" y="3" width="15" height="13"></rect>
                            <polygon points="16,3 21,8 21,16 16,16"></polygon>
                            <circle cx="5.5" cy="18.5" r="2.5"></circle>
                            <circle cx="18.5" cy="18.5" r="2.5"></circle>
                          </svg>
                          <span>Envío no incluido</span>
                        </div>
                        <div className="pickup-option">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                          </svg>
                          <span>Recoger en tienda</span>
                        </div>
                      </div>

                      <p className="modern-product-description">{product.description}</p>

                      {product.deliveryTime && (
                        <div className="delivery-info">
                          <svg className="delivery-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="1" y="3" width="15" height="13"></rect>
                            <polygon points="16,8 20,8 23,11 23,16 16,16"></polygon>
                            <circle cx="5.5" cy="18.5" r="2.5"></circle>
                            <circle cx="18.5" cy="18.5" r="2.5"></circle>
                          </svg>
                          <span>Entrega: {product.deliveryTime}</span>
                        </div>
                      )}

                      <div className="product-price-section">
                        <span className="modern-product-price">{product.price}</span>
                      </div>

                      <div className="modern-product-actions">
                        <button
                          className={`primary-action-btn ${product.stock === 0 ? 'no-stock' : ''}`}
                          onClick={() => handleAddToCart(product)}
                        >
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <circle cx="9" cy="21" r="1"></circle>
                            <circle cx="20" cy="21" r="1"></circle>
                            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                          </svg>
                          {product.stock === 0 ? 'Cotizar Tiempo de Entrega' : 'Agregar al Carrito'}
                        </button>

                        <button
                          className="secondary-action-btn"
                          onClick={() => handleSolicitarCotizacion(product)}
                        >
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                          </svg>
                          Cotizar
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-results">
                  <div className="no-results-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                      <circle cx="11" cy="11" r="8"></circle>
                      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                  </div>
                  <h3 className="no-results-title">No se encontraron productos</h3>
                  <p className="no-results-description">
                    No hay productos que coincidan con tu búsqueda. Intenta con otros términos o selecciona otra categoría.
                  </p>
                  <button
                    className="reset-filters-btn"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('Todos');
                    }}
                  >
                    Limpiar filtros
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="modern-products-list">
              {filteredProducts.length > 0 ? (
                filteredProducts.map((product, index) => {
                  // Buscar el producto original del almacén para obtener más información
                  const almacenProduct = almacenProducts.find(p => p.id === product.id);

                  return (
                    <div key={product.id} className="modern-product-list-item">
                      {/* Product Image */}
                      <div className="list-product-image">
                        {product.image && (product.image.startsWith('http') || product.image.startsWith('https')) ? (
                          <img
                            src={product.image}
                            alt={product.name}
                            className="list-image-element"
                            loading="lazy"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                            }}
                          />
                        ) : (
                          <ProductImage
                            src={product.image}
                            alt={product.name}
                            width={120}
                            height={120}
                            priority
                            className="list-image-element"
                          />
                        )}

                        {/* Stock Badge */}
                        <div className="list-stock-badge">
                          <span className={`stock-status ${product.stock > 0 ? 'available' : 'unavailable'}`}>
                            {product.stock > 0 ? `${product.stock} disponibles` : 'Sin stock'}
                          </span>
                        </div>
                      </div>

                      {/* Product Main Info */}
                      <div className="list-product-main-info">
                        <div className="list-product-header">
                          <div className="product-category-badge">
                            {product.category}
                          </div>
                          <div className="list-product-number">
                            #{almacenProduct?.numero_almacen || 'N/A'}
                          </div>
                        </div>

                        <h3 className="list-product-title">{product.name}</h3>

                        <div className="list-product-brand">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                          </svg>
                          <span>{product.brand}</span>
                        </div>

                        {/* Información de envío y recogida */}
                        <div className="product-shipping-info">
                          <div className="shipping-option">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <rect x="1" y="3" width="15" height="13"></rect>
                              <polygon points="16,3 21,8 21,16 16,16"></polygon>
                              <circle cx="5.5" cy="18.5" r="2.5"></circle>
                              <circle cx="18.5" cy="18.5" r="2.5"></circle>
                            </svg>
                            <span>Envío no incluido (varía según ubicación)</span>
                          </div>
                          <div className="pickup-option">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                              <circle cx="12" cy="10" r="3"></circle>
                            </svg>
                            <span>Disponible para recoger en tienda</span>
                          </div>
                        </div>

                        <p className="list-product-description">{product.description}</p>

                        {/* Additional Info */}
                        <div className="list-product-details">
                          {almacenProduct?.estante && (
                            <div className="detail-item">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9,22 9,12 15,12 15,22"></polyline>
                              </svg>
                              <span>Estante: {almacenProduct.estante}</span>
                            </div>
                          )}

                          {product.deliveryTime && (
                            <div className="detail-item">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="1" y="3" width="15" height="13"></rect>
                                <polygon points="16,8 20,8 23,11 23,16 16,16"></polygon>
                                <circle cx="5.5" cy="18.5" r="2.5"></circle>
                                <circle cx="18.5" cy="18.5" r="2.5"></circle>
                              </svg>
                              <span>Entrega: {product.deliveryTime}</span>
                            </div>
                          )}

                          {almacenProduct?.proveedor && (
                            <div className="detail-item">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                <circle cx="8.5" cy="7" r="4"></circle>
                                <line x1="20" y1="8" x2="20" y2="14"></line>
                                <line x1="23" y1="11" x2="17" y2="11"></line>
                              </svg>
                              <span>Proveedor: {almacenProduct.proveedor}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Product Price and Actions */}
                      <div className="list-product-actions-section">
                        <div className="list-price-section">
                          <span className="list-product-price">{product.price}</span>
                          {almacenProduct?.precio_us && !isNaN(Number(almacenProduct.precio_us)) && Number(almacenProduct.precio_us) > 0 && (
                            <span className="list-price-usd">
                              USD ${Number(almacenProduct.precio_us).toFixed(2)}
                            </span>
                          )}
                        </div>

                        <div className="list-product-actions">
                          <button
                            className={`list-primary-btn ${product.stock === 0 ? 'no-stock' : ''}`}
                            onClick={() => handleAddToCart(product)}
                          >
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <circle cx="9" cy="21" r="1"></circle>
                              <circle cx="20" cy="21" r="1"></circle>
                              <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            {product.stock === 0 ? 'Cotizar Entrega' : 'Agregar'}
                          </button>

                          <button
                            className="list-secondary-btn"
                            onClick={() => handleSolicitarCotizacion(product)}
                          >
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                            </svg>
                            Cotizar
                          </button>

                          <button className="list-details-btn" title="Ver detalles">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="no-results">
                  <div className="no-results-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                      <circle cx="11" cy="11" r="8"></circle>
                      <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                  </div>
                  <h3 className="no-results-title">No se encontraron productos</h3>
                  <p className="no-results-description">
                    No hay productos que coincidan con tu búsqueda. Intenta con otros términos o selecciona otra categoría.
                  </p>
                  <button
                    className="reset-filters-btn"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('Todos');
                    }}
                  >
                    Limpiar filtros
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Contact CTA Section */}
      <div className="catalog-cta-section">
        <div className="cta-container">
          <div className="cta-content">
            <div className="cta-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
            </div>
            <h3 className="cta-title">¿No encuentras lo que buscas?</h3>
            <p className="cta-description">
              Nuestro equipo de expertos está listo para ayudarte a encontrar la solución perfecta para tu proyecto de automatización industrial.
            </p>
            <div className="cta-actions">
              <button className="cta-primary-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
                Contactar Asesor
              </button>
              <button className="cta-secondary-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                Enviar Consulta
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Solicitud de Cotización */}
      <SolicitudCotizacionModal
        isOpen={modalOpen}
        onClose={handleCloseModal}
        product={selectedProduct}
      />
    </div>
  );
}
