/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // Colores del sistema ECCSA
      colors: {
        eccsa: {
          // Azul principal de ECCSA
          primary: '#0056a6',
          'primary-light': '#00a0e3',
          'primary-dark': '#003366',
          // Naranja acento
          accent: '#f7941d',
          'accent-light': '#ff8c00',
          'accent-dark': '#e58000',
          // Rojo ECCSA
          red: '#e31e24',
          'red-dark': '#c01a1f',
          // Grises del sistema
          gray: {
            50: '#f9fafb',
            100: '#f3f4f6',
            200: '#e5e7eb',
            300: '#d1d5db',
            400: '#9ca3af',
            500: '#6b7280',
            600: '#4b5563',
            700: '#374151',
            800: '#1f2937',
            900: '#111827',
          }
        }
      },

      // Tipografía fluida y responsiva
      fontSize: {
        'xs': ['clamp(0.75rem, 1.5vw, 0.875rem)', { lineHeight: '1.5' }],
        'sm': ['clamp(0.875rem, 1.75vw, 1rem)', { lineHeight: '1.5' }],
        'base': ['clamp(1rem, 2vw, 1.125rem)', { lineHeight: '1.6' }],
        'lg': ['clamp(1.125rem, 2.25vw, 1.25rem)', { lineHeight: '1.6' }],
        'xl': ['clamp(1.25rem, 2.5vw, 1.5rem)', { lineHeight: '1.5' }],
        '2xl': ['clamp(1.5rem, 3vw, 2rem)', { lineHeight: '1.4' }],
        '3xl': ['clamp(1.875rem, 3.75vw, 2.5rem)', { lineHeight: '1.3' }],
        '4xl': ['clamp(2.25rem, 4.5vw, 3rem)', { lineHeight: '1.2' }],
        '5xl': ['clamp(3rem, 6vw, 4rem)', { lineHeight: '1.1' }],
      },

      // Espaciado fluido
      spacing: {
        'fluid-xs': 'clamp(0.5rem, 1vw, 0.75rem)',
        'fluid-sm': 'clamp(0.75rem, 1.5vw, 1rem)',
        'fluid-md': 'clamp(1rem, 2vw, 1.5rem)',
        'fluid-lg': 'clamp(1.5rem, 3vw, 2rem)',
        'fluid-xl': 'clamp(2rem, 4vw, 3rem)',
        'fluid-2xl': 'clamp(3rem, 6vw, 4rem)',
        'fluid-3xl': 'clamp(4rem, 8vw, 6rem)',
      },

      // Breakpoints semánticos
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        // Container queries
        'container-sm': '640px',
        'container-md': '768px',
        'container-lg': '1024px',
        'container-xl': '1280px',
      },

      // Grid responsivo intrínseco
      gridTemplateColumns: {
        'auto-fit-xs': 'repeat(auto-fit, minmax(200px, 1fr))',
        'auto-fit-sm': 'repeat(auto-fit, minmax(250px, 1fr))',
        'auto-fit-md': 'repeat(auto-fit, minmax(300px, 1fr))',
        'auto-fit-lg': 'repeat(auto-fit, minmax(350px, 1fr))',
        'auto-fill-xs': 'repeat(auto-fill, minmax(200px, 1fr))',
        'auto-fill-sm': 'repeat(auto-fill, minmax(250px, 1fr))',
        'auto-fill-md': 'repeat(auto-fill, minmax(300px, 1fr))',
        'auto-fill-lg': 'repeat(auto-fill, minmax(350px, 1fr))',
      },

      // Animaciones y transiciones
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'parallax': 'parallax 20s ease-in-out infinite alternate',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        parallax: {
          '0%': { transform: 'translateY(-5px) scale(1.05)' },
          '100%': { transform: 'translateY(5px) scale(1.05)' },
        },
      },

      // Sombras modernas
      boxShadow: {
        'soft': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'medium': '0 4px 12px rgba(0, 0, 0, 0.15)',
        'strong': '0 8px 20px rgba(0, 0, 0, 0.2)',
        'eccsa': '0 4px 12px rgba(0, 86, 166, 0.2)',
        'accent': '0 4px 12px rgba(247, 148, 29, 0.2)',
      },
    },
  },
  plugins: [
    // Plugin para container queries
    require('@tailwindcss/container-queries'),
  ],
}
