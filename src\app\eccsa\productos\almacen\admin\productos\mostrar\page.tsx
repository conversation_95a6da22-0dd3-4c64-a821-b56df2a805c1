'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

export default function ProductosMostrarPage() {
  const [loading, setLoading] = useState(false);
  const [productos, setProductos] = useState([]);
  const [error, setError] = useState('');

  useEffect(() => {
    // Aquí se cargarían los productos a mostrar
    // Por ahora, dejamos un array vacío
  }, []);

  return (
    <AuthGuard>
      <div className="admin-header">
        <h1 className="admin-title">Productos a Mostrar</h1>
        <button className="admin-button">Agregar Producto</button>
      </div>

          <div className="admin-card">
            <div className="admin-card-header">
              <h2 className="admin-card-title">Catálogo de Productos</h2>
              <div className="admin-card-actions">
                <input
                  type="text"
                  placeholder="Buscar producto..."
                  className="admin-search-input"
                />
                <select className="admin-select">
                  <option value="">Todas las categorías</option>
                  <option value="categoria1">Categoría 1</option>
                  <option value="categoria2">Categoría 2</option>
                  <option value="categoria3">Categoría 3</option>
                </select>
              </div>
            </div>

            <div className="admin-card-content">
              {loading ? (
                <div className="admin-loading">
                  <div className="admin-loading-spinner"></div>
                  <p>Cargando productos...</p>
                </div>
              ) : error ? (
                <div className="admin-error">
                  <p>{error}</p>
                  <button
                    className="admin-button"
                    onClick={() => {
                      setError('');
                      // Recargar productos
                    }}
                  >
                    Reintentar
                  </button>
                </div>
              ) : productos.length === 0 ? (
                <div className="admin-empty">
                  <p>No hay productos para mostrar en el catálogo.</p>
                  <p>Agrega productos para comenzar a mostrarlos en tu sitio web.</p>
                </div>
              ) : (
                <div className="admin-products-grid">
                  {/* Aquí se mostrarían los productos en una cuadrícula */}
                  <div className="admin-empty-grid">
                    No hay productos para mostrar
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="admin-card mt-4">
            <div className="admin-card-header">
              <h2 className="admin-card-title">Configuración de Visualización</h2>
            </div>
            <div className="admin-card-content">
              <div className="admin-form-group">
                <label>Productos por página:</label>
                <select className="admin-select">
                  <option value="12">12</option>
                  <option value="24">24</option>
                  <option value="36">36</option>
                  <option value="48">48</option>
                </select>
              </div>
              <div className="admin-form-group">
                <label>Ordenar por:</label>
                <select className="admin-select">
                  <option value="nombre_asc">Nombre (A-Z)</option>
                  <option value="nombre_desc">Nombre (Z-A)</option>
                  <option value="precio_asc">Precio (Menor a Mayor)</option>
                  <option value="precio_desc">Precio (Mayor a Menor)</option>
                  <option value="fecha_asc">Fecha (Antiguo a Reciente)</option>
                  <option value="fecha_desc">Fecha (Reciente a Antiguo)</option>
                </select>
              </div>
              <div className="admin-form-group">
                <label>Mostrar productos sin stock:</label>
                <div className="admin-toggle">
                  <input type="checkbox" id="mostrar_sin_stock" />
                  <label htmlFor="mostrar_sin_stock"></label>
                </div>
              </div>
            </div>
          </div>
    </AuthGuard>
  );
}
