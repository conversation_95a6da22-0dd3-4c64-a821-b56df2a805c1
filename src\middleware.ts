import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rutas que requieren autenticación
const protectedRoutes = [
  '/eccsa/admin'
];

// Rutas públicas del admin (login)
const publicAdminRoutes = [
  '/eccsa/admin/login',
  '/api/auth/login',
  '/api/auth/logout'
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Verificar autenticación para rutas protegidas (excepto rutas públicas del admin)
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );
  const isPublicAdminRoute = publicAdminRoutes.includes(pathname);

  if (isProtectedRoute && !isPublicAdminRoute) {
    // Verificar token de autenticación
    const authToken = request.cookies.get('eccsa-auth-token')?.value;
    const sessionData = request.cookies.get('eccsa-session')?.value;

    // Si no hay token o sesión, redirigir al login
    if (!authToken || !sessionData) {
      const loginUrl = new URL('/eccsa/admin/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Verificar que el token no haya expirado
    try {
      const session = JSON.parse(sessionData);
      const now = Date.now();

      // Verificar expiración (24 horas)
      if (session.expires && now > session.expires) {
        const response = NextResponse.redirect(
          new URL('/eccsa/admin/login', request.url)
        );

        // Limpiar cookies expiradas
        response.cookies.delete('eccsa-auth-token');
        response.cookies.delete('eccsa-session');

        return response;
      }

    } catch (error) {
      // Si hay error al parsear la sesión, redirigir al login
      const response = NextResponse.redirect(
        new URL('/eccsa/admin/login', request.url)
      );

      response.cookies.delete('eccsa-auth-token');
      response.cookies.delete('eccsa-session');

      return response;
    }
  }

  // Si el usuario ya está autenticado y trata de acceder al login, redirigir al dashboard
  if (pathname === '/eccsa/admin/login') {
    const authToken = request.cookies.get('eccsa-auth-token')?.value;
    const sessionData = request.cookies.get('eccsa-session')?.value;

    if (authToken && sessionData) {
      try {
        const session = JSON.parse(sessionData);
        const now = Date.now();

        // Si la sesión es válida, redirigir al dashboard
        if (!session.expires || now < session.expires) {
          return NextResponse.redirect(
            new URL('/eccsa/admin', request.url)
          );
        }
      } catch (error) {
        // Si hay error, limpiar cookies y continuar al login
        const response = NextResponse.next();
        response.cookies.delete('eccsa-auth-token');
        response.cookies.delete('eccsa-session');
        return response;
      }
    }
  }

  // Redirect de la ruta antigua del login a la nueva
  if (pathname === '/eccsa/productos/almacen/admin') {
    return NextResponse.redirect(new URL('/eccsa/admin/login', request.url));
  }

  // Redirects para las rutas antiguas del admin
  if (pathname.startsWith('/eccsa/productos/almacen/admin') && pathname !== '/eccsa/productos/almacen/admin') {
    const newPath = pathname.replace('/eccsa/productos/almacen/admin', '/eccsa/admin');

    // Mapear rutas específicas
    const routeMap: { [key: string]: string } = {
      '/eccsa/admin/dashboard': '/eccsa/admin',
      '/eccsa/admin/productos/almacen': '/eccsa/admin/almacen',
      '/eccsa/admin/productos/precios': '/eccsa/admin/precios',
      '/eccsa/admin/productos/pedir': '/eccsa/admin/almacen',
      '/eccsa/admin/productos/seguimiento': '/eccsa/admin/almacen',
      '/eccsa/admin/productos/mercadolibre': '/eccsa/admin/almacen',
    };

    const finalPath = routeMap[newPath] || newPath;
    return NextResponse.redirect(new URL(finalPath, request.url));
  }

  // Redirects para las rutas públicas antiguas
  const publicRouteMap: { [key: string]: string } = {
    '/eccsa/productos': '/productos',
    '/eccsa/servicios': '/servicios',
    '/eccsa/proyectos': '/proyectos',
    '/eccsa/contacto': '/contacto',
    '/eccsa/carrito': '/carrito',
    '/services': '/servicios',
    '/products': '/productos',
    '/projects': '/proyectos',
    '/contact': '/contacto',
    '/cart': '/carrito',
  };

  if (publicRouteMap[pathname]) {
    return NextResponse.redirect(new URL(publicRouteMap[pathname], request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
