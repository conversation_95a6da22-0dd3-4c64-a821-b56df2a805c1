'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ANIMATION } from '@/constants';
import { ScrollAnimation, StaggerAnimation, TextReveal } from './ScrollAnimation';

interface Brand {
  src: string;
  alt: string;
  url?: string;
}

interface BrandsSliderProps {
  brands: Brand[];
  title?: string;
  speed?: number;
}

/**
 * Modern BrandsSlider component with beautiful design and smooth animations
 * Features auto-scroll, hover effects, and responsive design
 */
export default function BrandsSlider({
  brands,
  title = 'Marcas que Distribuimos',
  speed = ANIMATION.BRANDS_SCROLL_INTERVAL
}: BrandsSliderProps) {
  const sliderRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Touch support for mobile
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Calculate total width and maximum scroll position
  useEffect(() => {
    if (sliderRef.current) {
      const scrollWidth = sliderRef.current.scrollWidth;
      const clientWidth = sliderRef.current.clientWidth;
      setMaxScroll(scrollWidth - clientWidth);
    }
  }, [brands]);

  // Update scroll position when it changes
  const handleScroll = useCallback(() => {
    if (sliderRef.current) {
      setScrollPosition(sliderRef.current.scrollLeft);
    }
  }, []);

  // Auto-scroll effect
  useEffect(() => {
    if (isPaused || !sliderRef.current) return;

    const startAutoScroll = () => {
      scrollIntervalRef.current = setInterval(() => {
        if (sliderRef.current) {
          let newPosition = scrollPosition + 1;

          // Loop back to beginning when reaching the end
          if (newPosition >= maxScroll) {
            newPosition = 0;
          }

          sliderRef.current.scrollTo({
            left: newPosition,
            behavior: 'auto'
          });

          setScrollPosition(newPosition);
        }
      }, speed);
    };

    startAutoScroll();

    // Clean up interval when component unmounts or dependencies change
    return () => {
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
      }
    };
  }, [isPaused, scrollPosition, maxScroll, speed]);

  // Scroll left with loop
  const scrollLeft = useCallback(() => {
    setIsPaused(true);

    if (sliderRef.current) {
      // Jump to end if near beginning to simulate loop
      const newPosition = scrollPosition < 150
        ? maxScroll
        : scrollPosition - 300;

      sliderRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });

      setScrollPosition(newPosition);

      // Resume auto-scroll after a delay
      setTimeout(() => setIsPaused(false), 3000);
    }
  }, [scrollPosition, maxScroll]);

  // Scroll right with loop
  const scrollRight = useCallback(() => {
    setIsPaused(true);

    if (sliderRef.current) {
      // Jump to beginning if near end to simulate loop
      const newPosition = scrollPosition > maxScroll - 150
        ? 0
        : scrollPosition + 300;

      sliderRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth'
      });

      setScrollPosition(newPosition);

      // Resume auto-scroll after a delay
      setTimeout(() => setIsPaused(false), 3000);
    }
  }, [scrollPosition, maxScroll]);

  // Pause auto-scroll on hover
  const handleMouseEnter = () => setIsPaused(true);
  const handleMouseLeave = () => setIsPaused(false);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setIsPaused(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      scrollRight();
    } else if (isRightSwipe) {
      scrollLeft();
    }

    // Resume auto-scroll after touch
    setTimeout(() => setIsPaused(false), 3000);
  };

  if (!brands || brands.length === 0) return null;

  return (
    <section className="modern-brands-section">
      <div className="modern-brands-container">
        {/* Header Section */}
        <ScrollAnimation animation="fade-in" delay={100}>
          <div className="modern-brands-header">
            <div className="modern-brands-title-wrapper">
              <TextReveal>
                <h2 className="modern-brands-title">{title}</h2>
              </TextReveal>
              <div className="modern-brands-subtitle">
                Trabajamos con las marcas líderes mundiales en automatización industrial
              </div>
            </div>
            <div className="modern-brands-decorative">
              <div className="brands-decorative-line"></div>
              <div className="brands-decorative-dot"></div>
            </div>
          </div>
        </ScrollAnimation>

        {/* Brands Grid */}
        <ScrollAnimation animation="slide-up" delay={300}>
          <div className="modern-brands-grid">
            {brands.map((brand, index) => (
              <ScrollAnimation key={`brand-${index}`} animation="scale-up" delay={400 + index * 100}>
                <div className="modern-brand-card">
                  <div className="modern-brand-inner">
                    <div className="modern-brand-logo-container">
                      {brand.url ? (
                        <a
                          href={brand.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label={`Visitar ${brand.alt}`}
                          className="modern-brand-link"
                        >
                          <img
                            src={brand.src}
                            alt={brand.alt}
                            className="modern-brand-logo"
                            loading="lazy"
                          />
                        </a>
                      ) : (
                        <img
                          src={brand.src}
                          alt={brand.alt}
                          className="modern-brand-logo"
                          loading="lazy"
                        />
                      )}
                    </div>
                    <div className="modern-brand-overlay">
                      <div className="modern-brand-name">{brand.alt}</div>
                    </div>
                  </div>
                  <div className="modern-brand-glow"></div>
                </div>
              </ScrollAnimation>
            ))}
          </div>
        </ScrollAnimation>

        {/* Bottom Section */}
        <ScrollAnimation animation="fade-in" delay={800}>
          <div className="modern-brands-bottom">
            <div className="modern-brands-stats">
              <div className="modern-brands-stat">
                <div className="modern-brands-stat-number">{brands.length}+</div>
                <div className="modern-brands-stat-label">Marcas Aliadas</div>
              </div>
              <div className="modern-brands-stat">
                <div className="modern-brands-stat-number">25+</div>
                <div className="modern-brands-stat-label">Años de Experiencia</div>
              </div>
              <div className="modern-brands-stat">
                <div className="modern-brands-stat-number">100%</div>
                <div className="modern-brands-stat-label">Productos Originales</div>
              </div>
            </div>
          </div>
        </ScrollAnimation>
      </div>
    </section>
  );
}
