import crypto from 'crypto';

/**
 * Genera un hash seguro de contraseña
 * @param {string} password - Contraseña en texto plano
 * @returns {string} - Hash de la contraseña con formato salt:hash
 */
export function hashPassword(password: string): string {
  // Generar un salt aleatorio
  const salt = crypto.randomBytes(16).toString('hex');
  
  // Crear un hash con el salt
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  
  // Devolver el salt y el hash juntos
  return `${salt}:${hash}`;
}

/**
 * Verifica si una contraseña coincide con un hash almacenado
 * @param {string} storedPassword - Hash almacenado con formato salt:hash
 * @param {string} suppliedPassword - Contraseña en texto plano a verificar
 * @returns {boolean} - True si la contraseña coincide, false en caso contrario
 */
export function verifyPassword(storedPassword: string, suppliedPassword: string): boolean {
  // Verificar si la contraseña almacenada tiene el formato correcto
  if (!storedPassword.includes(':')) {
    // Si no tiene el formato correcto, es una contraseña antigua sin hash
    return storedPassword === suppliedPassword;
  }
  
  // Separar el salt y el hash almacenados
  const [salt, storedHash] = storedPassword.split(':');
  
  // Hash de la contraseña proporcionada con el mismo salt
  const suppliedHash = crypto.pbkdf2Sync(suppliedPassword, salt, 1000, 64, 'sha512').toString('hex');
  
  // Comparar los hashes
  return storedHash === suppliedHash;
}
