'use client';

import { FaUserTie, FaT<PERSON>hy, FaHandshake, FaTools } from 'react-icons/fa';
import { IconType } from 'react-icons';
import { ScrollAnimation, StaggerAnimation, TextReveal, LineDraw } from './ScrollAnimation';

interface ServiceFeatureProps {
  icon: IconType;
  title: string;
  description: string;
  borderColor?: string;
}

export default function ServiceFeatures() {
  const features: ServiceFeatureProps[] = [
    {
      icon: FaUserTie,
      title: "Ingenieros especializados en soluciones eléctricas",
      description: "Nuestros ingenieros se mantienen en constante capacitación para dominar las tecnologías más avanzadas y ofrecer soluciones de vanguardia en cada proyecto.",
      borderColor: "#0056a6"
    },
    {
      icon: FaTrophy,
      title: "Más de 25 años generando soluciones",
      description: "Fundada en 1999, ECCSA se ha mantenido como un referente de la industria eléctrica en el norte del país, garantizando soluciones seguras y eficientes.",
      borderColor: "#f7941d"
    },
    {
      icon: FaHandshake,
      title: "Las mejores marcas de material eléctrico",
      description: "Distribuimos todas las marcas de equipo y material eléctrico de la más alta calidad para sus proyectos de automatización industrial.",
      borderColor: "#e31e24"
    },
    {
      icon: FaTools,
      title: "Asesores especializados a su servicio",
      description: "Nuestros asesores le podrán atender para solucionar cualquier necesidad de material, equipo o instalaciones eléctricas con la mayor profesionalidad.",
      borderColor: "#00a0e3"
    }
  ];

  return (
    <section className="features-modern-section">
      <div className="features-modern-container">
        {/* Header de la sección con animaciones */}
        <ScrollAnimation animation="slide-down" delay={200}>
          <div className="features-modern-header">
            <TextReveal>
              <h2 className="features-modern-title">
                ¿Por qué elegir <span className="features-highlight">ECCSA</span>?
              </h2>
            </TextReveal>
            <ScrollAnimation animation="fade-in" delay={400}>
              <p className="features-modern-subtitle">
                Más de dos décadas de experiencia nos respaldan como líderes en automatización industrial
              </p>
            </ScrollAnimation>
          </div>
        </ScrollAnimation>

        {/* Grid de características con stagger animation */}
        <StaggerAnimation
          staggerDelay={150}
          className="features-modern-grid"
        >
          {features.map((feature, index) => (
            <div
              key={index}
              className="feature-modern-card scroll-hover-enhance"
              style={{ '--accent-color': feature.borderColor } as React.CSSProperties}
            >
              {/* Número de la característica */}
              <ScrollAnimation animation="bounce-in" delay={300 + index * 100}>
                <div className="feature-number">
                  {String(index + 1).padStart(2, '0')}
                </div>
              </ScrollAnimation>

              {/* Icono con efectos */}
              <ScrollAnimation animation="scale-up" delay={400 + index * 100}>
                <div className="feature-icon-container">
                  <div className="feature-icon-bg"></div>
                  <div className="feature-icon">
                    <feature.icon />
                  </div>
                  <div className="feature-icon-glow"></div>
                </div>
              </ScrollAnimation>

              {/* Contenido */}
              <div className="feature-content">
                <ScrollAnimation animation="slide-up" delay={500 + index * 100}>
                  <LineDraw delay={600 + index * 100}>
                    <h3 className="feature-title">{feature.title}</h3>
                  </LineDraw>
                </ScrollAnimation>

                <ScrollAnimation animation="fade-in" delay={700 + index * 100}>
                  <p className="feature-description">{feature.description}</p>
                </ScrollAnimation>

                {/* Barra de progreso decorativa */}
                <ScrollAnimation animation="slide-right" delay={800 + index * 100}>
                  <div className="feature-progress-bar">
                    <div className="feature-progress-fill"></div>
                  </div>
                </ScrollAnimation>

                {/* Badge de calidad */}
                <ScrollAnimation animation="zoom-in" delay={900 + index * 100}>
                  <div className="feature-badge">
                    <span className="badge-text">Certificado</span>
                    <div className="badge-icon">✓</div>
                  </div>
                </ScrollAnimation>
              </div>

              {/* Efectos decorativos */}
              <div className="feature-decoration">
                <div className="decoration-circle decoration-1"></div>
                <div className="decoration-circle decoration-2"></div>
                <div className="decoration-circle decoration-3"></div>
              </div>

              {/* Hover overlay */}
              <div className="feature-hover-overlay">
                <div className="hover-content">
                  <div className="hover-icon">
                    <feature.icon />
                  </div>
                  <span className="hover-text">Conoce más</span>
                </div>
              </div>
            </div>
          ))}
        </StaggerAnimation>

        {/* CTA Section con animaciones */}
        <ScrollAnimation animation="slide-up" delay={600}>
          <div className="features-cta-section">
            <ScrollAnimation animation="scale-up" delay={800}>
              <div className="features-cta-content">
                <TextReveal delay={900}>
                  <h3>¿Listo para transformar su industria?</h3>
                </TextReveal>
                <ScrollAnimation animation="fade-in" delay={1000}>
                  <p>Contáctenos hoy y descubra cómo nuestras soluciones pueden optimizar sus procesos</p>
                </ScrollAnimation>
                <StaggerAnimation
                  staggerDelay={200}
                  className="features-cta-buttons"
                >
                  <a href="/contact" className="cta-primary-btn">
                    <span>Solicitar Cotización</span>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="m9 18 6-6-6-6"/>
                    </svg>
                  </a>
                  <a href="/products" className="cta-secondary-btn">
                    <span>Ver Productos</span>
                  </a>
                </StaggerAnimation>
              </div>
            </ScrollAnimation>
          </div>
        </ScrollAnimation>
      </div>
    </section>
  );
}
