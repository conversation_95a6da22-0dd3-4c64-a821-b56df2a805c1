/**
 * Script para crear la tabla de usuarios en la base de datos EccsaWeb
 * 
 * Este script lee el archivo SQL y lo ejecuta en la base de datos usando Node.js
 */

const fs = require('fs');
const mysql = require('serverless-mysql');
const path = require('path');

// Configuración de la base de datos
const db = mysql({
  config: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'eccsa',
    password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
    database: process.env.DB_NAME || 'EccsaWeb',
    port: parseInt(process.env.DB_PORT || '3306', 10),
  },
});

// Función para ejecutar una consulta SQL
async function executeQuery(query) {
  try {
    const results = await db.query(query);
    await db.end();
    return results;
  } catch (error) {
    console.error('Error al ejecutar la consulta:', error);
    throw error;
  }
}

// Función principal
async function main() {
  try {
    console.log('Iniciando la creación de la tabla de usuarios...');

    // Leer el archivo SQL
    const sqlFilePath = path.join(__dirname, 'crear_tabla_usuarios.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Dividir el archivo SQL en consultas individuales
    const queries = sql.split(';').filter(query => query.trim() !== '');

    // Ejecutar cada consulta
    for (const query of queries) {
      try {
        await executeQuery(query);
        console.log('Consulta ejecutada con éxito:', query.substring(0, 50) + '...');
      } catch (error) {
        console.error('Error al ejecutar la consulta:', query.substring(0, 100));
        console.error('Detalles del error:', error.message);
        // Continuar con la siguiente consulta
      }
    }

    // Verificar que la tabla se haya creado correctamente
    const tablesResult = await executeQuery("SHOW TABLES LIKE 'usuarios'");
    
    if (tablesResult.length > 0) {
      console.log('Verificación: La tabla "usuarios" existe en la base de datos.');
      
      // Mostrar la estructura de la tabla
      const describeResult = await executeQuery('DESCRIBE usuarios');
      console.log('\nEstructura de la tabla "usuarios":');
      console.log('------------------------------------');
      console.table(describeResult);
      
      // Mostrar los usuarios insertados
      const usersResult = await executeQuery('SELECT id, nombre, nombre_usuario, correo, nivel_ingeniero FROM usuarios');
      console.log('\nUsuarios insertados:');
      console.log('------------------------------------');
      console.table(usersResult);
    } else {
      console.log('Error: La tabla "usuarios" no se creó correctamente.');
    }

    console.log('\nProceso completado.');
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    // Asegurarse de que la conexión se cierre
    await db.end();
    process.exit(0);
  }
}

// Ejecutar la función principal
main();
