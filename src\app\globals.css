/* Importar Tailwind CSS para el rediseño moderno */
@import '../styles/tailwind.css';

/* Importar estilos legacy solo para componentes específicos */
@import './styles.css';

/* Estilos globales adicionales para compatibilidad */
html, body {
  width: 100%;
  overflow-x: hidden;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* Ajustes temporales para compatibilidad con componentes legacy */
@media (max-width: 768px) {
  .container {
    width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    overflow-x: hidden !important;
  }
}

/* Animación de spin para loading spinners */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
