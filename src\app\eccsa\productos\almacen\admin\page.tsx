'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [userRole, setUserRole] = useState<number | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Call the API route for authentication
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Importante para cookies
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setIsAuthenticated(true);
        setUserData(data.user);
        setUserRole(data.user.role_level);

        // Mantener localStorage como fallback
        localStorage.setItem('eccsaUserData', JSON.stringify(data.user));
        localStorage.setItem('eccsaIsAuthenticated', 'true');

        setError('');

        // Redirigir al dashboard después de login exitoso
        window.location.href = '/admin';
      } else {
        setError(data.error || 'Usuario o contraseña incorrectos');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Error de conexión. Intente nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      // Llamar a la API de logout para limpiar cookies
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    }

    // Limpiar estado local
    setIsAuthenticated(false);
    setUsername('');
    setPassword('');
    setUserData(null);
    setUserRole(null);

    // Eliminar los datos del usuario del localStorage
    localStorage.removeItem('eccsaUserData');
    localStorage.removeItem('eccsaIsAuthenticated');

    // Recargar la página para asegurar limpieza completa
    window.location.reload();
  };

  // Verificar si hay una sesión guardada al cargar la página
  useEffect(() => {
    const checkExistingSession = async () => {
      // Solo ejecutar en el cliente
      if (typeof window !== 'undefined') {
        try {
          // Verificar autenticación con el servidor primero
          const response = await fetch('/api/auth/login', {
            method: 'GET',
            credentials: 'include'
          });

          if (response.ok) {
            const data = await response.json();
            if (data.authenticated) {
              setIsAuthenticated(true);
              setUserData(data.user);
              setUserRole(data.user.role_level);
              return;
            }
          }

          // Fallback a localStorage si no hay cookies válidas
          const savedAuth = localStorage.getItem('eccsaIsAuthenticated');
          const savedUserData = localStorage.getItem('eccsaUserData');

          if (savedAuth === 'true' && savedUserData) {
            try {
              const userData = JSON.parse(savedUserData);
              setIsAuthenticated(true);
              setUserData(userData);
              setUserRole(userData.role_level);
            } catch (error) {
              console.error('Error parsing saved user data:', error);
              // Limpiar datos corruptos
              localStorage.removeItem('eccsaUserData');
              localStorage.removeItem('eccsaIsAuthenticated');
            }
          }
        } catch (error) {
          console.error('Session check error:', error);
          // En caso de error, intentar con localStorage
          const savedAuth = localStorage.getItem('eccsaIsAuthenticated');
          const savedUserData = localStorage.getItem('eccsaUserData');

          if (savedAuth === 'true' && savedUserData) {
            try {
              const userData = JSON.parse(savedUserData);
              setIsAuthenticated(true);
              setUserData(userData);
              setUserRole(userData.role_level);
            } catch (error) {
              console.error('Error parsing saved user data:', error);
              localStorage.removeItem('eccsaUserData');
              localStorage.removeItem('eccsaIsAuthenticated');
            }
          }
        }
      }
    };

    checkExistingSession();
  }, []); // El array vacío asegura que esto solo se ejecute una vez al montar el componente

  return (
    <div className="admin-page">
      {!isAuthenticated ? (
        <div className="login-container">
          <div className="login-left-panel">
            <div className="login-illustration">
              <img
                src="/images/logos/logo_pequeno.png"
                alt="ECCSA Logo"
                className="login-logo"
              />
              <div className="login-illustration-content">
                <div className="login-illustration-gear gear-blue"></div>
                <div className="login-illustration-gear gear-orange"></div>
                <div className="login-illustration-dots"></div>
              </div>
            </div>
            <h2 className="login-left-title">Panel de Control ECCSA</h2>
            <p className="login-left-subtitle">Gestión de productos y servicios</p>
          </div>

          <div className="login-right-panel">
            <h1 className="login-title">Área de Administración</h1>
            <p className="login-description">Acceso restringido solo para personal autorizado.</p>



            {error && <div className="login-error">{error}</div>}

            <form onSubmit={handleLogin} className="login-form">
              <div className="login-input-group">
                <div className="login-input-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <input
                  type="text"
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  placeholder="Usuario"
                  className="login-input"
                />
              </div>

              <div className="login-input-group">
                <div className="login-input-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                </div>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder="Contraseña"
                  className="login-input"
                />
              </div>

              <div className="login-remember">
                <input type="checkbox" id="remember" />
                <label htmlFor="remember">Recordar sesión</label>
              </div>

              <button type="submit" className="login-button" disabled={isLoading}>
                {isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
              </button>

              <div className="login-footer">
                <a href="#" className="login-forgot">¿Olvidó su contraseña?</a>
              </div>
            </form>
          </div>
        </div>
      ) : (
        <div className="admin-dashboard">
          <h1 className="admin-title">Panel de Administración</h1>
          <p className="admin-welcome">Bienvenido, {userData?.username || username}. Aquí puedes gestionar los productos y servicios de ECCSA.</p>

              <div className="admin-stats">
                <div className="admin-stat-card">
                  <div className="admin-stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                      <line x1="3" y1="6" x2="21" y2="6"></line>
                      <path d="M16 10a4 4 0 0 1-8 0"></path>
                    </svg>
                  </div>
                  <div className="admin-stat-content">
                    <h3 className="admin-stat-title">Productos</h3>
                    <p className="admin-stat-value">128</p>
                  </div>
                </div>

                <div className="admin-stat-card">
                  <div className="admin-stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                      <polyline points="2 17 12 22 22 17"></polyline>
                      <polyline points="2 12 12 17 22 12"></polyline>
                    </svg>
                  </div>
                  <div className="admin-stat-content">
                    <h3 className="admin-stat-title">Categorías</h3>
                    <p className="admin-stat-value">12</p>
                  </div>
                </div>

                <div className="admin-stat-card">
                  <div className="admin-stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                      <line x1="7" y1="7" x2="7.01" y2="7"></line>
                    </svg>
                  </div>
                  <div className="admin-stat-content">
                    <h3 className="admin-stat-title">Marcas</h3>
                    <p className="admin-stat-value">24</p>
                  </div>
                </div>

                <div className="admin-stat-card">
                  <div className="admin-stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                    </svg>
                  </div>
                  <div className="admin-stat-content">
                    <h3 className="admin-stat-title">Solicitudes</h3>
                    <p className="admin-stat-value">7</p>
                  </div>
                </div>
              </div>

              <div className="admin-actions">
                <h2 className="admin-section-title">Acciones Rápidas</h2>
                <div className="admin-buttons">
                  <button className="admin-action-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Agregar Producto
                  </button>
                  <button className="admin-action-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
                      <polygon points="18 2 22 6 12 16 8 16 8 12 18 2"></polygon>
                    </svg>
                    Editar Productos
                  </button>
                  <button className="admin-action-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="8" y1="6" x2="21" y2="6"></line>
                      <line x1="8" y1="12" x2="21" y2="12"></line>
                      <line x1="8" y1="18" x2="21" y2="18"></line>
                      <line x1="3" y1="6" x2="3.01" y2="6"></line>
                      <line x1="3" y1="12" x2="3.01" y2="12"></line>
                      <line x1="3" y1="18" x2="3.01" y2="18"></line>
                    </svg>
                    Gestionar Categorías
                  </button>
                  <button className="admin-action-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                    </svg>
                    Ver Solicitudes
                  </button>
                </div>
              </div>

              <div className="admin-recent">
                <h2 className="admin-section-title">Actividad Reciente</h2>
                <div className="admin-activity-list">
                  <div className="admin-activity-item">
                    <div className="admin-activity-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="12" y1="18" x2="12" y2="12"></line>
                        <line x1="9" y1="15" x2="15" y2="15"></line>
                      </svg>
                    </div>
                    <div className="admin-activity-content">
                      <p className="admin-activity-text">Se agregó el producto <strong>"PLC Siemens S7-1500"</strong></p>
                      <p className="admin-activity-time">Hace 2 horas</p>
                    </div>
                  </div>

                  <div className="admin-activity-item">
                    <div className="admin-activity-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
                        <polygon points="18 2 22 6 12 16 8 16 8 12 18 2"></polygon>
                      </svg>
                    </div>
                    <div className="admin-activity-content">
                      <p className="admin-activity-text">Se actualizó el precio de <strong>"Variador ABB ACS880"</strong></p>
                      <p className="admin-activity-time">Hace 5 horas</p>
                    </div>
                  </div>

                  <div className="admin-activity-item">
                    <div className="admin-activity-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                      </svg>
                    </div>
                    <div className="admin-activity-content">
                      <p className="admin-activity-text">Se recibió una solicitud de cotización para <strong>"Sensor Allen-Bradley"</strong></p>
                      <p className="admin-activity-time">Hace 1 día</p>
                    </div>
                  </div>

                  <div className="admin-activity-item">
                    <div className="admin-activity-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                        <polyline points="2 17 12 22 22 17"></polyline>
                        <polyline points="2 12 12 17 22 12"></polyline>
                      </svg>
                    </div>
                    <div className="admin-activity-content">
                      <p className="admin-activity-text">Se agregó la categoría <strong>"Robótica Industrial"</strong></p>
                      <p className="admin-activity-time">Hace 2 días</p>
                    </div>
                  </div>

                  <div className="admin-activity-item">
                    <div className="admin-activity-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <path d="M16 10a4 4 0 0 1-8 0"></path>
                      </svg>
                    </div>
                    <div className="admin-activity-content">
                      <p className="admin-activity-text">Se actualizó el inventario de <strong>"Fuentes de Alimentación"</strong></p>
                      <p className="admin-activity-time">Hace 3 días</p>
                    </div>
                  </div>
                </div>
              </div>
        </div>
      )}
    </div>
  );
}
