/**
 * CSS Fallback Script
 * 
 * Este script verifica si los estilos CSS se han cargado correctamente.
 * Si no se han cargado, intenta cargarlos de forma alternativa.
 */
(function() {
  // Función para verificar si los estilos CSS se han cargado correctamente
  function checkCssLoaded() {
    // Verificar si hay elementos con estilo visible
    // Si no hay estilos aplicados, el body tendrá fondo blanco y texto negro
    var body = document.body;
    var computedStyle = window.getComputedStyle(body);
    var backgroundColor = computedStyle.backgroundColor;
    
    // Si el color de fondo es blanco (o no se ha aplicado ningún estilo),
    // asumimos que los estilos no se han cargado correctamente
    if (backgroundColor === 'rgb(255, 255, 255)' || backgroundColor === 'rgba(0, 0, 0, 0)') {
      console.warn('CSS styles may not have loaded correctly. Attempting fallback...');
      loadCssFallback();
    }
  }
  
  // Función para cargar los estilos CSS de forma alternativa
  function loadCssFallback() {
    // Buscar todos los enlaces a hojas de estilo
    var cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
    
    // Para cada enlace, intentar cargarlo de forma alternativa
    cssLinks.forEach(function(link) {
      var href = link.getAttribute('href');
      
      // Si el enlace apunta a un archivo CSS en _next/static/css/
      if (href && href.indexOf('/_next/static/css/') !== -1) {
        // Crear una nueva solicitud para obtener el contenido del CSS
        var xhr = new XMLHttpRequest();
        xhr.open('GET', href, true);
        
        xhr.onload = function() {
          if (xhr.status === 200) {
            // Crear un elemento style e insertar el contenido del CSS
            var style = document.createElement('style');
            style.textContent = xhr.responseText;
            document.head.appendChild(style);
            console.log('Fallback CSS loaded for: ' + href);
          }
        };
        
        xhr.send();
      }
    });
  }
  
  // Esperar a que el DOM esté completamente cargado
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      // Esperar un poco para que los estilos se apliquen
      setTimeout(checkCssLoaded, 500);
    });
  } else {
    // El DOM ya está cargado
    setTimeout(checkCssLoaded, 500);
  }
})();
