'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import AdminSidebar from './AdminSidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export default function AdminLayout({ children, title = 'Panel de Administración' }: AdminLayoutProps) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();

  // Detectar si es móvil
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Verificar autenticación
  useEffect(() => {
    const checkAuth = () => {
      if (typeof window !== 'undefined') {
        const savedAuth = localStorage.getItem('eccsaIsAuthenticated');
        setIsAuthenticated(savedAuth === 'true');
      }
    };

    checkAuth();

    // Escuchar cambios en localStorage (para cuando se hace login/logout)
    window.addEventListener('storage', checkAuth);

    return () => window.removeEventListener('storage', checkAuth);
  }, []);

  // También verificar cuando cambia la ruta
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedAuth = localStorage.getItem('eccsaIsAuthenticated');
      setIsAuthenticated(savedAuth === 'true');
    }
  }, [pathname]);

  const toggleMobileMenu = () => {
    setMobileOpen(!mobileOpen);
  };

  const closeMobileMenu = () => {
    setMobileOpen(false);
  };

  const handleSidebarToggle = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed);
  };

  // Si no está autenticado, solo mostrar el contenido sin sidebar
  if (!isAuthenticated) {
    return (
      <div className="admin-layout-no-sidebar">
        {children}
      </div>
    );
  }

  // Si está autenticado, mostrar el layout completo con sidebar
  return (
    <div className="admin-layout">
      {/* Header móvil */}
      {isMobile && (
        <div className="mobile-header">
          <button
            className="mobile-menu-button"
            onClick={toggleMobileMenu}
            aria-label="Abrir menú"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
          <div className="mobile-header-title">{title}</div>
          <div style={{ width: '40px' }}></div> {/* Spacer para centrar el título */}
        </div>
      )}

      {/* Overlay para móvil */}
      {isMobile && (
        <div
          className={`mobile-overlay ${mobileOpen ? 'active' : ''}`}
          onClick={closeMobileMenu}
        ></div>
      )}

      {/* Sidebar */}
      <AdminSidebar
        mobileOpen={mobileOpen}
        onMobileToggle={closeMobileMenu}
        onToggle={handleSidebarToggle}
      />

      {/* Contenido principal */}
      <div className={`admin-content ${sidebarCollapsed && !isMobile ? 'sidebar-collapsed' : ''}`}>
        {children}
      </div>
    </div>
  );
}
