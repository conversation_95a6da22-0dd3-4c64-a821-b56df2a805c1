'use client';

import { useState, useCallback } from 'react';

interface ConfirmOptions {
  title: string;
  message: string;
  type?: 'danger' | 'warning' | 'info';
  confirmText?: string;
  cancelText?: string;
}

interface ConfirmState extends ConfirmOptions {
  isOpen: boolean;
  onConfirm: (() => void) | null;
  isLoading: boolean;
}

export function useConfirm() {
  const [confirmState, setConfirmState] = useState<ConfirmState>({
    isOpen: false,
    title: '',
    message: '',
    type: 'warning',
    confirmText: 'Confirmar',
    cancelText: 'Cancelar',
    onConfirm: null,
    isLoading: false
  });

  const showConfirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmState({
        isOpen: true,
        title: options.title,
        message: options.message,
        type: options.type || 'warning',
        confirmText: options.confirmText || 'Confirmar',
        cancelText: options.cancelText || 'Cancelar',
        onConfirm: () => {
          resolve(true);
          setConfirmState(prev => ({ ...prev, isOpen: false, onConfirm: null }));
        },
        isLoading: false
      });

      // Auto-resolve to false if modal is closed without confirmation
      const timeoutId = setTimeout(() => {
        setConfirmState(prev => {
          if (prev.isOpen) {
            resolve(false);
            return { ...prev, isOpen: false, onConfirm: null };
          }
          return prev;
        });
      }, 300000); // 5 minutes timeout

      // Clear timeout when promise resolves
      const originalOnConfirm = () => {
        clearTimeout(timeoutId);
        resolve(true);
        setConfirmState(prev => ({ ...prev, isOpen: false, onConfirm: null }));
      };

      setConfirmState(prev => ({ ...prev, onConfirm: originalOnConfirm }));
    });
  }, []);

  const hideConfirm = useCallback(() => {
    setConfirmState(prev => ({ ...prev, isOpen: false, onConfirm: null }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setConfirmState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  return {
    confirmState,
    showConfirm,
    hideConfirm,
    setLoading
  };
}
