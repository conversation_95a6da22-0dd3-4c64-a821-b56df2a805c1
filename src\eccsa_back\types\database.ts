// User model
export interface User {
  id: number;
  username: string;
  password?: string;
  email?: string;
  role: string; // Texto del rol (Administrador, <PERSON><PERSON>, Senior, etc.)
  role_level?: number; // Nivel numérico (0=Administrador, 1=<PERSON><PERSON>, 2=Senior, etc.)
  created_at?: Date;
  updated_at?: Date;

  // New fields for the usuarios table
  name?: string;
  photo?: string;
  phone?: string;
  description?: string;
  active?: boolean;
}

// Product model
export interface Product {
  id: number;
  name: string;
  description: string;
  price?: number;
  category_id: number;
  image_url?: string;
  brand?: string;
  created_at: Date;
  updated_at: Date;
}

// Category model
export interface Category {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

// Order model
export interface Order {
  id: number;
  user_id: number;
  status: string;
  total: number;
  created_at: Date;
  updated_at: Date;
}

// OrderItem model
export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  price: number;
  created_at: Date;
  updated_at: Date;
}

// Database connection info
export interface ConnectionInfo {
  host: string;
  user: string;
  database: string;
  port: number;
  usingMock?: boolean;
}

// Database connection status
export interface ConnectionStatus {
  info: ConnectionInfo;
  status: string;
  message: string;
  testResult: any;
}

// Table statistics
export interface TableStat {
  name: string;
  count: number;
}

// Database statistics
export interface DbStats {
  tables: TableStat[];
}

// Almacén (Warehouse) model
export interface Almacen {
  id?: number;
  numero_almacen: string;
  estante?: string;
  modelo_existente: string;
  descripcion?: string;
  precio_venta?: number;
  precio_ml?: number;
  vendidos?: number;
  cantidad_nuevo?: number;
  minimo?: number;
  maximo?: number;
  pedir_cantidad?: number;
  precio_us?: number;
  precio_mx?: number;
  impuesto?: number;
  codigo_sat?: string;
  nota?: string;
  proveedor?: string;
  marcas?: string;
  tiempo_entrega_proveedor?: string;
  fecha_pedido?: Date | string;
  fecha_recibido?: Date | string;
  fecha_creacion?: Date;
  fecha_actualizacion?: Date;
  activo?: boolean;
  Url_imagen?: string;
  Datos_importantes_Descripcion_muestra?: string;
  tiempo_de_Entrega?: string;
}

// Database status response
export interface DatabaseStatus {
  success: boolean;
  connection: ConnectionStatus;
  stats: DbStats;
  connectionString: string;
  error?: string;
  details?: string;
}
