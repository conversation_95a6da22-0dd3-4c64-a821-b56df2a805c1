'use client';

import { usePathname } from 'next/navigation';
import Navbar from '@/components/Navbar';

export default function ConditionalNavbar() {
  const pathname = usePathname();

  // Hide navbar on all admin pages (including login)
  const isAdminPage = pathname.startsWith('/eccsa/admin') || pathname.startsWith('/admin');

  // Only show navbar on public pages (not admin pages)
  if (!isAdminPage) {
    return <Navbar />;
  }

  return null;
}
