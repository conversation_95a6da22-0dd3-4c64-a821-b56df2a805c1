<?php
/**
 * Script para crear la tabla de almacén en la base de datos EccsaWeb
 * 
 * Este script lee el archivo SQL y lo ejecuta en la base de datos
 */

// Configuración de la base de datos
$host = 'localhost';
$usuario = 'eccsa';
$contrasena = 'eccsa?web?Admin';
$base_datos = 'EccsaWeb';

// Conectar a la base de datos
try {
    $conexion = new PDO("mysql:host=$host;dbname=$base_datos", $usuario, $contrasena);
    $conexion->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Conexión exitosa a la base de datos.\n";
} catch (PDOException $e) {
    die("Error de conexión: " . $e->getMessage() . "\n");
}

// Leer el archivo SQL
try {
    $sql = file_get_contents('crear_tabla_almacen.sql');
    if ($sql === false) {
        throw new Exception("No se pudo leer el archivo SQL.");
    }
} catch (Exception $e) {
    die("Error al leer el archivo SQL: " . $e->getMessage() . "\n");
}

// Ejecutar las consultas SQL
try {
    // Dividir el archivo SQL en consultas individuales
    $consultas = explode(';', $sql);
    
    // Ejecutar cada consulta
    foreach ($consultas as $consulta) {
        $consulta = trim($consulta);
        if (!empty($consulta) && !preg_match('/^--/', $consulta)) {
            $conexion->exec($consulta);
            echo "Consulta ejecutada con éxito: " . substr($consulta, 0, 50) . "...\n";
        }
    }
    
    echo "La tabla de almacén ha sido creada exitosamente.\n";
} catch (PDOException $e) {
    die("Error al ejecutar las consultas SQL: " . $e->getMessage() . "\n");
}

// Verificar que la tabla se haya creado correctamente
try {
    $resultado = $conexion->query("SHOW TABLES LIKE 'almacen'");
    if ($resultado->rowCount() > 0) {
        echo "Verificación: La tabla 'almacen' existe en la base de datos.\n";
        
        // Mostrar la estructura de la tabla
        $resultado = $conexion->query("DESCRIBE almacen");
        echo "\nEstructura de la tabla 'almacen':\n";
        echo "------------------------------------\n";
        echo "Campo | Tipo | Nulo | Clave | Predeterminado | Extra\n";
        echo "------------------------------------\n";
        while ($fila = $resultado->fetch(PDO::FETCH_ASSOC)) {
            echo $fila['Field'] . " | " . $fila['Type'] . " | " . $fila['Null'] . " | " . $fila['Key'] . " | " . $fila['Default'] . " | " . $fila['Extra'] . "\n";
        }
        
        // Mostrar los productos insertados
        $resultado = $conexion->query("SELECT id, numero_almacen, modelo_existente, precio_venta, cantidad_nuevo, proveedor FROM almacen ORDER BY numero_almacen");
        echo "\nProductos insertados en el almacén:\n";
        echo "------------------------------------\n";
        echo "ID | Número | Modelo | Precio Venta | Cantidad | Proveedor\n";
        echo "------------------------------------\n";
        while ($fila = $resultado->fetch(PDO::FETCH_ASSOC)) {
            echo $fila['id'] . " | " . $fila['numero_almacen'] . " | " . substr($fila['modelo_existente'], 0, 20) . "... | $" . $fila['precio_venta'] . " | " . $fila['cantidad_nuevo'] . " | " . substr($fila['proveedor'], 0, 15) . "\n";
        }
        
        // Mostrar estadísticas básicas
        $resultado = $conexion->query("SELECT COUNT(*) as total_productos, SUM(cantidad_nuevo) as total_cantidad, AVG(precio_venta) as precio_promedio FROM almacen");
        $estadisticas = $resultado->fetch(PDO::FETCH_ASSOC);
        echo "\nEstadísticas del almacén:\n";
        echo "------------------------------------\n";
        echo "Total de productos: " . $estadisticas['total_productos'] . "\n";
        echo "Cantidad total en inventario: " . $estadisticas['total_cantidad'] . "\n";
        echo "Precio promedio: $" . number_format($estadisticas['precio_promedio'], 2) . "\n";
        
    } else {
        echo "Error: La tabla 'almacen' no se creó correctamente.\n";
    }
} catch (PDOException $e) {
    echo "Error al verificar la tabla: " . $e->getMessage() . "\n";
}

// Cerrar la conexión
$conexion = null;
echo "\nProceso completado.\n";
echo "La tabla de almacén está lista para ser utilizada en el sistema ECCSA.\n";
?>
