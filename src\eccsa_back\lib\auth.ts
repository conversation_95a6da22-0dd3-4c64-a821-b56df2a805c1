import { executeQuery } from './db';
import { User } from '../types/database';
import { verifyPassword, hashPassword } from './password';

/**
 * Convierte un rol de texto a un número
 * @param {string} role - Rol en formato de texto
 * @returns {number} - Nivel de ingeniero como número
 */
function convertRoleToNumber(role: string): number {
  switch (role.toLowerCase()) {
    case 'administrador':
      return 0;
    case 'jefe':
      return 1;
    case 'senior':
      return 2;
    case 'semi-senior':
      return 3;
    case 'junior':
      return 4;
    default:
      return 2; // Por defecto, Senior
  }
}

/**
 * Convierte un nivel de ingeniero de número a texto
 * @param {number} level - Nivel de ingeniero como número
 * @returns {string} - Rol en formato de texto
 */
function convertNumberToRole(level: number): string {
  switch (level) {
    case 0:
      return 'Administrador';
    case 1:
      return 'Jefe';
    case 2:
      return 'Senior';
    case 3:
      return 'Semi-Senior';
    case 4:
      return 'Junior';
    default:
      return 'Senior';
  }
}

/**
 * Authenticate a user by username and password
 */
export async function authenticateUser(username: string, password: string): Promise<User | null> {
  try {
    // Obtener el usuario por nombre de usuario o correo
    const query = `
      SELECT
        id,
        nombre_usuario as username,
        nivel_ingeniero as role_level,
        correo as email,
        nombre as name,
        foto_usuario as photo,
        telefono as phone,
        descripcion as description,
        contrasena as password
      FROM usuarios
      WHERE (nombre_usuario = ? OR correo = ?) AND activo = TRUE
    `;

    const users = await executeQuery({
      query,
      values: [username, username],
    }) as any[];

    // Verificar si se encontró un usuario
    if (users.length > 0) {
      const user = users[0];
      // Verificar la contraseña
      if (verifyPassword(user.password, password)) {
        // Eliminar la contraseña del objeto de usuario antes de devolverlo
        delete user.password;
      } else {
        // Si la contraseña no coincide, devolver null
        return null;
      }
    }

    if (users.length === 0) {
      // Fallback to the old users table for backward compatibility
      const oldTableQuery = `
        SELECT id, username, role, email
        FROM users
        WHERE username = ? AND password = ?
      `;

      const oldUsers = await executeQuery({
        query: oldTableQuery,
        values: [username, password],
      }) as User[];

      if (oldUsers.length === 0) {
        return null;
      }

      return oldUsers[0];
    }

    // Convertir el nivel de ingeniero a texto para mantener compatibilidad
    const user = users[0];
    const roleLevel = user.role_level;

    // Crear un objeto de usuario con el formato esperado
    const formattedUser: User = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: convertNumberToRole(roleLevel), // Convertir el nivel a texto
      role_level: roleLevel, // Mantener el nivel numérico
      name: user.name,
      photo: user.photo,
      phone: user.phone,
      description: user.description
    };

    return formattedUser;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

/**
 * Get a user by ID
 */
export async function getUserById(id: number): Promise<User | null> {
  try {
    // Try to get user from the new usuarios table
    const query = `
      SELECT
        id,
        nombre_usuario as username,
        nivel_ingeniero as role,
        correo as email,
        fecha_creacion as created_at,
        fecha_actualizacion as updated_at
      FROM usuarios
      WHERE id = ? AND activo = TRUE
    `;

    const users = await executeQuery({
      query,
      values: [id],
    }) as User[];

    if (users.length === 0) {
      // Fallback to the old users table for backward compatibility
      const oldTableQuery = `
        SELECT id, username, role, email, created_at, updated_at
        FROM users
        WHERE id = ?
      `;

      const oldUsers = await executeQuery({
        query: oldTableQuery,
        values: [id],
      }) as User[];

      if (oldUsers.length === 0) {
        return null;
      }

      return oldUsers[0];
    }

    return users[0];
  } catch (error) {
    console.error(`Error fetching user with ID ${id}:`, error);
    return null;
  }
}

/**
 * Create a new user
 */
export async function createUser(user: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<number | null> {
  try {
    // Hash the password before storing it
    const hashedPassword = user.password ? hashPassword(user.password) : '';

    const query = `
      INSERT INTO usuarios (
        nombre,
        nombre_usuario,
        correo,
        contrasena,
        nivel_ingeniero,
        descripcion
      )
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery({
      query,
      values: [
        user.name || user.username, // Use name if available, otherwise username
        user.username,
        user.email || null,
        hashedPassword, // Use the hashed password
        user.role || 'Junior',
        user.description || null,
      ],
    }) as any;

    return result.insertId;
  } catch (error) {
    console.error('Error creating user:', error);

    // Fallback to the old users table for backward compatibility
    try {
      // Hash the password for the old table too
      const hashedPassword = user.password ? hashPassword(user.password) : '';

      const oldTableQuery = `
        INSERT INTO users (username, password, email, role, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `;

      const oldResult = await executeQuery({
        query: oldTableQuery,
        values: [
          user.username,
          hashedPassword,
          user.email || null,
          user.role,
        ],
      }) as any;

      return oldResult.insertId;
    } catch (fallbackError) {
      console.error('Error creating user in fallback table:', fallbackError);
      return null;
    }
  }
}

/**
 * Update an existing user
 */
export async function updateUser(id: number, user: Partial<User>): Promise<boolean> {
  try {
    // Try to update in the new usuarios table
    // Build the SET part of the query dynamically based on provided fields
    const updates: string[] = [];
    const values: any[] = [];

    if (user.name !== undefined) {
      updates.push('nombre = ?');
      values.push(user.name);
    }

    if (user.username !== undefined) {
      updates.push('nombre_usuario = ?');
      values.push(user.username);
    }

    if (user.password !== undefined) {
      // Hash the password before storing it
      const hashedPassword = user.password ? hashPassword(user.password) : '';
      updates.push('contrasena = ?');
      values.push(hashedPassword);
    }

    if (user.email !== undefined) {
      updates.push('correo = ?');
      values.push(user.email);
    }

    if (user.role !== undefined) {
      updates.push('nivel_ingeniero = ?');
      // Convertir el rol a número si es necesario
      const nivelIngeniero = typeof user.role === 'string'
        ? convertRoleToNumber(user.role)
        : user.role;
      values.push(nivelIngeniero);
    }

    if (user.description !== undefined) {
      updates.push('descripcion = ?');
      values.push(user.description);
    }

    if (user.photo !== undefined) {
      updates.push('foto_usuario = ?');
      values.push(user.photo);
    }

    if (user.phone !== undefined) {
      updates.push('telefono = ?');
      values.push(user.phone);
    }

    // If no fields to update, return false
    if (updates.length === 0) {
      return false;
    }

    const query = `
      UPDATE usuarios
      SET ${updates.join(', ')}
      WHERE id = ?
    `;

    // Add the ID to the values array
    values.push(id);

    const result = await executeQuery({ query, values }) as any;

    if (result.affectedRows > 0) {
      return true;
    }

    // Fallback to the old users table for backward compatibility
    const oldUpdates: string[] = [];
    const oldValues: any[] = [];

    if (user.username !== undefined) {
      oldUpdates.push('username = ?');
      oldValues.push(user.username);
    }

    if (user.password !== undefined) {
      // Hash the password for the old table too
      const hashedPassword = user.password ? hashPassword(user.password) : '';
      oldUpdates.push('password = ?');
      oldValues.push(hashedPassword);
    }

    if (user.email !== undefined) {
      oldUpdates.push('email = ?');
      oldValues.push(user.email);
    }

    if (user.role !== undefined) {
      oldUpdates.push('role = ?');
      oldValues.push(user.role);
    }

    // Always update the updated_at timestamp
    oldUpdates.push('updated_at = NOW()');

    // If no fields to update, return false
    if (oldUpdates.length === 0) {
      return false;
    }

    const oldQuery = `
      UPDATE users
      SET ${oldUpdates.join(', ')}
      WHERE id = ?
    `;

    // Add the ID to the values array
    oldValues.push(id);

    const oldResult = await executeQuery({ query: oldQuery, values: oldValues }) as any;

    return oldResult.affectedRows > 0;
  } catch (error) {
    console.error(`Error updating user with ID ${id}:`, error);
    return false;
  }
}

/**
 * Delete a user
 */
export async function deleteUser(id: number): Promise<boolean> {
  try {
    // In the new usuarios table, we mark users as inactive instead of deleting them
    const query = 'UPDATE usuarios SET activo = FALSE WHERE id = ?';

    const result = await executeQuery({ query, values: [id] }) as any;

    if (result.affectedRows > 0) {
      return true;
    }

    // Fallback to the old users table for backward compatibility
    const oldQuery = 'DELETE FROM users WHERE id = ?';

    const oldResult = await executeQuery({ query: oldQuery, values: [id] }) as any;

    return oldResult.affectedRows > 0;
  } catch (error) {
    console.error(`Error deleting user with ID ${id}:`, error);
    return false;
  }
}
