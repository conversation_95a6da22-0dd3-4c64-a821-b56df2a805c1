'use client';

interface DashboardCardProps {
  title: string;
  value: string;
  change: string;
  icon: string;
}

export default function DashboardCard({ title, value, change, icon }: DashboardCardProps) {
  const isPositive = change.startsWith('+');

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">{title}</h3>
        <span className="card-icon">{icon}</span>
      </div>
      <div className="card-content">
        <div className="card-value">{value}</div>
        <div className={`card-change ${isPositive ? 'positive' : 'negative'}`}>
          {change} desde el mes pasado
        </div>
      </div>
    </div>
  );
}
