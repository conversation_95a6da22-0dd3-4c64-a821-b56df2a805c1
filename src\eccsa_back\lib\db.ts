import mysql from 'serverless-mysql';

// Determine if we're using a mock database for development
const useMockDb = process.env.USE_MOCK_DB === 'true' || !process.env.DB_HOST;

// Database connection configuration
const db = mysql({
  config: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'eccsa',
    password: process.env.DB_PASSWORD || 'eccsa?web?Admin',
    database: process.env.DB_NAME || 'EccsaWeb',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    connectTimeout: 3000, // 3 seconds timeout
    ssl: process.env.DB_SSL === 'true' ? {
      rejectUnauthorized: false
    } : undefined,
  },
});

// Helper function to execute SQL queries
export async function executeQuery({ query, values = [] }: { query: string; values?: any[] }) {
  try {
    if (useMockDb) {
      console.log('Using mock database. Query:', query);
      // Return mock data based on the query
      return mockDatabaseResponse(query, values);
    }

    // Connect to the database with a timeout
    const results = await Promise.race([
      db.query(query, values),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database query timeout')), 2000)
      )
    ]);

    // Release the connection
    await db.end();

    // Return the results
    return results;
  } catch (error) {
    console.error('Database query error:', error);

    // If we're not explicitly using mock mode but got an error, fall back to mock data
    console.log('Falling back to mock data due to database error');
    return mockDatabaseResponse(query, values);
  }
}

// Function to generate mock responses for development
function mockDatabaseResponse(query: string, values: any[] = []): any {
  console.log('MOCK DB: Generating mock response for query:', query);

  // Mock users for authentication
  if (query.includes('SELECT') && query.includes('FROM users')) {
    if (values.length >= 2 && values[0] === 'admin' && values[1] === 'eccsa2023') {
      return [{ id: 1, username: 'admin', role: 'admin' }];
    }
    return [];
  }

  // Mock products list
  if (query.includes('SELECT') && query.includes('FROM products')) {
    if (query.includes('WHERE p.id = ?')) {
      const productId = values[0];
      return [mockProducts.find(p => p.id === productId) || null];
    }
    return mockProducts;
  }

  // Mock categories
  if (query.includes('SELECT') && query.includes('FROM categories')) {
    return mockCategories;
  }

  // Mock almacen products
  if (query.includes('SELECT') && query.includes('FROM almacen')) {
    if (query.includes('WHERE id = ?')) {
      const productId = values[0];
      return [mockAlmacenProducts.find(p => p.id === productId) || null];
    }
    if (query.includes('WHERE numero_almacen = ?')) {
      const numeroAlmacen = values[0];
      return [mockAlmacenProducts.find(p => p.numero_almacen === numeroAlmacen) || null];
    }
    return mockAlmacenProducts;
  }

  // Mock database test query
  if (query.includes('SELECT 1 as test')) {
    return [{ test: 1 }];
  }

  // Mock SHOW TABLES query
  if (query.includes('SHOW TABLES')) {
    const dbName = process.env.DB_NAME || 'eccsa_db';
    return mockTables.map(table => ({ [`Tables_in_${dbName}`]: table }));
  }

  // Mock COUNT query
  if (query.includes('COUNT(*)')) {
    const tableName = query.split('FROM ')[1].trim();
    if (tableName === 'users') return [{ count: 1 }];
    if (tableName === 'products') return [{ count: 3 }];
    if (tableName === 'categories') return [{ count: 3 }];
    if (tableName === 'orders') return [{ count: 0 }];
    return [{ count: 0 }];
  }

  // Mock insert operations
  if (query.includes('INSERT INTO')) {
    return { insertId: Math.floor(Math.random() * 1000) + 100 };
  }

  // Mock update operations
  if (query.includes('UPDATE')) {
    return { affectedRows: 1 };
  }

  // Mock delete operations
  if (query.includes('DELETE')) {
    return { affectedRows: 1 };
  }

  // Default mock response for any other query
  return [];
}

// Mock data for development
const mockProducts = [
  {
    id: 1,
    name: "PLC Siemens S7-1200",
    description: "Controlador lógico programable para automatización industrial",
    price: 5000,
    category_id: 1,
    category_name: "Controladores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "Siemens",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 2,
    name: "Variador de Frecuencia ABB ACS580",
    description: "Variador de frecuencia para control de motores",
    price: 8000,
    category_id: 2,
    category_name: "Variadores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "ABB",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 3,
    name: "Sensor de Proximidad Allen-Bradley",
    description: "Sensor inductivo para detección de objetos metálicos",
    price: 1200,
    category_id: 3,
    category_name: "Sensores",
    image_url: "/images/logos/logo_pequeno.png",
    brand: "Allen-Bradley",
    created_at: new Date(),
    updated_at: new Date()
  }
];

const mockCategories = [
  {
    id: 1,
    name: "Controladores",
    description: "PLCs y sistemas de control",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 2,
    name: "Variadores",
    description: "Variadores de frecuencia y arrancadores",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    id: 3,
    name: "Sensores",
    description: "Sensores industriales de todo tipo",
    created_at: new Date(),
    updated_at: new Date()
  }
];

// Mock data for almacen
const mockAlmacenProducts = [
  {
    id: 1,
    numero_almacen: 'ALM-001',
    estante: 'A1-01',
    modelo_existente: 'SIMATIC S7-1200 CPU 1214C',
    descripcion: 'Controlador compacto SIMATIC S7-1200, CPU 1214C, DC/DC/DC, 14 DI/10 DO/2 AI',
    precio_venta: '15500.00',
    precio_ml: '16200.00',
    vendidos: 5,
    cantidad_nuevo: 3,
    minimo: 2,
    maximo: 10,
    pedir_cantidad: 0,
    precio_us: '850.00',
    precio_mx: '15500.00',
    impuesto: '16.00',
    codigo_sat: '85371099',
    nota: 'Producto estrella de automatización',
    proveedor: 'SIEMENS México',
    marcas: 'SIEMENS',
    tiempo_entrega_proveedor: '2-3 semanas',
    fecha_pedido: null,
    fecha_recibido: null,
    fecha_creacion: new Date().toISOString(),
    fecha_actualizacion: new Date().toISOString(),
    activo: 1
  },
  {
    id: 2,
    numero_almacen: 'ALM-002',
    estante: 'A1-02',
    modelo_existente: 'SINAMICS G120C 0.75kW',
    descripcion: 'Variador de frecuencia SINAMICS G120C, 0.75kW, 380-480V AC',
    precio_venta: '8900.00',
    precio_ml: '9200.00',
    vendidos: 8,
    cantidad_nuevo: 5,
    minimo: 3,
    maximo: 15,
    pedir_cantidad: 2,
    precio_us: '485.00',
    precio_mx: '8900.00',
    impuesto: '16.00',
    codigo_sat: '85044082',
    nota: 'Variador compacto para aplicaciones básicas',
    proveedor: 'SIEMENS México',
    marcas: 'SIEMENS',
    tiempo_entrega_proveedor: '1-2 semanas',
    fecha_pedido: null,
    fecha_recibido: null,
    fecha_creacion: new Date().toISOString(),
    fecha_actualizacion: new Date().toISOString(),
    activo: 1
  },
  {
    id: 3,
    numero_almacen: 'ALM-003',
    estante: 'B2-05',
    modelo_existente: 'SICK WT100-P132',
    descripcion: 'Sensor fotoeléctrico SICK WT100-P132, alcance 100m, PNP',
    precio_venta: '2850.00',
    precio_ml: '3100.00',
    vendidos: 12,
    cantidad_nuevo: 8,
    minimo: 5,
    maximo: 20,
    pedir_cantidad: 0,
    precio_us: '155.00',
    precio_mx: '2850.00',
    impuesto: '16.00',
    codigo_sat: '85365019',
    nota: 'Sensor de alta precisión para detección',
    proveedor: 'SICK México',
    marcas: 'SICK',
    tiempo_entrega_proveedor: '3-4 semanas',
    fecha_pedido: null,
    fecha_recibido: null,
    fecha_creacion: new Date().toISOString(),
    fecha_actualizacion: new Date().toISOString(),
    activo: 1
  },
  {
    id: 4,
    numero_almacen: 'ALM-004',
    estante: 'C3-10',
    modelo_existente: 'Phoenix Contact QUINT-PS',
    descripcion: 'Fuente de alimentación QUINT-PS/1AC/24DC/10, 24V DC, 10A',
    precio_venta: '4200.00',
    precio_ml: '4500.00',
    vendidos: 3,
    cantidad_nuevo: 2,
    minimo: 1,
    maximo: 8,
    pedir_cantidad: 3,
    precio_us: '230.00',
    precio_mx: '4200.00',
    impuesto: '16.00',
    codigo_sat: '85044030',
    nota: 'Fuente confiable para automatización',
    proveedor: 'Phoenix Contact',
    marcas: 'Phoenix Contact',
    tiempo_entrega_proveedor: '2-3 semanas',
    fecha_pedido: null,
    fecha_recibido: null,
    fecha_creacion: new Date().toISOString(),
    fecha_actualizacion: new Date().toISOString(),
    activo: 1
  },
  {
    id: 5,
    numero_almacen: 'ALM-005',
    estante: 'D1-03',
    modelo_existente: 'Schneider Electric ATV320U15M2C',
    descripcion: 'Variador de velocidad ATV320, 1.5kW, 220V monofásico',
    precio_venta: '6800.00',
    precio_ml: '7200.00',
    vendidos: 6,
    cantidad_nuevo: 4,
    minimo: 2,
    maximo: 12,
    pedir_cantidad: 1,
    precio_us: '370.00',
    precio_mx: '6800.00',
    impuesto: '16.00',
    codigo_sat: '85044082',
    nota: 'Variador para motores pequeños',
    proveedor: 'Schneider Electric',
    marcas: 'Schneider Electric',
    tiempo_entrega_proveedor: '1-2 semanas',
    fecha_pedido: null,
    fecha_recibido: null,
    fecha_creacion: new Date().toISOString(),
    fecha_actualizacion: new Date().toISOString(),
    activo: 1
  }
];

const mockTables = [
  'users',
  'products',
  'categories',
  'orders',
  'order_items',
  'almacen'
];

// Function to get a connection string (for reference)
export function getConnectionString() {
  return `server=${process.env.DB_HOST};uid=${process.env.DB_USER};pwd=${process.env.DB_PASSWORD};database=${process.env.DB_NAME}`;
}

// Function to test database connection
export async function testConnection() {
  try {
    if (useMockDb) {
      console.log('Using mock database for connection test');
      return {
        success: true,
        result: [{ test: 1 }],
        mock: true
      };
    }

    // Try to connect with a timeout
    const result = await Promise.race([
      executeQuery({
        query: 'SELECT 1 as test',
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout')), 2000)
      )
    ]);

    return { success: true, result };
  } catch (error) {
    console.error('Database connection test error:', error);

    // Return mock data as fallback
    return {
      success: true,
      result: [{ test: 1 }],
      mock: true,
      originalError: error instanceof Error ? error.message : String(error)
    };
  }
}

// Function to get all tables in the database
export async function getAllTables() {
  try {
    const result = await executeQuery({
      query: `SHOW TABLES FROM ${process.env.DB_NAME || 'eccsa_db'}`,
    }) as any[];

    return result.map(row => {
      const tableName = row[`Tables_in_${process.env.DB_NAME || 'eccsa_db'}`];
      return tableName;
    });
  } catch (error) {
    console.error('Error getting tables:', error);
    return [];
  }
}

// Function to get record count for a table
export async function getTableRecordCount(tableName: string) {
  try {
    const result = await executeQuery({
      query: `SELECT COUNT(*) as count FROM ${tableName}`,
    }) as any[];

    return result[0].count;
  } catch (error) {
    console.error(`Error getting record count for table ${tableName}:`, error);
    return 0;
  }
}

// Function to get database statistics
export async function getDatabaseStats() {
  try {
    if (useMockDb) {
      console.log('Using mock database for statistics');
      return {
        tables: mockTables.map(tableName => {
          let count = 0;
          if (tableName === 'users') count = 1;
          else if (tableName === 'products') count = mockProducts.length;
          else if (tableName === 'categories') count = mockCategories.length;
          else if (tableName === 'almacen') count = mockAlmacenProducts.length;

          return {
            name: tableName,
            count
          };
        }),
        mock: true
      };
    }

    // Get all tables
    const tables = await getAllTables();

    // Get record count for each table
    const tableStats = [];

    for (const tableName of tables) {
      const count = await getTableRecordCount(tableName);

      tableStats.push({
        name: tableName,
        count
      });
    }

    return {
      tables: tableStats
    };
  } catch (error) {
    console.error('Error getting database stats:', error);

    // Return mock data as fallback
    return {
      tables: mockTables.map(tableName => {
        let count = 0;
        if (tableName === 'users') count = 1;
        else if (tableName === 'products') count = mockProducts.length;
        else if (tableName === 'categories') count = mockCategories.length;
        else if (tableName === 'almacen') count = mockAlmacenProducts.length;

        return {
          name: tableName,
          count
        };
      }),
      mock: true,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export default db;
