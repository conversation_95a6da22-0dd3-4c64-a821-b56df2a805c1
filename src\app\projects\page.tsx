'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaIndustry, FaTools, FaChartLine, FaLightbulb, FaCheck, FaArrowRight, FaCalendarAlt, FaBuilding } from 'react-icons/fa';

// Definición de tipos para los proyectos
interface Project {
  id: string;
  title: string;
  category: string;
  description: string;
  challenge: string;
  solution: string;
  results: string;
  imageSrc: string;
  industry: string;
  year: number;
  duration: string;
  client: string;
  technologies: string[];
  benefits: string[];
}

export default function ProjectsPage() {
  // Categorías de proyectos
  const categories = [
    'Todos',
    'Automatización Industrial',
    'Programación de PLC',
    'Sistemas SCADA',
    'Mantenimiento Industrial',
    'Integración de Sistemas'
  ];

  // Estado para la categoría seleccionada
  const [selectedCategory, setSelectedCategory] = useState('Todos');

  // Datos de proyectos (ejemplos)
  const projects: Project[] = [
    {
      id: 'proyecto-1',
      title: 'Automatización de Línea de Producción',
      category: 'Automatización Industrial',
      description: 'Implementación de un sistema de automatización completo para una línea de producción en una planta de manufactura automotriz.',
      challenge: 'El cliente necesitaba aumentar su capacidad de producción en un 30% mientras reducía los errores humanos y mejoraba la seguridad de los operadores.',
      solution: 'Diseñamos e implementamos un sistema de automatización basado en PLCs Siemens S7-1500, con interfaces HMI y sistema SCADA para monitoreo en tiempo real.',
      results: 'Aumento del 35% en la producción, reducción del 80% en errores de proceso y mejora significativa en la seguridad laboral.',
      imageSrc: '/images/defaults/Automatización-Industrial.jpg',
      industry: 'Automotriz',
      year: 2022,
      duration: '8 meses',
      client: 'Planta Automotriz del Norte',
      technologies: ['Siemens S7-1500', 'WinCC SCADA', 'Profinet', 'Safety Integrated'],
      benefits: ['35% aumento producción', '80% reducción errores', 'Mejora seguridad', 'ROI en 18 meses']
    },
    {
      id: 'proyecto-2',
      title: 'Sistema de Control para Planta Procesadora',
      category: 'Programación de PLC',
      description: 'Desarrollo e implementación de un sistema de control basado en PLC para una planta procesadora de alimentos.',
      challenge: 'La planta requería un sistema de control preciso para mantener parámetros críticos como temperatura y presión dentro de rangos específicos.',
      solution: 'Programamos PLCs Allen-Bradley ControlLogix con comunicación EtherNet/IP para integrar todos los subsistemas de la planta.',
      results: 'Mejora del 40% en la consistencia del producto, reducción del 25% en el consumo energético y aumento del 20% en la eficiencia operativa.',
      imageSrc: '/images/defaults/PROGRAMACION-PLC.jpeg',
      industry: 'Alimentos y Bebidas',
      year: 2023,
      duration: '6 meses',
      client: 'Procesadora de Alimentos SA',
      technologies: ['Allen-Bradley ControlLogix', 'EtherNet/IP', 'FactoryTalk View', 'Historian'],
      benefits: ['40% mejora consistencia', '25% reducción energía', '20% eficiencia operativa', 'Trazabilidad completa']
    },
    {
      id: 'proyecto-3',
      title: 'Sistema SCADA para Monitoreo de Procesos',
      category: 'Sistemas SCADA',
      description: 'Implementación de un sistema SCADA para el monitoreo y control de procesos en una planta química.',
      challenge: 'El cliente necesitaba visualizar y controlar múltiples procesos desde una sala de control centralizada, con alertas en tiempo real y registro histórico de datos.',
      solution: 'Desarrollamos un sistema SCADA utilizando Wonderware InTouch con comunicación OPC a los PLCs existentes y base de datos SQL para almacenamiento histórico.',
      results: 'Reducción del 50% en el tiempo de respuesta a incidentes, mejora del 30% en la calidad del producto y capacidad de análisis de tendencias para optimización continua.',
      imageSrc: '/images/defaults/sistema-scada-gas-natual-eisenberg-monterrey.jpg',
      industry: 'Química',
      year: 2021,
      duration: '10 meses',
      client: 'Petroquímica del Golfo',
      technologies: ['Wonderware InTouch', 'OPC Server', 'SQL Server', 'Historian'],
      benefits: ['50% reducción tiempo respuesta', '30% mejora calidad', 'Análisis predictivo', 'Cumplimiento normativo']
    },
    {
      id: 'proyecto-4',
      title: 'Programa de Mantenimiento Predictivo',
      category: 'Mantenimiento Industrial',
      description: 'Implementación de un programa de mantenimiento predictivo basado en sensores IoT para una planta de generación eléctrica.',
      challenge: 'La planta sufría paradas no programadas costosas debido a fallos inesperados en equipos críticos.',
      solution: 'Instalamos sensores IoT conectados a un sistema de análisis predictivo que monitorea vibraciones, temperatura y otros parámetros clave.',
      results: 'Reducción del 70% en paradas no programadas, aumento del 25% en la vida útil de los equipos y ahorro anual estimado de $1.2 millones en costos de mantenimiento.',
      imageSrc: '/images/defaults/Mantenimiento-Industrial.jpg',
      industry: 'Energía',
      year: 2022,
      duration: '12 meses',
      client: 'Generadora Eléctrica del Centro',
      technologies: ['Sensores IoT', 'Machine Learning', 'Azure IoT', 'Power BI'],
      benefits: ['70% reducción paradas', '25% vida útil equipos', '$1.2M ahorro anual', 'Mantenimiento predictivo']
    },
    {
      id: 'proyecto-5',
      title: 'Integración de Sistemas de Control',
      category: 'Integración de Sistemas',
      description: 'Integración de múltiples sistemas de control existentes en una plataforma unificada para una planta siderúrgica.',
      challenge: 'La planta operaba con sistemas aislados de diferentes fabricantes y épocas, dificultando la operación coordinada y el análisis global.',
      solution: 'Desarrollamos una capa de integración utilizando OPC UA y middleware personalizado para unificar todos los sistemas en una plataforma de control centralizada.',
      results: 'Mejora del 45% en la coordinación entre áreas, reducción del 30% en tiempos de cambio de producto y capacidad de optimización integral de la planta.',
      imageSrc: '/images/defaults/integracao-de-sistemas.jpg',
      industry: 'Siderúrgica',
      year: 2023,
      duration: '14 meses',
      client: 'Aceros del Norte SA',
      technologies: ['OPC UA', 'Middleware', 'Unified Architecture', 'Data Analytics'],
      benefits: ['45% mejora coordinación', '30% reducción cambios', 'Optimización integral', 'Visibilidad total']
    }
  ];

  // Filtrar proyectos por categoría
  const filteredProjects = selectedCategory === 'Todos'
    ? projects
    : projects.filter(project => project.category === selectedCategory);

  return (
    <div className="modern-projects-page">
      {/* Hero Section */}
      <div className="projects-hero">
        <div className="projects-hero-content">
          <div className="hero-badge">
            <span className="badge-icon">🏭</span>
            <span>Casos de Éxito</span>
          </div>
          <h1 className="projects-hero-title">
            Nuestros <span className="title-highlight">Proyectos</span>
          </h1>
          <p className="projects-hero-description">
            Soluciones de automatización industrial que transforman procesos y optimizan resultados.
            Descubre cómo hemos ayudado a empresas líderes a alcanzar sus objetivos.
          </p>
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">500+</span>
              <span className="stat-label">Proyectos</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">25+</span>
              <span className="stat-label">Años</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">100%</span>
              <span className="stat-label">Éxito</span>
            </div>
          </div>
        </div>
        <div className="hero-decoration">
          <div className="decoration-circle decoration-circle-1"></div>
          <div className="decoration-circle decoration-circle-2"></div>
          <div className="decoration-circle decoration-circle-3"></div>
        </div>
      </div>

      {/* Filtro de categorías */}
      <section className="projects-filter-section">
        <div className="filter-container">
          <h2 className="filter-title">Explora Nuestros Proyectos</h2>
          <div className="filter-buttons-grid">
            {categories.map((category) => (
              <button
                key={category}
                className={`modern-filter-button ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                <span className="filter-icon">
                  {category === 'Todos' && '🔍'}
                  {category === 'Automatización Industrial' && '⚙️'}
                  {category === 'Programación de PLC' && '💻'}
                  {category === 'Sistemas SCADA' && '📊'}
                  {category === 'Mantenimiento Industrial' && '🔧'}
                  {category === 'Integración de Sistemas' && '🔗'}
                </span>
                <span className="filter-text">{category}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Lista de proyectos */}
      <section className="projects-showcase">
        <div className="projects-container">
          <div className="projects-grid-modern">
            {filteredProjects.map((project) => (
              <div key={project.id} className="modern-project-card">
                <div className="project-image-container">
                  <img
                    src={project.imageSrc}
                    alt={project.title}
                    className="project-image-modern"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/images/logos/logo_pequeno.png";
                    }}
                  />
                  <div className="project-overlay">
                    <div className="project-category-badge">{project.category}</div>
                    <div className="project-year-badge">{project.year}</div>
                  </div>
                </div>

                <div className="project-content-modern">
                  <div className="project-header">
                    <h3 className="project-title-modern">{project.title}</h3>
                    <div className="project-meta">
                      <span className="meta-item">
                        <FaBuilding className="meta-icon" />
                        {project.industry}
                      </span>
                      <span className="meta-item">
                        <FaCalendarAlt className="meta-icon" />
                        {project.duration}
                      </span>
                    </div>
                  </div>

                  <p className="project-description-modern">{project.description}</p>

                  <div className="project-technologies">
                    <h4 className="tech-title">Tecnologías:</h4>
                    <div className="tech-tags">
                      {project.technologies.map((tech, index) => (
                        <span key={index} className="tech-tag">{tech}</span>
                      ))}
                    </div>
                  </div>

                  <div className="project-benefits">
                    <h4 className="benefits-title">Beneficios Clave:</h4>
                    <div className="benefits-list">
                      {project.benefits.map((benefit, index) => (
                        <div key={index} className="benefit-item">
                          <FaCheck className="benefit-icon" />
                          <span className="benefit-text">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="project-actions">
                    <button className="project-details-btn">
                      Ver Detalles
                      <FaArrowRight className="btn-icon" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Sección CTA */}
      <section className="projects-cta-modern">
        <div className="cta-container-modern">
          <div className="cta-content-modern">
            <div className="cta-header">
              <h2 className="cta-title">¿Listo para tu Próximo Proyecto?</h2>
              <p className="cta-description">
                Transforma tu industria con soluciones de automatización personalizadas.
                Nuestro equipo de expertos está listo para hacer realidad tu visión.
              </p>
            </div>

            <div className="cta-features">
              <div className="cta-feature">
                <div className="feature-icon">⚡</div>
                <div className="feature-content">
                  <h3>Implementación Rápida</h3>
                  <p>Proyectos ejecutados en tiempo récord</p>
                </div>
              </div>
              <div className="cta-feature">
                <div className="feature-icon">🎯</div>
                <div className="feature-content">
                  <h3>Soluciones Personalizadas</h3>
                  <p>Adaptadas a tus necesidades específicas</p>
                </div>
              </div>
              <div className="cta-feature">
                <div className="feature-icon">🏆</div>
                <div className="feature-content">
                  <h3>Resultados Garantizados</h3>
                  <p>25+ años de experiencia comprobada</p>
                </div>
              </div>
            </div>

            <div className="cta-actions">
              <Link href="/contact" className="cta-button-primary">
                <span>Solicitar Cotización</span>
                <FaArrowRight className="button-icon" />
              </Link>
              <Link href="/services" className="cta-button-secondary">
                <span>Ver Servicios</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
