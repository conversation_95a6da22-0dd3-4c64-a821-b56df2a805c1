import { executeQuery } from '@/eccsa_back/lib/db';

/**
 * Interfaz para el modelo de usuario
 */
export interface User {
  id?: number;
  nombre: string;
  nombre_usuario: string;
  foto_usuario?: string;
  correo: string;
  contrasena?: string; // No incluir en respuestas al cliente
  telefono?: string;
  nivel_ingeniero?: number; // 0=Administrador, 1=Je<PERSON>, 2=Senior, 3=Semi-Senior, 4=Junior
  descripcion?: string;
  fecha_creacion?: Date;
  fecha_actualizacion?: Date;
  activo?: boolean;
}

/**
 * Clase para manejar operaciones CRUD de usuarios
 */
export class UserModel {
  /**
   * Obtiene todos los usuarios
   * @returns {Promise<User[]>} Lista de usuarios
   */
  static async getAll(): Promise<User[]> {
    try {
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion,
            COALESCE(activo, 1) as activo
          FROM usuarios
          ORDER BY nombre
        `
      }) as User[];

      return users;
    } catch (error) {
      console.error('Error al obtener usuarios:', error);
      throw error;
    }
  }

  /**
   * Obtiene un usuario por su ID
   * @param {number} id - ID del usuario
   * @returns {Promise<User|null>} Usuario encontrado o null
   */
  static async getById(id: number): Promise<User | null> {
    try {
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion,
            COALESCE(activo, 1) as activo
          FROM usuarios
          WHERE id = ?
        `,
        values: [id]
      }) as User[];

      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error(`Error al obtener usuario con ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Obtiene un usuario por su nombre de usuario
   * @param {string} username - Nombre de usuario
   * @returns {Promise<User|null>} Usuario encontrado o null
   */
  static async getByUsername(username: string): Promise<User | null> {
    try {
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion, activo
          FROM usuarios
          WHERE nombre_usuario = ? AND activo = TRUE
        `,
        values: [username]
      }) as User[];

      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error(`Error al obtener usuario con nombre de usuario ${username}:`, error);
      throw error;
    }
  }

  /**
   * Obtiene un usuario por su correo electrónico
   * @param {string} email - Correo electrónico del usuario
   * @returns {Promise<User|null>} Usuario encontrado o null
   */
  static async getByEmail(email: string): Promise<User | null> {
    try {
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion,
            COALESCE(activo, 1) as activo
          FROM usuarios
          WHERE correo = ?
        `,
        values: [email]
      }) as User[];

      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error(`Error al obtener usuario con email ${email}:`, error);
      throw error;
    }
  }

  /**
   * Crea un nuevo usuario
   * @param {User} user - Datos del usuario
   * @returns {Promise<number>} ID del usuario creado
   */
  static async create(user: User): Promise<number> {
    try {
      const result = await executeQuery({
        query: `
          INSERT INTO usuarios (
            nombre, nombre_usuario, foto_usuario, correo,
            contrasena, telefono, nivel_ingeniero, descripcion, activo
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        values: [
          user.nombre,
          user.nombre_usuario,
          user.foto_usuario || null,
          user.correo,
          user.contrasena,
          user.telefono || null,
          user.nivel_ingeniero !== undefined ? user.nivel_ingeniero : 4, // 4 = Junior por defecto
          user.descripcion || null,
          user.activo !== undefined ? user.activo : true
        ]
      }) as { insertId: number };

      return result.insertId;
    } catch (error) {
      console.error('Error al crear usuario:', error);
      throw error;
    }
  }

  /**
   * Actualiza un usuario existente
   * @param {number} id - ID del usuario
   * @param {Partial<User>} userData - Datos a actualizar
   * @returns {Promise<boolean>} True si se actualizó correctamente
   */
  static async update(id: number, userData: Partial<User>): Promise<boolean> {
    try {
      // Construir la consulta dinámicamente
      const fields: string[] = [];
      const values: any[] = [];

      if ('nombre' in userData) {
        fields.push('nombre = ?');
        values.push(userData.nombre);
      }

      if ('nombre_usuario' in userData) {
        fields.push('nombre_usuario = ?');
        values.push(userData.nombre_usuario);
      }

      if ('foto_usuario' in userData) {
        fields.push('foto_usuario = ?');
        values.push(userData.foto_usuario);
      }

      if ('correo' in userData) {
        fields.push('correo = ?');
        values.push(userData.correo);
      }

      if ('contrasena' in userData) {
        fields.push('contrasena = ?');
        values.push(userData.contrasena);
      }

      if ('telefono' in userData) {
        fields.push('telefono = ?');
        values.push(userData.telefono);
      }

      if ('nivel_ingeniero' in userData) {
        fields.push('nivel_ingeniero = ?');
        values.push(userData.nivel_ingeniero);
      }

      if ('descripcion' in userData) {
        fields.push('descripcion = ?');
        values.push(userData.descripcion);
      }

      if (userData.activo !== undefined) {
        fields.push('activo = ?');
        values.push(userData.activo);
      }

      // Si no hay campos para actualizar, retornar
      if (fields.length === 0) {
        return false;
      }

      // Agregar el ID al final de los valores
      values.push(id);

      const result = await executeQuery({
        query: `
          UPDATE usuarios
          SET ${fields.join(', ')}
          WHERE id = ?
        `,
        values
      }) as { affectedRows: number };

      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error al actualizar usuario con ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Elimina un usuario (marcándolo como inactivo)
   * @param {number} id - ID del usuario
   * @returns {Promise<boolean>} True si se eliminó correctamente
   */
  static async delete(id: number): Promise<boolean> {
    try {
      const result = await executeQuery({
        query: 'UPDATE usuarios SET activo = FALSE WHERE id = ?',
        values: [id]
      }) as { affectedRows: number };

      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error al eliminar usuario con ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Elimina un usuario permanentemente de la base de datos
   * @param {number} id - ID del usuario
   * @returns {Promise<boolean>} True si se eliminó correctamente
   */
  static async deleteForever(id: number): Promise<boolean> {
    try {
      const result = await executeQuery({
        query: 'DELETE FROM usuarios WHERE id = ?',
        values: [id]
      }) as { affectedRows: number };

      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error al eliminar permanentemente usuario con ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Obtiene usuarios por rol/nivel
   * @param {number} nivel - Nivel de ingeniero
   * @returns {Promise<User[]>} Lista de usuarios con ese nivel
   */
  static async getByRole(nivel: number): Promise<User[]> {
    try {
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion,
            COALESCE(activo, 1) as activo
          FROM usuarios
          WHERE nivel_ingeniero = ? AND COALESCE(activo, 1) = 1
        `,
        values: [nivel]
      }) as User[];

      return users;
    } catch (error) {
      console.error(`Error al obtener usuarios con nivel ${nivel}:`, error);
      throw error;
    }
  }

  /**
   * Verifica las credenciales de un usuario
   * @param {string} username - Nombre de usuario o correo
   * @param {string} password - Contraseña
   * @returns {Promise<User|null>} Usuario autenticado o null
   */
  static async authenticate(username: string, password: string): Promise<User | null> {
    try {
      // En producción, deberíamos verificar el hash de la contraseña
      const users = await executeQuery({
        query: `
          SELECT
            id, nombre, nombre_usuario, foto_usuario, correo,
            telefono, nivel_ingeniero, descripcion,
            fecha_creacion, fecha_actualizacion, activo
          FROM usuarios
          WHERE (nombre_usuario = ? OR correo = ?) AND contrasena = ? AND activo = TRUE
        `,
        values: [username, username, password]
      }) as User[];

      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error('Error al autenticar usuario:', error);
      throw error;
    }
  }
}
