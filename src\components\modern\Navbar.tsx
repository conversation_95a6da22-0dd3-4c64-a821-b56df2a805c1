'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { preventBodyScroll } from '@/utils';
import { MAIN_ROUTES, IMAGES } from '@/constants';

/**
 * Modern Navbar component with Tailwind CSS
 * Implements responsive design without traditional media queries
 */
export default function ModernNavbar() {
  const pathname = usePathname();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [cartItems, setCartItems] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // Handle client hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Cart functionality
  const handleCartClick = () => {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('openCart'));
    }
  };

  // Update cart count
  const updateCartCount = () => {
    const savedCart = localStorage.getItem('eccsa-cart');
    if (savedCart) {
      try {
        const cartItems = JSON.parse(savedCart);
        const totalItems = cartItems.reduce((total: number, item: any) => total + (item.cantidad || 0), 0);
        setCartItems(totalItems);
      } catch (error) {
        console.error('Error parsing cart:', error);
        setCartItems(0);
      }
    } else {
      setCartItems(0);
    }
  };

  // Listen for cart changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      updateCartCount();
      window.addEventListener('storage', updateCartCount);
      window.addEventListener('cartUpdated', updateCartCount);
      const interval = setInterval(updateCartCount, 100);

      return () => {
        window.removeEventListener('storage', updateCartCount);
        window.removeEventListener('cartUpdated', updateCartCount);
        clearInterval(interval);
      };
    }
  }, [isClient]);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Prevent scroll when menu is open
  useEffect(() => {
    preventBodyScroll(isMenuOpen);
    return () => preventBodyScroll(false);
  }, [isMenuOpen]);

  // Handle navigation with prefetch
  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setIsMenuOpen(false);

    if (pathname !== href) {
      router.push(href);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Navigation links configuration
  const navLinks = [
    { href: MAIN_ROUTES.HOME, label: 'Nosotros' },
    { href: MAIN_ROUTES.SERVICES, label: 'Servicios' },
    { href: MAIN_ROUTES.PRODUCTS, label: 'Productos' },
    { href: MAIN_ROUTES.PROJECTS, label: 'Proyectos' },
    { href: MAIN_ROUTES.CONTACT, label: 'Contacto' },
  ];

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-soft">
      <nav className="bg-white border-b border-eccsa-gray-200">
        <div className="flex justify-between items-center px-fluid-md lg:px-fluid-xl max-w-7xl mx-auto py-fluid-sm">
          {/* Logo */}
          <div className="flex items-center z-50">
            <Link
              href={MAIN_ROUTES.HOME}
              onClick={(e) => handleNavigation(e, MAIN_ROUTES.HOME)}
              prefetch={true}
              className="block"
            >
              <Image
                src={IMAGES.LOGO.LARGE}
                alt="ECCSA Logo"
                width={180}
                height={40}
                priority
                className="h-8 lg:h-10 w-auto object-contain"
              />
            </Link>
          </div>

          {/* Mobile menu button */}
          <button 
            className="lg:hidden z-50 p-2 text-eccsa-primary hover:bg-eccsa-gray-100 rounded-lg transition-colors"
            onClick={toggleMenu} 
            aria-label="Menú"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span className={`block h-0.5 w-6 bg-current transition-all duration-300 ${
                isMenuOpen ? 'rotate-45 translate-y-0.5' : '-translate-y-1'
              }`} />
              <span className={`block h-0.5 w-6 bg-current transition-all duration-300 ${
                isMenuOpen ? 'opacity-0' : 'opacity-100'
              }`} />
              <span className={`block h-0.5 w-6 bg-current transition-all duration-300 ${
                isMenuOpen ? '-rotate-45 -translate-y-0.5' : 'translate-y-1'
              }`} />
            </div>
          </button>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 ${
                  pathname === link.href 
                    ? 'text-eccsa-primary' 
                    : 'text-eccsa-gray-700 hover:text-eccsa-primary'
                }`}
                onClick={(e) => handleNavigation(e, link.href)}
                prefetch={true}
              >
                <span>{link.label}</span>
                {pathname === link.href && (
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-eccsa-primary rounded-full" />
                )}
              </Link>
            ))}
          </div>

          {/* Cart Icon */}
          <div className="flex items-center">
            <button
              className="relative p-2 text-eccsa-gray-600 hover:text-eccsa-primary hover:bg-eccsa-gray-100 rounded-lg transition-colors"
              onClick={handleCartClick}
              aria-label="Carrito de compras"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6" />
              </svg>
              {isClient && cartItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-eccsa-accent text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItems}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`lg:hidden fixed inset-0 z-40 transition-opacity duration-300 ${
          isMenuOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
        }`}>
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setIsMenuOpen(false)} />
          <div className={`absolute top-0 right-0 h-full w-80 max-w-full bg-white shadow-strong transform transition-transform duration-300 ${
            isMenuOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
            <div className="flex flex-col h-full pt-20 px-6">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`py-4 text-lg font-medium border-b border-eccsa-gray-200 transition-colors ${
                    pathname === link.href 
                      ? 'text-eccsa-primary' 
                      : 'text-eccsa-gray-700 hover:text-eccsa-primary'
                  }`}
                  onClick={(e) => handleNavigation(e, link.href)}
                  prefetch={true}
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </nav>
    </div>
  );
}
