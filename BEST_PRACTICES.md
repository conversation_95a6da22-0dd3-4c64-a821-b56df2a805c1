# 🏆 Mejores Prácticas - Diseño Responsivo Moderno ECCSA

## 🎯 **PRINCIPIOS FUNDAMENTALES**

### **1. Mobile-First + Container-Aware**
```tsx
// ✅ CORRECTO: Diseño que se adapta al contenedor
<div className="grid grid-cols-auto-fit-sm gap-4 lg:gap-8">
  {/* Se adapta automáticamente al espacio disponible */}
</div>

// ❌ INCORRECTO: Breakpoints fijos
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
  {/* Rígi<PERSON>, no se adapta al contenido */}
</div>
```

### **2. Tipografía Fluida Siempre**
```tsx
// ✅ CORRECTO: Escalado suave
<h1 className="text-2xl lg:text-4xl">Título</h1>

// ❌ INCORRECTO: Saltos bruscos
<h1 className="text-xl md:text-3xl lg:text-5xl">T<PERSON><PERSON><PERSON></h1>
```

### **3. Espaciado Consistente**
```tsx
// ✅ CORRECTO: Sistema de espaciado fluido
<Section spacing="lg" className="py-fluid-xl">

// ❌ INCORRECTO: Valores arbitrarios
<div className="py-8 md:py-16 lg:py-24">
```

---

## 🧩 **PATRONES DE COMPONENTES**

### **1. Componente de Tarjeta Responsiva**
```tsx
// ✅ PATRÓN RECOMENDADO
export function ResponsiveCard({ children, hover = true }) {
  return (
    <div className={`
      @container bg-white rounded-xl shadow-soft p-6
      ${hover ? 'hover:shadow-strong hover:-translate-y-1' : ''}
      transition-all duration-300
    `}>
      <div className="@md:flex @md:gap-6">
        {children}
      </div>
    </div>
  );
}
```

### **2. Grid Adaptativo**
```tsx
// ✅ PATRÓN RECOMENDADO
export function AdaptiveGrid({ 
  children, 
  minItemWidth = '300px',
  gap = 'lg' 
}) {
  return (
    <div 
      className={`grid gap-${gap}`}
      style={{
        gridTemplateColumns: `repeat(auto-fit, minmax(min(100%, ${minItemWidth}), 1fr))`
      }}
    >
      {children}
    </div>
  );
}
```

### **3. Navegación Responsiva**
```tsx
// ✅ PATRÓN RECOMENDADO
export function ResponsiveNav() {
  const isMobile = useIsMobile();
  
  return (
    <nav className="flex items-center justify-between p-fluid-md">
      <Logo />
      {isMobile ? <MobileMenu /> : <DesktopMenu />}
    </nav>
  );
}
```

---

## 🎨 **SISTEMA DE DISEÑO**

### **1. Colores Semánticos**
```tsx
// ✅ USAR: Colores semánticos del sistema ECCSA
className="bg-eccsa-primary text-white"
className="text-eccsa-accent hover:text-eccsa-accent-dark"
className="border-eccsa-gray-300"

// ❌ EVITAR: Colores arbitrarios
className="bg-blue-600 text-white"
className="text-orange-500"
```

### **2. Espaciado Fluido**
```tsx
// ✅ USAR: Espaciado fluido del sistema
className="p-fluid-lg gap-fluid-md"
className="py-fluid-xl px-fluid-md"

// ❌ EVITAR: Espaciado fijo
className="p-8 gap-4"
className="py-16 px-6"
```

### **3. Tipografía Escalable**
```tsx
// ✅ USAR: Tipografía que escala
className="text-lg lg:text-xl leading-relaxed"
className="text-2xl lg:text-4xl font-bold"

// ❌ EVITAR: Tamaños fijos
className="text-16px"
className="text-32px"
```

---

## 📱 **RESPONSIVE PATTERNS**

### **1. Layout Adaptativo**
```tsx
// ✅ PATRÓN: Sidebar que se convierte en drawer
export function AdaptiveLayout({ sidebar, children }) {
  const isMobile = useIsMobile();
  
  if (isMobile) {
    return (
      <div className="relative">
        <MobileDrawer>{sidebar}</MobileDrawer>
        <main className="p-fluid-md">{children}</main>
      </div>
    );
  }
  
  return (
    <div className="flex gap-fluid-lg">
      <aside className="w-64 flex-shrink-0">{sidebar}</aside>
      <main className="flex-1 min-w-0">{children}</main>
    </div>
  );
}
```

### **2. Contenido Adaptativo**
```tsx
// ✅ PATRÓN: Contenido que se reorganiza
export function ProductCard({ product }) {
  const isTablet = useIsTablet();
  
  return (
    <Card className="@container">
      <div className={`
        flex gap-4
        ${isTablet ? 'flex-col @md:flex-row' : 'flex-col'}
      `}>
        <ProductImage />
        <ProductInfo />
      </div>
    </Card>
  );
}
```

### **3. Navegación Contextual**
```tsx
// ✅ PATRÓN: Navegación que se adapta al contexto
export function ContextualNav({ items }) {
  const { containerWidth } = useContainerQuery(navRef);
  const visibleItems = Math.floor(containerWidth / 120); // 120px por item
  
  return (
    <nav ref={navRef} className="flex items-center">
      {items.slice(0, visibleItems).map(item => (
        <NavItem key={item.id} {...item} />
      ))}
      {items.length > visibleItems && (
        <MoreMenu items={items.slice(visibleItems)} />
      )}
    </nav>
  );
}
```

---

## ⚡ **OPTIMIZACIÓN DE PERFORMANCE**

### **1. Lazy Loading Inteligente**
```tsx
// ✅ PATRÓN: Lazy loading con intersection observer
export function LazySection({ children, threshold = 0.1 }) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef();
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold }
    );
    
    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, [threshold]);
  
  return (
    <div ref={ref}>
      {isVisible ? children : <Skeleton />}
    </div>
  );
}
```

### **2. Imágenes Responsivas**
```tsx
// ✅ PATRÓN: Imágenes optimizadas
export function ResponsiveImage({ src, alt, ...props }) {
  return (
    <Image
      src={src}
      alt={alt}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className="w-full h-auto object-cover"
      {...props}
    />
  );
}
```

### **3. Bundle Splitting**
```tsx
// ✅ PATRÓN: Componentes lazy
const HeavyComponent = lazy(() => import('./HeavyComponent'));

export function ConditionalRender() {
  const isDesktop = useIsDesktop();
  
  return (
    <Suspense fallback={<Skeleton />}>
      {isDesktop && <HeavyComponent />}
    </Suspense>
  );
}
```

---

## 🔧 **HERRAMIENTAS DE DESARROLLO**

### **1. Debugging Responsivo**
```tsx
// ✅ HERRAMIENTA: Debug de breakpoints
export function BreakpointIndicator() {
  const { currentBreakpoint } = useBreakpoint();
  
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div className="fixed top-0 right-0 bg-black text-white p-2 text-xs z-50">
      {currentBreakpoint}
    </div>
  );
}
```

### **2. Testing Responsivo**
```tsx
// ✅ HERRAMIENTA: Test de responsive
import { render, screen } from '@testing-library/react';
import { ResizeObserver } from '@juggle/resize-observer';

// Mock ResizeObserver para tests
global.ResizeObserver = ResizeObserver;

test('component adapts to mobile', () => {
  // Mock window.innerWidth
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: 375,
  });
  
  render(<ResponsiveComponent />);
  expect(screen.getByTestId('mobile-layout')).toBeInTheDocument();
});
```

---

## 📋 **CHECKLIST DE CALIDAD**

### **✅ Antes de hacer commit:**
- [ ] Componente funciona en todos los breakpoints
- [ ] Tipografía escala correctamente
- [ ] Espaciado es consistente con el sistema
- [ ] Imágenes tienen sizes apropiados
- [ ] No hay scroll horizontal en móvil
- [ ] Animaciones respetan prefers-reduced-motion
- [ ] Contraste cumple WCAG 2.1
- [ ] Componente es accesible con teclado

### **✅ Antes de deploy:**
- [ ] Bundle size no aumentó significativamente
- [ ] Core Web Vitals están optimizados
- [ ] Tests responsivos pasan
- [ ] Performance audit es satisfactorio
- [ ] Cross-browser testing completado

---

## 🚀 **PRÓXIMOS PASOS**

1. **Migrar componentes restantes** siguiendo estos patrones
2. **Implementar testing responsivo** automatizado
3. **Optimizar performance** con lazy loading
4. **Documentar componentes** con Storybook
5. **Eliminar CSS legacy** gradualmente

---

**💡 Recuerda: El diseño responsivo moderno no es solo sobre breakpoints, es sobre crear interfaces que se adaptan inteligentemente al contenido y contexto.**
