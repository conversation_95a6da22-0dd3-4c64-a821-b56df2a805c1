'use client';

import { useState, useEffect } from 'react';
import AuthGuard from '@/components/AuthGuard';

interface User {
  id?: number;
  username: string;
  name?: string;
  role?: string;
  role_level?: number;
  email?: string;
  phone?: string;
  photo?: string;
  description?: string;
  password?: string; // Añadimos la propiedad password para el tipo User
}

export default function EmpleadosPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<User>({
    username: '',
    name: '',
    email: '',
    phone: '',
    role_level: 4, // Por defecto, nivel Junior (4)
    description: ''
  });
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Cargar la lista de usuarios
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/users');
      const data = await response.json();

      if (response.ok && data.success) {
        // Mapear los datos del backend al formato esperado por el frontend
        const mappedUsers = data.data.map((user: any) => ({
          id: user.id,
          username: user.nombre_usuario,
          name: user.nombre,
          email: user.correo,
          phone: user.telefono,
          role_level: user.nivel_ingeniero,
          photo: user.foto_usuario,
          description: user.descripcion
        }));

        setUsers(mappedUsers);
        console.log('Usuarios cargados:', mappedUsers);
      } else {
        setError(data.error || 'Error al cargar los usuarios');
        console.error('Error en la respuesta:', data);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Error de conexión al servidor');
    } finally {
      setLoading(false);
    }
  };

  // Convertir nivel numérico a texto
  const getRoleName = (roleLevel: number | undefined) => {
    if (roleLevel === undefined) return 'Usuario';

    switch (roleLevel) {
      case 0: return 'Administrador';
      case 1: return 'Jefe';
      case 2: return 'Senior';
      case 3: return 'Semi-Senior';
      case 4: return 'Junior';
      default: return 'Usuario';
    }
  };

  // Manejar cambios en el formulario
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'role_level') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Abrir modal para agregar usuario
  const openAddModal = () => {
    setFormData({
      username: '',
      name: '',
      email: '',
      phone: '',
      role_level: 4,
      description: ''
    });
    setPassword('');
    setConfirmPassword('');
    setPasswordError('');
    setShowAddModal(true);
  };

  // Abrir modal para editar usuario
  const openEditModal = (user: User) => {
    setCurrentUser(user);
    setFormData({
      username: user.username,
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || '',
      role_level: user.role_level !== undefined ? user.role_level : 4,
      description: user.description || ''
    });
    setPassword('');
    setConfirmPassword('');
    setPasswordError('');
    setShowEditModal(true);
  };

  // Abrir modal para eliminar usuario
  const openDeleteModal = (user: User) => {
    setCurrentUser(user);
    setShowDeleteModal(true);
  };

  // Cerrar todos los modales
  const closeModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setCurrentUser(null);
  };

  // Agregar un nuevo usuario
  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      setPasswordError('Las contraseñas no coinciden');
      return;
    }

    if (password.length < 6) {
      setPasswordError('La contraseña debe tener al menos 6 caracteres');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Mapear los datos del frontend al formato esperado por el backend
      const userData = {
        nombre: formData.name,
        nombre_usuario: formData.username,
        correo: formData.email,
        contrasena: password,
        telefono: formData.phone,
        nivel_ingeniero: formData.role_level,
        descripcion: formData.description
      };

      console.log('Enviando datos:', userData);

      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();
      console.log('Respuesta del servidor:', data);

      if (response.ok && data.success) {
        setSuccessMessage('Usuario agregado correctamente');
        closeModals();
        fetchUsers();

        // Ocultar mensaje después de 3 segundos
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } else {
        setError(data.error || 'Error al agregar el usuario');
      }
    } catch (err) {
      console.error('Error adding user:', err);
      setError('Error de conexión al servidor');
    } finally {
      setLoading(false);
    }
  };

  // Actualizar un usuario existente
  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password && password !== confirmPassword) {
      setPasswordError('Las contraseñas no coinciden');
      return;
    }

    if (password && password.length < 6) {
      setPasswordError('La contraseña debe tener al menos 6 caracteres');
      return;
    }

    if (!currentUser?.id) return;

    setLoading(true);
    setError('');

    try {
      // Mapear los datos del frontend al formato esperado por el backend
      const userData: any = {
        nombre: formData.name,
        nombre_usuario: formData.username,
        correo: formData.email,
        telefono: formData.phone,
        nivel_ingeniero: formData.role_level,
        descripcion: formData.description
      };

      // Agregar la contraseña solo si se proporcionó una nueva
      if (password) {
        userData.contrasena = password;
      }

      console.log('Actualizando usuario:', userData);

      const response = await fetch(`/api/users/${currentUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();
      console.log('Respuesta del servidor:', data);

      if (response.ok && data.success) {
        setSuccessMessage('Usuario actualizado correctamente');
        closeModals();
        fetchUsers();

        // Ocultar mensaje después de 3 segundos
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } else {
        setError(data.error || 'Error al actualizar el usuario');
      }
    } catch (err) {
      console.error('Error updating user:', err);
      setError('Error de conexión al servidor');
    } finally {
      setLoading(false);
    }
  };

  // Eliminar un usuario
  const handleDeleteUser = async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    setError('');

    try {
      console.log('Eliminando usuario con ID:', currentUser.id);

      const response = await fetch(`/api/users/${currentUser.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      console.log('Respuesta del servidor:', data);

      if (response.ok && data.success) {
        setSuccessMessage('Usuario eliminado correctamente');
        closeModals();
        fetchUsers();

        // Ocultar mensaje después de 3 segundos
        setTimeout(() => {
          setSuccessMessage('');
        }, 3000);
      } else {
        setError(data.error || 'Error al eliminar el usuario');
      }
    } catch (err) {
      console.error('Error deleting user:', err);
      setError('Error de conexión al servidor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthGuard>
      <div className="modern-employees-page">
        {/* Header moderno */}
        <div className="employees-header">
          <div className="header-content">
            <div className="header-title-section">
              <div className="title-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <div className="title-text">
                <h1 className="employees-title">Gestión de Empleados</h1>
                <p className="employees-subtitle">Administra el equipo y sus permisos</p>
              </div>
            </div>
            <div className="header-actions">
              <button
                className="refresh-button"
                onClick={fetchUsers}
                disabled={loading}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 20 1 14 7 14"></polyline>
                  <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                {loading ? 'Actualizando...' : 'Actualizar'}
              </button>
              <button
                className="add-employee-button"
                onClick={openAddModal}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Agregar Empleado
              </button>
            </div>
          </div>
        </div>

        {/* Estadísticas de empleados */}
        <div className="employees-stats">
          <div className="stats-grid">
            <div className="stat-card primary">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{users.length}</div>
                <div className="stat-label">Total Empleados</div>
              </div>
            </div>

            <div className="stat-card danger">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{users.filter(u => u.role_level === 0).length}</div>
                <div className="stat-label">Administradores</div>
              </div>
            </div>

            <div className="stat-card warning">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{users.filter(u => u.role_level === 1).length}</div>
                <div className="stat-label">Jefes</div>
              </div>
            </div>

            <div className="stat-card success">
              <div className="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <div className="stat-content">
                <div className="stat-value">{users.filter(u => (u.role_level || 0) >= 2).length}</div>
                <div className="stat-label">Ingenieros</div>
              </div>
            </div>
          </div>
        </div>

        {/* Mensajes de estado */}
        {successMessage && (
          <div className="success-message">
            <div className="message-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </div>
            <span>{successMessage}</span>
          </div>
        )}

        {error && (
          <div className="error-message">
            <div className="message-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
            </div>
            <span>{error}</span>
          </div>
        )}

        {/* Sección de empleados moderna */}
        <div className="employees-content">
          <div className="employees-header-section">
            <div className="employees-title-section">
              <h2>Lista de Empleados</h2>
              <p>Gestiona los miembros del equipo y sus roles</p>
            </div>
          </div>

          <div className="employees-list">
            {loading && !users.length ? (
              <div className="loading-state">
                <div className="loading-spinner">
                  <div className="spinner"></div>
                </div>
                <h3>Cargando empleados...</h3>
                <p>Obteniendo información del equipo</p>
              </div>
            ) : users.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <h3>No hay empleados registrados</h3>
                <p>Comienza agregando el primer miembro del equipo para gestionar los permisos y roles.</p>
                <button
                  className="add-first-employee-button"
                  onClick={openAddModal}
                >
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Agregar Primer Empleado
                </button>
              </div>
            ) : (
              <>
              {/* Tabla para desktop */}
              <div className="admin-table-container desktop-only">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>Nombre</th>
                      <th>Usuario</th>
                      <th>Correo</th>
                      <th>Nivel</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.length > 0 ? (
                      users.map(user => (
                        <tr key={user.id}>
                          <td>{user.name || '-'}</td>
                          <td>{user.username}</td>
                          <td>{user.email || '-'}</td>
                          <td>{getRoleName(user.role_level)}</td>
                          <td>
                            <div className="admin-table-actions">
                              <button
                                className="admin-action-button admin-edit-button"
                                onClick={() => openEditModal(user)}
                                title="Editar empleado"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
                                  <polygon points="18 2 22 6 12 16 8 16 8 12 18 2"></polygon>
                                </svg>
                              </button>
                              <button
                                className="admin-action-button admin-delete-button"
                                onClick={() => openDeleteModal(user)}
                                title="Eliminar empleado"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polyline points="3 6 5 6 21 6"></polyline>
                                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                  <line x1="10" y1="11" x2="10" y2="17"></line>
                                  <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="admin-empty">
                          No hay empleados registrados
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Vista móvil en cards */}
              <div className="admin-table-mobile mobile-only">
                {users.length > 0 ? (
                  users.map(user => (
                    <div key={user.id} className="admin-card-item">
                      <div className="admin-card-item-header">
                        <div className="admin-card-item-title">
                          {user.name || user.username}
                        </div>
                        <span className="admin-role-badge" style={{
                          backgroundColor: user.role_level === 0 ? '#dc3545' :
                                         user.role_level === 1 ? '#fd7e14' :
                                         user.role_level === 2 ? '#198754' :
                                         user.role_level === 3 ? '#0dcaf0' : '#6c757d',
                          color: 'white',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '11px',
                          fontWeight: 'bold'
                        }}>
                          {getRoleName(user.role_level)}
                        </span>
                      </div>
                      <div className="admin-card-item-content">
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Usuario</div>
                          <div className="admin-card-item-value">{user.username}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Correo</div>
                          <div className="admin-card-item-value">{user.email || '-'}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Teléfono</div>
                          <div className="admin-card-item-value">{user.phone || '-'}</div>
                        </div>
                        <div className="admin-card-item-field">
                          <div className="admin-card-item-label">Descripción</div>
                          <div className="admin-card-item-value">{user.description || '-'}</div>
                        </div>
                      </div>
                      <div className="admin-table-actions" style={{ marginTop: '10px', justifyContent: 'center' }}>
                        <button
                          className="admin-action-button admin-edit-button"
                          onClick={() => openEditModal(user)}
                          title="Editar empleado"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
                            <polygon points="18 2 22 6 12 16 8 16 8 12 18 2"></polygon>
                          </svg>
                        </button>
                        <button
                          className="admin-action-button admin-delete-button"
                          onClick={() => openDeleteModal(user)}
                          title="Eliminar empleado"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="admin-empty">
                    <p>No hay empleados registrados</p>
                    <p>Agrega empleados para comenzar a gestionar el equipo.</p>
                  </div>
                )}
              </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Modal para agregar usuario */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>Agregar Empleado</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <form onSubmit={handleAddUser} className="modal-form">
              <div className="modal-form-group">
                <label htmlFor="username">Nombre de Usuario*</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="name">Nombre Completo*</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="email">Correo Electrónico*</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="phone">Teléfono</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="role_level">Nivel*</label>
                <select
                  id="role_level"
                  name="role_level"
                  value={formData.role_level}
                  onChange={handleInputChange}
                  required
                >
                  <option value={0}>Administrador</option>
                  <option value={1}>Jefe</option>
                  <option value={2}>Senior</option>
                  <option value={3}>Semi-Senior</option>
                  <option value={4}>Junior</option>
                </select>
              </div>

              <div className="modal-form-group">
                <label htmlFor="password">Contraseña*</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="confirmPassword">Confirmar Contraseña*</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
                {passwordError && <div className="modal-form-error">{passwordError}</div>}
              </div>

              <div className="modal-form-group">
                <label htmlFor="description">Descripción</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="modal-actions">
                <button type="button" className="modal-cancel" onClick={closeModals}>
                  Cancelar
                </button>
                <button type="submit" className="modal-submit" disabled={loading}>
                  {loading ? 'Guardando...' : 'Guardar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal para editar usuario */}
      {showEditModal && currentUser && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>Editar Empleado</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <form onSubmit={handleUpdateUser} className="modal-form">
              <div className="modal-form-group">
                <label htmlFor="username">Nombre de Usuario*</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="name">Nombre Completo*</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="email">Correo Electrónico*</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="phone">Teléfono</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="role_level">Nivel*</label>
                <select
                  id="role_level"
                  name="role_level"
                  value={formData.role_level}
                  onChange={handleInputChange}
                  required
                >
                  <option value={0}>Administrador</option>
                  <option value={1}>Jefe</option>
                  <option value={2}>Senior</option>
                  <option value={3}>Semi-Senior</option>
                  <option value={4}>Junior</option>
                </select>
              </div>

              <div className="modal-form-group">
                <label htmlFor="password">Nueva Contraseña (dejar en blanco para mantener la actual)</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>

              <div className="modal-form-group">
                <label htmlFor="confirmPassword">Confirmar Nueva Contraseña</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
                {passwordError && <div className="modal-form-error">{passwordError}</div>}
              </div>

              <div className="modal-form-group">
                <label htmlFor="description">Descripción</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="modal-actions">
                <button type="button" className="modal-cancel" onClick={closeModals}>
                  Cancelar
                </button>
                <button type="submit" className="modal-submit" disabled={loading}>
                  {loading ? 'Guardando...' : 'Guardar Cambios'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal para eliminar usuario */}
      {showDeleteModal && currentUser && (
        <div className="modal-overlay">
          <div className="modal-container modal-small">
            <div className="modal-header">
              <h2>Eliminar Empleado</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>
            <div className="modal-content">
              <div className="modal-warning-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#e74c3c" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
              </div>
              <div className="modal-warning-content">
                <p>¿Está seguro de que desea <strong>eliminar permanentemente</strong> al empleado?</p>
                <div className="modal-product-info">
                  <div><strong>Nombre:</strong> {currentUser.name || currentUser.username}</div>
                  <div><strong>Usuario:</strong> {currentUser.username}</div>
                  <div><strong>Correo:</strong> {currentUser.email || 'No especificado'}</div>
                  <div><strong>Rol:</strong> {getRoleName(currentUser.role_level)}</div>
                </div>
                <div className="modal-warning-text">
                  ⚠️ <strong>ADVERTENCIA:</strong> Esta acción eliminará completamente el empleado de la base de datos.
                  No se puede deshacer y se perderán todos los datos asociados.
                </div>
              </div>
            </div>
            <div className="modal-actions">
              <button type="button" className="modal-cancel" onClick={closeModals}>
                Cancelar
              </button>
              <button
                type="button"
                className="modal-delete"
                onClick={handleDeleteUser}
                disabled={loading}
              >
                {loading ? 'Eliminando...' : 'Eliminar Permanentemente'}
              </button>
            </div>
          </div>
        </div>
      )}
    </AuthGuard>
  );
}
