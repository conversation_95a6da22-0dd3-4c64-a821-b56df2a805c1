'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function EccsaAdminLoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Verificar si ya está autenticado al cargar la página
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        const response = await fetch('/api/auth/login', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          if (data.authenticated) {
            // Si ya está autenticado, redirigir al dashboard
            router.push('/eccsa/admin');
            return;
          }
        }
      } catch (error) {
        console.error('Session check error:', error);
      }
    };

    checkExistingSession();
  }, [router]);

  // Redirigir si ya está autenticado
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      router.push('/eccsa/admin');
    }
  }, [authLoading, isAuthenticated, router]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      console.log('Attempting login...');
      const result = await login(username, password);

      if (result.success) {
        console.log('Login successful, user:', result.user?.name);
        console.log('Reloading page to dashboard...');

        // Force a complete page reload to the dashboard
        // This ensures all state is fresh and sidebar appears correctly
        window.location.href = '/eccsa/admin';
      } else {
        console.log('Login failed:', result.error);
        setError(result.error || 'Usuario o contraseña incorrectos');
        setIsLoading(false);
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Error de conexión. Intente nuevamente.');
      setIsLoading(false);
    }
  };

  // Mostrar loading mientras se verifica la autenticación
  if (authLoading) {
    return (
      <div className="modern-login-page">
        <div className="modern-login-background">
          <div className="modern-login-shapes">
            <div className="shape shape-1"></div>
            <div className="shape shape-2"></div>
            <div className="shape shape-3"></div>
            <div className="shape shape-4"></div>
          </div>
        </div>
        <div className="modern-login-container">
          <div className="modern-login-card">
            <div className="modern-admin-loading">
              <div className="modern-admin-spinner"></div>
              <p>Verificando autenticación...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modern-login-page">
      {/* Fondo animado */}
      <div className="modern-login-background">
        <div className="modern-login-shapes">
          <div className="shape shape-1"></div>
          <div className="shape shape-2"></div>
          <div className="shape shape-3"></div>
          <div className="shape shape-4"></div>
        </div>
      </div>

      {/* Contenedor principal */}
      <div className="modern-login-container">
        {/* Tarjeta de login */}
        <div className="modern-login-card">
          {/* Header con logo */}
          <div className="modern-login-header">
            <div className="modern-login-logo-container">
              <img
                src="/images/logos/logo_pequeno.png"
                alt="ECCSA Logo"
                className="modern-login-logo"
              />
            </div>
            <h1 className="modern-login-title">ECCSA Admin</h1>
            <p className="modern-login-subtitle">
              Sistema de Automatización Industrial
            </p>
          </div>

          {/* Formulario */}
          <div className="modern-login-form-container">
            {error && (
              <div className="modern-login-error">
                <svg className="modern-error-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                {error}
              </div>
            )}

            <form onSubmit={handleLogin} className="modern-login-form">
              <div className="modern-input-group">
                <label className="modern-input-label">Usuario</label>
                <div className="modern-input-wrapper">
                  <svg className="modern-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  <input
                    type="text"
                    placeholder="Ingresa tu usuario"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="modern-input"
                    required
                    autoComplete="username"
                  />
                </div>
              </div>

              <div className="modern-input-group">
                <label className="modern-input-label">Contraseña</label>
                <div className="modern-input-wrapper">
                  <svg className="modern-input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <circle cx="12" cy="16" r="1"></circle>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  <input
                    type="password"
                    placeholder="Ingresa tu contraseña"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="modern-input"
                    required
                    autoComplete="current-password"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="modern-login-button"
              >
                {isLoading ? (
                  <>
                    <svg className="modern-spinner" width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" className="opacity-25"></circle>
                      <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" className="opacity-75"></path>
                    </svg>
                    Iniciando sesión...
                  </>
                ) : (
                  <>
                    <svg className="modern-button-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                      <polyline points="10,17 15,12 10,7"></polyline>
                      <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    Acceder al Sistema
                  </>
                )}
              </button>
            </form>

            {/* Footer */}
            <div className="modern-login-footer">
              <div className="modern-copyright">
                <p>© 2024 ECCSA - Automatización Industrial</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
