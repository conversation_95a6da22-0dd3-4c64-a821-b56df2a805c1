'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { NotificationModal, DeleteModal, FormModal } from '@/components/admin/ModernModals';

interface Solicitud {
  id: number;
  tipo_solicitud?: 'individual' | 'carrito';
  solicitud_grupo_id?: string;
  numero_almacen: string;
  modelo: string;
  marca?: string;
  descripcion?: string;
  estante?: string;
  precio_unitario?: number;
  cantidad_solicitada: number;
  cantidad_disponible?: number;
  cliente_email: string;
  cliente_whatsapp?: string;
  estado: 'pendiente' | 'procesando' | 'cotizado' | 'rechazado';
  alerta_stock?: boolean;
  tiempo_entrega_estimado?: string;
  notas?: string;
  productos_json?: string;
  fecha_solicitud: string;
  fecha_actualizacion?: string;
  Nombre?: string;
  // Campos calculados del JOIN con almacen
  modelo_existente?: string;
  marcas?: string;
  descripcion_producto?: string;
  stock_actual?: number;
}

export default function SolicitudesPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [solicitudes, setSolicitudes] = useState<Solicitud[]>([]);
  const [filteredSolicitudes, setFilteredSolicitudes] = useState<Solicitud[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEstado, setSelectedEstado] = useState('');
  const [selectedTipo, setSelectedTipo] = useState('');

  // Estados para modales
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedSolicitud, setSelectedSolicitud] = useState<Solicitud | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Estados para modales de notificación
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [modalTitle, setModalTitle] = useState('');

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/eccsa/admin/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Load solicitudes
  const loadSolicitudes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/solicitudes');
      const data = await response.json();

      if (response.ok && data.success) {
        const solicitudesData = data.solicitudes || [];
        setSolicitudes(solicitudesData);
        setFilteredSolicitudes(solicitudesData);
      }
    } catch (error) {
      console.error('Error loading solicitudes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSolicitudes();
  }, []);

  // Filter solicitudes
  useEffect(() => {
    let filtered = solicitudes.filter(solicitud => {
      const matchesSearch =
        (solicitud.Nombre && solicitud.Nombre.toLowerCase().includes(searchTerm.toLowerCase())) ||
        solicitud.cliente_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        solicitud.modelo.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (solicitud.marca && solicitud.marca.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (solicitud.numero_almacen && solicitud.numero_almacen.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (solicitud.solicitud_grupo_id && solicitud.solicitud_grupo_id.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesEstado = !selectedEstado || solicitud.estado === selectedEstado;
      const matchesTipo = !selectedTipo || solicitud.tipo_solicitud === selectedTipo;

      return matchesSearch && matchesEstado && matchesTipo;
    });

    setFilteredSolicitudes(filtered);
  }, [solicitudes, searchTerm, selectedEstado, selectedTipo]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  const getEstadoBadgeClass = (estado: string): string => {
    switch (estado) {
      case 'pendiente': return 'admin-badge-warning';
      case 'procesando': return 'admin-badge-info';
      case 'cotizado': return 'admin-badge-success';
      case 'rechazado': return 'admin-badge-danger';
      default: return 'admin-badge-secondary';
    }
  };

  const getTipoBadgeClass = (tipo: string): string => {
    switch (tipo) {
      case 'individual': return 'admin-badge-primary';
      case 'carrito': return 'admin-badge-success';
      default: return 'admin-badge-secondary';
    }
  };

  // Funciones para mostrar modales de notificación
  const showSuccessNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowSuccessModal(true);
  };

  const showErrorNotification = (title: string, message: string) => {
    setModalTitle(title);
    setModalMessage(message);
    setShowErrorModal(true);
  };

  // Funciones para manejar acciones
  const handleViewSolicitud = (solicitud: Solicitud) => {
    setSelectedSolicitud(solicitud);
    setShowViewModal(true);
  };

  const handleEditSolicitud = (solicitud: Solicitud) => {
    setSelectedSolicitud(solicitud);
    setShowEditModal(true);
  };

  const handleDeleteSolicitud = (solicitud: Solicitud) => {
    setSelectedSolicitud(solicitud);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedSolicitud) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/solicitudes/${selectedSolicitud.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Actualizar la lista local
        setSolicitudes(prev => prev.filter(s => s.id !== selectedSolicitud.id));
        setShowDeleteModal(false);
        setSelectedSolicitud(null);
        showSuccessNotification(
          'Solicitud Eliminada',
          'La solicitud ha sido eliminada exitosamente del sistema.'
        );
      } else {
        throw new Error(data.error || 'Error al eliminar solicitud');
      }
    } catch (error) {
      console.error('Error al eliminar solicitud:', error);
      showErrorNotification(
        'Error al Eliminar',
        'No se pudo eliminar la solicitud. Por favor, intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const updateSolicitud = async (updatedData: { estado: 'pendiente' | 'procesando' | 'cotizado' | 'rechazado'; notas?: string }) => {
    if (!selectedSolicitud) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/solicitudes/${selectedSolicitud.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Actualizar la lista local
        setSolicitudes(prev => prev.map(s =>
          s.id === selectedSolicitud.id
            ? { ...s, ...updatedData, fecha_actualizacion: new Date().toISOString() }
            : s
        ));
        setShowEditModal(false);
        setSelectedSolicitud(null);
        showSuccessNotification(
          'Solicitud Actualizada',
          'Los cambios han sido guardados exitosamente.'
        );
      } else {
        throw new Error(data.error || 'Error al actualizar solicitud');
      }
    } catch (error) {
      console.error('Error al actualizar solicitud:', error);
      showErrorNotification(
        'Error al Actualizar',
        'No se pudo actualizar la solicitud. Por favor, intenta de nuevo.'
      );
    } finally {
      setActionLoading(false);
    }
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem'
      }}>
        Verificando autenticación...
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="modern-admin-page">
      {/* Header */}
      <div className="modern-admin-header">
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div style={{
            background: 'linear-gradient(135deg, #f7941d, #e67e22)',
            padding: '1rem',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              <path d="M13 8l-3 3 3 3"></path>
            </svg>
          </div>
          <div>
            <h1 className="modern-admin-title">Gestión de Solicitudes</h1>
            <p style={{ color: '#64748b', margin: '0.5rem 0 0 0', fontSize: '1rem' }}>
              Administrar solicitudes de cotización y carrito de compras
            </p>
          </div>
        </div>
        <div className="modern-admin-actions">
          <button
            className="modern-admin-button modern-admin-button-secondary"
            onClick={loadSolicitudes}
            disabled={loading}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.75rem 1.5rem',
              background: loading ? '#f1f5f9' : 'linear-gradient(135deg, #0056a6, #003366)',
              color: loading ? '#64748b' : 'white',
              border: 'none',
              borderRadius: '8px',
              fontWeight: '600',
              cursor: loading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            {loading ? (
              <>
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid rgba(100,116,139,0.3)',
                  borderTop: '2px solid #64748b',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                Actualizando...
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Actualizar
              </>
            )}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="modern-admin-card">
        <div className="modern-admin-card-content">
          <div className="admin-filters" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            alignItems: 'end'
          }}>
            <div className="admin-filter-group">
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Buscar Solicitudes
              </label>
              <input
                type="text"
                placeholder="Cliente, email, producto, código..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="modern-admin-input"
                style={{
                  paddingLeft: '2.5rem',
                  backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 24 24\' stroke=\'%236b7280\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'2\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E")',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0.75rem center',
                  backgroundSize: '1rem'
                }}
              />
            </div>
            <div className="admin-filter-group">
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Estado
              </label>
              <select
                value={selectedEstado}
                onChange={(e) => setSelectedEstado(e.target.value)}
                className="modern-admin-select"
              >
                <option value="">Todos los estados</option>
                <option value="pendiente">Pendiente</option>
                <option value="procesando">Procesando</option>
                <option value="cotizado">Cotizado</option>
                <option value="rechazado">Rechazado</option>
              </select>
            </div>
            <div className="admin-filter-group">
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '0.875rem'
              }}>
                Tipo de Solicitud
              </label>
              <select
                value={selectedTipo}
                onChange={(e) => setSelectedTipo(e.target.value)}
                className="modern-admin-select"
              >
                <option value="">Todos los tipos</option>
                <option value="individual">Individual</option>
                <option value="carrito">Carrito</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Solicitudes Table */}
      <div className="modern-admin-card">
        <div className="modern-admin-card-header">
          <h2 className="modern-admin-card-title" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            Solicitudes de Cotización
            <span style={{
              background: 'linear-gradient(135deg, #f7941d, #e67e22)',
              color: 'white',
              padding: '0.25rem 0.75rem',
              borderRadius: '20px',
              fontSize: '0.875rem',
              fontWeight: '600'
            }}>
              {filteredSolicitudes.length}
            </span>
          </h2>
        </div>
        <div className="modern-admin-card-content">
          {loading ? (
            <div className="modern-admin-loading">
              <div className="modern-admin-spinner"></div>
              <p>Cargando solicitudes...</p>
            </div>
          ) : filteredSolicitudes.length === 0 ? (
            <div className="modern-admin-empty">
              <div className="modern-admin-empty-icon">
                <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="modern-admin-empty-title">No se encontraron solicitudes</h3>
              <p className="modern-admin-empty-text">No hay solicitudes que coincidan con los filtros aplicados.</p>
            </div>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th style={{ textAlign: 'left' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                          <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        Cliente
                      </div>
                    </th>
                    <th style={{ textAlign: 'left' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Producto/Carrito
                      </div>
                    </th>
                    <th style={{ textAlign: 'center' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4m-6 0V9a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2m-6 0h6"></path>
                        </svg>
                        Cantidad
                      </div>
                    </th>
                    <th style={{ textAlign: 'right' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'flex-end' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <line x1="12" y1="1" x2="12" y2="23"></line>
                          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                        </svg>
                        Precio
                      </div>
                    </th>
                    <th style={{ textAlign: 'center' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 0 1 0 2.828l-7 7a2 2 0 0 1-2.828 0l-7-7A1.994 1.994 0 0 1 2 12V7a5 5 0 0 1 5-5z"></path>
                        </svg>
                        Tipo
                      </div>
                    </th>
                    <th style={{ textAlign: 'center' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline>
                        </svg>
                        Estado
                      </div>
                    </th>
                    <th style={{ textAlign: 'center' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        Fecha
                      </div>
                    </th>
                    <th style={{ textAlign: 'center' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', justifyContent: 'center' }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="3"></circle>
                          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        Acciones
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSolicitudes.map((solicitud) => (
                    <tr key={solicitud.id}>
                      <td>
                        <div className="admin-client-info">
                          <div className="admin-client-name">
                            {solicitud.Nombre || 'Cliente sin nombre'}
                          </div>
                          <div className="admin-client-email">{solicitud.cliente_email}</div>
                          {solicitud.cliente_whatsapp && (
                            <div className="admin-client-phone">
                              {solicitud.cliente_whatsapp}
                            </div>
                          )}
                          {solicitud.solicitud_grupo_id && (
                            <div style={{
                              fontSize: '0.75rem',
                              color: '#6b7280',
                              marginTop: '0.25rem'
                            }}>
                              ID: {solicitud.solicitud_grupo_id}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div>
                          <div style={{ fontWeight: '600', color: '#1e293b' }}>
                            {solicitud.modelo}
                          </div>
                          {solicitud.marca && (
                            <div style={{
                              fontSize: '0.875rem',
                              color: '#64748b',
                              marginTop: '0.25rem'
                            }}>
                              {solicitud.marca}
                            </div>
                          )}
                          <div style={{
                            fontSize: '0.75rem',
                            color: '#6b7280',
                            marginTop: '0.25rem'
                          }}>
                            ALM: {solicitud.numero_almacen}
                          </div>
                          {solicitud.alerta_stock && (
                            <div style={{
                              fontSize: '0.75rem',
                              color: '#dc2626',
                              marginTop: '0.25rem',
                              fontWeight: '600'
                            }}>
                              Stock limitado
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: '1.1rem',
                            fontWeight: '600',
                            color: '#1e293b'
                          }}>
                            {solicitud.cantidad_solicitada}
                          </div>
                          {solicitud.cantidad_disponible !== undefined && (
                            <div style={{
                              fontSize: '0.75rem',
                              color: '#64748b'
                            }}>
                              Stock: {solicitud.cantidad_disponible}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div style={{ textAlign: 'right' }}>
                          {solicitud.precio_unitario ? (
                            <>
                              <div style={{
                                fontSize: '1rem',
                                fontWeight: '600',
                                color: '#0056a6'
                              }}>
                                {formatPrice(solicitud.precio_unitario)}
                              </div>
                              {solicitud.cantidad_solicitada > 1 && (
                                <div style={{
                                  fontSize: '0.75rem',
                                  color: '#64748b'
                                }}>
                                  Total: {formatPrice(solicitud.precio_unitario * solicitud.cantidad_solicitada)}
                                </div>
                              )}
                            </>
                          ) : (
                            <span style={{ color: '#64748b' }}>Consultar</span>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className={`admin-badge ${getTipoBadgeClass(solicitud.tipo_solicitud || 'individual')}`}>
                          {solicitud.tipo_solicitud === 'carrito' ? 'Carrito' : 'Individual'}
                        </span>
                      </td>
                      <td>
                        <span className={`admin-badge ${getEstadoBadgeClass(solicitud.estado)}`}>
                          {solicitud.estado}
                        </span>
                      </td>
                      <td className="admin-table-date">
                        {formatDate(solicitud.fecha_solicitud)}
                      </td>
                      <td>
                        <div className="admin-table-actions">
                          <button
                            onClick={() => handleViewSolicitud(solicitud)}
                            className="admin-action-btn view"
                            title="Ver detalles completos"
                            style={{
                              backgroundColor: '#f0f9ff',
                              color: '#0369a1',
                              border: '1px solid #bae6fd',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                          >
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            Ver
                          </button>
                          <button
                            onClick={() => handleEditSolicitud(solicitud)}
                            className="admin-action-btn edit"
                            title="Actualizar estado y notas"
                            style={{
                              backgroundColor: '#f0fdf4',
                              color: '#166534',
                              border: '1px solid #bbf7d0',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                          >
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Editar
                          </button>
                          <button
                            onClick={() => handleDeleteSolicitud(solicitud)}
                            className="admin-action-btn delete"
                            title="Eliminar solicitud"
                            style={{
                              backgroundColor: '#fef2f2',
                              color: '#dc2626',
                              border: '1px solid #fecaca',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              padding: '0.5rem 0.75rem',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                          >
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Eliminar
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modales */}
      {showViewModal && selectedSolicitud && (
        <ViewSolicitudModal
          solicitud={selectedSolicitud}
          onClose={() => {
            setShowViewModal(false);
            setSelectedSolicitud(null);
          }}
        />
      )}

      {showEditModal && selectedSolicitud && (
        <EditSolicitudModal
          solicitud={selectedSolicitud}
          onClose={() => {
            setShowEditModal(false);
            setSelectedSolicitud(null);
          }}
          onUpdate={updateSolicitud}
          loading={actionLoading}
        />
      )}

      {/* Modal de Eliminación Moderno */}
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedSolicitud(null);
        }}
        onConfirm={confirmDelete}
        loading={actionLoading}
        title="Eliminar Solicitud"
        itemName={selectedSolicitud ? `${selectedSolicitud.Nombre || 'Cliente'} - ${selectedSolicitud.tipo_solicitud}` : ''}
        itemDetails={selectedSolicitud ? `Cliente: ${selectedSolicitud.Nombre || 'Sin nombre'}\nEmail: ${selectedSolicitud.cliente_email}\nTipo: ${selectedSolicitud.tipo_solicitud === 'carrito' ? 'Carrito' : 'Individual'}\nEstado: ${selectedSolicitud.estado}` : ''}
        warningMessage="Esta acción eliminará permanentemente esta solicitud del sistema."
      />

      {/* Modales de Notificación Modernos */}
      {showSuccessModal && (
        <NotificationModal
          type="success"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowSuccessModal(false)}
        />
      )}

      {showErrorModal && (
        <NotificationModal
          type="error"
          title={modalTitle}
          message={modalMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}
    </div>
  );
}

// Modal para ver detalles de solicitud
interface ViewSolicitudModalProps {
  solicitud: Solicitud;
  onClose: () => void;
}

function ViewSolicitudModal({ solicitud, onClose }: ViewSolicitudModalProps) {
  const formatPrice = (price: number | null | undefined): string => {
    if (!price) return '$0.00';
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(price);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  let productosDetalle = [];
  try {
    if (solicitud.productos_json) {
      productosDetalle = JSON.parse(solicitud.productos_json);
    }
  } catch (error) {
    console.error('Error parsing productos_json:', error);
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '800px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #f1f5f9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'linear-gradient(135deg, #0056a6, #003366)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#1e293b',
                margin: '0 0 0.5rem 0'
              }}>
                Detalles de Solicitud
              </h2>
              <p style={{
                fontSize: '1rem',
                color: '#64748b',
                margin: 0
              }}>
                ID: {solicitud.solicitud_grupo_id || solicitud.id}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: '#64748b',
              cursor: 'pointer',
              padding: '0.5rem',
              borderRadius: '50%',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f1f5f9';
              e.currentTarget.style.color = '#dc2626';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = '#64748b';
            }}
          >
            ✕
          </button>
        </div>

        {/* Información del Cliente */}
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '1.5rem',
          borderRadius: '12px',
          marginBottom: '2rem',
          border: '1px solid #e2e8f0'
        }}>
          <h3 style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            Información del Cliente
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem'
          }}>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Nombre:
              </label>
              <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0', fontWeight: '600' }}>
                {solicitud.Nombre || 'No especificado'}
              </p>
            </div>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Email:
              </label>
              <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                {solicitud.cliente_email}
              </p>
            </div>
            {solicitud.cliente_whatsapp && (
              <div>
                <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                  WhatsApp:
                </label>
                <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                  📱 {solicitud.cliente_whatsapp}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Información del Pedido */}
        <div style={{
          backgroundColor: '#f0f9ff',
          padding: '1.5rem',
          borderRadius: '12px',
          marginBottom: '2rem',
          border: '1px solid #bae6fd'
        }}>
          <h3 style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            📦 Información del Pedido
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem'
          }}>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Tipo:
              </label>
              <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                {solicitud.tipo_solicitud === 'carrito' ? '🛒 Carrito' : '📦 Individual'}
              </p>
            </div>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Estado:
              </label>
              <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                <span style={{
                  padding: '0.25rem 0.75rem',
                  borderRadius: '20px',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  backgroundColor: solicitud.estado === 'pendiente' ? '#fef3c7' :
                                 solicitud.estado === 'procesando' ? '#dbeafe' :
                                 solicitud.estado === 'cotizado' ? '#dcfce7' : '#fecaca',
                  color: solicitud.estado === 'pendiente' ? '#92400e' :
                         solicitud.estado === 'procesando' ? '#1d4ed8' :
                         solicitud.estado === 'cotizado' ? '#166534' : '#dc2626'
                }}>
                  {solicitud.estado}
                </span>
              </p>
            </div>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Cantidad Total:
              </label>
              <p style={{ fontSize: '1.2rem', color: '#0056a6', margin: '0.25rem 0 0 0', fontWeight: 'bold' }}>
                {solicitud.cantidad_solicitada} unidades
              </p>
            </div>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Precio Total:
              </label>
              <p style={{ fontSize: '1.2rem', color: '#0056a6', margin: '0.25rem 0 0 0', fontWeight: 'bold' }}>
                {formatPrice(solicitud.precio_unitario)}
              </p>
            </div>
          </div>
        </div>

        {/* Productos Detallados (si es carrito) */}
        {productosDetalle.length > 0 && (
          <div style={{
            backgroundColor: '#f0fdf4',
            padding: '1.5rem',
            borderRadius: '12px',
            marginBottom: '2rem',
            border: '1px solid #bbf7d0'
          }}>
            <h3 style={{
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#1e293b',
              margin: '0 0 1rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              🛒 Productos en el Carrito ({productosDetalle.length})
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {productosDetalle.map((producto: any, index: number) => (
                <div key={index} style={{
                  backgroundColor: 'white',
                  padding: '1rem',
                  borderRadius: '8px',
                  border: '1px solid #e2e8f0',
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '1rem',
                  alignItems: 'center'
                }}>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Código:
                    </label>
                    <p style={{ fontSize: '0.875rem', color: '#1e293b', margin: '0.25rem 0 0 0', fontWeight: '600' }}>
                      {producto.numero_almacen}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Modelo:
                    </label>
                    <p style={{ fontSize: '0.875rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                      {producto.modelo}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Marca:
                    </label>
                    <p style={{ fontSize: '0.875rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                      {producto.marca || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Cantidad:
                    </label>
                    <p style={{ fontSize: '1rem', color: '#0056a6', margin: '0.25rem 0 0 0', fontWeight: 'bold' }}>
                      {producto.cantidad_solicitada}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Precio Unit.:
                    </label>
                    <p style={{ fontSize: '1rem', color: '#0056a6', margin: '0.25rem 0 0 0', fontWeight: 'bold' }}>
                      {formatPrice(producto.precio_unitario)}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '0.75rem', color: '#64748b', fontWeight: '600' }}>
                      Subtotal:
                    </label>
                    <p style={{ fontSize: '1rem', color: '#0056a6', margin: '0.25rem 0 0 0', fontWeight: 'bold' }}>
                      {formatPrice(producto.subtotal)}
                    </p>
                  </div>
                  {producto.alerta_stock && (
                    <div style={{ gridColumn: '1 / -1' }}>
                      <div style={{
                        backgroundColor: '#fef3c7',
                        color: '#92400e',
                        padding: '0.5rem',
                        borderRadius: '6px',
                        fontSize: '0.875rem',
                        fontWeight: '600'
                      }}>
                        ⚠️ Stock limitado o tiempo de entrega especial
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Fechas */}
        <div style={{
          backgroundColor: '#fefce8',
          padding: '1.5rem',
          borderRadius: '12px',
          marginBottom: '2rem',
          border: '1px solid #fde047'
        }}>
          <h3 style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            📅 Fechas Importantes
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem'
          }}>
            <div>
              <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                Fecha de Solicitud:
              </label>
              <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0', fontWeight: '600' }}>
                {formatDate(solicitud.fecha_solicitud)}
              </p>
            </div>
            {solicitud.fecha_actualizacion && (
              <div>
                <label style={{ fontSize: '0.875rem', color: '#64748b', fontWeight: '600' }}>
                  Última Actualización:
                </label>
                <p style={{ fontSize: '1rem', color: '#1e293b', margin: '0.25rem 0 0 0' }}>
                  {formatDate(solicitud.fecha_actualizacion)}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Notas */}
        {solicitud.notas && (
          <div style={{
            backgroundColor: '#f1f5f9',
            padding: '1.5rem',
            borderRadius: '12px',
            marginBottom: '2rem',
            border: '1px solid #e2e8f0'
          }}>
            <h3 style={{
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#1e293b',
              margin: '0 0 1rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              📝 Notas Adicionales
            </h3>
            <p style={{
              fontSize: '1rem',
              color: '#374151',
              lineHeight: '1.6',
              margin: 0,
              whiteSpace: 'pre-wrap'
            }}>
              {solicitud.notas}
            </p>
          </div>
        )}

        {/* Botón de cerrar */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          paddingTop: '1rem',
          borderTop: '1px solid #e2e8f0'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.875rem 2rem',
              backgroundColor: '#f1f5f9',
              color: '#64748b',
              border: '2px solid #e2e8f0',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#e2e8f0';
              e.currentTarget.style.borderColor = '#cbd5e1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#f1f5f9';
              e.currentTarget.style.borderColor = '#e2e8f0';
            }}
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
}

// Modal para editar solicitud
interface EditSolicitudModalProps {
  solicitud: Solicitud;
  onClose: () => void;
  onUpdate: (data: { estado: 'pendiente' | 'procesando' | 'cotizado' | 'rechazado'; notas?: string }) => void;
  loading: boolean;
}

function EditSolicitudModal({ solicitud, onClose, onUpdate, loading }: EditSolicitudModalProps) {
  const [estado, setEstado] = useState<'pendiente' | 'procesando' | 'cotizado' | 'rechazado'>(solicitud.estado);
  const [notas, setNotas] = useState(solicitud.notas || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate({ estado, notas: notas.trim() || undefined });
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 99999,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '20px',
        padding: '2.5rem',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        position: 'relative'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '2rem',
          paddingBottom: '1rem',
          borderBottom: '2px solid #f1f5f9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              background: 'linear-gradient(135deg, #166534, #15803d)',
              padding: '1rem',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                <path d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div>
              <h2 style={{
                fontSize: '1.8rem',
                fontWeight: 'bold',
                color: '#1e293b',
                margin: '0 0 0.5rem 0'
              }}>
                ✏️ Actualizar Solicitud
              </h2>
              <p style={{
                fontSize: '1rem',
                color: '#64748b',
                margin: 0
              }}>
                ID: {solicitud.solicitud_grupo_id || solicitud.id}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '1.5rem',
              color: loading ? '#9ca3af' : '#64748b',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '0.5rem',
              borderRadius: '50%',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = '#f1f5f9';
                e.currentTarget.style.color = '#dc2626';
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#64748b';
              }
            }}
          >
            ✕
          </button>
        </div>

        {/* Información del Cliente (solo lectura) */}
        <div style={{
          backgroundColor: '#f8fafc',
          padding: '1.5rem',
          borderRadius: '12px',
          marginBottom: '2rem',
          border: '1px solid #e2e8f0'
        }}>
          <h3 style={{
            fontSize: '1.1rem',
            fontWeight: '600',
            color: '#1e293b',
            margin: '0 0 1rem 0'
          }}>
            👤 Cliente: {solicitud.Nombre || 'Sin nombre'}
          </h3>
          <p style={{ fontSize: '0.9rem', color: '#64748b', margin: 0 }}>
            📧 {solicitud.cliente_email}
            {solicitud.cliente_whatsapp && (
              <span style={{ marginLeft: '1rem' }}>
                📱 {solicitud.cliente_whatsapp}
              </span>
            )}
          </p>
        </div>

        {/* Formulario */}
        <form onSubmit={handleSubmit}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Estado */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '1rem'
              }}>
                📊 Estado de la Solicitud *
              </label>
              <select
                value={estado}
                onChange={(e) => setEstado(e.target.value as 'pendiente' | 'procesando' | 'cotizado' | 'rechazado')}
                disabled={loading}
                required
                style={{
                  width: '100%',
                  padding: '0.875rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => {
                  if (!loading) {
                    e.target.style.borderColor = '#166534';
                    e.target.style.boxShadow = '0 0 0 3px rgba(22,101,52,0.1)';
                  }
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e2e8f0';
                  e.target.style.boxShadow = 'none';
                }}
              >
                <option value="pendiente">⏳ Pendiente</option>
                <option value="procesando">🔄 Procesando</option>
                <option value="cotizado">✅ Cotizado</option>
                <option value="rechazado">❌ Rechazado</option>
              </select>
            </div>

            {/* Notas */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontWeight: '600',
                color: '#374151',
                fontSize: '1rem'
              }}>
                📝 Notas Internas (opcional)
              </label>
              <textarea
                value={notas}
                onChange={(e) => setNotas(e.target.value)}
                disabled={loading}
                placeholder="Agregar notas sobre el estado de la solicitud, seguimiento, etc..."
                rows={4}
                style={{
                  width: '100%',
                  padding: '0.875rem',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'all 0.3s ease',
                  backgroundColor: loading ? '#f9fafb' : 'white',
                  cursor: loading ? 'not-allowed' : 'text',
                  boxSizing: 'border-box',
                  resize: 'vertical',
                  fontFamily: 'inherit'
                }}
                onFocus={(e) => {
                  if (!loading) {
                    e.target.style.borderColor = '#166534';
                    e.target.style.boxShadow = '0 0 0 3px rgba(22,101,52,0.1)';
                  }
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e2e8f0';
                  e.target.style.boxShadow = 'none';
                }}
              />
              <p style={{
                color: '#64748b',
                fontSize: '0.875rem',
                margin: '0.5rem 0 0 0'
              }}>
                Estas notas son solo para uso interno del equipo de administración
              </p>
            </div>
          </div>

          {/* Botones */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            marginTop: '2rem',
            paddingTop: '1.5rem',
            borderTop: '1px solid #e2e8f0'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                flex: 1,
                padding: '0.875rem',
                backgroundColor: loading ? '#f9fafb' : '#f1f5f9',
                color: loading ? '#9ca3af' : '#64748b',
                border: '2px solid #e2e8f0',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#e2e8f0';
                  e.currentTarget.style.borderColor = '#cbd5e1';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                  e.currentTarget.style.borderColor = '#e2e8f0';
                }
              }}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              style={{
                flex: 2,
                padding: '0.875rem',
                background: loading
                  ? 'linear-gradient(135deg, #94a3b8, #64748b)'
                  : 'linear-gradient(135deg, #166534, #15803d)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(22,101,52,0.4)';
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {loading ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid rgba(255,255,255,0.3)',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Actualizando...
                </>
              ) : (
                <>
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 6L9 17l-5-5" />
                  </svg>
                  Actualizar Solicitud
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}


